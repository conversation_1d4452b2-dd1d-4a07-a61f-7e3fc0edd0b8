{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "1b507f11b8887b00b854e8728f37389b", "packages": [{"name": "azjezz/psl", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/azjezz/psl.git", "reference": "a8685b297644f3898c986e89b15309d31b1f47bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/azjezz/psl/zipball/a8685b297644f3898c986e89b15309d31b1f47bf", "reference": "a8685b297644f3898c986e89b15309d31b1f47bf", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-sodium": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "revolt/event-loop": "^1.0.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.22.0", "php-coveralls/php-coveralls": "^2.6.0", "php-standard-library/psalm-plugin": "^2.2.1", "phpbench/phpbench": "^1.2.14", "phpunit/phpunit": "^9.6.10", "roave/infection-static-analysis-plugin": "^1.32.0", "squizlabs/php_codesniffer": "^3.7.2", "vimeo/psalm": "^5.13.1"}, "suggest": {"php-standard-library/psalm-plugin": "Psalm integration"}, "type": "library", "extra": {"thanks": {"name": "hhvm/hsl", "url": "https://github.com/hhvm/hsl"}}, "autoload": {"files": ["src/bootstrap.php"], "psr-4": {"Psl\\": "src/Psl"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "azjezz", "email": "<EMAIL>"}], "description": "PHP Standard Library", "support": {"issues": "https://github.com/azjezz/psl/issues", "source": "https://github.com/azjezz/psl/tree/2.9.0"}, "funding": [{"url": "https://github.com/azjezz", "type": "github"}], "time": "2023-12-29T10:23:32+00:00"}, {"name": "brick/math", "version": "0.10.2", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "459f2781e1a08d52ee56b0b1444086e038561e3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/459f2781e1a08d52ee56b0b1444086e038561e3f", "reference": "459f2781e1a08d52ee56b0b1444086e038561e3f", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "4.25.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.10.2"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2022-08-10T22:54:19+00:00"}, {"name": "bunny/bunny", "version": "v0.5.1", "source": {"type": "git", "url": "https://github.com/jakubkulhan/bunny.git", "reference": "e03224f076a21fa386b1da1b29381c3e0acab603"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jakubkulhan/bunny/zipball/e03224f076a21fa386b1da1b29381c3e0acab603", "reference": "e03224f076a21fa386b1da1b29381c3e0acab603", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "react/event-loop": "^1.0 || ^0.5 || ^0.4", "react/promise": "~2.2"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Bunny\\": "src/Bunny/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Performant pure-PHP AMQP (RabbitMQ) sync/async (ReactPHP) library", "keywords": ["AMQP", "bunny", "exchange", "message", "messaging", "queue", "queueing", "rabbit", "rabbitmq", "react", "react-php", "reactphp"], "support": {"issues": "https://github.com/jakubkulhan/bunny/issues", "source": "https://github.com/jakubkulhan/bunny/tree/v0.5.1"}, "time": "2021-09-12T21:45:53+00:00"}, {"name": "cbschuld/browser.php", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/cbschuld/Browser.php.git", "reference": "9d07d6410977d494d7b8ecc2f3c877645c5477d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cbschuld/Browser.php/zipball/9d07d6410977d494d7b8ecc2f3c877645c5477d9", "reference": "9d07d6410977d494d7b8ecc2f3c877645c5477d9", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"classmap": ["src/Browser.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://chrisschuld.com"}], "description": "A PHP Class to detect a user's Browser.  This encapsulation provides a breakdown of the browser and the version of the browser using the browser's user-agent string.  This is not a guaranteed solution but provides an overall accurate way to detect what browser a user is using.", "homepage": "https://chrisschuld.com/projects/browser-php-detecting-a-users-browser-from-php/", "keywords": ["browser", "detection", "user agent"], "support": {"issues": "https://github.com/cbschuld/Browser.php/issues", "source": "https://github.com/cbschuld/Browser.php/tree/v1.9.6"}, "time": "2020-04-14T18:46:44+00:00"}, {"name": "cjario/omnipay-saferpay", "version": "v2.0", "source": {"type": "git", "url": "https://github.com/cjario/omnipay-saferpay.git", "reference": "f8db3acfcdc4dfc796a22e028fd375472a37880a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cjario/omnipay-saferpay/zipball/f8db3acfcdc4dfc796a22e028fd375472a37880a", "reference": "f8db3acfcdc4dfc796a22e028fd375472a37880a", "shasum": ""}, "require": {"omnipay/common": "*"}, "require-dev": {"omnipay/tests": "*"}, "type": "library", "autoload": {"psr-4": {"Omnipay\\Saferpay\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Saferpay driver for the Omnipay payment processing library", "homepage": "https://github.com/cjario/omnipay-saferpay", "support": {"issues": "https://github.com/cjario/omnipay-saferpay/issues", "source": "https://github.com/cjario/omnipay-saferpay/tree/v2.0"}, "time": "2025-04-27T17:34:35+00:00"}, {"name": "clue/stream-filter", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/d6169430c7731d8509da7aecd0af756a5747b78e", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/php-stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.6.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-21T13:15:14+00:00"}, {"name": "colinodell/json5", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/colinodell/json5.git", "reference": "15b063f8cb5e6deb15f0cd39123264ec0d19c710"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/colinodell/json5/zipball/15b063f8cb5e6deb15f0cd39123264ec0d19c710", "reference": "15b063f8cb5e6deb15f0cd39123264ec0d19c710", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.1.3|^8.0"}, "conflict": {"scrutinizer/ocular": "1.7.*"}, "require-dev": {"mikehaertl/php-shellcommand": "^1.2.5", "phpstan/phpstan": "^1.4", "scrutinizer/ocular": "^1.6", "squizlabs/php_codesniffer": "^2.3 || ^3.0", "symfony/finder": "^4.4|^5.4|^6.0", "symfony/phpunit-bridge": "^5.4|^6.0"}, "bin": ["bin/json5"], "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"files": ["src/global.php"], "psr-4": {"ColinODell\\Json5\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Developer"}], "description": "UTF-8 compatible JSON5 parser for PHP", "homepage": "https://github.com/colinodell/json5", "keywords": ["JSON5", "json", "json5_decode", "json_decode"], "support": {"issues": "https://github.com/colinodell/json5/issues", "source": "https://github.com/colinodell/json5/tree/v2.3.0"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}], "time": "2022-12-27T16:44:40+00:00"}, {"name": "composer/pcre", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "e300eb6c535192decd27a85bc72a9290f0d6b3bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/e300eb6c535192decd27a85bc72a9290f0d6b3bd", "reference": "e300eb6c535192decd27a85bc72a9290f0d6b3bd", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T20:21:48+00:00"}, {"name": "contributte/aop", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/contributte/aop.git", "reference": "80dcdcbe40c7a4aedce159f3d47b111f41a61de0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/aop/zipball/80dcdcbe40c7a4aedce159f3d47b111f41a61de0", "reference": "80dcdcbe40c7a4aedce159f3d47b111f41a61de0", "shasum": ""}, "require": {"nette/di": "^3.0.3", "nette/php-generator": "^3.5", "nette/tokenizer": "~3.0", "php": ">=8.0", "symfony/property-access": "^4.4|~5.0"}, "require-dev": {"doctrine/collections": "~1.6", "latte/latte": "~2.10", "nette/application": "^3.1", "nette/bootstrap": "~3.1", "nette/caching": "~3.1", "nette/component-model": "~3.0", "nette/database": "~3.1", "nette/finder": "~2.5", "nette/forms": "~3.1", "nette/http": "~3.1", "nette/mail": "~3.1", "nette/neon": "~3.1", "nette/robot-loader": "^3.4", "nette/safe-stream": "~2.4", "nette/security": "~3.0", "nette/utils": "^3.2", "ninjify/qa": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^9.5", "tracy/tracy": "~2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Contributte\\Aop\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "AOP for Nette Framework Dependency Injection Container", "homepage": "https://github.com/contributte/aop", "keywords": ["aop", "aspect", "contributte", "di", "dic", "nette"], "support": {"issues": "https://github.com/contributte/aop/issues", "source": "https://github.com/contributte/aop/tree/v3.0.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-04-23T17:01:27+00:00"}, {"name": "contributte/application", "version": "v0.5.1", "source": {"type": "git", "url": "https://github.com/contributte/application.git", "reference": "2579ab2bc3b7c95ae32a2e664ac9a8cc038777f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/application/zipball/2579ab2bc3b7c95ae32a2e664ac9a8cc038777f9", "reference": "2579ab2bc3b7c95ae32a2e664ac9a8cc038777f9", "shasum": ""}, "require": {"nette/application": "^3.0.0", "php": ">=7.2"}, "require-dev": {"nette/http": "~2.4.8 || ~3.0.0", "ninjify/nunjuck": "^0.3.0", "ninjify/qa": "^0.9.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-deprecation-rules": "^0.11.0", "phpstan/phpstan-nette": "^0.11.1", "phpstan/phpstan-shim": "^0.11.2", "phpstan/phpstan-strict-rules": "^0.11.0", "psr/http-message": "~1.0.1", "tracy/tracy": "~2.6.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Application\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Extra contrib to nette/application", "homepage": "https://github.com/contributte/application", "keywords": ["application", "component", "control", "nette", "presenter"], "support": {"issues": "https://github.com/contributte/application/issues", "source": "https://github.com/contributte/application/tree/v0.5.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-03-10T21:48:30+00:00"}, {"name": "contributte/console", "version": "v0.9.2", "source": {"type": "git", "url": "https://github.com/contributte/console.git", "reference": "2ef14843453414f74b21f9f9b3d66e8f185f62ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/console/zipball/2ef14843453414f74b21f9f9b3d66e8f185f62ab", "reference": "2ef14843453414f74b21f9f9b3d66e8f185f62ab", "shasum": ""}, "require": {"contributte/di": "^0.5.1", "php": ">=7.2", "symfony/console": "^4.2.9|^5.0.0|^6.0.0"}, "require-dev": {"nette/http": "~3.0.1", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "symfony/event-dispatcher": "^4.3 || ^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best Symfony Console for Nette Framework", "homepage": "https://github.com/contributte/console", "keywords": ["console", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/console/issues", "source": "https://github.com/contributte/console/tree/v0.9.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-02-09T12:46:08+00:00"}, {"name": "contributte/di", "version": "v0.5.3", "source": {"type": "git", "url": "https://github.com/contributte/di.git", "reference": "7fb8abed72ddf6b8bd9819fb709f2c0a024d6ffc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/di/zipball/7fb8abed72ddf6b8bd9819fb709f2c0a024d6ffc", "reference": "7fb8abed72ddf6b8bd9819fb709f2c0a024d6ffc", "shasum": ""}, "require": {"nette/di": "~3.0.13", "nette/utils": "^3.0.3", "php": ">=7.2"}, "conflict": {"nette/di": "<3.0.0", "nette/schema": "<1.1.0"}, "require-dev": {"nette/bootstrap": "^3.0.0", "nette/robot-loader": "^3.0.4", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.13", "phpstan/phpstan": "^1.8.0", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-nette": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.0.0"}, "suggest": {"nette/reflection": "to use AutoloadExtension[CompilerExtension]", "nette/robot-loader": "to use AutoloadExtension[CompilerExtension]"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\DI\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Extra contrib to nette/di", "homepage": "https://github.com/contributte/di", "keywords": ["dependency", "inject", "nette"], "support": {"issues": "https://github.com/contributte/di/issues", "source": "https://github.com/contributte/di/tree/v0.5.3"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-07-08T10:07:27+00:00"}, {"name": "contributte/elastica", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/contributte/elastica.git", "reference": "c6c0d6828ef4318ef51b7196f4d593ac956ca0fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/elastica/zipball/c6c0d6828ef4318ef51b7196f4d593ac956ca0fa", "reference": "c6c0d6828ef4318ef51b7196f4d593ac956ca0fa", "shasum": ""}, "require": {"nette/di": "^3.0", "nette/utils": "^3.0", "php": ">=7.2", "ruflin/elastica": "^7.0"}, "require-dev": {"nette/bootstrap": "^3.0", "nette/http": "^3.0", "ninjify/qa": "^v0.10", "phpstan/phpstan": "^0.12.6", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0|8.0|^9.0", "tracy/tracy": "^2.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Elastica\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Elastica implementation for Nette Framework", "homepage": "https://github.com/contributte/elastica", "keywords": ["elastic", "elastica", "elasticsearch", "es", "nette", "search"], "support": {"issues": "https://github.com/contributte/elastica/issues", "source": "https://github.com/contributte/elastica/tree/v1.0.0"}, "time": "2020-12-20T20:16:29+00:00"}, {"name": "contributte/event-dispatcher", "version": "v0.8.1", "source": {"type": "git", "url": "https://github.com/contributte/event-dispatcher.git", "reference": "d7bf70522438b62221b0adb50c29c05c7923e27c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/event-dispatcher/zipball/d7bf70522438b62221b0adb50c29c05c7923e27c", "reference": "d7bf70522438b62221b0adb50c29c05c7923e27c", "shasum": ""}, "require": {"nette/di": "^3.0.0", "php": ">=7.2", "symfony/event-dispatcher": "^4.3.1 || ^5.0.0 || ^6.0.0"}, "conflict": {"nette/di": "<3.0.0"}, "require-dev": {"ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.13", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.9.x-dev"}}, "autoload": {"psr-4": {"Contributte\\EventDispatcher\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best event dispatcher / event manager / event emitter for Nette Framework", "homepage": "https://github.com/contributte/event-dispatcher", "keywords": ["dispatcher", "emitter", "event", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/event-dispatcher/issues", "source": "https://github.com/contributte/event-dispatcher/tree/v0.8.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-02-09T13:05:59+00:00"}, {"name": "contributte/forms-bootstrap", "version": "v0.5.3", "source": {"type": "git", "url": "https://github.com/contributte/forms-bootstrap.git", "reference": "9708a54e509504ffffea1e5d9527f0d0812fd506"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/forms-bootstrap/zipball/9708a54e509504ffffea1e5d9527f0d0812fd506", "reference": "9708a54e509504ffffea1e5d9527f0d0812fd506", "shasum": ""}, "require": {"nette/application": "^3.0", "nette/forms": "3.1.7", "php": ">=7.2"}, "require-dev": {"ninjify/qa": "^v0.12", "phpstan/phpstan": "^0.12.6", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\FormsBootstrap\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Nette extension for Bootstrap forms", "homepage": "https://github.com/contributte/forms-bootstrap", "keywords": ["Forms", "bootstrap", "library", "nette"], "support": {"issues": "https://github.com/contributte/forms-bootstrap/issues", "source": "https://github.com/contributte/forms-bootstrap/tree/v0.5.3"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-05-23T08:52:09+00:00"}, {"name": "contributte/forms-multiplier", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/contributte/forms-multiplier.git", "reference": "d7854344b3bc6c363a0dd31daa36bc7c9dfa4265"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/forms-multiplier/zipball/d7854344b3bc6c363a0dd31daa36bc7c9dfa4265", "reference": "d7854344b3bc6c363a0dd31daa36bc7c9dfa4265", "shasum": ""}, "require": {"nette/forms": "^3.1.0", "php": ">=7.2"}, "require-dev": {"codeception/codeception": "^4.0.0", "codeception/module-asserts": "^1.3", "codeception/module-phpbrowser": "^1.0", "latte/latte": "^2.7.0", "nette/application": "^3.0.0", "nette/di": "^3.0.0", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "webchemistry/testing-helpers": "~2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"psr-4": {"Contributte\\FormMultiplier\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "description": "Multiplier for nette forms", "keywords": ["Forms", "contributte", "multiplier", "nette"], "support": {"issues": "https://github.com/contributte/forms-multiplier/issues", "source": "https://github.com/contributte/forms-multiplier/tree/v3.2.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-03-09T08:05:53+00:00"}, {"name": "contributte/forms-wizard", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/contributte/forms-wizard.git", "reference": "ed2697e6a07e052062e9dd987ae77faf10b8ae72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/forms-wizard/zipball/ed2697e6a07e052062e9dd987ae77faf10b8ae72", "reference": "ed2697e6a07e052062e9dd987ae77faf10b8ae72", "shasum": ""}, "require": {"nette/forms": "^3.0", "nette/http": "^3.0", "php": ">=7.1"}, "require-dev": {"codeception/codeception": "^4.1.20", "codeception/module-asserts": "^1.3.1", "codeception/module-phpbrowser": "^1.0.2", "latte/latte": "^2.7", "nette/application": "^3.0", "nette/di": "^3.0", "nette/tester": "^2.3.2", "ninjify/nunjuck": "^0.3", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "webchemistry/testing-helpers": "^3.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Contributte\\FormWizard\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "description": "Wizard component for nette/forms", "keywords": ["Forms", "contributte", "nette", "wizard"], "support": {"issues": "https://github.com/contributte/forms-wizard/issues", "source": "https://github.com/contributte/forms-wizard/tree/3.1.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-06-21T19:21:42+00:00"}, {"name": "contributte/guzzlette", "version": "v3.2.2", "source": {"type": "git", "url": "https://github.com/contributte/guzzlette.git", "reference": "b083c68e92189aecb60c4a2b2cfd9a869a35144f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/guzzlette/zipball/b083c68e92189aecb60c4a2b2cfd9a869a35144f", "reference": "b083c68e92189aecb60c4a2b2cfd9a869a35144f", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3.3|^7.0", "nette/di": "~3.0.0", "php": ">=7.2", "tracy/tracy": "^2.8.0"}, "require-dev": {"mockery/mockery": "^1.0", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Guzzlette\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle integration into Nette Framework", "homepage": "https://github.com/contributte/guzzlette", "keywords": ["Guzzle", "nette"], "support": {"issues": "https://github.com/contributte/guzzlette/issues", "source": "https://github.com/contributte/guzzlette/tree/v3.2.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-07-19T17:04:26+00:00"}, {"name": "contributte/menu-control", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/contributte/menu-control.git", "reference": "f606e8f42407066aaf7877f367fcd5f42c9e36ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/menu-control/zipball/f606e8f42407066aaf7877f367fcd5f42c9e36ed", "reference": "f606e8f42407066aaf7877f367fcd5f42c9e36ed", "shasum": ""}, "require": {"nette/application": "^3.0.4", "nette/di": "^3.0", "nette/http": "^3.0", "nette/utils": "^3.0", "php": ">=7.1"}, "require-dev": {"mockery/mockery": "dev-master", "nette/tester": "^2.1", "ninjify/coding-standard": "^0.9.0"}, "type": "library", "autoload": {"psr-4": {"Contributte\\MenuControl\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://kudera.dev"}, {"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://tomasjacik.cz"}], "description": "Menu control for Nette framework", "homepage": "https://github.com/contributte/menu-control", "keywords": ["breadcrumbs", "component", "control", "menu", "nette"], "support": {"issues": "https://github.com/contributte/menu-control/issues", "source": "https://github.com/contributte/menu-control/tree/2.2.1"}, "time": "2020-03-28T13:40:11+00:00"}, {"name": "contributte/monolog", "version": "v0.5.0", "source": {"type": "git", "url": "https://github.com/contributte/monolog.git", "reference": "a47a3f634c7d9ba21f0923d3479f38a459767182"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/monolog/zipball/a47a3f634c7d9ba21f0923d3479f38a459767182", "reference": "a47a3f634c7d9ba21f0923d3479f38a459767182", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "monolog/monolog": "^2.0.0", "nette/utils": "^3.0.0", "php": ">=7.2"}, "conflict": {"tracy/tracy": "<2.6.2"}, "require-dev": {"ninjify/qa": "^0.10.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^8.1.3", "tracy/tracy": "~2.6.2 || ~2.7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Monolog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Monolog integration into Nette Framework", "homepage": "https://github.com/contributte/monolog", "keywords": ["logging", "monolog", "nette"], "support": {"issues": "https://github.com/contributte/monolog/issues", "source": "https://github.com/contributte/monolog/tree/v0.5.0"}, "time": "2020-12-10T14:31:08+00:00"}, {"name": "contributte/pdf", "version": "v6.1.0", "source": {"type": "git", "url": "https://github.com/contributte/pdf.git", "reference": "9c3feafefb970e92ffc3e98defbc3854733fb7eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/pdf/zipball/9c3feafefb970e92ffc3e98defbc3854733fb7eb", "reference": "9c3feafefb970e92ffc3e98defbc3854733fb7eb", "shasum": ""}, "require": {"mpdf/mpdf": "^8.0", "nette/application": "~3.0", "nette/http": "~3.0", "php": ">=7.1"}, "require-dev": {"latte/latte": "^2.10", "nette/di": "^3.0.0", "ninjify/nunjuck": "^0.3.0", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "symfony/css-selector": "^4.4|^5.0", "symfony/dom-crawler": "^4.4|^5.0"}, "suggest": {"nette/nette": "PHP framework to which this extension belongs to.", "symfony/dom-crawler": "Allows filtering html tags."}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "autoload": {"files": ["src/compatibility.php"], "psr-4": {"Contributte\\PdfResponse\\": "src/", "Joseki\\Application\\Responses\\": "src-old/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.webnazakazku.cz/"}, {"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Pdf response extension for Nette Framework", "support": {"issues": "https://github.com/contributte/pdf/issues", "source": "https://github.com/contributte/pdf/tree/v6.1.0"}, "funding": [{"url": "https://github.com/f3l1x", "type": "github"}, {"url": "https://github.com/petrparolek", "type": "github"}], "time": "2021-02-28T18:30:39+00:00"}, {"name": "contributte/rabbitmq", "version": "v9.1.0", "source": {"type": "git", "url": "https://github.com/contributte/rabbitmq.git", "reference": "f9a641fbb1a13e4c1b3ecd29a92ab5dd6a724a26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/rabbitmq/zipball/f9a641fbb1a13e4c1b3ecd29a92ab5dd6a724a26", "reference": "f9a641fbb1a13e4c1b3ecd29a92ab5dd6a724a26", "shasum": ""}, "require": {"bunny/bunny": "^0.2.4 || ^0.3 || ^0.4 || ^0.5", "nette/di": "^2.4.17 || ^3.0.7", "nette/utils": "^2.5.4 || ^3.2.0", "php": ">=7.4", "symfony/console": "~3.3 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"contributte/code-rules": "^1.1.0", "mockery/mockery": "^1.3.3", "nette/neon": "^2.4.3 || ^3.2.1", "ninjify/nunjuck": "^0.4", "tracy/tracy": "^2.5"}, "suggest": {"tracy/tracy": "Allows using tracy bar panel"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.0.x-dev"}}, "autoload": {"psr-4": {"Contributte\\RabbitMQ\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Nette extension for RabbitMQ (using BunnyPHP)", "homepage": "https://github.com/contributte/rabbitmq", "keywords": ["bunny", "bunny<PERSON>p", "extension", "nette", "php", "rabbit", "rabbitmq"], "support": {"issues": "https://github.com/contributte/rabbitmq/issues", "source": "https://github.com/contributte/rabbitmq/tree/v9.1.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-02-07T09:01:15+00:00"}, {"name": "contributte/redis", "version": "v0.5.2", "source": {"type": "git", "url": "https://github.com/contributte/redis.git", "reference": "f784f8c5d7c80b7b77e16716d088bf434b7530eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/redis/zipball/f784f8c5d7c80b7b77e16716d088bf434b7530eb", "reference": "f784f8c5d7c80b7b77e16716d088bf434b7530eb", "shasum": ""}, "require": {"ext-json": "*", "nette/di": "^2.4.17 || ^3.0.1", "php": ">=7.2", "predis/predis": "^1.1.6"}, "require-dev": {"mockery/mockery": "^1.3.3", "nette/caching": "^2.5.0 || ^3.1.3", "nette/http": "^2.4.0 || ^3.0.1", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "tracy/tracy": "^2.7.0"}, "suggest": {"ext-igbinary": "For better serialization"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Redis\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Redis client integration into Nette framework", "homepage": "https://github.com/contributte/redis", "keywords": ["cache", "nette", "predis", "redis"], "support": {"issues": "https://github.com/contributte/redis/issues", "source": "https://github.com/contributte/redis/tree/v0.5.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-05-18T09:03:13+00:00"}, {"name": "dibi/dibi", "version": "v4.2.6", "source": {"type": "git", "url": "https://github.com/dg/dibi.git", "reference": "9d5d430d3d04ad8aff0e1570390e9cfbb7f3c538"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/dibi/zipball/9d5d430d3d04ad8aff0e1570390e9cfbb7f3c538", "reference": "9d5d430d3d04ad8aff0e1570390e9cfbb7f3c538", "shasum": ""}, "require": {"php": ">=7.2"}, "replace": {"dg/dibi": "*"}, "require-dev": {"nette/di": "^3.0", "nette/tester": "~2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "~2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Dibi is Database Abstraction Library for PHP", "homepage": "https://dibiphp.com", "keywords": ["access", "database", "dbal", "mssql", "mysql", "odbc", "oracle", "pdo", "postgresql", "sqlite", "sqlsrv"], "support": {"issues": "https://github.com/dg/dibi/issues", "source": "https://github.com/dg/dibi/tree/v4.2.6"}, "time": "2022-01-19T17:38:15+00:00"}, {"name": "doctrine/inflector", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "d9d313a36c872fd6ee06d9a6cbcf713eaa40f024"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/d9d313a36c872fd6ee06d9a6cbcf713eaa40f024", "reference": "d9d313a36c872fd6ee06d9a6cbcf713eaa40f024", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.6"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2022-10-20T09:10:12+00:00"}, {"name": "dodo-it/dibi-entity-generator", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/dodo-it/dibi-entity-generator.git", "reference": "67c1324ce043ab37964f630540f69227175fef91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dodo-it/dibi-entity-generator/zipball/67c1324ce043ab37964f630540f69227175fef91", "reference": "67c1324ce043ab37964f630540f69227175fef91", "shasum": ""}, "require": {"dibi/dibi": "^4.0", "dodo-it/entity-generator": "~1.0", "nette/di": "^2.4 | ^3.0", "php": "^7.1|^8.0"}, "require-dev": {"phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"DodoIt\\DibiEntityGenerator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>"}], "description": "Entity generator from database for dibi", "keywords": ["database", "dibi", "entity", "generator", "mysql", "nette", "table"], "support": {"issues": "https://github.com/dodo-it/dibi-entity-generator/issues", "source": "https://github.com/dodo-it/dibi-entity-generator/tree/1.1.1"}, "time": "2020-12-08T11:17:45+00:00"}, {"name": "dodo-it/entity-generator", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/dodo-it/entity-generator.git", "reference": "b1ac63f3c2dac9ab037c3ea4d8f3d166eaad0692"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dodo-it/entity-generator/zipball/b1ac63f3c2dac9ab037c3ea4d8f3d166eaad0692", "reference": "b1ac63f3c2dac9ab037c3ea4d8f3d166eaad0692", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "nette/php-generator": "^3.0", "nette/utils": "^3.0", "php": "^8.0"}, "require-dev": {"ninjify/qa": ">=0.8", "phpunit/phpunit": ">=9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"DodoIt\\EntityGenerator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>"}], "description": "Entity generator from database for almost any DBAL", "keywords": ["database", "entity", "generator", "mysql", "nette", "pdo", "table"], "support": {"issues": "https://github.com/dodo-it/entity-generator/issues", "source": "https://github.com/dodo-it/entity-generator/tree/1.2.1"}, "time": "2022-02-23T22:09:23+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.0", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "1890f9d7fde076b5a3ddcf579a802af05b2e781b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/1890f9d7fde076b5a3ddcf579a802af05b2e781b", "reference": "1890f9d7fde076b5a3ddcf579a802af05b2e781b", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.0"}, "time": "2022-02-03T13:40:04+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "abe3791d231167f14eb80d413420d1eab91163a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/abe3791d231167f14eb80d413420d1eab91163a8", "reference": "abe3791d231167f14eb80d413420d1eab91163a8", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.0.1"}, "time": "2020-02-14T23:11:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "92b8161404ab1ad84059ebed41d9f757e897ce74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/92b8161404ab1ad84059ebed41d9f757e897ce74", "reference": "92b8161404ab1ad84059ebed41d9f757e897ce74", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.0"}, "time": "2021-11-16T11:51:30+00:00"}, {"name": "firebase/php-jwt", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "018dfc4e1da92ad8a1b90adc4893f476a3b41cb8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/018dfc4e1da92ad8a1b90adc4893f476a3b41cb8", "reference": "018dfc4e1da92ad8a1b90adc4893f476a3b41cb8", "shasum": ""}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^1.1", "phpunit/phpunit": "^7.5||^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.3.0"}, "time": "2022-07-15T16:48:45+00:00"}, {"name": "goetas-webservices/xsd-reader", "version": "0.4.4", "source": {"type": "git", "url": "https://github.com/goetas-webservices/xsd-reader.git", "reference": "ee51ff52b3033041907aad3c358d05948c49470e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/goetas-webservices/xsd-reader/zipball/ee51ff52b3033041907aad3c358d05948c49470e", "reference": "ee51ff52b3033041907aad3c358d05948c49470e", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.6", "sebastian/phpcpd": "^6.0", "vimeo/psalm": "^5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "autoload": {"psr-4": {"GoetasWebservices\\CS\\": "./php-cs-fixer/", "GoetasWebservices\\XML\\XSDReader\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Read any XML Schema (XSD) programmatically with PHP", "keywords": ["parser", "php", "xml", "xmlschema", "xsd"], "support": {"issues": "https://github.com/goetas-webservices/xsd-reader/issues", "source": "https://github.com/goetas-webservices/xsd-reader/tree/0.4.4"}, "funding": [{"url": "https://www.goetas.com/", "type": "custom"}, {"url": "https://github.com/goetas", "type": "github"}, {"url": "https://www.patreon.com/goetas", "type": "patreon"}], "time": "2024-01-16T14:37:10+00:00"}, {"name": "google/apiclient", "version": "v2.12.6", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "f92aa126903a9e2da5bd41a280d9633cb249e79e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/f92aa126903a9e2da5bd41a280d9633cb249e79e", "reference": "f92aa126903a9e2da5bd41a280d9633cb249e79e", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0||~3.0||~4.0||~5.0||~6.0", "google/apiclient-services": "~0.200", "google/auth": "^1.10", "guzzlehttp/guzzle": "~5.3.3||~6.0||~7.0", "guzzlehttp/psr7": "^1.8.4||^2.2.1", "monolog/monolog": "^1.17||^2.0||^3.0", "php": "^5.6|^7.0|^8.0", "phpseclib/phpseclib": "~2.0||^3.0.2"}, "require-dev": {"cache/filesystem-adapter": "^0.3.2|^1.1", "composer/composer": "^1.10.22", "phpcompatibility/php-compatibility": "^9.2", "phpspec/prophecy-phpunit": "^1.1||^2.0", "phpunit/phpunit": "^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"files": ["src/aliases.php"], "psr-4": {"Google\\": "src/"}, "classmap": ["src/aliases.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client/issues", "source": "https://github.com/googleapis/google-api-php-client/tree/v2.12.6"}, "time": "2022-06-06T20:00:19+00:00"}, {"name": "google/apiclient-services", "version": "v0.258.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "71eb32534aba05e373fe317c1373a9645065881c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/71eb32534aba05e373fe317c1373a9645065881c", "reference": "71eb32534aba05e373fe317c1373a9645065881c", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^5.7||^8.5.13"}, "type": "library", "autoload": {"files": ["autoload.php"], "psr-4": {"Google\\Service\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client-services/issues", "source": "https://github.com/googleapis/google-api-php-client-services/tree/v0.258.0"}, "time": "2022-07-18T01:10:11+00:00"}, {"name": "google/auth", "version": "v1.21.1", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "aa3b9ca29258ac6347ce3c8937a2418c5d78f840"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/aa3b9ca29258ac6347ce3c8937a2418c5d78f840", "reference": "aa3b9ca29258ac6347ce3c8937a2418c5d78f840", "shasum": ""}, "require": {"firebase/php-jwt": "^5.5||^6.0", "guzzlehttp/guzzle": "^6.2.1|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "php": "^7.1||^8.0", "psr/cache": "^1.0|^2.0|^3.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "^0.2.5|^0.5.1", "phpseclib/phpseclib": "^2.0.31", "phpspec/prophecy-phpunit": "^1.1", "phpunit/phpunit": "^7.5||^8.5", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.21.1"}, "time": "2022-05-16T19:34:15+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.4.5", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "1dd98b0564cb3f6bd16ce683cb755f94c10fbd82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/1dd98b0564cb3f6bd16ce683cb755f94c10fbd82", "reference": "1dd98b0564cb3f6bd16ce683cb755f94c10fbd82", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9 || ^2.4", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.4-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.5"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:13+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2021-10-22T20:56:57+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "13388f00956b1503577598873fffb5ae994b5737"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/13388f00956b1503577598873fffb5ae994b5737", "reference": "13388f00956b1503577598873fffb5ae994b5737", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.4.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2022-06-20T21:43:11+00:00"}, {"name": "hobnob/xml-stream-reader", "version": "v1.0.8", "source": {"type": "git", "url": "https://github.com/hobnob/xmlStreamReader.git", "reference": "fc26cbc5d5802fa5db7d46277ae6cc9bf4c4ee3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hobnob/xmlStreamReader/zipball/fc26cbc5d5802fa5db7d46277ae6cc9bf4c4ee3e", "reference": "fc26cbc5d5802fa5db7d46277ae6cc9bf4c4ee3e", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "library", "autoload": {"psr-0": {"Hobnob\\XmlStreamReader": "/classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "PHP SAX XML Stream Reader", "homepage": "https://github.com/hobnob/xmlStreamReader", "keywords": ["stream", "xml"], "support": {"issues": "https://github.com/hobnob/xmlStreamReader/issues", "source": "https://github.com/hobnob/xmlStreamReader/tree/master"}, "time": "2015-03-10T15:52:44+00:00"}, {"name": "hubspot/api-client", "version": "8.3.0", "source": {"type": "git", "url": "https://github.com/HubSpot/hubspot-api-php.git", "reference": "93d63bd3055bb04c84825be475bcd195e35bf3cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/HubSpot/hubspot-api-php/zipball/93d63bd3055bb04c84825be475bcd195e35bf3cd", "reference": "93d63bd3055bb04c84825be475bcd195e35bf3cd", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/guzzle": "^7.3", "guzzlehttp/psr7": "^1.7 || ^2.0", "php": ">=7.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "phpspec/phpspec": "^7.1", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"HubSpot\\": "lib/", "Hubspot\\Tests\\": "tests/", "HubSpot\\Client\\": "codegen/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "HubSpot", "homepage": "https://hubspot.com/"}], "description": "Hubspot API client", "homepage": "https://hubspot.com/", "keywords": ["api", "hubspot", "php", "sdk", "swagger"], "support": {"issues": "https://github.com/HubSpot/hubspot-api-php/issues", "source": "https://github.com/HubSpot/hubspot-api-php/tree/8.3.0"}, "time": "2022-07-15T12:48:11+00:00"}, {"name": "hubspot/hubspot-php", "version": "4.0.2", "source": {"type": "git", "url": "https://github.com/HubSpot/hubspot-php.git", "reference": "4efda64a56f054889e16d0f361712e49b1f8de9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/HubSpot/hubspot-php/zipball/4efda64a56f054889e16d0f361712e49b1f8de9d", "reference": "4efda64a56f054889e16d0f361712e49b1f8de9d", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^7.3", "php": ">=7.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "phpspec/phpspec": "^7.1", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"SevenShores\\Hubspot\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ryanwinchester.ca"}], "description": "HubSpot PHP API client", "keywords": ["api", "hubspot"], "support": {"issues": "https://github.com/HubSpot/hubspot-php/issues", "source": "https://github.com/HubSpot/hubspot-php/tree/v4.0.2"}, "time": "2022-07-28T10:42:33+00:00"}, {"name": "jschaedl/iban-validation", "version": "v1.8.2", "source": {"type": "git", "url": "https://github.com/jschaedl/iban-validation.git", "reference": "8b635c4872ff8818e8e9325058092c5add8defcd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jschaedl/iban-validation/zipball/8b635c4872ff8818e8e9325058092c5add8defcd", "reference": "8b635c4872ff8818e8e9325058092c5add8defcd", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/options-resolver": "^3.4|^4.4.11|^5.1|^6.0", "symfony/yaml": "^3.4|^4.4.11|^5.1|^6.0"}, "require-dev": {"phpunit/phpunit": "^7|^8|^9"}, "suggest": {"ext-bcmath": "This library makes use of bcmod function, so an installed bcmath extension is recommended."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Iban\\Validation\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.linkedin.com/in/janschaedlich"}], "description": "A small library for validating International BankAccount Numbers (IBANs).", "homepage": "https://github.com/jschaedl/iban-validation", "keywords": ["IBAN", "validation"], "support": {"issues": "https://github.com/jschaedl/iban-validation/issues", "source": "https://github.com/jschaedl/iban-validation/tree/v1.8.2"}, "time": "2022-03-07T19:25:35+00:00"}, {"name": "laminas/laminas-code", "version": "4.13.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-code.git", "reference": "7353d4099ad5388e84737dd16994316a04f48dbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-code/zipball/7353d4099ad5388e84737dd16994316a04f48dbf", "reference": "7353d4099ad5388e84737dd16994316a04f48dbf", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "require-dev": {"doctrine/annotations": "^2.0.1", "ext-phar": "*", "laminas/laminas-coding-standard": "^2.5.0", "laminas/laminas-stdlib": "^3.17.0", "phpunit/phpunit": "^10.3.3", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.15.0"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "homepage": "https://laminas.dev", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-10-18T10:00:55+00:00"}, {"name": "latte/latte", "version": "v2.11.5", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "89e647e51213af8a270fe9903b8735a2f6c83ad1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/89e647e51213af8a270fe9903b8735a2f6c83ad1", "reference": "89e647e51213af8a270fe9903b8735a2f6c83ad1", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": ">=7.1 <8.2"}, "conflict": {"nette/application": "<2.4.1"}, "require-dev": {"nette/php-generator": "^3.3.4", "nette/tester": "^2.0", "nette/utils": "^3.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.11-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v2.11.5"}, "time": "2022-06-26T10:12:18+00:00"}, {"name": "league/uri", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "c7a7e9c5b096c0591a60324276dc901c561fb821"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/c7a7e9c5b096c0591a60324276dc901c561fb821", "reference": "c7a7e9c5b096c0591a60324276dc901c561fb821", "shasum": ""}, "require": {"league/uri-interfaces": "^7.0", "php": "^8.1"}, "conflict": {"league/uri-schemes": "^1.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-fileinfo": "to create Data URI from file contennts", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain", "league/uri-components": "Needed to easily manipulate URI objects components", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.0.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2023-08-10T14:26:14+00:00"}, {"name": "league/uri-components", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-components.git", "reference": "de986d9f3a64085064472f9b9e3a2946d824e3c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-components/zipball/de986d9f3a64085064472f9b9e3a2946d824e3c5", "reference": "de986d9f3a64085064472f9b9e3a2946d824e3c5", "shasum": ""}, "require": {"league/uri": "^7.0", "php": "^8.1"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-fileinfo": "to create Data URI from file contennts", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI components manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["authority", "components", "fragment", "host", "middleware", "modifier", "path", "port", "query", "rfc3986", "scheme", "uri", "url", "userinfo"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-components/tree/7.0.0"}, "funding": [{"url": "https://github.com/nyamsprod", "type": "github"}], "time": "2023-08-10T14:26:14+00:00"}, {"name": "league/uri-interfaces", "version": "7.0.0-beta.2", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "684e688fa3b3cf3726e651af16961df30c38f642"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/684e688fa3b3cf3726e651af16961df30c38f642", "reference": "684e688fa3b3cf3726e651af16961df30c38f642", "shasum": ""}, "require": {"php": "^8.1"}, "suggest": {"ext-intl": "to use the IDNA feature", "symfony/polyfill-intl-idn": "to use the IDNA feature via Symfony Polyfill"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interfaces and classes for URI representation and interaction", "homepage": "https://uri.thephpleague.com", "keywords": ["authority", "components", "fragment", "host", "idna", "path", "port", "query", "rfc3986", "scheme", "uri", "url", "userinfo"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.0.0-beta.2"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2023-06-17T10:08:33+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "6187e9cc4493da94b9b63eb2315821552015fca9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/6187e9cc4493da94b9b63eb2315821552015fca9", "reference": "6187e9cc4493da94b9b63eb2315821552015fca9", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.1"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^10.0", "vimeo/psalm": "^5.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.1"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "time": "2024-10-10T12:33:01+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "milo/embedded-svg", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/milo/embedded-svg.git", "reference": "227501f591f069ea026d620ccab00c4b2e38e359"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/milo/embedded-svg/zipball/227501f591f069ea026d620ccab00c4b2e38e359", "reference": "227501f591f069ea026d620ccab00c4b2e38e359", "shasum": ""}, "require": {"ext-dom": "*", "latte/latte": "^2.4.0", "php": ">=7.1.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/milo"}], "description": "Latte macro to embed SVG from file into HTML", "keywords": ["embedded", "latte", "macro", "nette", "svg"], "support": {"issues": "https://github.com/milo/embedded-svg/issues", "source": "https://github.com/milo/embedded-svg/tree/master"}, "time": "2019-04-15T16:57:50+00:00"}, {"name": "moneyphp/money", "version": "v4.6.0", "source": {"type": "git", "url": "https://github.com/moneyphp/money.git", "reference": "ddf6a86b574808f8844777ed4e8c4f92a10dac9b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/ddf6a86b574808f8844777ed4e8c4f92a10dac9b", "reference": "ddf6a86b574808f8844777ed4e8c4f92a10dac9b", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-filter": "*", "ext-json": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cache/taggable-cache": "^1.1.0", "doctrine/coding-standard": "^12.0", "doctrine/instantiator": "^1.5.0 || ^2.0", "ext-gmp": "*", "ext-intl": "*", "florianv/exchanger": "^2.8.1", "florianv/swap": "^4.3.0", "moneyphp/crypto-currencies": "^1.1.0", "moneyphp/iso-currencies": "^3.4", "php-http/message": "^1.16.0", "php-http/mock-client": "^1.6.0", "phpbench/phpbench": "^1.2.5", "phpunit/phpunit": "^10.5.9", "psalm/plugin-phpunit": "^0.18.4", "psr/cache": "^1.0.1 || ^2.0 || ^3.0", "vimeo/psalm": "~5.20.0"}, "suggest": {"ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "support": {"issues": "https://github.com/moneyphp/money/issues", "source": "https://github.com/moneyphp/money/tree/v4.6.0"}, "time": "2024-11-22T10:59:03+00:00"}, {"name": "monolog/monolog", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "5579edf28aee1190a798bfa5be8bc16c563bd524"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/5579edf28aee1190a798bfa5be8bc16c563bd524", "reference": "5579edf28aee1190a798bfa5be8bc16c563bd524", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5.14", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.7.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:59:12+00:00"}, {"name": "mpdf/mpdf", "version": "v8.1.2", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "a8a22f4874157e490d41b486053a20bec42e182c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/a8a22f4874157e490d41b486053a20bec42e182c", "reference": "a8a22f4874157e490d41b486053a20bec42e182c", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0", "php-http/message-factory": "^1.0", "psr/http-message": "^1.0", "psr/log": "^1.0 || ^2.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "^2.4", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "http://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2022-08-15T08:15:09+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2022-03-03T13:19:32+00:00"}, {"name": "nesbot/carbon", "version": "2.64.1", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "f2e59963f4c4f4fdfb9fcfd752e8d2e2b79a4e2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/f2e59963f4c4f4fdfb9fcfd752e8d2e2b79a4e2c", "reference": "f2e59963f4c4f4fdfb9fcfd752e8d2e2b79a4e2c", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2023-01-01T23:17:36+00:00"}, {"name": "nette/application", "version": "v3.1.7", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "a831a22c8291638624b39a673d40935c854371e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/a831a22c8291638624b39a673d40935c854371e3", "reference": "a831a22c8291638624b39a673d40935c854371e3", "shasum": ""}, "require": {"nette/component-model": "^3.0", "nette/http": "^3.0.2", "nette/routing": "^3.0.2", "nette/utils": "^3.2.1", "php": ">=7.2"}, "conflict": {"latte/latte": "<2.7.1 || >=3.1 || =3.0.0", "nette/caching": "<3.1", "nette/di": "<3.0.7", "nette/forms": "<3.0", "nette/schema": "<1.2", "tracy/tracy": "<2.5"}, "require-dev": {"latte/latte": "^2.10.2 || ^3.0.1", "mockery/mockery": "^1.0", "nette/di": "^v3.0", "nette/forms": "^3.0", "nette/robot-loader": "^3.2", "nette/security": "^3.0", "nette/tester": "^2.3.1", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.6"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "support": {"issues": "https://github.com/nette/application/issues", "source": "https://github.com/nette/application/tree/v3.1.7"}, "time": "2022-06-01T12:25:37+00:00"}, {"name": "nette/bootstrap", "version": "v3.1.2", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "3ab4912a08af0c16d541c3709935c3478b5ee090"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/3ab4912a08af0c16d541c3709935c3478b5ee090", "reference": "3ab4912a08af0c16d541c3709935c3478b5ee090", "shasum": ""}, "require": {"nette/di": "^3.0.5", "nette/utils": "^3.2.1", "php": ">=7.2 <8.2"}, "conflict": {"tracy/tracy": "<2.6"}, "require-dev": {"latte/latte": "^2.8", "nette/application": "^3.1", "nette/caching": "^3.0", "nette/database": "^3.0", "nette/forms": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0", "nette/robot-loader": "^3.0", "nette/safe-stream": "^2.2", "nette/security": "^3.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.6"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🅱  Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/v3.1.2"}, "time": "2021-11-24T16:51:46+00:00"}, {"name": "nette/caching", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "e1e38105956bb631e2295ef7a2fdef83485238e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/e1e38105956bb631e2295ef7a2fdef83485238e9", "reference": "e1e38105956bb631e2295ef7a2fdef83485238e9", "shasum": ""}, "require": {"nette/finder": "^2.4 || ^3.0", "nette/utils": "^2.4 || ^3.0", "php": ">=7.2 <8.3"}, "require-dev": {"latte/latte": "^2.11 || ^3.0", "nette/di": "^v3.0", "nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.4"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "support": {"issues": "https://github.com/nette/caching/issues", "source": "https://github.com/nette/caching/tree/v3.1.4"}, "time": "2022-10-18T23:27:44+00:00"}, {"name": "nette/component-model", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "20a39df12009029c7e425bc5e0439ee4ab5304af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/20a39df12009029c7e425bc5e0439ee4ab5304af", "reference": "20a39df12009029c7e425bc5e0439ee4ab5304af", "shasum": ""}, "require": {"nette/utils": "^2.5 || ^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⚛ Nette Component Model", "homepage": "https://nette.org", "keywords": ["components", "nette"], "support": {"issues": "https://github.com/nette/component-model/issues", "source": "https://github.com/nette/component-model/tree/v3.0.2"}, "time": "2021-08-25T14:52:12+00:00"}, {"name": "nette/di", "version": "v3.0.13", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "9878f2958a0a804b08430dbc719a52e493022739"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/9878f2958a0a804b08430dbc719a52e493022739", "reference": "9878f2958a0a804b08430dbc719a52e493022739", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/neon": "^3.3 || ^4.0", "nette/php-generator": "^3.5.4 || ^4.0", "nette/robot-loader": "^3.2", "nette/schema": "^1.1", "nette/utils": "^3.1.6", "php": ">=7.1 <8.2"}, "conflict": {"nette/bootstrap": "<3.0"}, "require-dev": {"nette/tester": "^2.2", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v3.0.13"}, "time": "2022-03-10T02:43:04+00:00"}, {"name": "nette/finder", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "991aefb42860abeab8e003970c3809a9d83cb932"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/991aefb42860abeab8e003970c3809a9d83cb932", "reference": "991aefb42860abeab8e003970c3809a9d83cb932", "shasum": ""}, "require": {"nette/utils": "^2.4 || ^3.0", "php": ">=7.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔍 Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "support": {"issues": "https://github.com/nette/finder/issues", "source": "https://github.com/nette/finder/tree/v2.6.0"}, "time": "2022-10-13T01:31:15+00:00"}, {"name": "nette/forms", "version": "v3.1.7", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "fe2109ce8b77846a5f664bc412c7cf3008f63074"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/fe2109ce8b77846a5f664bc412c7cf3008f63074", "reference": "fe2109ce8b77846a5f664bc412c7cf3008f63074", "shasum": ""}, "require": {"nette/component-model": "^3.0", "nette/http": "^3.1", "nette/utils": "^3.2.1", "php": ">=7.2 <8.2"}, "conflict": {"latte/latte": ">=3.1", "nette/di": "<3.0-stable"}, "require-dev": {"latte/latte": "^2.10.2 || ^3.0", "nette/application": "^3.0", "nette/di": "^3.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📝 Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "support": {"issues": "https://github.com/nette/forms/issues", "source": "https://github.com/nette/forms/tree/v3.1.7"}, "time": "2022-05-12T15:30:17+00:00"}, {"name": "nette/http", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "65bfe68f9c611e7cd1935a5f794a560c52e4614f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/65bfe68f9c611e7cd1935a5f794a560c52e4614f", "reference": "65bfe68f9c611e7cd1935a5f794a560c52e4614f", "shasum": ""}, "require": {"nette/utils": "^3.1", "php": ">=7.2 <8.2"}, "conflict": {"nette/di": "<3.0.3", "nette/schema": "<1.2"}, "require-dev": {"nette/di": "^3.0", "nette/security": "^3.0", "nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of uploaded files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v3.1.6"}, "time": "2022-04-02T16:05:07+00:00"}, {"name": "nette/mail", "version": "v3.1.8", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "69b43ae9a5c63ff68804531ef0113c372c676ce6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/69b43ae9a5c63ff68804531ef0113c372c676ce6", "reference": "69b43ae9a5c63ff68804531ef0113c372c676ce6", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^3.1", "php": ">=7.1 <8.2"}, "conflict": {"nette/di": "<3.0-stable"}, "require-dev": {"nette/di": "^3.0.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of attached files", "ext-openssl": "to use Nette\\Mail\\DkimSigner"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📧 Nette Mail: handy email creation and transfer library for PHP with both text and MIME-compliant support.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "support": {"issues": "https://github.com/nette/mail/issues", "source": "https://github.com/nette/mail/tree/v3.1.8"}, "time": "2021-08-25T00:07:03+00:00"}, {"name": "nette/neon", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "22e384da162fab42961d48eb06c06d3ad0c11b95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/22e384da162fab42961d48eb06c06d3ad0c11b95", "reference": "22e384da162fab42961d48eb06c06d3ad0c11b95", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.7"}, "bin": ["bin/neon-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍸 Nette NEON: encodes and decodes NEON file format.", "homepage": "https://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v3.3.3"}, "time": "2022-03-10T02:04:26+00:00"}, {"name": "nette/php-generator", "version": "v3.6.7", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "b9ba414c9895fd9420887f20eeb4eabde123677f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/b9ba414c9895fd9420887f20eeb4eabde123677f", "reference": "b9ba414c9895fd9420887f20eeb4eabde123677f", "shasum": ""}, "require": {"nette/utils": "^3.1.2", "php": ">=7.2 <8.2"}, "require-dev": {"nette/tester": "^2.4", "nikic/php-parser": "^4.13", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::withBodiesFrom() & GlobalFunction::withBodyFrom()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.1 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v3.6.7"}, "time": "2022-03-10T01:51:00+00:00"}, {"name": "nette/robot-loader", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "e2adc334cb958164c050f485d99c44c430f51fe2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/e2adc334cb958164c050f485d99c44c430f51fe2", "reference": "e2adc334cb958164c050f485d99c44c430f51fe2", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/finder": "^2.5 || ^3.0", "nette/utils": "^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v3.4.1"}, "time": "2021-08-25T15:53:54+00:00"}, {"name": "nette/routing", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/nette/routing.git", "reference": "5e02bdde257029db0223d3291c281d913abd587f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/routing/zipball/5e02bdde257029db0223d3291c281d913abd587f", "reference": "5e02bdde257029db0223d3291c281d913abd587f", "shasum": ""}, "require": {"nette/http": "^3.0", "nette/utils": "^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Routing: two-ways URL conversion", "homepage": "https://nette.org", "keywords": ["nette"], "support": {"issues": "https://github.com/nette/routing/issues", "source": "https://github.com/nette/routing/tree/v3.0.3"}, "time": "2022-04-16T23:08:16+00:00"}, {"name": "nette/safe-stream", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "8bbbeda8415b8352642d7566dfa18169d40c2e54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/8bbbeda8415b8352642d7566dfa18169d40c2e54", "reference": "8bbbeda8415b8352642d7566dfa18169d40c2e54", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette SafeStream: provides isolation for thread safe manipulation with files via native PHP functions.", "homepage": "https://nette.org", "keywords": ["atomic", "filesystem", "isolation", "nette", "safe", "thread safe"], "support": {"issues": "https://github.com/nette/safe-stream/issues", "source": "https://github.com/nette/safe-stream/tree/v2.5.0"}, "time": "2022-01-03T23:13:32+00:00"}, {"name": "nette/schema", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "9a39cef03a5b34c7de64f551538cbba05c2be5df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/9a39cef03a5b34c7de64f551538cbba05c2be5df", "reference": "9a39cef03a5b34c7de64f551538cbba05c2be5df", "shasum": ""}, "require": {"nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0", "php": ">=7.1 <8.2"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.2"}, "time": "2021-10-15T11:40:02+00:00"}, {"name": "nette/security", "version": "v3.1.5", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "c120893f561b09494486c66594720b2abcb099b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/c120893f561b09494486c66594720b2abcb099b2", "reference": "c120893f561b09494486c66594720b2abcb099b2", "shasum": ""}, "require": {"nette/utils": "^3.2.1", "php": ">=7.2 <8.2"}, "conflict": {"nette/di": "<3.0-stable", "nette/http": "<3.1.3"}, "require-dev": {"nette/di": "^3.0.1", "nette/http": "^3.0.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔑 Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "support": {"issues": "https://github.com/nette/security/issues", "source": "https://github.com/nette/security/tree/v3.1.5"}, "time": "2021-09-20T15:20:25+00:00"}, {"name": "nette/tokenizer", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/tokenizer.git", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tokenizer/zipball/370c5e4e2e10eb4d3e406d3a90526f821de98190", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "<PERSON><PERSON>", "homepage": "https://nette.org", "support": {"source": "https://github.com/nette/tokenizer/tree/v3.1.1"}, "abandoned": true, "time": "2022-02-09T22:28:54+00:00"}, {"name": "nette/utils", "version": "v3.2.8", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "02a54c4c872b99e4ec05c4aec54b5a06eb0f6368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/02a54c4c872b99e4ec05c4aec54b5a06eb0f6368", "reference": "02a54c4c872b99e4ec05c4aec54b5a06eb0f6368", "shasum": ""}, "require": {"php": ">=7.2 <8.3"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.8"}, "time": "2022-09-12T23:36:20+00:00"}, {"name": "nextras/migrations", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/nextras/migrations.git", "reference": "c1013ffde270f92cae841ce45ad82a1334a52086"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/migrations/zipball/c1013ffde270f92cae841ce45ad82a1334a52086", "reference": "c1013ffde270f92cae841ce45ad82a1334a52086", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"dibi/dibi": "~3.0 | ~4.0", "doctrine/cache": "~1.5", "doctrine/dbal": "~2.5 | ~3.0", "doctrine/orm": "~2.5", "ext-openssl": "*", "mockery/mockery": "~0.9 | ~1.0", "nette/database": "~2.2", "nette/di": "~2.3.12 | ~2.4", "nette/tester": "~1.7 | ~2.0", "nette/utils": "~2.3", "nextras/dbal": "~1.0 | ~2.0 | ~3.0 | ~4.0 | ~5.0@dev", "symfony/config": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0", "symfony/console": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0", "symfony/dependency-injection": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0", "symfony/framework-bundle": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0", "symfony/http-kernel": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0", "tracy/tracy": "^2.2"}, "suggest": {"dibi/dibi": "to use DibiAdapter", "doctrine/dbal": "to use DoctrineAdapter", "doctrine/orm": "to generate migrations with Doctrine SchemaTool", "nette/database": "to use NetteAdapter", "nextras/dbal": "to use NextrasAdapter", "symfony/console": "to use Symfony commands"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Nextras\\Migrations\\": "src/"}, "classmap": ["src/exceptions.php", "src/deprecated"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Database migrations runner", "support": {"issues": "https://github.com/nextras/migrations/issues", "source": "https://github.com/nextras/migrations/tree/v3.2.0"}, "time": "2022-06-14T19:31:05+00:00"}, {"name": "nyholm/dsn", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/Nyholm/dsn.git", "reference": "9445621b426bac8c0ca161db8cd700da00a4e618"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/dsn/zipball/9445621b426bac8c0ca161db8cd700da00a4e618", "reference": "9445621b426bac8c0ca161db8cd700da00a4e618", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"symfony/phpunit-bridge": "^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Nyholm\\Dsn\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parse your DSN strings in a powerful and flexible way", "homepage": "http://tnyholm.se", "keywords": ["database", "dsn", "dsn parser", "parser"], "support": {"issues": "https://github.com/Nyholm/dsn/issues", "source": "https://github.com/Nyholm/dsn/tree/2.0.1"}, "funding": [{"url": "https://github.com/Nyholm", "type": "github"}], "time": "2021-11-18T09:23:29+00:00"}, {"name": "om/icalparser", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/OzzyCzech/icalparser.git", "reference": "e180dea8447c9e78f92306f892f809d5f5ad2948"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/OzzyCzech/icalparser/zipball/e180dea8447c9e78f92306f892f809d5f5ad2948", "reference": "e180dea8447c9e78f92306f892f809d5f5ad2948", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"nette/tester": "*"}, "suggest": {"ext-dom": "for timezone tool"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Simple ical parser", "keywords": ["calendar", "ical", "parser"], "support": {"issues": "https://github.com/OzzyCzech/icalparser/issues", "source": "https://github.com/OzzyCzech/icalparser/tree/v1.0.1"}, "time": "2020-09-21T09:20:40+00:00"}, {"name": "omnipay/common", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/omnipay-common.git", "reference": "e278ff00676c05cd0f4aaaf6189a226f26ae056e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/omnipay-common/zipball/e278ff00676c05cd0f4aaaf6189a226f26ae056e", "reference": "e278ff00676c05cd0f4aaaf6189a226f26ae056e", "shasum": ""}, "require": {"moneyphp/money": "^3.1|^4.0.3", "php": "^7.2|^8", "php-http/client-implementation": "^1", "php-http/discovery": "^1.14", "php-http/message": "^1.5", "symfony/http-foundation": "^2.1|^3|^4|^5|^6"}, "require-dev": {"omnipay/tests": "^4.1", "php-http/guzzle7-adapter": "^1", "php-http/mock-client": "^1", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"league/omnipay": "The default Omnipay package provides a default HTTP Adapter."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Omnipay\\Common\\": "src/Common"}, "classmap": ["src/Omnipay.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Del"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-common/contributors"}], "description": "Common components for Omnipay payment processing library", "homepage": "https://github.com/thephpleague/omnipay-common", "keywords": ["gateway", "merchant", "omnipay", "pay", "payment", "purchase"], "support": {"issues": "https://github.com/thephpleague/omnipay-common/issues", "source": "https://github.com/thephpleague/omnipay-common/tree/v3.2.0"}, "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "time": "2021-12-30T11:32:00+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "58c3f47f650c94ec05a151692652a868995d2938"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/58c3f47f650c94ec05a151692652a868995d2938", "reference": "58c3f47f650c94ec05a151692652a868995d2938", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2022-06-14T06:56:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-http/client-common", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "1e19c059b0e4d5f717bf5d524d616165aeab0612"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/1e19c059b0e4d5f717bf5d524d616165aeab0612", "reference": "1e19c059b0e4d5f717bf5d524d616165aeab0612", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.7.1"}, "time": "2023-11-30T10:31:25+00:00"}, {"name": "php-http/discovery", "version": "1.14.3", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "31d8ee46d0215108df16a8527c7438e96a4d7735"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/31d8ee46d0215108df16a8527c7438e96a4d7735", "reference": "31d8ee46d0215108df16a8527c7438e96a4d7735", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0"}, "require-dev": {"graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1"}, "suggest": {"php-http/message": "Allow to use Guzzle, Diactoros or Slim Framework factories"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds installed HTTPlug implementations and PSR-7 message factories", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.14.3"}, "time": "2022-07-11T14:04:40+00:00"}, {"name": "php-http/guzzle7-adapter", "version": "0.1.1", "source": {"type": "git", "url": "https://github.com/php-http/guzzle7-adapter.git", "reference": "1967de656b9679a2a6a66d0e4e16fa99bbed1ad1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle7-adapter/zipball/1967de656b9679a2a6a66d0e4e16fa99bbed1ad1", "reference": "1967de656b9679a2a6a66d0e4e16fa99bbed1ad1", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "php": "^7.2 | ^8.0", "php-http/httplug": "^2.0", "psr/http-client": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.0|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.2.x-dev"}}, "autoload": {"psr-4": {"Http\\Adapter\\Guzzle7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 7 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "support": {"issues": "https://github.com/php-http/guzzle7-adapter/issues", "source": "https://github.com/php-http/guzzle7-adapter/tree/0.1.1"}, "time": "2020-10-21T17:30:51+00:00"}, {"name": "php-http/httplug", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "f640739f80dfa1152533976e3c112477f69274eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/f640739f80dfa1152533976e3c112477f69274eb", "reference": "f640739f80dfa1152533976e3c112477f69274eb", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1", "phpspec/phpspec": "^5.1 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.3.0"}, "time": "2022-02-21T09:52:22+00:00"}, {"name": "php-http/message", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "7886e647a30a966a1a8d1dad1845b71ca8678361"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/7886e647a30a966a1a8d1dad1845b71ca8678361", "reference": "7886e647a30a966a1a8d1dad1845b71ca8678361", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.1 || ^8.0", "php-http/message-factory": "^1.0.2", "psr/http-message": "^1.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0", "laminas/laminas-diactoros": "^2.0", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.13.0"}, "time": "2022-02-11T13:41:14+00:00"}, {"name": "php-http/message-factory", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/a478cb11f66a6ac48d8954216cfed9aa06a501a1", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/master"}, "time": "2015-12-19T14:08:53+00:00"}, {"name": "php-http/promise", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "4c4c1f9b7289a2ec57cde7f1e9762a5789506f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/4c4c1f9b7289a2ec57cde7f1e9762a5789506f88", "reference": "4c4c1f9b7289a2ec57cde7f1e9762a5789506f88", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2", "phpspec/phpspec": "^5.1.2 || ^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.1.0"}, "time": "2020-07-07T09:29:14+00:00"}, {"name": "php-soap/engine", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/php-soap/engine.git", "reference": "eaaa1b959d8a140dd94e0d6decab1192513d8dac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-soap/engine/zipball/eaaa1b959d8a140dd94e0d6decab1192513d8dac", "reference": "eaaa1b959d8a140dd94e0d6decab1192513d8dac", "shasum": ""}, "require": {"azjezz/psl": "^2.5", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "require-dev": {"php-standard-library/psalm-plugin": "^2.2", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^5.9"}, "type": "library", "autoload": {"psr-4": {"Soap\\Engine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}], "description": "SOAP engine design", "support": {"issues": "https://github.com/php-soap/engine/issues", "source": "https://github.com/php-soap/engine/tree/2.6.0"}, "funding": [{"url": "https://opencollective.com/php-soap", "type": "open_collective"}], "time": "2024-01-26T09:15:44+00:00"}, {"name": "php-soap/ext-soap-engine", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/php-soap/ext-soap-engine.git", "reference": "893ab16cea8642aa1c58a8affacb46b91af7630d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-soap/ext-soap-engine/zipball/893ab16cea8642aa1c58a8affacb46b91af7630d", "reference": "893ab16cea8642aa1c58a8affacb46b91af7630d", "shasum": ""}, "require": {"azjezz/psl": "^2.1", "ext-dom": "*", "ext-soap": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "php-soap/engine": "^1.3|^2.0", "php-soap/wsdl": "^1.3", "symfony/options-resolver": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"php-soap/engine-integration-tests": "^1.4", "php-soap/xml": "^1.4", "phpunit/phpunit": "^10.0.19"}, "type": "library", "autoload": {"psr-4": {"Soap\\ExtSoapEngine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}], "description": "An ext-soap engine implementation", "support": {"issues": "https://github.com/php-soap/ext-soap-engine/issues", "source": "https://github.com/php-soap/ext-soap-engine/tree/1.6.0"}, "funding": [{"url": "https://opencollective.com/php-soap", "type": "open_collective"}], "time": "2024-01-26T09:44:40+00:00"}, {"name": "php-soap/psr18-transport", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/php-soap/psr18-transport.git", "reference": "fbb3c3961ef18c906ebceef327f6d62bbd086b59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-soap/psr18-transport/zipball/fbb3c3961ef18c906ebceef327f6d62bbd086b59", "reference": "fbb3c3961ef18c906ebceef327f6d62bbd086b59", "shasum": ""}, "require": {"ext-dom": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "php-http/client-common": "^2.3", "php-http/discovery": "^1.12", "php-soap/engine": "^1.3|^2.0", "php-soap/wsdl": "^1.3", "php-soap/xml": "^1.4", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.0.1|^2.0", "psr/http-message-implementation": "^1.0", "veewee/xml": "^2.2"}, "require-dev": {"ext-soap": "*", "guzzlehttp/guzzle": "^7.5", "nyholm/psr7": "^1.5", "php-http/mock-client": "^1.5", "php-soap/engine-integration-tests": "^1.4", "php-soap/ext-soap-engine": "^1.4", "phpunit/phpunit": "^10.0"}, "type": "library", "autoload": {"psr-4": {"Soap\\Psr18Transport\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}], "description": "PSR-18 HTTP Client transport for SOAP", "support": {"issues": "https://github.com/php-soap/psr18-transport/issues", "source": "https://github.com/php-soap/psr18-transport/tree/1.5.0"}, "funding": [{"url": "https://opencollective.com/php-soap", "type": "open_collective"}], "time": "2023-11-24T07:43:39+00:00"}, {"name": "php-soap/wsdl", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/php-soap/wsdl.git", "reference": "ca6bc7b1b199d6aeb0778b740b8f300c8c01a60b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-soap/wsdl/zipball/ca6bc7b1b199d6aeb0778b740b8f300c8c01a60b", "reference": "ca6bc7b1b199d6aeb0778b740b8f300c8c01a60b", "shasum": ""}, "require": {"azjezz/psl": "^2.1", "ext-dom": "*", "league/uri": "^7.0", "league/uri-components": "^7.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "php-soap/xml": "^1.4", "symfony/console": "^5.4 || ^6.0 || ^7.0", "veewee/xml": "~2.2"}, "require-dev": {"phpunit/phpunit": "^9.5", "psalm/plugin-symfony": "^5.0", "vimeo/psalm": "^5.9.0"}, "bin": ["bin/wsdl"], "type": "library", "autoload": {"psr-4": {"Soap\\Wsdl\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}], "description": "Deals with WSDLs", "support": {"issues": "https://github.com/php-soap/wsdl/issues", "source": "https://github.com/php-soap/wsdl/tree/1.7.0"}, "funding": [{"url": "https://opencollective.com/php-soap", "type": "open_collective"}], "time": "2024-01-14T14:55:04+00:00"}, {"name": "php-soap/wsdl-reader", "version": "0.9.0", "source": {"type": "git", "url": "https://github.com/php-soap/wsdl-reader.git", "reference": "dedf74fe25daddfd74a580f66f643e25aa1a82a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-soap/wsdl-reader/zipball/dedf74fe25daddfd74a580f66f643e25aa1a82a1", "reference": "dedf74fe25daddfd74a580f66f643e25aa1a82a1", "shasum": ""}, "require": {"azjezz/psl": "^2.4", "ext-dom": "*", "goetas-webservices/xsd-reader": "^0.4.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "php-soap/engine": "^2.6", "php-soap/wsdl": "^1.4", "symfony/console": "^5.4 || ^6.0 || ^7.0", "veewee/xml": "^2.6"}, "require-dev": {"php-soap/engine-integration-tests": "^1.5.0", "php-standard-library/psalm-plugin": "^2.2", "psalm/plugin-symfony": "^5.0", "symfony/var-dumper": "^6.1", "vimeo/psalm": "^5.6"}, "type": "library", "autoload": {"psr-4": {"Soap\\WsdlReader\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}], "description": "A WSDL reader in PHP", "support": {"issues": "https://github.com/php-soap/wsdl-reader/issues", "source": "https://github.com/php-soap/wsdl-reader/tree/0.9.0"}, "funding": [{"url": "https://opencollective.com/php-soap", "type": "open_collective"}], "time": "2024-01-26T09:22:49+00:00"}, {"name": "php-soap/xml", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/php-soap/xml.git", "reference": "6c65aa1533183174c972c72a8e8334260c18a28c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-soap/xml/zipball/6c65aa1533183174c972c72a8e8334260c18a28c", "reference": "6c65aa1533183174c972c72a8e8334260c18a28c", "shasum": ""}, "require": {"ext-dom": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "veewee/xml": "^2.2"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Soap\\Xml\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}], "description": "XML wrappers for SOAP", "support": {"issues": "https://github.com/php-soap/xml/issues", "source": "https://github.com/php-soap/xml/tree/1.5.0"}, "funding": [{"url": "https://opencollective.com/php-soap", "type": "open_collective"}], "time": "2023-11-24T06:49:56+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "4.2.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "5f6d7410e5fd72cac1aa67d4f05f4fe664d01ba6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/5f6d7410e5fd72cac1aa67d4f05f4fe664d01ba6", "reference": "5f6d7410e5fd72cac1aa67d4f05f4fe664d01ba6", "shasum": ""}, "require": {"composer/pcre": "^1||^2||^3", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^8.1", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1 || ^2.0", "phpstan/phpstan-deprecation-rules": "^1.0 || ^2.0", "phpstan/phpstan-phpunit": "^1.0 || ^2.0", "phpunit/phpunit": "^10.5", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/4.2.0"}, "time": "2025-04-17T02:41:45+00:00"}, {"name": "phpro/soap-client", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/phpro/soap-client.git", "reference": "60a663170f2b748a471e7bfd763ae30b20973ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpro/soap-client/zipball/60a663170f2b748a471e7bfd763ae30b20973ad3", "reference": "60a663170f2b748a471e7bfd763ae30b20973ad3", "shasum": ""}, "require": {"azjezz/psl": "^2.1", "laminas/laminas-code": "^4.8.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "php-soap/engine": "^2.4", "php-soap/ext-soap-engine": "^1.4", "php-soap/psr18-transport": "^1.3", "php-soap/wsdl-reader": "~0.6", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/console": "~5.4 || ~6.0 || ~7.0", "symfony/event-dispatcher": "~5.4 || ~6.0 || ~7.0", "symfony/filesystem": "~5.4 || ~6.0 || ~7.0", "symfony/validator": "~5.4 || ~6.0 || ~7.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.5.0", "nyholm/psr7": "^1.5", "php-http/vcr-plugin": "^1.2", "php-parallel-lint/php-parallel-lint": "^1.3", "phpro/grumphp-shim": "^2.3", "phpspec/phpspec": "~7.2", "phpspec/prophecy-phpunit": "^2.0.1", "phpstan/phpstan": "^1.10.15", "phpunit/phpunit": "~9.5", "squizlabs/php_codesniffer": "^3.7.1"}, "bin": ["bin/soap-client"], "type": "library", "autoload": {"psr-0": {"Phpro\\SoapClient\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}], "description": "A general purpose SoapClient library", "keywords": ["soap"], "support": {"issues": "https://github.com/phpro/soap-client/issues", "source": "https://github.com/phpro/soap-client/tree/3.1.1"}, "funding": [{"url": "https://opencollective.com/php-soap", "type": "open_collective"}], "time": "2023-12-21T13:19:09+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.14", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "2f0b7af658cbea265cbb4a791d6c29a6613f98ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/2f0b7af658cbea265cbb4a791d6c29a6613f98ef", "reference": "2f0b7af658cbea265cbb4a791d6c29a6613f98ef", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.14"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2022-04-04T05:15:45+00:00"}, {"name": "pragmarx/coollection", "version": "dev-upd/php8", "source": {"type": "git", "url": "https://github.com/nidjo17/coollection.git", "reference": "6a8cfd018bc9f87d3381cf8997b5ca3526997b2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nidjo17/coollection/zipball/6a8cfd018bc9f87d3381cf8997b5ca3526997b2d", "reference": "6a8cfd018bc9f87d3381cf8997b5ca3526997b2d", "shasum": ""}, "require": {"php": ">=8.0", "pragmarx/ia-arr": "~5.0|~6.0|~7.0", "pragmarx/ia-collection": "dev-upd/php8", "pragmarx/ia-str": "~5.0|~6.0|~7.0"}, "require-dev": {"mockery/mockery": "~1.0", "phpunit/php-timer": "~1.0|~2.0", "phpunit/phpunit": "~6.0|~7.0|~8.0|~9.0", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"PragmaRX\\Coollection\\Package\\": "src/package", "PragmaRX\\Coollection\\Tests\\": "tests", "IlluminateExtracted\\": "src/package/Support/IlluminateExtracted", "IlluminateExtracted\\Tests\\": "tests/IlluminateExtracted"}, "files": ["src/package/Support/helpers.php"]}, "autoload-dev": {"psr-4": {"PragmaRX\\Coollection\\Tests\\": "tests"}}, "scripts": {"test": ["@composer install", "vendor/bin/phpunit"], "check-style": ["phpcs -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests"], "fix-style": ["phpcbf -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://antoniocarlosribeiro.com", "role": "Creator"}], "description": "Laravel Illuminate collection with objectified properties", "homepage": "https://github.com/antonioribeiro/coollection", "keywords": ["collection", "laravel", "pragmarx"], "support": {"source": "https://github.com/nidjo17/coollection/tree/upd/php8"}, "time": "2023-01-03T20:00:23+00:00"}, {"name": "pragmarx/countries", "version": "dev-upd/php8", "source": {"type": "git", "url": "https://github.com/nidjo17/countries.git", "reference": "6f13216acc2ef011c021da15cb7d42b070ba4f94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nidjo17/countries/zipball/6f13216acc2ef011c021da15cb7d42b070ba4f94", "reference": "6f13216acc2ef011c021da15cb7d42b070ba4f94", "shasum": ""}, "require": {"colinodell/json5": "^1.0|^2.0", "nette/caching": "^2.5|^3.0", "php": ">=8.0", "pragmarx/coollection": "dev-upd/php8", "psr/simple-cache": "^1.0|^2.0"}, "require-dev": {"gasparesganga/php-shapefile": "^3.4", "phpunit/phpunit": "~6.0|~7.0|~8.0|^9.0", "squizlabs/php_codesniffer": "^2.3|^3.6"}, "type": "library", "autoload": {"psr-4": {"PragmaRX\\Countries\\Package\\": "src/package", "PragmaRX\\Countries\\Update\\": "src/update"}, "files": ["src/package/Support/helpers.php"]}, "autoload-dev": {"psr-4": {"PragmaRX\\Countries\\Tests\\": "tests/", "PragmaRX\\Countries\\Tests\\PhpUnit\\": "tests/PhpUnit/"}}, "scripts": {"test": ["phpunit"], "check-style": ["phpcs -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests"], "fix-style": ["phpcbf -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator"}], "description": "PHP Countries and Currencies", "keywords": ["borders", "cities", "countries", "currencies", "flag", "geometry", "states", "taxes", "timezones", "topology"], "support": {"source": "https://github.com/nidjo17/countries/tree/upd/php8"}, "time": "2023-01-03T22:27:52+00:00"}, {"name": "pragmarx/ia-arr", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/antonioribeiro/ia-arr.git", "reference": "b526eb701791faaa548bcfa7b7bcd12748739839"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/ia-arr/zipball/b526eb701791faaa548bcfa7b7bcd12748739839", "reference": "b526eb701791faaa548bcfa7b7bcd12748739839", "shasum": ""}, "require": {"php": ">=7.0", "symfony/var-dumper": "~3.3|~4.0|~5.0|~6.0"}, "require-dev": {"mockery/mockery": "~1.0", "nesbot/carbon": "^1.26 || ^2.00", "phpunit/phpunit": "~6.0|~7.0|~8.0|~9.0", "squizlabs/php_codesniffer": "^2.3"}, "suggest": {"nesbot/carbon": "Required to use Carbon datetime."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/Support/helpers.php", "src/Support/alias.php"], "psr-4": {"IlluminateAgnostic\\Arr\\": "src/", "IlluminateAgnostic\\Arr\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://antoniocarlosribeiro.com", "role": "Creator"}], "description": "Laravel Illuminate Agnostic Arr", "homepage": "https://github.com/antonioribeiro/ia-arr", "keywords": ["arr", "array", "helpers", "laravel", "pragmarx"], "support": {"issues": "https://github.com/antonioribeiro/ia-arr/issues", "source": "https://github.com/antonioribeiro/ia-arr/tree/v7.3.2"}, "time": "2022-09-07T10:25:34+00:00"}, {"name": "pragmarx/ia-collection", "version": "dev-upd/php8", "source": {"type": "git", "url": "https://github.com/nidjo17/ia-collection.git", "reference": "d626cfc96c1934a92649f8aa63e4100f92de6c44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nidjo17/ia-collection/zipball/d626cfc96c1934a92649f8aa63e4100f92de6c44", "reference": "d626cfc96c1934a92649f8aa63e4100f92de6c44", "shasum": ""}, "require": {"nesbot/carbon": "^1.26 || ^2.00", "php": ">=8.0", "ramsey/uuid": "~3.7|~4.0", "symfony/var-dumper": "~3.3|~4.0|~5.0|^6.0", "voku/portable-ascii": "^1.4|^2.0"}, "require-dev": {"mockery/mockery": "~1.0", "moontoast/math": "^1.1", "phpunit/phpunit": "~6.0|~7.0|~9.0", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"IlluminateAgnostic\\Collection\\": "src/", "IlluminateAgnostic\\Collection\\Tests\\": "tests/"}, "files": ["src/Support/helpers.php", "src/Support/alias.php"]}, "scripts": {"test": ["@composer install", "vendor/bin/phpunit"], "check-style": ["phpcs -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests"], "fix-style": ["phpcbf -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://antoniocarlosribeiro.com", "role": "Creator"}], "description": "Laravel Illuminate Agnostic Collection", "homepage": "https://github.com/antonioribeiro/ia-collection", "keywords": ["collection", "helpers", "laravel", "pragmarx"], "support": {"source": "https://github.com/nidjo17/ia-collection/tree/upd/php8"}, "time": "2023-01-03T20:03:16+00:00"}, {"name": "pragmarx/ia-str", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/antonioribeiro/ia-str.git", "reference": "ce022c92a972bee66e1d54f32a604b0ee19e6488"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/ia-str/zipball/ce022c92a972bee66e1d54f32a604b0ee19e6488", "reference": "ce022c92a972bee66e1d54f32a604b0ee19e6488", "shasum": ""}, "require": {"php": ">=7.0", "voku/portable-ascii": "^1.4|^2.0"}, "require-dev": {"doctrine/inflector": "^1.2", "mockery/mockery": "~1.0", "moontoast/math": "^1.1", "nesbot/carbon": "^1.26 || ^2.00", "phpunit/phpunit": "~6.0|~7.0|~8.0|~9.0", "ramsey/uuid": "^3.7|^4.0", "squizlabs/php_codesniffer": "^2.3", "symfony/var-dumper": "~3.3|~4.0|~5.0|^6.0"}, "suggest": {"doctrine/inflector": "", "nesbot/carbon": "", "ramsey/uuid": "", "symfony/var-dumper": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/Support/helpers.php", "src/Support/alias.php"], "psr-4": {"IlluminateAgnostic\\Str\\": "src/", "IlluminateAgnostic\\Str\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://antoniocarlosribeiro.com", "role": "Creator"}], "description": "Laravel Illuminate Agnostic Str", "homepage": "https://github.com/antonioribeiro/ia-str", "keywords": ["helpers", "laravel", "pragmarx", "str", "string"], "support": {"issues": "https://github.com/antonioribeiro/ia-str/issues", "source": "https://github.com/antonioribeiro/ia-str/tree/v7.3.1"}, "time": "2022-09-07T10:27:55+00:00"}, {"name": "predis/predis", "version": "v1.1.10", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.10"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-01-05T17:46:08+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "8707bf3cea6f710bf6ef05491234e3ab06f6432a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/8707bf3cea6f710bf6ef05491234e3ab06f6432a", "reference": "8707bf3cea6f710bf6ef05491234e3ab06f6432a", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/2.0.0"}, "time": "2021-10-29T13:22:09+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/ad7475d1c9e70b190ecffc58f2d989416af339b4", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.3.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-27T19:12:24+00:00"}, {"name": "ramsey/uuid", "version": "4.8.1", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28", "reference": "fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.8.1"}, "time": "2025-06-01T06:28:46+00:00"}, {"name": "react/event-loop", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/reactphp/event-loop.git", "reference": "187fb56f46d424afb6ec4ad089269c72eec2e137"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/event-loop/zipball/187fb56f46d424afb6ec4ad089269c72eec2e137", "reference": "187fb56f46d424afb6ec4ad089269c72eec2e137", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.35"}, "suggest": {"ext-event": "~1.0 for ExtEventLoop", "ext-pcntl": "For signal handling support when using the StreamSelectLoop", "ext-uv": "* for ExtUvLoop"}, "type": "library", "autoload": {"psr-4": {"React\\EventLoop\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "ReactPHP's core reactor event loop that libraries can use for evented I/O.", "keywords": ["asynchronous", "event-loop"], "support": {"issues": "https://github.com/reactphp/event-loop/issues", "source": "https://github.com/reactphp/event-loop/tree/v1.3.0"}, "funding": [{"url": "https://github.com/WyriHaximus", "type": "github"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-03-17T11:10:22+00:00"}, {"name": "react/promise", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/234f8fd1023c9158e2314fa9d7d0e6a83db42910", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.9.0"}, "funding": [{"url": "https://github.com/WyriHaximus", "type": "github"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-11T10:27:51+00:00"}, {"name": "revolt/event-loop", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/revoltphp/event-loop.git", "reference": "25de49af7223ba039f64da4ae9a28ec2d10d0254"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/revoltphp/event-loop/zipball/25de49af7223ba039f64da4ae9a28ec2d10d0254", "reference": "25de49af7223ba039f64da4ae9a28ec2d10d0254", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^9", "psalm/phar": "^5.15"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Revolt\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Rock-solid event loop for concurrent PHP applications.", "keywords": ["async", "asynchronous", "concurrency", "event", "event-loop", "non-blocking", "scheduler"], "support": {"issues": "https://github.com/revoltphp/event-loop/issues", "source": "https://github.com/revoltphp/event-loop/tree/v1.0.6"}, "time": "2023-11-30T05:34:44+00:00"}, {"name": "ruflin/elastica", "version": "7.1.5", "source": {"type": "git", "url": "https://github.com/ruflin/Elastica.git", "reference": "72a4598544e3f99b5dd8cacb05d009ee75c2a701"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ruflin/Elastica/zipball/72a4598544e3f99b5dd8cacb05d009ee75c2a701", "reference": "72a4598544e3f99b5dd8cacb05d009ee75c2a701", "shasum": ""}, "require": {"elasticsearch/elasticsearch": "^7.1.1", "ext-json": "*", "nyholm/dsn": "^2.0.0", "php": "^7.2 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2.2 || ^3.0", "symfony/polyfill-php73": "^1.19"}, "require-dev": {"aws/aws-sdk-php": "^3.155", "guzzlehttp/guzzle": "^6.3 || ^7.2", "phpunit/phpunit": "^8.5.8 || ^9.4", "symfony/phpunit-bridge": "^5.1.1"}, "suggest": {"aws/aws-sdk-php": "Allow using IAM authentication with Amazon ElasticSearch Service", "egeloen/http-adapter": "Allow using httpadapter as transport", "guzzlehttp/guzzle": "Allow using guzzle as transport", "monolog/monolog": "Logging request"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0.x-dev"}}, "autoload": {"psr-4": {"Elastica\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://ruflin.com/"}], "description": "Elasticsearch Client", "homepage": "http://elastica.io/", "keywords": ["client", "search"], "support": {"issues": "https://github.com/ruflin/Elastica/issues", "source": "https://github.com/ruflin/Elastica/tree/7.1.5"}, "time": "2022-03-29T15:37:28+00:00"}, {"name": "setasign/fpdi", "version": "v2.3.6", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.31", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.3.6"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2021-02-11T11:37:01+00:00"}, {"name": "symfony/console", "version": "v5.4.10", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "4d671ab4ddac94ee439ea73649c69d9d200b5000"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/4d671ab4ddac94ee439ea73649c69d9d200b5000", "reference": "4d671ab4ddac94ee439ea73649c69d9d200b5000", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.10"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-26T13:00:04+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-05T16:45:39+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/f98b54df6ad059855739db6fcbc2d36995283fe1", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.9", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "36a017fa4cce1eff1b8e8129ff53513abcef05ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/36a017fa4cce1eff1b8e8129ff53513abcef05ba", "reference": "36a017fa4cce1eff1b8e8129ff53513abcef05ba", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-20T13:55:35+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.18", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "d0492d6217e5ab48f51fca76f64cf8e78919d0db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/d0492d6217e5ab48f51fca76f64cf8e78919d0db", "reference": "d0492d6217e5ab48f51fca76f64cf8e78919d0db", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-09T15:48:56+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "54f14e36aa73cb8f7261d7686691fd4d75ea2690"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/54f14e36aa73cb8f7261d7686691fd4d75ea2690", "reference": "54f14e36aa73cb8f7261d7686691fd4d75ea2690", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T13:00:38+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/5bbc823adecdae860bb64756d639ecfec17b050a", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "433d05519ce6990bf3530fba6957499d327395c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/433d05519ce6990bf3530fba6957499d327395c2", "reference": "433d05519ce6990bf3530fba6957499d327395c2", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "219aa369ceff116e673852dce47c3a41794c14bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/219aa369ceff116e673852dce47c3a41794c14bd", "reference": "219aa369ceff116e673852dce47c3a41794c14bd", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/e440d35fa0286f77fb45b79a03fedbeda9307e85", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "707403074c8ea6e2edaf8794b0157a0bfa52157a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/707403074c8ea6e2edaf8794b0157a0bfa52157a", "reference": "707403074c8ea6e2edaf8794b0157a0bfa52157a", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/86fcae159633351e5fd145d1c47de6c528f8caff", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-php80": "^1.14"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/property-access", "version": "v5.4.8", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "fe501d498d6ec7e9efe928c90fabedf629116495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/fe501d498d6ec7e9efe928c90fabedf629116495", "reference": "fe501d498d6ec7e9efe928c90fabedf629116495", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/property-info": "^5.2|^6.0"}, "require-dev": {"symfony/cache": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v5.4.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-12T15:48:08+00:00"}, {"name": "symfony/property-info", "version": "v5.4.10", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "924406e19365953870517eb7f63ac3f7bfb71875"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/924406e19365953870517eb7f63ac3f7bfb71875", "reference": "924406e19365953870517eb7f63ac3f7bfb71875", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/string": "^5.1|^6.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v5.4.10"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-31T05:14:08+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-30T19:17:29+00:00"}, {"name": "symfony/string", "version": "v5.4.10", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "4432bc7df82a554b3e413a8570ce2fea90e94097"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/4432bc7df82a554b3e413a8570ce2fea90e94097", "reference": "4432bc7df82a554b3e413a8570ce2fea90e94097", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.10"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-26T15:57:47+00:00"}, {"name": "symfony/translation", "version": "v5.4.14", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "f0ed07675863aa6e3939df8b1bc879450b585cab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/f0ed07675863aa6e3939df8b1bc879450b585cab", "reference": "f0ed07675863aa6e3939df8b1bc879450b585cab", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/console": "<5.3", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v5.4.14"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-07T08:01:20+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T16:58:25+00:00"}, {"name": "symfony/validator", "version": "v6.4.2", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "15fe2c6ed815b06b6b8636d8ba3ef9807ee1a75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/15fe2c6ed815b06b6b8636d8ba3ef9807ee1a75c", "reference": "15fe2c6ed815b06b6b8636d8ba3ef9807ee1a75c", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.4.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-29T16:34:12+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.17", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "ad74890513d07060255df2575703daf971de92c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/ad74890513d07060255df2575703daf971de92c7", "reference": "ad74890513d07060255df2575703daf971de92c7", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-12-22T10:31:03+00:00"}, {"name": "symfony/yaml", "version": "v5.4.12", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c", "reference": "7a3aa21ac8ab1a96cc6de5bbcab4bc9fc943b18c", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.12"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-02T15:52:22+00:00"}, {"name": "tracy/tracy", "version": "v2.9.4", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "0ed605329b095f5f5fe2db2adc3d1ee80c917294"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/0ed605329b095f5f5fe2db2adc3d1ee80c917294", "reference": "0ed605329b095f5f5fe2db2adc3d1ee80c917294", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": ">=7.2 <8.2"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"latte/latte": "^2.5", "nette/di": "^3.0", "nette/mail": "^3.0", "nette/tester": "^2.2", "nette/utils": "^3.0", "phpstan/phpstan": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.9-dev"}}, "autoload": {"files": ["src/Tracy/functions.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎  Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.9.4"}, "time": "2022-07-19T14:06:15+00:00"}, {"name": "ublaboo/datagrid", "version": "v6.9.5", "source": {"type": "git", "url": "https://github.com/contributte/datagrid.git", "reference": "e7b0ceb758572422c5152abd3f7a560746e9499c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/datagrid/zipball/e7b0ceb758572422c5152abd3f7a560746e9499c", "reference": "e7b0ceb758572422c5152abd3f7a560746e9499c", "shasum": ""}, "require": {"contributte/application": "^0.5.0", "nette/di": "^3.0.0", "nette/forms": "^3.1.3", "nette/utils": "^3.0.1", "php": ">=7.2", "symfony/property-access": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0"}, "require-dev": {"contributte/code-rules": "^1.1.0", "dibi/dibi": "^3.0.0 || ^4.0.0", "doctrine/annotations": "^1.12.1", "doctrine/cache": "^1.11.0", "doctrine/orm": "^2.11.1", "elasticsearch/elasticsearch": "^7.1", "mockery/mockery": "^1.3.3", "nette/database": "^3.0.2", "nette/tester": "^2.3.4", "nextras/dbal": "^3.0.1 || ^4.0", "nextras/orm": "^3.1.0 || ^4.0", "ninjify/coding-standard": "^0.12.1", "phpstan/phpstan-nette": "^1.0.0", "tharos/leanmapper": "^3.4.2 || ^4.0.0", "tracy/tracy": "^2.6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.10.x-dev"}}, "autoload": {"psr-4": {"Ublaboo\\DataGrid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://paveljanda.com"}, {"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "DataGrid for Nette Framework: filtering, sorting, pagination, tree view, table view, translator, etc", "keywords": ["contributte", "data", "datagrid", "grid", "nette", "table"], "support": {"issues": "https://github.com/contributte/datagrid/issues", "source": "https://github.com/contributte/datagrid/tree/v6.9.5"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-04-29T12:05:58+00:00"}, {"name": "ublaboo/mailing", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/ublaboo/mailing.git", "reference": "710b808c77944a85c6748b85b2cc32d24fbe5636"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ublaboo/mailing/zipball/710b808c77944a85c6748b85b2cc32d24fbe5636", "reference": "710b808c77944a85c6748b85b2cc32d24fbe5636", "shasum": ""}, "require": {"latte/latte": "~2.4 || ~3.0", "nette/application": "~2.4 || ~3.0", "nette/di": "~2.4 || ~3.0", "nette/mail": "~2.4 || ~3.0", "nette/utils": "~2.4 || ~3.0", "php": ">= 7.1"}, "require-dev": {"mockery/mockery": "~1.0", "nette/tester": "~2.0", "tracy/tracy": "^2.4"}, "type": "library", "autoload": {"psr-4": {"Ublaboo\\Mailing\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://paveljanda.com"}], "description": "Extension for Nette Framework: Easy & object-oriented way of sending & logging mails", "homepage": "https://ublaboo.org/mailing", "keywords": ["extension", "log", "mail", "mailing", "nette"], "support": {"issues": "https://github.com/ublaboo/mailing/issues", "source": "https://github.com/ublaboo/mailing/tree/v1.2.1"}, "time": "2021-02-15T09:45:01+00:00"}, {"name": "veewee/xml", "version": "2.14.0", "source": {"type": "git", "url": "https://github.com/veewee/xml.git", "reference": "143c5655c3af11b187157af16340ee69a244e633"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/veewee/xml/zipball/143c5655c3af11b187157af16340ee69a244e633", "reference": "143c5655c3af11b187157af16340ee69a244e633", "shasum": ""}, "require": {"azjezz/psl": "^2.0.3", "ext-dom": "*", "ext-libxml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-xsl": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "webmozart/assert": "^1.10"}, "require-dev": {"php-standard-library/psalm-plugin": "^2.2", "symfony/finder": "^6.1", "veewee/composer-run-parallel": "^1.0.0", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"files": ["src/bootstrap.php"], "psr-4": {"VeeWee\\Xml\\": "src/Xml"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}], "description": "XML without worries", "keywords": ["Xpath", "array-to-xml", "dom", "dom-manipulation", "reader", "writer", "xml", "xml-to-array", "xml_decode", "xml_encode", "xsd", "xslt"], "support": {"issues": "https://github.com/veewee/xml/issues", "source": "https://github.com/veewee/xml/tree/2.14.0"}, "funding": [{"url": "https://github.com/veewee", "type": "github"}], "time": "2024-01-14T12:13:42+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b56450eed252f6801410d810c8e1727224ae0743"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b56450eed252f6801410d810c8e1727224ae0743", "reference": "b56450eed252f6801410d810c8e1727224ae0743", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2022-03-08T17:03:00+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "wedo/api", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/WEDOehf/api.git", "reference": "eb3d0345620c86eaf19f9c6a89ac52a3f38c2a6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WEDOehf/api/zipball/eb3d0345620c86eaf19f9c6a89ac52a3f38c2a6b", "reference": "eb3d0345620c86eaf19f9c6a89ac52a3f38c2a6b", "shasum": ""}, "require": {"nette/application": "^3.0", "nette/di": "^3.0", "nette/forms": "^3.1.2", "php": "^8.0", "psr/log": "^1.1", "wedo/utilities": "^2.0"}, "require-dev": {"contributte/qa": "^0.1", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-dibi": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Wedo\\Api\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "rest api", "support": {"issues": "https://github.com/WEDOehf/api/issues", "source": "https://github.com/WEDOehf/api/tree/v2.1.1"}, "time": "2022-07-11T09:31:38+00:00"}, {"name": "wedo/openapi-generator", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/WEDOehf/openapi-generator.git", "reference": "95ca18a62dda86c7ad792404cc23ed9a650a136f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WEDOehf/openapi-generator/zipball/95ca18a62dda86c7ad792404cc23ed9a650a136f", "reference": "95ca18a62dda86c7ad792404cc23ed9a650a136f", "shasum": ""}, "require": {"nette/finder": "^2.4", "nette/utils": "^2.4|^3.0", "php": "^8.0"}, "require-dev": {"contributte/qa": "^0.1", "phpstan/phpstan": "^0.12.6", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Wedo\\OpenApiGenerator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>"}], "description": "Open api json generator from typed classes", "keywords": ["api", "documentation", "generator", "openapi", "swagger"], "support": {"issues": "https://github.com/WEDOehf/openapi-generator/issues", "source": "https://github.com/WEDOehf/openapi-generator/tree/v2.0.0"}, "time": "2022-01-05T08:43:57+00:00"}, {"name": "wedo/utilities", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/WEDOehf/utilities.git", "reference": "e45ffdd99d749868a265908a39a12f0b9f679a1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WEDOehf/utilities/zipball/e45ffdd99d749868a265908a39a12f0b9f679a1b", "reference": "e45ffdd99d749868a265908a39a12f0b9f679a1b", "shasum": ""}, "require": {"contributte/aop": "^3.0", "nette/caching": "^3.0", "nette/security": "^3.0", "nette/utils": "^3.0", "php": "^8.0"}, "require-dev": {"contributte/qa": "^0.1", "dg/bypass-finals": "^1.2", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-dibi": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.0", "psr/log": "^1.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Wedo\\Utilities\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Utility classes", "support": {"issues": "https://github.com/WEDOehf/utilities/issues", "source": "https://github.com/WEDOehf/utilities/tree/v2.0.1"}, "time": "2022-07-11T09:46:03+00:00"}], "packages-dev": [{"name": "composer/xdebug-handler", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "ced299686f41dce890debac69273b47ffe98a40c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ced299686f41dce890debac69273b47ffe98a40c", "reference": "ced299686f41dce890debac69273b47ffe98a40c", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T21:32:43+00:00"}, {"name": "contributte/qa", "version": "v0.1.0", "source": {"type": "git", "url": "https://github.com/contributte/qa.git", "reference": "60b1e32e2fc46277ee5cf766f570ff9c42ed57dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/qa/zipball/60b1e32e2fc46277ee5cf766f570ff9c42ed57dc", "reference": "60b1e32e2fc46277ee5cf766f570ff9c42ed57dc", "shasum": ""}, "require": {"php": ">=7.4", "slevomat/coding-standard": "^7.0.16", "squizlabs/php_codesniffer": "^3.6.1"}, "bin": ["bin/codesniffer", "bin/codefixer"], "type": "library", "extra": {"branch-alias": {"dev-master": "0.1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Tuned & very strict coding standards for PHP projects. Trusted by Contributte, Apitte, Nettrine and many others.", "homepage": "https://github.com/contributte/qa", "keywords": ["Codestyle", "codesniffer", "contributte", "qa", "quality assurance"], "support": {"issues": "https://github.com/contributte/qa/issues", "source": "https://github.com/contributte/qa/tree/v0.1.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-01-06T13:00:48+00:00"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "dg/bypass-finals", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/dg/bypass-finals.git", "reference": "495f5bc762e7bf30a13ed8253f44bb3a701767bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/bypass-finals/zipball/495f5bc762e7bf30a13ed8253f44bb3a701767bb", "reference": "495f5bc762e7bf30a13ed8253f44bb3a701767bb", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "^2.3", "phpstan/phpstan": "^0.12"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Removes final keyword from source code on-the-fly and allows mocking of final methods and classes", "keywords": ["finals", "mocking", "phpunit", "testing", "unit"], "support": {"issues": "https://github.com/dg/bypass-finals/issues", "source": "https://github.com/dg/bypass-finals/tree/v1.3.1"}, "time": "2021-04-09T10:42:55+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/10dcfce151b967d20fde1b34ae6640712c3891bc", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-03-03T08:28:38+00:00"}, {"name": "nextras/mail-panel", "version": "v2.6.1", "source": {"type": "git", "url": "https://github.com/nextras/mail-panel.git", "reference": "db8febadbdfa572ff859d23b63d46676f96a9477"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/mail-panel/zipball/db8febadbdfa572ff859d23b63d46676f96a9477", "reference": "db8febadbdfa572ff859d23b63d46676f96a9477", "shasum": ""}, "require": {"latte/latte": "~2.6 || ~3.0", "nette/http": "~3.0", "nette/mail": "~3.1", "nette/utils": "~3.0", "php": "~7.1 || ~8.0", "tracy/tracy": "~2.4"}, "require-dev": {"mockery/mockery": "~1.3.3", "nette/tester": "~2.0", "phpstan/phpstan": "1.4.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Nextras\\MailPanel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://www.jandrabek.cz"}, {"name": "<PERSON>", "homepage": "http://www.janmarek.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "MailPanel is extension for Nette Framework which captures sent e-mails in development mode and shows them in debugger bar.", "keywords": ["debugging", "framework", "mail", "mail panel", "mailing", "mailpanel"], "support": {"issues": "https://github.com/nextras/mail-panel/issues", "source": "https://github.com/nextras/mail-panel/tree/v2.6.1"}, "time": "2022-05-30T06:34:36+00:00"}, {"name": "nikic/php-parser", "version": "v4.14.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "34bea19b6e03d8153165d8f30bba4c3be86184c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/34bea19b6e03d8153165d8f30bba4c3be86184c1", "reference": "34bea19b6e03d8153165d8f30bba4c3be86184c1", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.14.0"}, "time": "2022-05-31T20:59:12+00:00"}, {"name": "pdepend/pdepend", "version": "2.10.3", "source": {"type": "git", "url": "https://github.com/pdepend/pdepend.git", "reference": "da3166a06b4a89915920a42444f707122a1584c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pdepend/pdepend/zipball/da3166a06b4a89915920a42444f707122a1584c9", "reference": "da3166a06b4a89915920a42444f707122a1584c9", "shasum": ""}, "require": {"php": ">=5.3.7", "symfony/config": "^2.3.0|^3|^4|^5|^6.0", "symfony/dependency-injection": "^2.3.0|^3|^4|^5|^6.0", "symfony/filesystem": "^2.3.0|^3|^4|^5|^6.0"}, "require-dev": {"easy-doc/easy-doc": "0.0.0|^1.2.3", "gregwar/rst": "^1.0", "phpunit/phpunit": "^4.8.36|^5.7.27", "squizlabs/php_codesniffer": "^2.0.0"}, "bin": ["src/bin/pdepend"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"PDepend\\": "src/main/php/PDepend"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Official version of pdepend to be handled with Composer", "support": {"issues": "https://github.com/pdepend/pdepend/issues", "source": "https://github.com/pdepend/pdepend/tree/2.10.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/pdepend/pdepend", "type": "tidelift"}], "time": "2022-02-23T07:53:09+00:00"}, {"name": "phar-io/manifest", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "reference": "97803eca37d319dfa7826cc2437fc020857acb53", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "time": "2021-07-20T11:28:43+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "77a32518733312af16a44300404e945338981de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/77a32518733312af16a44300404e945338981de3", "reference": "77a32518733312af16a44300404e945338981de3", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.1"}, "time": "2022-03-15T21:29:03+00:00"}, {"name": "phpmd/phpmd", "version": "2.12.0", "source": {"type": "git", "url": "https://github.com/phpmd/phpmd.git", "reference": "c0b678ba71902f539c27c14332aa0ddcf14388ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpmd/phpmd/zipball/c0b678ba71902f539c27c14332aa0ddcf14388ec", "reference": "c0b678ba71902f539c27c14332aa0ddcf14388ec", "shasum": ""}, "require": {"composer/xdebug-handler": "^1.0 || ^2.0 || ^3.0", "ext-xml": "*", "pdepend/pdepend": "^2.10.3", "php": ">=5.3.9"}, "require-dev": {"easy-doc/easy-doc": "0.0.0 || ^1.3.2", "ext-json": "*", "ext-simplexml": "*", "gregwar/rst": "^1.0", "mikey179/vfsstream": "^1.6.8", "phpunit/phpunit": "^4.8.36 || ^5.7.27", "squizlabs/php_codesniffer": "^2.0"}, "bin": ["src/bin/phpmd"], "type": "library", "autoload": {"psr-0": {"PHPMD\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/man<PERSON><PERSON><PERSON>", "role": "Project Founder"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ravage84", "role": "Project Maintainer"}, {"name": "Other contributors", "homepage": "https://github.com/phpmd/phpmd/graphs/contributors", "role": "Contributors"}], "description": "PHPMD is a spin-off project of PHP Depend and aims to be a PHP equivalent of the well known Java tool PMD.", "homepage": "https://phpmd.org/", "keywords": ["mess detection", "mess detector", "pdepend", "phpmd", "pmd"], "support": {"irc": "irc://irc.freenode.org/phpmd", "issues": "https://github.com/phpmd/phpmd/issues", "source": "https://github.com/phpmd/phpmd/tree/2.12.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/phpmd/phpmd", "type": "tidelift"}], "time": "2022-03-24T13:33:01+00:00"}, {"name": "phpspec/prophecy", "version": "v1.15.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "bbcd7380b0ebf3961ee21409db7b38bc31d69a13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/bbcd7380b0ebf3961ee21409db7b38bc31d69a13", "reference": "bbcd7380b0ebf3961ee21409db7b38bc31d69a13", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2", "php": "^7.2 || ~8.0, <8.2", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0 || ^7.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.15.0"}, "time": "2021-12-08T12:19:24+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.6.4", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "135607f9ccc297d6923d49c2bcf309f509413215"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/135607f9ccc297d6923d49c2bcf309f509413215", "reference": "135607f9ccc297d6923d49c2bcf309f509413215", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.6.4"}, "time": "2022-06-26T13:09:08+00:00"}, {"name": "phpstan/phpstan", "version": "1.10.19", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "af5a296ff02610c1bfb4ddfac9fd4a08657b9046"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/af5a296ff02610c1bfb4ddfac9fd4a08657b9046", "reference": "af5a296ff02610c1bfb4ddfac9fd4a08657b9046", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2023-06-14T15:26:58+00:00"}, {"name": "phpstan/phpstan-nette", "version": "1.2.9", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-nette.git", "reference": "0e3a6805917811d685e59bb83c2286315f2f6d78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-nette/zipball/0e3a6805917811d685e59bb83c2286315f2f6d78", "reference": "0e3a6805917811d685e59bb83c2286315f2f6d78", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.10"}, "conflict": {"nette/application": "<2.3.0", "nette/component-model": "<2.3.0", "nette/di": "<2.3.0", "nette/forms": "<2.3.0", "nette/http": "<2.3.0", "nette/utils": "<2.3.0"}, "require-dev": {"nette/application": "^3.0", "nette/forms": "^3.0", "nette/utils": "^2.3.0 || ^3.0.0", "nikic/php-parser": "^4.13.2", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-php-parser": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Nette Framework class reflection extension for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-nette/issues", "source": "https://github.com/phpstan/phpstan-nette/tree/1.2.9"}, "time": "2023-04-12T14:11:53+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.15", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "2e9da11878c4202f97915c1cb4bb1ca318a63f5f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2e9da11878c4202f97915c1cb4bb1ca318a63f5f", "reference": "2e9da11878c4202f97915c1cb4bb1ca318a63f5f", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.13.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcov": "*", "ext-xdebug": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.15"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-03-07T09:28:20+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.5.21", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "0e32b76be457de00e83213528f6bb37e2a38fcb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/0e32b76be457de00e83213528f6bb37e2a38fcb1", "reference": "0e32b76be457de00e83213528f6bb37e2a38fcb1", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=7.3", "phpspec/prophecy": "^1.12.1", "phpunit/php-code-coverage": "^9.2.13", "phpunit/php-file-iterator": "^3.0.5", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.3", "phpunit/php-timer": "^5.0.2", "sebastian/cli-parser": "^1.0.1", "sebastian/code-unit": "^1.0.6", "sebastian/comparator": "^4.0.5", "sebastian/diff": "^4.0.3", "sebastian/environment": "^5.1.3", "sebastian/exporter": "^4.0.3", "sebastian/global-state": "^5.0.1", "sebastian/object-enumerator": "^4.0.3", "sebastian/resource-operations": "^3.0.3", "sebastian/type": "^3.0", "sebastian/version": "^3.0.2"}, "require-dev": {"phpspec/prophecy-phpunit": "^2.0.1"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.5.21"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-06-19T12:14:25+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/442e7c7e687e42adc03470c7b668bc4b2402c0b2", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:08:49+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "55f4261989e546dc112258c7a75935a81a7ce382"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/55f4261989e546dc112258c7a75935a81a7ce382", "reference": "55f4261989e546dc112258c7a75935a81a7ce382", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T15:49:45+00:00"}, {"name": "sebastian/complexity", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/739b35e53379900cc9ac327b2147867b8b6efd88", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88", "shasum": ""}, "require": {"nikic/php-parser": "^4.7", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T15:52:27+00:00"}, {"name": "sebastian/diff", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "3461e3fccc7cfdfc2720be910d3bd73c69be590d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/3461e3fccc7cfdfc2720be910d3bd73c69be590d", "reference": "3461e3fccc7cfdfc2720be910d3bd73c69be590d", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:10:38+00:00"}, {"name": "sebastian/environment", "version": "5.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "1b5dff7bb151a4db11d49d90e5408e4e938270f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/1b5dff7bb151a4db11d49d90e5408e4e938270f7", "reference": "1b5dff7bb151a4db11d49d90e5408e4e938270f7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-04-03T09:37:03+00:00"}, {"name": "sebastian/exporter", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "65e8b7db476c5dd267e65eea9cab77584d3cfff9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/65e8b7db476c5dd267e65eea9cab77584d3cfff9", "reference": "65e8b7db476c5dd267e65eea9cab77584d3cfff9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-11-11T14:18:36+00:00"}, {"name": "sebastian/global-state", "version": "5.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "0ca8db5a5fc9c8646244e629625ac486fa286bf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/0ca8db5a5fc9c8646244e629625ac486fa286bf2", "reference": "0ca8db5a5fc9c8646244e629625ac486fa286bf2", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-02-14T08:28:10+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/c1c2e997aa3146983ed888ad08b15470a2e22ecc", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc", "shasum": ""}, "require": {"nikic/php-parser": "^4.6", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-28T06:42:11+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "cd9d8cf3c5804de4341c283ed787f099f5506172"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/cd9d8cf3c5804de4341c283ed787f099f5506172", "reference": "cd9d8cf3c5804de4341c283ed787f099f5506172", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:17:30+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:45:17+00:00"}, {"name": "sebastian/type", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "b233b84bc4465aff7b57cf1c4bc75c86d00d6dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/b233b84bc4465aff7b57cf1c4bc75c86d00d6dad", "reference": "b233b84bc4465aff7b57cf1c4bc75c86d00d6dad", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-03-15T09:54:48+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "slevomat/coding-standard", "version": "7.2.1", "source": {"type": "git", "url": "https://github.com/slevomat/coding-standard.git", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slevomat/coding-standard/zipball/aff06ae7a84e4534bf6f821dc982a93a5d477c90", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7", "php": "^7.2 || ^8.0", "phpstan/phpdoc-parser": "^1.5.1", "squizlabs/php_codesniffer": "^3.6.2"}, "require-dev": {"phing/phing": "2.17.3", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpstan/phpstan": "1.4.10|1.7.1", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-phpunit": "1.0.0|1.1.1", "phpstan/phpstan-strict-rules": "1.2.3", "phpunit/phpunit": "7.5.20|8.5.21|9.5.20"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"SlevomatCodingStandard\\": "SlevomatCodingStandard"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Slevomat Coding Standard for PHP_CodeSniffer complements Consistence Coding Standard by providing sniffs with additional checks.", "support": {"issues": "https://github.com/slevomat/coding-standard/issues", "source": "https://github.com/slevomat/coding-standard/tree/7.2.1"}, "funding": [{"url": "https://github.com/kukulich", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/slevomat/coding-standard", "type": "tidelift"}], "time": "2022-05-25T10:58:12+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.7.1", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "1359e176e9307e906dc3d890bcc9603ff6d90619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/1359e176e9307e906dc3d890bcc9603ff6d90619", "reference": "1359e176e9307e906dc3d890bcc9603ff6d90619", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "time": "2022-06-18T07:21:10+00:00"}, {"name": "symfony/config", "version": "v5.4.9", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "8f551fe22672ac7ab2c95fe46d899f960ed4d979"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/8f551fe22672ac7ab2c95fe46d899f960ed4d979", "reference": "8f551fe22672ac7ab2c95fe46d899f960ed4d979", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<4.4"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v5.4.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-17T10:39:36+00:00"}, {"name": "symfony/dependency-injection", "version": "v5.4.10", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "88d1c0d38c2e60f757fa11d89cfc885f0b7f5171"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/88d1c0d38c2e60f757fa11d89cfc885f0b7f5171", "reference": "88d1c0d38c2e60f757fa11d89cfc885f0b7f5171", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1.1", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<5.3", "symfony/finder": "<4.4", "symfony/proxy-manager-bridge": "<4.4", "symfony/yaml": "<4.4.26"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0|2.0"}, "require-dev": {"symfony/config": "^5.3|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4.26|^5.0|^6.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v5.4.10"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-26T13:00:04+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"cbschuld/browser.php": 20, "phpmd/phpmd": 0, "pragmarx/countries": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=8.1"}, "platform-dev": {}, "platform-overrides": {"php": "8.1"}, "plugin-api-version": "2.6.0"}
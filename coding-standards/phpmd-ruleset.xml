<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="Dodo IT">
    <description>Dodo IT code metrics rulesets</description>
    <rule ref="rulesets/codesize.xml/ExcessiveMethodLength">
        <properties>
            <property name="minimum" value="30"/>
            <property name="ignore-whitespace" value="true"/>
        </properties>
    </rule>
    <rule ref="rulesets/codesize.xml/ExcessiveClassLength">
        <properties>
            <property name="minimum" value="250"/>
            <property name="ignore-whitespace" value="true"/>
        </properties>
    </rule>
    <rule ref="rulesets/codesize.xml/ExcessiveParameterList"/>
    <rule ref="rulesets/codesize.xml/ExcessivePublicCount">
        <properties>
            <property name="minimum" value="20"/>
        </properties>
    </rule>
    <rule ref="rulesets/controversial.xml/Superglobals"/>
    <rule ref="rulesets/design.xml/ExitExpression"/>
    <rule ref="rulesets/design.xml/EvalExpression"/>
    <rule ref="rulesets/design.xml/GotoStatement"/>
    <rule ref="rulesets/design.xml/DepthOfInheritance"/>
    <rule ref="rulesets/design.xml/CouplingBetweenObjects"/>
    <rule ref="rulesets/naming.xml/ConstantNamingConventions"/>
    <rule ref="rulesets/unusedcode.xml/UnusedPrivateField"/>
    <rule ref="rulesets/unusedcode.xml/UnusedLocalVariable"/>
    <rule ref="rulesets/unusedcode.xml/UnusedPrivateMethod"/>
</ruleset>
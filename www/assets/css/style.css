/* ------------------------------------------------------------------- */
/* Table of Contents
----------------------------------------------------------------------
	01. Bootstrap grid

	02. Layout Components ................... Generic layout elements.
		# Forms Reset & Styles
		# Header
		# Sticky Header
		# Back to top
		# Navigation
		# User Menu
		# Main Search Container
		# Sort by custom select
		# Input with dropdown
		# Panel Dropdown
		# Single Listing Page
		# Contact Page
		# Listing Item Layout Style
		# Titlebar
		# USP block
		# Newsletter / Help block
		# Contact
		# Sidebar
		# Hubspot parts
		# Thank you page
		# Loaders
		# Static pages
		# Testimonials

	03. Shortcodes .......................... Template elements.
		# Buttons
		# Loaders
		# Tables
		# Accordion / Toggles
		# Tooltips
		# Info Box
		# Notification Boxes
		# Custom Checkboxes

	04. Script Styles ....................... jQuery add-ons CSS.
		# Google Maps
		# Magnific Popup
		# Slick Carousel
		# Select2
		# NoUiSlider
		# Light Gallery

	05. Others .............................. CSS helper classes etc.
		# Common Styles
		# Offsets

	06. Media Queries ....................... Mobile style sheets.

	07. Colours
*/

:root {
	/*--system-ui: system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";*/
	--font-primary: 'Poppins', sans-serif;
	--button-cta: #2A7C2A;
	--button-cta__hover: #318931;
}


/* ------------------------------------------------------------------- */
/* 01. Bootstrap grid - Bootstrap v4.0.0-beta (https://getbootstrap.com)
---------------------------------------------------------------------- */
legend,td,th{padding:0}body,figure{margin:0}@media print{blockquote,img,pre,tr{page-break-inside:avoid}*,::after,::before{text-shadow:none!important;box-shadow:none!important}a,a:visited{text-decoration:underline}abbr[title]::after{content:" (" attr(title) ")"}pre{white-space:pre-wrap!important}blockquote,pre{border:1px solid #999}thead{display:table-header-group}h2,h3,p{orphans:3;widows:3}h2,h3{page-break-after:avoid}.navbar{display:none}.badge{border:1px solid #000}.table{border-collapse:collapse!important}.table td,.table th{background-color:#fff!important}.table-bordered td,.table-bordered th{border:1px solid #ddd!important}}hr,img,legend{border:0}html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}b,strong{font-weight:600}dfn{font-style:italic}h1{margin:.67em 0}mark{background:#ff0;color:#8a803e}mark.color{color:#fff}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}img{vertical-align:middle}svg:not(:root){overflow:hidden}hr{box-sizing:content-box;height:0}pre,textarea{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]{-webkit-appearance:textfield;box-sizing:content-box}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}optgroup{font-weight:600}table{border-collapse:collapse;border-spacing:0}@media print{blockquote,img,pre,tr{page-break-inside:avoid}*,:after,:before{background:0 0!important;color:#000!important;-webkit-box-shadow:none!important;box-shadow:none!important;text-shadow:none!important}a,a:visited{text-decoration:underline}a[href]:after{content:" (" attr(href) ")"}abbr[title]:after{content:" (" attr(title) ")"}a[href^="javascript:"]:after,a[href^="#"]:after{content:""}blockquote,pre{border:1px solid #999}thead{display:table-header-group}img{max-width:100%!important}h2,h3,p{orphans:3;widows:3}h2,h3{page-break-after:avoid}.navbar{display:none}.label{border:1px solid #000}.table{border-collapse:collapse!important}.table td,.table th{background-color:#fff!important}.table-bordered td,.table-bordered th{border:1px solid #ddd!important}}*,:after,:before{box-sizing:border-box}html{font-size:10px;-webkit-tap-highlight-color:transparent}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit}a{color:#333;text-decoration:none;transition:color .2s}a:focus,a:hover{text-decoration:underline}a:focus{outline:dotted thin;outline:-webkit-focus-ring-color auto 5px;outline-offset:-2px}.img-responsive{display:block;max-width:100%;height:auto}.img-rounded{border-radius:6px}.img-thumbnail{padding:4px;line-height:1.42857143;background-color:#fff;border:1px solid #ddd;border-radius:4px;transition:all .2s ease-in-out;display:inline-block;max-width:100%;height:auto}.img-circle{border-radius:50%}hr{margin-top:20px;margin-bottom:20px;border-top:1px solid #eee}.sr-only{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}[role=button]{cursor:pointer}.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6{font-family:inherit;font-weight:400;line-height:1.1;color:inherit}.h1 .small,.h1 small,.h2 .small,.h2 small,.h3 .small,.h3 small,.h4 .small,.h4 small,.h5 .small,.h5 small,.h6 .small,.h6 small,h1 .small,h1 small,h2 .small,h2 small,h3 .small,h3 small,h4 .small,h4 small,h5 .small,h5 small,h6 .small,h6 small{font-weight:400;line-height:1;color:#333}.h1,.h2,.h3,h1,h2,h3{margin-top:20px;margin-bottom:10px}.h1 .small,.h1 small,.h2 .small,.h2 small,.h3 .small,.h3 small,h1 .small,h1 small,h2 .small,h2 small,h3 .small,h3 small{font-size:65%}.h4,.h5,.h6,h4,h5,h6{margin-top:10px;margin-bottom:10px}.h4 .small,.h4 small,.h5 .small,.h5 small,.h6 .small,.h6 small,h4 .small,h4 small,h5 .small,h5 small,h6 .small,h6 small{font-size:75%}.h1,h1{font-size:2.25rem}.h2,h2{font-size:1.875rem}.h3,h3{font-size:24px}.h4,h4{font-size:1.125rem}.h5,h5{font-size:.875rem}.h6,h6{font-size:12px}p{margin:0 0 17px}.lead{margin-bottom:20px;font-size:16px;font-weight:300;line-height:1.4}@media (min-width:768px){.lead{font-size:21px}}.small,small{font-size:85%}.mark,mark{background-color:#fcf8e3;padding:.2em}.text-left{text-align:left}.text-right{text-align:right}.text-center{text-align:center}.text-justify{text-align:justify}.text-nowrap{white-space:nowrap}.text-lowercase{text-transform:lowercase}.text-uppercase{text-transform:uppercase}.text-capitalize{text-transform:capitalize}.text-muted{color:#777}.bg-primary{color:#fff;background-color:#337ab7}a.bg-primary:focus,a.bg-primary:hover{background-color:#286090}.bg-success{background-color:#dff0d8}a.bg-success:focus,a.bg-success:hover{background-color:#c1e2b3}.bg-info{background-color:#d9edf7}a.bg-info:focus,a.bg-info:hover{background-color:#afd9ee}.bg-warning{background-color:#fcf8e3}a.bg-warning:focus,a.bg-warning:hover{background-color:#f7ecb5}.bg-danger{background-color:#f2dede}a.bg-danger:focus,a.bg-danger:hover{background-color:#e4b9b9}.page-header{padding-bottom:9px;margin:40px 0 20px;border-bottom:1px solid #eee}dl,ol,ul{margin-top:0}blockquote ol:last-child,blockquote p:last-child,blockquote ul:last-child,ol ol,ol ul,ul ol,ul ul{margin-bottom:0}ol,ul{margin-bottom:10px;padding-left:15px}.list-inline,.list-unstyled{padding-left:0;list-style:none}.list-inline{margin-left:-5px}.list-inline>li{display:inline-block;padding-left:5px;padding-right:5px}dl{margin-bottom:20px}dd,dt{line-height:1.42857143}dt{font-weight:600}dd{margin-left:0}@media (min-width:768px){.dl-horizontal dt{float:left;width:160px;clear:left;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.dl-horizontal dd{margin-left:180px}.container{width:750px}}abbr[data-original-title],abbr[title]{cursor:help;border-bottom:1px dotted #777}.initialism{font-size:90%;text-transform:uppercase}blockquote{padding:10px 20px;margin:0 0 20px;font-size:17px;line-height:30px;color:#888;border-left:5px solid #eee}.container,.container-fluid{margin-right:auto;margin-left:auto}.table,address{margin-bottom:20px}blockquote .small,blockquote footer,blockquote small{display:block;font-size:80%;line-height:1.42857143;color:#777}blockquote .small:before,blockquote footer:before,blockquote small:before{content:'\2014 \00A0'}.blockquote-reverse,blockquote.pull-right{padding-right:15px;padding-left:0;border-right:5px solid #eee;border-left:0;text-align:right}.container,.container-fluid{padding-left:15px;padding-right:15px}.container--no-padding{padding-left:0;padding-right:0}caption,th{text-align:left}.blockquote-reverse .small:before,.blockquote-reverse footer:before,.blockquote-reverse small:before,blockquote.pull-right .small:before,blockquote.pull-right footer:before,blockquote.pull-right small:before{content:''}.blockquote-reverse .small:after,.blockquote-reverse footer:after,.blockquote-reverse small:after,blockquote.pull-right .small:after,blockquote.pull-right footer:after,blockquote.pull-right small:after{content:'\00A0 \2014'}address{font-style:normal;line-height:1.42857143}@media (min-width:992px){.container{width:970px}}@media (min-width:1240px){.container{width:1090px}}@media (min-width:1440px){.container{width:1410px}}.row{margin-left:-15px;margin-right:-15px}.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-xs-1,.col-xs-10,.col-xs-11,.col-xs-12,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9{position:relative;min-height:1px;padding-left:15px;padding-right:15px}.col-xs-1,.col-xs-10,.col-xs-11,.col-xs-12,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9{float:left}.col-xs-12{width:100%}.col-xs-11{width:91.66666667%}.col-xs-10{width:83.33333333%}.col-xs-9{width:75%}.col-xs-8{width:66.66666667%}.col-xs-7{width:58.33333333%}.col-xs-6{width:50%}.col-xs-5{width:41.66666667%}.col-xs-4{width:33.33333333%}.col-xs-3{width:25%}.col-xs-2{width:16.66666667%}.col-xs-1{width:8.33333333%}.col-xs-pull-12{right:100%}.col-xs-pull-11{right:91.66666667%}.col-xs-pull-10{right:83.33333333%}.col-xs-pull-9{right:75%}.col-xs-pull-8{right:66.66666667%}.col-xs-pull-7{right:58.33333333%}.col-xs-pull-6{right:50%}.col-xs-pull-5{right:41.66666667%}.col-xs-pull-4{right:33.33333333%}.col-xs-pull-3{right:25%}.col-xs-pull-2{right:16.66666667%}.col-xs-pull-1{right:8.33333333%}.col-xs-pull-0{right:auto}.col-xs-push-12{left:100%}.col-xs-push-11{left:91.66666667%}.col-xs-push-10{left:83.33333333%}.col-xs-push-9{left:75%}.col-xs-push-8{left:66.66666667%}.col-xs-push-7{left:58.33333333%}.col-xs-push-6{left:50%}.col-xs-push-5{left:41.66666667%}.col-xs-push-4{left:33.33333333%}.col-xs-push-3{left:25%}.col-xs-push-2{left:16.66666667%}.col-xs-push-1{left:8.33333333%}.col-xs-push-0{left:auto}.col-xs-offset-12{margin-left:100%}.col-xs-offset-11{margin-left:91.66666667%}.col-xs-offset-10{margin-left:83.33333333%}.col-xs-offset-9{margin-left:75%}.col-xs-offset-8{margin-left:66.66666667%}.col-xs-offset-7{margin-left:58.33333333%}.col-xs-offset-6{margin-left:50%}.col-xs-offset-5{margin-left:41.66666667%}.col-xs-offset-4{margin-left:33.33333333%}.col-xs-offset-3{margin-left:25%}.col-xs-offset-2{margin-left:16.66666667%}.col-xs-offset-1{margin-left:8.33333333%}.col-xs-offset-0{margin-left:0}@media (min-width:768px){.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9{float:left}.col-sm-12{width:100%}.col-sm-11{width:91.66666667%}.col-sm-10{width:83.33333333%}.col-sm-9{width:75%}.col-sm-8{width:66.66666667%}.col-sm-7{width:58.33333333%}.col-sm-6{width:50%}.col-sm-5{width:41.66666667%}.col-sm-4{width:33.33333333%}.col-sm-3{width:25%}.col-sm-2{width:16.66666667%}.col-sm-1{width:8.33333333%}.col-sm-pull-12{right:100%}.col-sm-pull-11{right:91.66666667%}.col-sm-pull-10{right:83.33333333%}.col-sm-pull-9{right:75%}.col-sm-pull-8{right:66.66666667%}.col-sm-pull-7{right:58.33333333%}.col-sm-pull-6{right:50%}.col-sm-pull-5{right:41.66666667%}.col-sm-pull-4{right:33.33333333%}.col-sm-pull-3{right:25%}.col-sm-pull-2{right:16.66666667%}.col-sm-pull-1{right:8.33333333%}.col-sm-pull-0{right:auto}.col-sm-push-12{left:100%}.col-sm-push-11{left:91.66666667%}.col-sm-push-10{left:83.33333333%}.col-sm-push-9{left:75%}.col-sm-push-8{left:66.66666667%}.col-sm-push-7{left:58.33333333%}.col-sm-push-6{left:50%}.col-sm-push-5{left:41.66666667%}.col-sm-push-4{left:33.33333333%}.col-sm-push-3{left:25%}.col-sm-push-2{left:16.66666667%}.col-sm-push-1{left:8.33333333%}.col-sm-push-0{left:auto}.col-sm-offset-12{margin-left:100%}.col-sm-offset-11{margin-left:91.66666667%}.col-sm-offset-10{margin-left:83.33333333%}.col-sm-offset-9{margin-left:75%}.col-sm-offset-8{margin-left:66.66666667%}.col-sm-offset-7{margin-left:58.33333333%}.col-sm-offset-6{margin-left:50%}.col-sm-offset-5{margin-left:41.66666667%}.col-sm-offset-4{margin-left:33.33333333%}.col-sm-offset-3{margin-left:25%}.col-sm-offset-2{margin-left:16.66666667%}.col-sm-offset-1{margin-left:8.33333333%}.col-sm-offset-0{margin-left:0}}@media (min-width:992px){.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9{float:left}.col-md-12{width:100%}.col-md-11{width:91.66666667%}.col-md-10{width:83.33333333%}.col-md-9{width:75%}.col-md-8{width:66.66666667%}.col-md-7{width:58.33333333%}.col-md-6{width:50%}.col-md-5{width:41.66666667%}.col-md-4{width:33.33333333%}.col-md-3{width:25%}.col-md-2{width:16.66666667%}.col-md-1{width:8.33333333%}.col-md-pull-12{right:100%}.col-md-pull-11{right:91.66666667%}.col-md-pull-10{right:83.33333333%}.col-md-pull-9{right:75%}.col-md-pull-8{right:66.66666667%}.col-md-pull-7{right:58.33333333%}.col-md-pull-6{right:50%}.col-md-pull-5{right:41.66666667%}.col-md-pull-4{right:33.33333333%}.col-md-pull-3{right:25%}.col-md-pull-2{right:16.66666667%}.col-md-pull-1{right:8.33333333%}.col-md-pull-0{right:auto}.col-md-push-12{left:100%}.col-md-push-11{left:91.66666667%}.col-md-push-10{left:83.33333333%}.col-md-push-9{left:75%}.col-md-push-8{left:66.66666667%}.col-md-push-7{left:58.33333333%}.col-md-push-6{left:50%}.col-md-push-5{left:41.66666667%}.col-md-push-4{left:33.33333333%}.col-md-push-3{left:25%}.col-md-push-2{left:16.66666667%}.col-md-push-1{left:8.33333333%}.col-md-push-0{left:auto}.col-md-offset-12{margin-left:100%}.col-md-offset-11{margin-left:91.66666667%}.col-md-offset-10{margin-left:83.33333333%}.col-md-offset-9{margin-left:75%}.col-md-offset-8{margin-left:66.66666667%}.col-md-offset-7{margin-left:58.33333333%}.col-md-offset-6{margin-left:50%}.col-md-offset-5{margin-left:41.66666667%}.col-md-offset-4{margin-left:33.33333333%}.col-md-offset-3{margin-left:25%}.col-md-offset-2{margin-left:16.66666667%}.col-md-offset-1{margin-left:8.33333333%}.col-md-offset-0{margin-left:0}}@media (min-width:1260px){.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9{float:left}.col-lg-12{width:100%}.col-lg-11{width:91.66666667%}.col-lg-10{width:83.33333333%}.col-lg-9{width:75%}.col-lg-8{width:66.66666667%}.col-lg-7{width:58.33333333%}.col-lg-6{width:50%}.col-lg-5{width:41.66666667%}.col-lg-4{width:33.33333333%}.col-lg-3{width:25%}.col-lg-2{width:16.66666667%}.col-lg-1{width:8.33333333%}.col-lg-pull-12{right:100%}.col-lg-pull-11{right:91.66666667%}.col-lg-pull-10{right:83.33333333%}.col-lg-pull-9{right:75%}.col-lg-pull-8{right:66.66666667%}.col-lg-pull-7{right:58.33333333%}.col-lg-pull-6{right:50%}.col-lg-pull-5{right:41.66666667%}.col-lg-pull-4{right:33.33333333%}.col-lg-pull-3{right:25%}.col-lg-pull-2{right:16.66666667%}.col-lg-pull-1{right:8.33333333%}.col-lg-pull-0{right:auto}.col-lg-push-12{left:100%}.col-lg-push-11{left:91.66666667%}.col-lg-push-10{left:83.33333333%}.col-lg-push-9{left:75%}.col-lg-push-8{left:66.66666667%}.col-lg-push-7{left:58.33333333%}.col-lg-push-6{left:50%}.col-lg-push-5{left:41.66666667%}.col-lg-push-4{left:33.33333333%}.col-lg-push-3{left:25%}.col-lg-push-2{left:16.66666667%}.col-lg-push-1{left:8.33333333%}.col-lg-push-0{left:auto}.col-lg-offset-12{margin-left:100%}.col-lg-offset-11{margin-left:91.66666667%}.col-lg-offset-10{margin-left:83.33333333%}.col-lg-offset-9{margin-left:75%}.col-lg-offset-8{margin-left:66.66666667%}.col-lg-offset-7{margin-left:58.33333333%}.col-lg-offset-6{margin-left:50%}.col-lg-offset-5{margin-left:41.66666667%}.col-lg-offset-4{margin-left:33.33333333%}.col-lg-offset-3{margin-left:25%}.col-lg-offset-2{margin-left:16.66666667%}.col-lg-offset-1{margin-left:8.33333333%}.col-lg-offset-0{margin-left:0}}table{background-color:transparent}caption{padding-top:8px;padding-bottom:8px;color:#777}.table{width:100%;max-width:100%}.clearfix:after,.clearfix:before,.container-fluid:after,.container-fluid:before,.container:after,.container:before,.dl-horizontal dd:after,.dl-horizontal dd:before,.row:after,.row:before{content:" ";display:table}.clearfix:after,.container-fluid:after,.container:after,.dl-horizontal dd:after,.row:after{clear:both}.center-block{display:block;margin-left:auto;margin-right:auto}.pull-right{float:right!important}.pull-left{float:left!important}.hide{display:none!important}.show{display:block!important}.hidden,.visible-lg,.visible-lg-block,.visible-lg-inline,.visible-lg-inline-block,.visible-md,.visible-md-block,.visible-md-inline,.visible-md-inline-block,.visible-sm,.visible-sm-block,.visible-sm-inline,.visible-sm-inline-block,.visible-xs,.visible-xs-block,.visible-xs-inline,.visible-xs-inline-block{display:none!important}.invisible{visibility:hidden}.text-hide{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}.affix{position:fixed}@-ms-viewport{width:device-width}@media (max-width:767px){.visible-xs{display:block!important}table.visible-xs{display:table!important}tr.visible-xs{display:table-row!important}td.visible-xs,th.visible-xs{display:table-cell!important}.visible-xs-block{display:block!important}.visible-xs-inline{display:inline!important}.visible-xs-inline-block{display:inline-block!important}}@media (min-width:768px) and (max-width:991px){.visible-sm{display:block!important}table.visible-sm{display:table!important}tr.visible-sm{display:table-row!important}td.visible-sm,th.visible-sm{display:table-cell!important}.visible-sm-block{display:block!important}.visible-sm-inline{display:inline!important}.visible-sm-inline-block{display:inline-block!important}}@media (min-width:992px) and (max-width:1199px){.visible-md{display:block!important}table.visible-md{display:table!important}tr.visible-md{display:table-row!important}td.visible-md,th.visible-md{display:table-cell!important}.visible-md-block{display:block!important}.visible-md-inline{display:inline!important}.visible-md-inline-block{display:inline-block!important}}@media (min-width:1200px){.visible-lg{display:block!important}table.visible-lg{display:table!important}tr.visible-lg{display:table-row!important}td.visible-lg,th.visible-lg{display:table-cell!important}.visible-lg-block{display:block!important}.visible-lg-inline{display:inline!important}.visible-lg-inline-block{display:inline-block!important}.hidden-lg{display:none!important}}@media (max-width:767px){.hidden-xs{display:none!important}}@media (min-width:768px) and (max-width:991px){.hidden-sm{display:none!important}}@media (min-width:992px) and (max-width:1199px){.hidden-md{display:none!important}}.visible-print{display:none!important}@media print{.visible-print{display:block!important}table.visible-print{display:table!important}tr.visible-print{display:table-row!important}td.visible-print,th.visible-print{display:table-cell!important}}.visible-print-block{display:none!important}@media print{.visible-print-block{display:block!important}}.visible-print-inline{display:none!important}@media print{.visible-print-inline{display:inline!important}}.visible-print-inline-block{display:none!important}@media print{.visible-print-inline-block{display:inline-block!important}.hidden-print{display:none!important}}


/* ------------------------------------------------------------------- */
/*  02. Layout Components
---------------------------------------------------------------------- */
@font-face {
	font-family: 'Poppins';
	font-display: swap;
	src: url('../../fonts/Poppins-Regular.woff2') format('woff2'), url('../../fonts/Poppins-Regular.woff') format('woff');
	font-weight: 400;
	font-style: normal;
}

@font-face {
	font-family: 'Poppins';
	font-display: swap;
	src: url('../../fonts/Poppins-Medium.woff2') format('woff2'), url('../../fonts/Poppins-Medium.woff') format('woff');
	font-weight: 500;
	font-style: normal;
}

@font-face {
	font-family: 'Poppins';
	font-display: swap;
	src: url('../../fonts/Poppins-SemiBold.woff2') format('woff2'), url('../../fonts/Poppins-SemiBold.woff') format('woff');
	font-weight: 600;
	font-style: normal;
}

@font-face {
	font-family: 'Butler';
	font-display: swap;
	src: url('../../fonts/butler-regular.woff2') format('woff2'), url('../../fonts/butler-regular.woff') format('woff');
	font-weight: 600;
	font-style: normal;
}

:root {
	--color-anchor: #20399d;
	--button-background-anchor: linear-gradient(to right, rgba(255,237,174,1) 0%, rgba(222,202,132,1) 100%);
	--button-color-anchor: #252525;
}


/* Forms Reset & Styles
------------------------------------- */
input,
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
textarea,
select {
	height: 51px;
	line-height: 51px;
	padding: 0 20px;
	outline: none;
	font-size: 0.938rem; /* 15px */
	color: #252525;
	margin: 0 0 16px 0;
	max-width: 100%;
	width: 100%;
	box-sizing: border-box;
	display: block;
	background-color: #fff;
	border: 1px solid #333;
	font-weight: 400;
	opacity: 1;
	border-radius: 4px;
}

input.form-input,
input[type="text"].form-input,
input[type="password"].form-input,
input[type="email"].form-input,
input[type="number"].form-input,
textarea.form-textarea {
	border-color: #333;
}

label.form-label {
	font-size: 0.938rem;
	font-weight: 600;
}

.form-note {
	font-size: 1rem;
	color: #333;
	line-height: 1.2;
	display: flex;
	align-items: center;
}

.form-note__icon {
	margin-right: 8px;
}

.form-row--flex {
	display: flex;
}

.form-row--flex-column {
	flex-direction: column;
}

.form-row--flex-row {
	flex-direction: row;
}

.form-row--flex-centered {
	align-items: center;
	justify-content: space-between;
}

.form-column--persons {
	display: flex;
	width: 100%;
	margin-bottom: 10px;
}

input.no-margin,
textarea.no-margin,
select.no-margin {
	margin: 0;
}

input.error {
	border: 1px solid red;
}

input.focused {
	border: 1px solid #252525;
}

select {
	cursor: pointer;
}

.row-flex,
.col-flex {
	display: flex;
}

.row-flex-centered {
	align-items: center;
}

.col-flex-center {
	justify-content: center;
}

.col-flex-vertical-center {
	align-items: center;
}

.form-upload-note {
	font-style: italic;
	font-size: .75rem;
	color: #666;
	margin-top: -14px;
	display: block;
	margin-bottom: 5px;
}

input {
	transition: all .1s ease-in-out;
}

input:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
textarea:focus {
	transition: box-shadow .2s !important;
	opacity: 1;
}

.main-search-input-item input:focus {
	color: #252525;
}

input[type="submit"] {
	border: none;
	padding: 11px 18px;
	width: auto;
}

input[type="checkbox"] {
	display: inline;
}

input[type="radio"] {
	width: 15px;
	height: 15px;
	cursor: pointer;
	box-shadow: none;
	opacity: 1;
}

.input-persons-placeholder {
	font-size: 0.938rem;
	line-height: 1.1;
	position: absolute;
	left: 45px;
	top: 50%;
	transform: translateY(-50%);
	color: #252525;
	cursor: pointer;
}

input[type="text"].form-control-visitor,
select.form-control-visitor {
	border-radius: 6px!important;
	border: 1px solid #D1D0D4!important;
	height: 38px;
	padding: 0 14px;
	margin-bottom: 0!important;
}

input[type="text"].form-control-visitor:not([readonly]):focus,
select.form-control-visitor:focus {
	border-color: #8e8e8f!important;
}

input[type="text"].form-control-visitor::placeholder,
select.form-control-visitor option[disabled] {
    font-size: .95rem!important;
    color: #999;
}

select.form-control-visitor {
  color: #999;
  font-size: .95rem;
}

select.form-control-visitor:valid {
	font-size: .95rem;
	color: #000;
}


input[type="text"].form-control-visitor.form-visitor-error,
select.form-control-visitor.form-visitor-error,
.evisitor-layout .select2-container--default .select2-selection--single.form-visitor-error {
	border: 1px solid #e52d2d!important;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

.form-visitor-error.shake {
	animation: shake 0.3s ease;
	animation-fill-mode: forwards;
}

.inputs-wrapper {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 24px;
}

.gender-wrapper {
	display: flex;
	align-items: center;
	gap: 20px;
	margin-bottom: 24px;
}

.tax-type-wrapper {
	display: flex;
	flex-direction: column;
	grid-column: 1 / -1;
}

.tax-type-label {
	color: #444050;
	font-size: 1rem;
	font-weight: 400;
	margin-bottom: 16px;
	margin-top: 16px;
}

.gender-wrapper input,
.gender-wrapper label,
.tax-type-wrapper input,
.tax-type-wrapper label {
	margin-bottom: 0;
}

.tax-type-item-wrapper {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 12px;
}

.tax-type-item-wrapper:last-of-type {
	margin-bottom: 0;
}

.gender-wrapper input[type="radio"],
.tax-type-wrapper input[type="radio"] {
	display: none;
}

.gender-wrapper label,
.tax-type-wrapper label {
	display: inline-flex;
	align-items: center;
	cursor: pointer;
	position: relative;
	padding-left: 28px;
	font-size: 16px;
	user-select: none;
}

/* Custom circle */
.gender-wrapper label::before,
.tax-type-wrapper label::before {
	content: "";
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 18px;
	height: 18px;
	border: 2px solid #ACAAB1;
	border-radius: 50%;
	background-color: transparent;
	box-sizing: border-box;
	transition: border-color 0.2s ease;
}

/* Inner dot when selected */
.gender-wrapper input[type="radio"]:checked + label::before,
.tax-type-wrapper input[type="radio"]:checked + label::before {
	border: 5px solid #252525;
}

.input-persons-placeholder.input-persons-placeholder--main-search {
	left: 15px;
	top: 40px;
	cursor: pointer;
	transform: none;
}

.input-persons-placeholder--disabled {
	display: none;
}

.input.required {
	color: red;
}

input.highlighted {
	border: 1px solid #e05656;
	background: #f6ebeb;
}

input[type="number"].input-number-small {
	padding-left: 10px;
	padding-right: 0;
}

/* Input Placeholder Color */
::-webkit-input-placeholder {
	color: #999;
	opacity: 1;
	font-weight: 400;
}

.placeholder-light::-webkit-input-placeholder {
	color: #999;
	opacity: 1;
}

.main-search-input-item__placeholder input::-webkit-input-placeholder {
	color: #999;
	opacity: 1;
	font-weight: 400;
	font-size: 0.938rem;
}

:-moz-placeholder {
	color: #999;
	opacity: 1;
	font-weight: 400;
}

.placeholder-light:-moz-placeholder {
	color: #999;
	opacity: 1;
}

::-moz-placeholder {
	color: #999;
	opacity: 1;
	font-weight: 400;
}

.placeholder-light::-moz-placeholder {
	color: #999;
	opacity: 1;
}

:-ms-input-placeholder {
	color: #999;
	opacity: 1;
	font-weight: 400;
}

.placeholder-light:-ms-input-placeholder {
	color: #999;
	opacity: 1;
}

textarea {
	height: auto;
	line-height: 1.4;
	padding: 20px;
	min-height: 130px;
	transition: none !important;
	min-width: 100%;
}

label,
legend {
	display: block;
	font-size: .875rem;
	font-weight: 500;
	margin-bottom: 6px;
}

label[for=cursor-pointer] {
	margin-bottom: 0;
}

label span,
legend span {
	font-weight: normal;
	font-size: 0.938rem;
	color: #444;
}

fieldset {
	padding: 0;
	border: none;
}

.form-input-inline {
	display: inline-block;
}

.form-input-flex {
	display: flex;
	align-items: center;
}


.payment-two-installments-container > div,
.full-payment-container > div {
	border-radius: 8px;
	border: 1px solid #D0D5DD;
	background: #FCFCFD;
}

.checks {
	padding: 0 0 0 15px;
	font-size: .75rem;
	margin: 0 0 17px;
}

.checks li {
	display: block;
	font-weight: 400;
	text-indent: -32px;
	padding: 0 0 0 27px;
}

.checks.horizontal li {
	display: inline-block;
	margin-left: 10px;
}

.checks li strong {
	font-weight: 500;
}

.checks.horizontal li:first-child {
	margin-left: 0;
}

a, button {
	outline: none !important;
}

a:focus,
a:hover {
	text-decoration: none;
	color: #333;
}

a.underline {
	text-decoration: underline;
}

img {
	max-width: 100%;
}

/* Header
------------------------------------- */
.fullscreen-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	background: rgba(0,0,0,.35);
	z-index: 201;
	display: none;
}

.mobile-menu__overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0,0,0,.3);
	z-index: 301;
	display: none;
}

html.noscroll,
body.noscroll {
	overflow: hidden !important;
	width: auto;
	height: 100%;
}

.header-container.header-container--home {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
}

.header.cloned {
	box-shadow: 0 2px 7px rgba(0, 0, 0, .13);
}

#header {
	background-color: #fff;
	position: relative;
	z-index: 999;
	display: flex;
	align-items: center;
	max-height: 100px;
	height: 100px;
	padding: 20px 0;
}

#header .left-side.navigation {
	margin-left: 170px;
	padding: 0;
}

.header__body {
	display: flex;
	align-items: center;
}

.header__logo-anchor {
	display: flex;
}

.header__logo-anchor--bg {
	background-image: url('/assets/img/logo-black.svg');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 170px;
	height: 35px;
}

.header__right-side,
.header-navigation,
.header-navigation__actions,
.header-navigation__list-container,
.header-navigation__container {
	display: flex;
	align-items: center;
	margin: 0;
	padding: 0;
	flex: 1 1 100%;
}

.header-navigation__actions {
	flex: 0 0 auto;
	justify-content: flex-end;
	position: relative;
}

.left-side {
	float: left;
	display: inline-block;
}

.right-side {
	float: right;
	display: inline-block;
}

.float-right {
	float: right;
}

.float-left {
	float: left;
}

.header-contact-container {
	background: #f4f4f4;
	padding: 8px 0;
	position: relative;
	z-index: 202;
}

.header-contact {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.header-contact__phone {
	color: #252525;
	font-size: .875rem;
	background: #fff;
	border-radius: 4px;
	padding: 4px 9px;
	display: flex;
	align-items: center;
	margin-left: 12px;
}

.header-contact__phone:first-of-type {
	margin-left: 0;
}

.header-contact__phone-icon--rounded {
	border-radius: 50%;
}

.header-contact__phone-title {
	display: flex;
	margin: 0 6px;
}

.header-contact__phone-title--mobile {
	display: none;
}

/* Sticky Header
------------------------------------- */
#header.cloned {
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 1000;
}

#header.cloned .left-side,
#header.cloned .header-navigation__item {
	padding: 12px 0;
	height: 70px;
}

#header.cloned .header-navigation__item--language-switcher,
#header.cloned .header-navigation__item--need-help {
	padding: 16px 0;
}

#header.cloned .left-side.navigation {
	padding: 0;
}

.sticky-logo {
	display: none;
}

#header.cloned.unsticky {
	opacity: 1;
	visibility: hidden;
	pointer-events: none;
	transform: translate(0,-100%) scale(1);
	transition: .3s;
}

#header.cloned.sticky {
	opacity: 1;
	visibility: visible;
	transform: translate(0,0) scale(1);
	transition: .3s;
	max-height: 70px;
	height: 70px;
}

#header.cloned.sticky .header-navigation__submenu {
	top: 100%;
}

#header.cloned.sticky .header-navigation__language {
	top: 90%;
}

#header.cloned.sticky .header-navigation__item-anchor {
	padding: 28px 20px 26px 20px;
}

/* Footer
------------------------------------- */
#footer {
	color: #333;
	background: #fff;
	padding: 50px 0 40px 0;
	border-bottom: 1px solid #eee;
}

.footer-logo {
	filter: invert(1);
}

#footer .left-block {
	margin-bottom: 15px;
}

#footer .left-block.need-help p {
	font-size: 0.938rem;
	line-height: 1.2;
}

#footer .left-block.need-help a {
	color: #333;
}

#footer .left-block h4 {
	font-family: var(--font-primary);
	margin: 0 0 6px;
}

.footer-links-container {
	width: 250px;
	float: left;
}

.footer-links {
	margin: 0;
	padding: 0;
}

#footer .menu {
	padding-left: 0;
}

.footer-links-title {
	font-size: .875rem;
	margin-bottom: 20px;
	font-weight: 500;
	letter-spacing: 1px;
	margin-top: 0;
	text-transform: uppercase;
	font-family: var(--font-primary);
}

.footer-links-subtitle {
	margin-bottom: 0;
	font-weight: 500;
}

.footer-links-anchor {
	color: #333;
	text-decoration: underline;
}

.footer-link {
	position: relative;
	list-style: none;
	margin-bottom: 4px;
	width: 90%;
}

.footer-link__anchor {
	display: inline-block;
	color: #333;
	line-height: 1.6;
	vertical-align: top;
	font-size: .875rem;
}

.footer-link__anchor:hover {
	text-decoration: underline;
}

.footer-contact__phone {
	background: #efefef;
	padding: 4px 9px;
	font-size: .875rem;
	border-radius: 6px;
	margin-top: 5px;
	display: inline-flex;
	align-items: center;
	color: #252525;
}

.footer-contact__phone:hover {
	background: #f5f5f5;
}

.footer-contact__phone-title {
	display: flex;
	margin: 0 6px;
}

.copyrights {
	color: #252525;
	border-top: 1px solid rgba(0,0,0,.09);
	margin-top: 30px;
	margin-bottom: 20px;
	padding: 20px 0 0;
	display: inline-block;
	width: 100%;
	font-size: .875rem;
}

.copyright {
	display: flex;
	flex-basis: 100%;
}

.social-footer a {
	display: inline-block;
	margin: 0 2px;
}

.social-footer a:before {
	font-size: 1.728rem;
	color: #fff;
	transition: color .2s;
}

.social-footer a:hover:before {
	color: #d8c66d;
}

#footer .member {
	display: inline-block;
	float: right;
	color: #333;
	font-size: 13px;
}

#footer .member > a {
	display: inline-block;
	vertical-align: top;
	margin-left: 6px;
}

.bottom {
	background: #fff;
}

.footer-area {
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
}

.footer-payments {
	display: flex;
	flex-direction: column;
	padding: 25px 0;
	justify-content: flex-start;
}

.footer-payments__title {
	margin-bottom: 0;
	color: #333;
	font-size: .875rem;
}

.footer-payments__list {
	display: flex;
	flex-direction: row;
	list-style: none;
	margin: 0;
	padding-left: 0;
	align-items: center;
	margin-top: 5px;
}

.footer-payments .payment-methods-1 li:not(:first-child) {
	margin-left: 8px;
}

.brands-footer {
	display: flex;
	justify-content: flex-end;
	align-items: flex-start;
	padding: 25px 0;
	flex-wrap: wrap;
	flex: 0 0 460px;
}

.brands-footer__content {
	margin-bottom: 0;
	display: flex;
	justify-content: flex-end;
	flex-basis: 100%;
}

.brands-footer__text-logo {
	margin: 0 8px;
}

.brands-footer__anchor {
	margin: -30px 0 0 15px;
}

.brands-footer__anchor--inline {
	margin: 0 8px;
}

.brands-footer__logo--villas-guide:not(.brands-footer__logo--default-color) {
	filter: invert(100%);
}

/* Back to top
------------------------------------- */
#backtotop {
	position: fixed;
	right: 0;
	opacity: 0;
	visibility: hidden;
	bottom: 25px;
	margin: 0 25px 0 0;
	z-index: 999;
	transition: .35s;
	transform: scale(.7);
}

#backtotop.visible {
	opacity: 1;
	visibility: visible;
	transform: scale(1);
}

#backtotop a {
	color: #fff;
	text-decoration: none;
	border: 0 none;
	display: block;
	width: 46px;
	height: 46px;
	background-color: #666;
	opacity: 1;
	transition: all .3s;
	border-radius: 50%;
	text-align: center;
	font-size: 26px
}

#backtotop a:after {
	content: '';
	filter: invert(100%) ;
	display: inline-block;
	width: 19px;
	height: 19px;
	background-image: url('/assets/img/svg/left-arrow.svg');
	background-size: 19px 19px;
	background-repeat: no-repeat;
	top: 32%;
	transform: translateY(-55%) rotate(90deg);
	position: relative;
}

/* Navigation
------------------------------------- */
.header-navigation__list-container {
	flex: 1 1 72%;
	justify-content: center;
	list-style: none;
}

.header-navigation__list {
	display: flex;
	margin-bottom: 0;
	padding-left: 0;
}

.header-navigation__item-anchor {
	display: block;
	text-decoration: none;
	padding: 45px 20px 38px 20px;
	color: #252525;
	cursor: pointer;
	font-size: .813rem;
	line-height: 20px;
	letter-spacing: 1px;
	text-transform: uppercase;
}

.header-navigation__item-anchor--need-help {
	padding: 3px 10px;
	display: flex;
	align-items: center;
}

.header-navigation__item-anchor--spo {
	display: flex;
	align-items: center;
	gap: 6px;
}

.header-navigation__spo-icon path {
	fill: #D22E2E;
}

.header-navigation__language {
	left: auto;
	right: 0;
	width: auto;
	opacity: 0;
	visibility: hidden;
	position: absolute;
}

.listing-item__spo-last {
	background-image: url('/assets/img/home-hero-cropped.jpg');
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 424px;
	border-radius: 3px;
	overflow: hidden;
	padding: 20px;
}

.listing-item__spo-label {
	font-size: 1.89rem;
	font-weight: 400;
	line-height: normal;
	color: #010024;
	text-transform: capitalize;
}

.listing-item__spo-btn {
	text-align: center;
}

.listing-item__spo-btn--mobile-wrapper {
	display: flex;
	justify-content: center;
	margin-top: 24px;
}

.header-navigation__submenu:before {
	content: '';
	position: absolute;
	left: 0;
	top: -10px;
	background: rgba(255,255,255,0);
	width: 100%;
	height: 10px;
}

#navigation ul li.submenu-opened a:after {
	transform: rotate(-135deg);
}


/* Dropdown Styles
------------------------------------- */
.header-navigation__submenu {
	background-color: #fff;
	box-shadow: 0 6px 8px 0 rgba(0,0,0,.12);
	padding: 20px 30px;
	box-sizing: border-box;
	opacity: 0;
	visibility: hidden;
	border-top: 1px solid #ddd;
	position: absolute;
	top: 100%;
	z-index: 300;
	margin: 0;
	transition: all 150ms ease;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
}

.header-navigation__item--has-submenu .header-navigation__item-anchor:after {
	opacity: .9;
	position: relative;
	display: inline-block;
	border: solid;
	border-width: 0 1px 1px 0;
	content: '';
	margin-left: 10px;
	padding: 2px;
	top: -4px;
	transform: rotate(45deg);
}

.header-navigation__item--has-submenu:hover .header-navigation__submenu {
	transition-delay: 150ms;
	opacity: 1;
	visibility: visible;
	width: 100%;
	left: 0;
}

.header-navigation__submenu--destinations-wrapper {
	display: flex;
	flex-direction: row;
}

.header-navigation__submenu-anchors--destinations {
	width: 100%
}

.header-navigation__sub-items-title {
	font-size: 1.125rem;
	font-weight: 500;
	color: #252525;
	margin-bottom: 16px;
}

.menu-separator {
	width: 8px;
	background: #efefef;
	margin: 0 60px;
}

.position-static {
	position: static;
}

.header-navigation__submenu-anchor--destinations {
	display: flex;
	flex-direction: row;
}

.header-navigation__submenu--interests {
	min-width: 700px;
}

/* Menu
------------------------------------- */
.header-navigation .current {
	background-color: transparent;
	color: #666;
}

.header-navigation .current:after {
	opacity: 1;
}

.header-navigation__item {
	margin: 0;
	display: flex;
	height: 100px;
	align-items: center;
	border-bottom: 4px solid transparent;
	transition: border 150ms ease;
	font-weight: 500;
}

.header-navigation__item--need-help {
	padding: 22px 0;
	margin-left: 25px;
}

.header-navigation__item--has-submenu:hover {
	transition-delay: 150ms;
	border-bottom: 4px solid #DECA84;
}

.header-navigation__item:hover .header-navigation__item-anchor--need-help {
	color: #333;
}

.header-navigation__sub-items-top {
	display: none;
}

.header-navigation__item-anchor--language {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 4px 10px!important;
	margin: 45px 10px 38px 10px!important;
	border-radius: 6px;
	border: 1px solid#D0D5DD;
	background:#FFF;
	box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
}

.header-navigation__language {
	padding: 0;
	top: calc(100% - 20px);
	background: #fff;
	border: 1px solid #D0D5DD;
	border-radius: 6px;
	right: 10px;
}

.header-navigation__language--opened {
	opacity: 1;
	visibility: visible;
}

.select-language-label {
	font-size: .875rem;
	color: #101828;
	border-bottom: 1px solid #D9D9D9;
	padding: 6px 16px;
	display: block;
	text-transform: lowercase;
	font-weight: 400;
}

.select-language-label::first-letter {
	text-transform: uppercase;
}

.header-navigation__language-item {
	color: #252525;
	display: flex;
	align-items: center;
	text-transform: capitalize;
	padding: 6px 16px;
	font-size: .875rem;
	font-weight: 400;
}

.header-navigation__language-item:hover {
	background: #fafafa;
	border-radius: 6px;
}

.header-navigation__language-item:after {
	content: none;
}

.header-navigation__language-flag {
	position: relative;
	margin-right: 8px;
}

.header-navigation__submenu-item--anchors {
    display: flex;
	flex: 1;
	flex-direction: column;
}

.header-navigation__countries-wrapper {
	display: flex;
	flex-direction: column;
	flex: 1;
}

.header-navigation__icon--phone {
	margin-right: 6px;
}

.header-navigation__submenu-anchors {
	display: flex;
	list-style: none;
	flex-direction: row;
	padding-left: 0;
	flex-wrap: wrap;
}

.header-navigation-interests__anchor {
	color: #333;
	font-size: 0.938rem;
	font-weight: 500;
}


.header-navigation__submenu-anchors--interests {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	grid-template-rows: 1fr;
	gap: 20px;
	row-gap: 10px;
}

.header-navigation__submenu--interests .header-navigation__submenu-anchors {
	column-count: 3;
	display: block;
}

.you-save--wrapper {
	display: block;
	font-size: .875rem;
	color: #357B27;
	font-weight: 500;
}

.header-navigation__submenu-anchor {
	display: flex;
	flex-direction: row;
	margin-right: 25px;
	gap: 24px;
}

.header-navigation__submenu-anchor--interests {
	display: flex;
	flex-direction: column;
	gap: 0;
	margin-right: 0;
}

.header-navigation__sub-items-container {
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
}

.header-navigation__submenu--interests .header-navigation__submenu-anchor {
	break-inside: avoid-column;
	margin-right: 0;
	margin-bottom: 10px;
}

.header-navigation__submenu--interests .header-navigation__submenu-anchor a {
	color: #333;
	display: flex;
	flex-direction: column;
	font-weight: 600;
	font-size: 0.938rem;
	line-height: 1.4;
}

.header-navigation__submenu--interests .header-navigation__submenu-anchor a:hover {
	text-decoration: underline;
}

.header-navigation__submenu--interests .header-navigation__submenu-anchor-subtitle {
	display: block;
	color: #666;
	font-size: .875rem;
	font-weight: normal;
}

.header-navigation__submenu-anchor-subtitle {
	display: block;
	color: #666;
	font-size: .813rem;
	font-weight: normal;
}

.header-navigation__submenu--interests .header-navigation__submenu-anchor a:hover .header-navigation__submenu-anchor-subtitle {
	text-decoration: none;
}

.header-navigtaion__submenu-anchor-line {
	height: 1px;
	width: 130px;
	background: #ddd;
	display: inline-block;
	margin-top: 8px;
}

.header-navigation__submenu-anchor:last-of-type {
	margin-right: 0;
}

.header-navigation__submenu-main-anchor {
	display: flex;
	flex-direction: column;
	font-size: 1.2rem;
	color: #252525;
	font-weight: 600;
	margin-bottom: 0;
	position: relative;
}

.header-navigation__submenu-country-image {
	border-radius: 8px;
	object-fit: cover;
	height: 266px;
}

.header-navigation__submenu-main-anchor--destinations {
	height: 266px;
	width: 227px;
	min-width: 227px;
}

.header-navigation__submenu-name {
	position: absolute;
	bottom: 5px;
	left: 15px;
	color: #fff;
	font-size: 1.375rem;
	font-weight: 500;
}

.header-navigation__submenu-main-anchor:hover {
	text-decoration: underline;
}

.header-navigation__submenu-anchor-image {
	width: 75px;
	height: 75px;
	margin-bottom: 5px;
}

.header-navigation__submenu-image-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100%;
	width: 100%;
	background: linear-gradient(10deg, rgba(35, 35, 37, .9) 0%, rgba(35, 35, 37, .4) 20%, rgba(22, 22, 23, 0) 40%, rgba(0, 0, 0, 0) 60%);
	border-bottom-left-radius: 8px;
	border-bottom-right-radius: 8px;
}

.header-navigation__submenu-subtitle {
	font-size: .938rem;
	color: #333;
	display: block;
	margin-bottom: 8px;
}

.header-navigation__submenu-sub-container {
	display: flex;
	flex-wrap: wrap;
}

.destination-separator {
	width: 80%;
	height: 1px;
	background: #ddd;
	margin: 20px 0;
}

.header-navigation__mobile-close {
	display: none;
}

.header-navigation__submenu-sub-anchor {
	color: #252525;
	font-size: .75rem;
	line-height: 1.4;
	margin: 5px 10px 5px 0;
	background: #efefef;
	padding: 4px 10px;
	border-radius: 6px;
}

.header-navigation__submenu-sub-anchor:hover {
	text-decoration: underline;
}

.header-navigation__mobile-top {
	display: none;
}

.header-navigation__item--hidden-desktop {
	display: none;
}

.mmenu-trigger {
	display: none;
}

.header__mobile-elements {
	display: none;
}

.loading-box-map {
	position: absolute;
	top: 20px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 990;
	background-color: #fff;
	border-radius: 10px;
	padding: 6px 12px 3px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 4px;
	box-shadow: 0 6px 7px -4px #000000ba;
}

.loading-dot-listing-map {
	display: inline-block;
	width: 8px;
	height: 8px;
	background-color: #101828;
	border-radius: 50%;
	animation: wave 1s infinite ease-in-out;
}

.loading-dot-listing-map:nth-child(1) {
	animation-delay: 0s;
}

.loading-dot-listing-map:nth-child(2) {
	animation-delay: .2s;
}

.loading-dot-listing-map:nth-child(3) {
	animation-delay: .4s;
}

@keyframes wave {
0%, 100% {
	transform: translateY(0);
	opacity: 1;
}
50% {
	transform: translateY(-5px);
	opacity: 0.6;
}
}

.skeleton-element--loading {
	color: transparent!important;
	position: relative;
}

.skeleton-element--loading::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border-radius: 4px;
	background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
	background-size: 200% 100%;
	animation: shine 1.5s linear infinite;
	color: #fff;
	z-index: 100;
}

.skeleton-element--loading svg,
.skeleton-element--loading img,
.skeleton-element--loading i {
	visibility: hidden!important;
}

@keyframes shine {
	to {
		background-position-x: -200%;
	}
}

.skeleton-element--shorter.skeleton-element--loading::after {
	width: 80%;
}

.listing-item-location.skeleton-element--loading::after {
	margin-top: 4px;
}

/* Hamburger Icon */
.hamburger {
	cursor: pointer;
	text-transform: none;
	border: 0;
	background: transparent;
	margin-top: 0;
	height: 17px;
	width: 30px;
	position: relative;
}

.hamburger-inner {
	display: block;
	top: 50%;
	margin-top: -2px;
	text-indent: -9999px;
}

.hamburger-inner,
.hamburger-inner:before,
.hamburger-inner:after {
	width: 30px;
	height: 3px;
	background-color: #252525;
	position: absolute;
}

.hamburger-inner:before,
.hamburger-inner:after {
	content: '';
	display: block;
}

.hamburger-inner:before {
	top: -7px;
}

.hamburger--collapse .hamburger-inner {
	top: auto;
	right: 0;
	bottom: 0;
}

.hamburger--collapse .hamburger-inner:after {
	top: -14px;
}

.honeypot-input {
	display: none!important;
}

@media (max-width: 1440px) {
	.header-navigation__item-anchor {
		padding: 45px 10px 38px 10px;
	}
}

@media (max-width: 1024px) {
	.header__mobile-elements {
		display: flex;
		position: absolute;
		right: 20px;
	}

	.overflow-x-hidden {
		overflow-x: hidden;
	}

	.no-padding-on-mobile {
		padding: 0;
	}

	.destination-separator {
		width: 100%;
	}

	.header-navigation__submenu--destinations-wrapper {
		flex-direction: column;
	}

	.menu-separator {
		display: none;
	}

	.header-navigation__item--has-submenu:hover .header-navigation__submenu {
		width: 100%;
		left: 100%;
	}

	.loading-box-map {
		top: 50px;
	}

	.map-icon-show-map {
		margin-right: 4px;
	}

	.header-navigation__spo-icon {
		margin-top: 3px;
	}

	.header__mobile-element {
		font-size: .75rem;
		line-height: 1.4;
		color: #252525;
		text-transform: uppercase;
		text-decoration: underline;
	}

	.header-navigation__list {
		display: block;
	}

	.header-navigation__mobile-close {
		display: flex;
		position: absolute;
		right: 10px;
		top: 12px;
		padding: 10px;
		z-index: 303;
		background: #fff;
	}

	.header-navigation__mobile-close-icon {
		width: 18px;
		height: 18px;
		display: block;
	}

	.header-navigation__container {
		height: 100%;
		margin-left: -100%;
		overflow-y: auto;
		transition: all .2s ease-in-out;
		visibility: hidden;
		width: 100%;
		flex-direction: column;
		align-items: unset;
	}

	.header-navigation__container--visible {
		left: 0;
		margin-left: 0;
		visibility: visible;
	}

	.header-navigation__list-container,
	.header-navigation__actions,
	.header-navigation__container,
	.header__right-side,
	.header-navigation {
		display: block;
		flex: initial;
	}

	.header-navigation__actions {
		border-top: 1px solid #e2e2e2;
	}

	.mmenu-trigger {
		display: flex;
		cursor: pointer;
		position: absolute;
		left: 15px;
	}

	.header__left-side {
		border-right: none;
		padding-right: 0;
		margin-right: 0;
		display: flex;
		width: 100%;
		justify-content: center;
	}

	.header__right-side {
		display: block;
		position: fixed;
		flex: none;
		left: 0;
		z-index: 302;
	}

	.listing-items-carousel > .keen-prev,
	.listing-items-carousel > .keen-next {
		display: none;
	}

	.header-navigation {
		position: fixed;
		transform: translate(-100%, 0);
		transition: all .35s ease;
		z-index: 303;
		height: 100vh;
		margin-top: 0;
		top: 0;
		background: #fff;
		left: -100%;
	}

	.header-navigation--visible {
		transform: translate(0, 0);
		height: 100%;
		overflow-y: auto;
		overflow-x: hidden;
		left: 0;
		width: 85vw;
	}

	.header-navigation__item {
		display: block;
		float: none;
		width: 100%;
		padding: 0;
		height: auto;
		border-bottom: none;
	}

	.header-navigation__submenu {
		bottom: 0;
		height: 100%;
		left: 100%;
		overflow-y: auto;
		position: absolute;
		right: 0;
		top: 0;
		transition: all .2s ease-in-out;
		visibility: hidden;
		width: 100%;
		display: block;
		padding: 0;
		opacity: 1 !important;
		box-shadow: none;
	}

	.header-navigation__submenu {
		border-top: 0;
	}

	.header-navigation__item--has-submenu:hover .header-navigation__submenu {
		transition-delay: 0ms;
	}

	.header-navigation__submenu--mobile-visible {
		left: 0!important;
		visibility: visible!important;
	}

	.header-navigtaion__submenu-anchor-line {
		display: none;
	}

	.header-navigation__submenu--interests .header-navigation__submenu-anchor {
		margin-bottom: 15px;
	}

	.header-navigation__item--has-submenu .header-navigation__item-anchor:after {
		position: absolute;
		right: 20px;
		top: calc(50% - 5px);
		opacity: 1;
		transform: rotate(-45deg);
		border-width: 0 1px 1px 0;
		padding: 4px;
		content: '';
	}

	.header-navigation__item-anchor {
		padding: 15px;
		font-size: .875rem;
		font-weight: 400;
		position: relative;
		background-color: transparent;
		text-transform: initial;
		display: flex;
		align-items: center;
	}

	.header-navigation__item:hover .header-navigation__item-anchor {
		color: #252525;
	}

	.header-navigation__item--has-submenu:hover {
		border-bottom: none;
	}

	.header-navigation__item--hidden-desktop {
		display: block;
	}

	.header-navigation__item--need-help {
		margin-left: 0;
	}

	.header-navigation__item--static a {
		padding: 15px;
		background: #fff;
		font-size: 14px;
        border-radius: 0;
		display: flex;
		align-items: center;
	}

	.header-navigation__icon--phone {
		filter: invert(0%);
	}

	.header-navigation__icon--email,
	.header-navigation__icon--home {
		margin-right: 6px;
	}

	.header-navigation__item--static a:before {
		content: "→";
		font-size: 19px;
		margin-right: 8px;
	}

	.header-navigation__item.call-us a:hover,
	.header-navigation__item.email-us a:hover {
		background: #fff;
	}

	.header-navigation__item--language-switcher {
		margin-left: 0;
		display: flex;
		background: #fff;
		border-top: 1px solid #e2e2e2;
		flex-direction: column;
		align-items: flex-start;
		position: relative;
	}

	.header-navigation__item-anchor--language {
		background: #fff;
		font-size: .75rem;
		padding-right: 0;
		padding-top: 20px;
		padding-bottom: 12px;
		text-transform: uppercase;
		margin: 15px!important;
	}

	.header-navigation__language {
		box-shadow: none;
		top: 100%;
		left: 15px;
		right: unset;
	}

	.header-navigation__submenu-anchors {
		flex-direction: column;
		width: 100%;
		float: none;
		padding: 15px;
	}

	.header-navigation__submenu-anchor {
		margin-right: 0;
		margin-bottom: 10px;
		padding-bottom: 10px;
	}

	.header-navigation__submenu-subtitle {
		margin-bottom: 10px;
		display: flex;
	}

	.header-navigation__submenu-anchor-image {
		display: none;
	}

	.header-navigation__submenu-item--anchors {
		flex: auto;
		display: block;
	}

	.header-navigation__submenu--interests .header-navigation__submenu-anchors {
		column-count: 1;
	}

	.header-navigation__mobile-top,
	.header-navigation__sub-items-top {
		display: flex;
		border-bottom: 1px solid #e7e7e7;
		padding: 18px 0;
	}

	.header-navigation__mobile-top-title,
	.header-navigation__sub-items-top-anchor {
		font-size: .875rem;
		font-weight: 500;
		line-height: 1.4;
		padding: 0 15px;
		margin: 0;
		display: flex;
		align-items: center;
	}

	.header-navigation__sub-items-top-anchor:before {
		display: flex;
		position: relative;
		border: solid;
		border-width: 0 1px 1px 0;
		content: '';
		padding: 3px;
		transform: rotate(135deg);
		margin-right: 10px;
		width: 8px;
		height: 8px;
	}

	.header-navigation__sub-items-title {
		display: flex;
		padding: 15px;
		font-weight: 600;
		text-align: center;
		border-bottom: 1px solid #e2e2e2;
	}

	.header-navigation__submenu--interests,
	.header-navigation__submenu--destinations {
		min-width: 0;
		margin-left: 0;
	}

	.header-navigation__submenu--interests .header-navigation__submenu-anchor a {
		font-size: 0.938rem;
	}

	.header-navigation__submenu--interests .header-navigation__submenu-anchor-subtitle {
		font-size: 1rem;
	}

	#header {
		height: 65px;
		max-height: 65px;
		padding: 8px 0;
		display: block;
	}

	.block-main-usps .col-md-3:last-of-type .usp-item {
		margin-bottom: 0;
	}
}

.special-offer-label {
	background: #D22E2E;
	color: #fff;
	font-size: .75rem;
	border-radius: 6px;
	font-weight: 500;
	z-index: 2;
	border: 1px solid #fff;
	text-transform: uppercase;
	display: flex;
	box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .2);
	line-height: 1.3;
}

.special-offer-label--right-position {
	position: absolute;
	top: 10px;
	right: 10px;
}

.special-offer-label--left-position {
	position: absolute;
	top: 10px;
	left: 10px;
}

.special-offer-left {
	display: flex;
	gap: 6px;
	padding: 4px 8px;
}

.special-offer--left__icon {
	margin-top: 3px;
}

.additional-guest-price-wrapper {
	border-radius: 4px;
	background: #F8F8F8;
	padding: 10px 15px;
	margin-top: 16px;
	width: 100%;
	margin-right: auto;
	color: #000;
	font-size: 1rem;
}

.additional-guest-price-description {
	margin-bottom: 0;
}

.special-offer-right {
	display: flex;
	flex-direction: column;
	align-items: center;
	background: white;
	color: #D22E2E;
	padding: 4px 8px;
	font-size: .65rem;
	font-weight: 600;
	border-radius: 0 3px 3px 0;
}

.special-offer--discount {
	font-size: 1rem;
	font-weight: 600;
}

.special-offer--discount-with-range {
	margin-top: .5rem;
}

.special-offer-left__label {
	display: flex;
	flex-direction: column;
}

.evisitor-label {
	color: #444050;
	font-size: 1rem;
	font-weight: 400;
	margin-bottom: 8px;
}

.label-required-asterix {
	color: #E80000;
}

.evisitor-complete-check-icon {
	filter: brightness(0) saturate(100%) invert(57%) sepia(8%) saturate(3162%) hue-rotate(67deg) brightness(101%) contrast(80%);
	margin-top: -1px;
}

.date-error-msg,
.city-error-msg {
	font-size: .925rem;
	color: #e52d2d;
}

.evisitor-complete-info {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 0;
	color: #52AA48;
	font-weight: 600;
	font-size: .95rem;
}

.evisitor-modal-close-btn {
	display: none;
}

.evisitor-guest-list-item-title-number--mobile {
	display: none;
}

.evisitor-spinner-wrapper {
	position: absolute;
	left: 0;
	top: 128px;
	width: 100vw;
	height: 100vh;
	background-color: #fff;
	z-index: 1;
}

.evisitor-spinner {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, calc(-50% - 100px));
}

.form-control-visitor option {
	color: #101828;
}

.evisitor-navbar {
	width: 100%;
	background: #fff;
	box-shadow: 0 4px 8px 0 rgba(47, 43, 61, 0.12);
}

.evisitor-navbar-inner {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	padding: 16px 0;
}

.evisitor-navbar-headline {
	font-size: 1.25rem;
	color: #444050;
	font-weight: 600;
	margin: 0;
}


/* Main Search Container
------------------------------------- */
.main-search-inner__main-heading {
	font-size: 3.2rem;
	line-height: 1.2;
	color: #fff;
	font-weight: 600;
	width: 700px;
	margin-top: 5px
}

.main-search-inner__paragraph {
	font-family: var(--font-primary);
	font-size: .875rem;
	color: #fff;
	margin-top: 0;
	text-transform: uppercase;
	letter-spacing: 1px;
	margin-bottom: 5px;
	font-weight: 500;
}

.main-search-container .image-container {
	display: block;
	position: absolute;
	width: calc(100% - 30px);
	max-width: calc(100% - 30px);
	height: 680px;
	margin: 0 auto;
	left: 50%;
	transform: translateX(-50%);
	object-fit: cover;
}

.main-search-container.dark-overlay:before {
	content: none;
	background: rgba(0, 0, 0, .02);
}

.main-search-container.dark-overlay h2,
.main-search-container.dark-overlay h3,
.main-search-container.dark-overlay h4 {
	color: #252525;
}

.main-search-inner {
	display: block;
	margin-top: 100px;
	height: 680px;
}

.main-search-inner__text {
	padding-top: 180px;
}

.main-search-input__mobile {
	display: none;
}

.main-search-inner select {
	border: none;
	padding-top: 2px;
	padding-bottom: 0;
	height: 44px;
	box-shadow: none;
}


/* Main Search Input */
.main-search-input {
	margin-top: 35px;
	width: 100%;
	background-color: #fff;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	transition: background-color .15s linear;
	padding: 20px;
	border-radius: 3px;
}

.main-search-input--search-results {
	margin-top: 0;
	padding-top: 5px;
	padding-bottom: 5px;
}

.main-search-input__close-button {
	display: none;
}

.main-search-input input#location-search-input {
	width: 270px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

#search-results-form #location-search-input {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-right: 30px;
}

.main-search-input-item {
	flex: 1;
	position: relative;
	justify-content: stretch;
	display: flex;
	flex-flow: wrap;
}

.main-search-input-item:after {
	content:'';
	width: 1px;
	height: 55px;
	background-color: #999;
	position: absolute;
	right: 0;
	top: 10px;
}

.main-search-input-item--filters:after {
	content: none;
}

.main-search-input-item:hover .select2-container--focus .select2-selection--multiple {
	background-color: #fff;
}

.main-search-input-item__title {
	font-size: .938rem;
	font-weight: 500;
	line-height: 120%;
	letter-spacing: 1px;
	text-transform: uppercase;
	color: #252525;
	position: relative;
	left: 16px;
	z-index: 1;
	top: 8px;
	padding-bottom: 9px;
	display: inline-block;
}

.main-search-input-item__icon {
	position: absolute;
	left: 15px;
	top: 42px;
}

.main-search-input-item__icon:before {
	content: '';
	display: inline-block;
	width: 18px;
	height: 18px;
	background-size: 18px 18px;
	background-repeat: no-repeat;
	position: relative;
	top: 1px;
}

.main-search-input-item--persons {
	border-right: 0;
	margin: 0 0 0 20px;
}

.main-search-input-item--persons:after {
	display: none;
}

.main-search-input--search-results .main-search-input-item--persons {
	min-width: 180px;
}


.main-search-input-item--location {
	flex: 2.45;
	min-height: 70px;
	background: #fff;
	max-width: 380px;
}

.main-search-input-item__icon--location:before {
	background-image: url("/assets/img/svg/pin-2.svg");
}

.main-search-input-item--datepicker {
	margin-left: 20px;
	min-width: 380px;
}

.main-search-input--search-results .main-search-input-item--datepicker {
	min-width: 240px;
}

.main-search-input--search-results .main-search-input-item--location {
	min-width: 220px;
}

.main-search-input-item--location a {
	position: absolute;
	right: 10px;
	top: 50%;
	transform: translateY(-50%);
	color: #b0b0b0;
	padding: 10px;
}

.main-search-input-item--filters {
	flex: 0 0 120px;
	margin-left: 15px;
}

.main-search-input input,
.main-search-input input:focus {
	font-size: .938rem;
	border: none;
	background-color: transparent;
	margin: 0;
	height: 40px;
	line-height: 40px;
	box-shadow: none;
	padding: 0 0 0 15px;
}

.main-search-input select {
	background-color: transparent;
}

.search-block__input-error {
	background: #a30000;
	border-radius: 2px;
	color: #fff;
	position: absolute;
	top: calc(100% + 10px);
	font-size: .75rem;
	z-index: 100;
	padding: 4px 8px;
	left: 0;
}

.search-block__input-error:before {
	content: '';
	width: 0;
	height: 0;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-bottom: 8px solid #a30000;
	position: absolute;
	bottom: 98%;
	left: 20px;
}

.main-search-input .panel-dropdown-content button.button {
	font-weight: 600;
	height: 35px;
	font-size: 14px;
	width: 100%;
	margin-top: 5px;
}

.main-search-input button.button {
	font-size: 1rem;
	font-weight: 400;
	padding: 0 32px;
	margin-right: 0;
	height: 70px;
	outline: none;
	letter-spacing: 1px;
	text-transform: uppercase;
	justify-content: center;
}

button.button.main-search-input__submit--search-results {
	height: 57px;
}

.main-search-input-submit__icon {
	position: relative;
	padding: 0;
	top: 4px;
	margin-left: 6px;
}

.main-search-input-submit__icon:after {
	content: '';
	display: inline-block;
	width: 18px;
	height: 18px;
	background-size: 18px 18px;
	background-repeat: no-repeat;
	filter: invert(99%) sepia(100%) saturate(9%) hue-rotate(
			241deg) brightness(104%) contrast(100%) drop-shadow(1px 1px 0px rgba(0, 37, 60, .2));
	position: relative;
}

.main-search-input-submit__icon--magnifying-glass:after {
	background-image: url("/assets/img/svg/magnifying-glass.svg");
}

.input-with-item__icon,
.main-search-input-item--location a {
	padding: 5px 10px;
	z-index: 101;
}

#autocomplete-container,
#autocomplete-input {
	position: relative;
	z-index: 101;
}

@media (min-width: 992px) {
	.main-search-input-item,
	.main-search-input-item #autocomplete-input {
		background: #fff;
	}

	.lg-outer.floorplan-gallery .lg-img-wrap {
		padding-right: 400px !important;
	}

	.lg-outer.floorplan-gallery .floorplan-legend {
		height: 100%;
		overflow-y: auto;
		position: absolute;
		right: 0;
		top: 0;
		width: 420px;
		z-index: 99999;
	}

	.lg-outer.floorplan-gallery .lg-toolbar {
		right: 420px;
		width: auto;
	}

	.lg-outer.floorplan-gallery .lg-actions .lg-next {
		right: 420px;
	}

	.lg-outer.floorplan-gallery .lg-sub-html {
		padding: 0;
		position: static;
	}
}

/* Input With Icon */
.input-with-icon {
	position: relative;
}

.input-with-item__icon {
	position: absolute;
	left: 15px;
	top: 50%;
	transform: translateY(-50%);
	padding: 10px 0;
}

.input-with-item__icon:before {
	content: '';
	display: inline-block;
	width: 20px;
	height: 20px;
	background-size: 20px 20px;
	background-repeat: no-repeat;
	position: relative;
	top: 3px;
}

.input-with-item__icon--calendar:before {
	background-image: url('/assets/img/svg/calendar-alt.svg');
}

.input-with-item__icon--with-label {
	top: 58%;
}

.input-with-item__icon--users:before {
	background-image: url('/assets/img/svg/people.svg');
}

.input-with-item__icon--pin:before {
	background-image: url('/assets/img/svg/pin.svg');
}

.input-with-icon.right-align i {
	right: 18px;
	left: auto;
}

.input-with-icon input.booking-date,
.input-with-icon .input-icon-padding {
	padding-left: 45px !important
}

.input-with-icon.right-align > input {
	padding-left: 18px !important
}

.input-with-icon.location a i {
	position: absolute;
	right: 1px;
	left: auto;
	top: 50%;
	transform: translateY(-50%);
	color: #999;
	padding: 15px 20px 15px 15px;
	background: #fff;
	pointer-events: all
}

.input-with-icon.location a i:hover {
	color: #66676b;
}

.input-with-item__icon--down-arrow {
	position: absolute;
	right: 15px;
	left: auto;
}

.input-with-item__icon--down-arrow:before {
	width: 18px;
	height: 18px;
	background-size: 18px 18px;
	background-image: url('/assets/img/svg/down-arrow.svg');
}

/* Sort by custom select
------------------------------------- */
.sort-by {
	position: relative;
}

.sort-by--position-normal {
	top: 0;
}

.sort-by label,
.sort-by .sort-by-select {
	display: inline-block;
	float: right;
}

.sort-by .sort-by-select.sort-align-left {
	float: left;
}

.sort-by .sort-by-select--no-float {
	float: none;
}

.sort-by-select select {
	color: #252525;
	height: auto;
	padding: 7px;
	box-shadow: none;
	border: none;
	font-size: .875rem;
	line-height: 1.4;
	background: url('/assets/img/svg/up-and-down-arrow.svg') no-repeat 95% 0 #fff;
	-webkit-appearance: none;
	background-size: 31px 35px;
	margin-bottom: 0;
	padding-right: 35px;
}

.listing-container--search-results .sort-by-select select {
	border: 1px solid rgb(204, 204, 204);
	background-size: 18px 35px;
}

.sort-by-select--no-margin select {
	margin-bottom: 0;
}

.sort-by label {
	line-height: 40px;
	margin-top: 1px;
	padding: 0;
}

.filter-default--right-spacing {
	margin-right: 15px;
}

@media (min-width: 991px) {
	header.fixed #header .right-side {
		padding: 0;
	}
}

/* Input with dropdown
------------------------------------- */
.select-input {
	position: relative;
}

.select-input input {
	position: relative;
	z-index: 101;
}

.select-input .select {
	position: absolute;
	top: -8px;
}

.select-hidden {
	display: none;
	visibility: hidden;
}

.select {
	cursor: pointer;
	display: inline-block;
	position: relative;
	font-size: 15px;
	color: #fff;
	width: 100%;
	height: 51px;
}

.select-list-container {
	padding: 0 4px;
	max-height: 300px;
	overflow: auto;
	display: inline-block;
	width: 100%
}

/* Panel Dropdown
------------------------------------- */
.panel-dropdown {
	position: relative;
	display: inline-block
}

.panel-dropdown__icon-triangle {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	right: 15px;
	display: inline-flex;
	width: 18px;
	height: 18px;
	pointer-events: none;
}

.panel-dropdown--static {
	position: static;
}

.panel-dropdown a {
	color: #252525;
	font-size: 0.938rem;
	padding: 3px 14px;
	transition: all .3s;
	display: inline-block;
	border-radius: 2px;
}

.panel-dropdown a:after {
	content: '';
	filter: invert(13%) sepia(41%) saturate(1640%) hue-rotate(199deg) brightness(98%) contrast(92%);
	display: inline-block;
	width: 11px;
	height: 11px;
	background-image: url("/assets/img/svg/left-arrow.svg");
	background-size: 11px 11px;
	background-repeat: no-repeat;
	padding-left: 0;
	transition: all .3s;
	margin-left: 6px;
	transform: rotate(-90deg);
}

.panel-dropdown.active a:after {
	transform: rotate(90deg);
}

.panel-dropdown .panel-dropdown-content,
.main-search-input-item .panel-dropdown-content {
	opacity: 0;
	visibility: hidden;
	position: absolute;
	top: 50px;
	left: 0;
	z-index: 110;
	background: #fff;
	border: 1px solid #ddd;
	box-shadow: 0 4px 10px 0 rgba(0,0,0,.1);
	padding: 20px 24px;
	overflow-y: auto;
	white-space: normal;
	width: 460px;
	border-radius: 4px;
}

.panel-dropdown .panel-dropdown-content {
	top: 60px;
}

.panel-dropdown .panel-dropdown-content--persons {
	width: 340px;
}

.panel-dropdown-content__subtitle {
	display: block;
	color: #666;
	font-size: .75rem;
	font-weight: 400;
}

.panel-dropdown .panel-dropdown-content.panel-dropdown-content--location {
	padding: 10px;
	max-height: 450px;
	font-size: .75rem;
}

.panel-dropdown .panel-dropdown-content.filters,
.panel-dropdown .panel-dropdown-content.filters.checkboxes label {
	font-size: 1rem;
}

.panel-dropdown .panel-dropdown-content.filters h3 {
	font-size: 1.25rem;
	font-weight: 400;
	margin-top: 10px;
	font-family: var(--font-primary);
	border-bottom: 1px solid #a6a6a6;
	padding-bottom: 15px;
	margin-bottom: 15px;
}

.panel-dropdown-content__title {
	font-size: 1.44rem;
	font-weight: 600;
	margin-top: 0;
	margin-bottom: 15px;
}

.panel-dropdown-content__close {
	display: none;
}

.main-search-input-item .panel-dropdown-content {
	max-width: 290px;
	padding: 20px;
	top: 77px;
}

.main-search-input-item .panel-dropdown-content--homepage {
	max-width: 350px;
	top: 86px;
}

.main-search-input-item--location .panel-dropdown-content {
	width: 330px;
}

.panel-dropdown.wide .panel-dropdown-content {
	width: 580px;
}

.search .panel-dropdown.wide .panel-dropdown-content {
	width: 1120px;
	left: -15px;
}

.panel-dropdown.active .panel-dropdown-content {
	opacity: 1;
	visibility: visible;
}

.panel-dropdown.active .panel-dropdown-content #results {
	list-style: none;
	padding-left: 0;
	margin-bottom: 0;
}
.panel-dropdown.active .panel-dropdown-content #results li {
	cursor: pointer;
	padding: 4px 10px;
	font-weight: 400;
	border: 1px solid transparent;
	border-radius: 2px;
}

.panel-dropdown.active .panel-dropdown-content #results li:hover {
	background: #f8f8f8;
	color: #252525;
	border: 1px solid #eee;
}

.panel-dropdown.active .panel-dropdown-content .no-results {
	display: none;
	padding: 4px 10px;
}

.panel-dropdown-content__filters-title {
	display: inline-block;
	margin-bottom: 4px;
	line-height: 1.4;
	font-size: .875rem;
}

.listings-container {
	opacity: 1;
	transition: all .3s;
}

.fs-inner-container.content.faded-out .listings-container {
	opacity: .2;
	background: #fff;
	pointer-events: none;
}

.fs-inner-container.content.faded-out{
	background-color: #fcfcfc;
}

.panel-dropdown .checkboxes label {
	margin-bottom: 10px;
	display: block;
}

.panel-dropdown-content.filters select {
	height: auto;
	padding: 5px 10px;
	line-height: 1;
}

.panel-dropdown .row {
	margin-left: -10px;
	margin-right: -10px;
}

button.panel-cancel,
button.panel-apply {
	padding: 6px 16px;
	display: block;
	outline: none;
	font-weight: 400;
	float: right;
	margin: 0;
	font-size: 0.938rem;
	background: #252525 none;
	color: #fff;
	transition: all .3s;
	border-radius: 4px;
	border: 1px solid #252525;
}

button.panel-apply {
	padding: 6px 25px;
	position: relative;
}

button.panel-clear {
	border: none;
	background: transparent;
	text-decoration: underline;
	padding: 8px 12px;
}

button.panel-apply:hover {
	opacity: .9;
}

button.panel-clear {
	float: right;
}

button.panel-cancel:hover {
	background-color: #e9e9e9;
}

.panel-buttons {
	width: 100%;
	margin-top: 10px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.panel-buttons > span {
	display: none;
}

/* Float Right */
.panel-dropdown.float-right {
	float: right;
}

.panel-dropdown.float-right .panel-dropdown-content {
	left: auto;
	right: 0;
}

/* Half Screen Map
------------------------------------- */
.fs-container {
	height: 100%;
	width: 100%;
}

.fs-container #map-container {
	position: relative;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	z-index: 990;
	margin-bottom: 0;
}

.fs-content {
	max-height: 100%;
	padding: 0;
}

.fs-container section.search {
	margin: 20px 40px 20px 40px;
	background-color: #fff;
	box-shadow: 1px 1px 20px 1px rgb(143 143 143 / 28%);
	border-radius: 3px;
}

.search__row {
	display: flex;
	align-items: stretch;
}

.search__column {
	flex: 1;
	margin-right: 10px;
}

.search__column input {

}

.search__column--top-spacing {
	margin-top: 15px;
}

.search__column--location {
	flex: 1 1 100%;
	max-width: 380px;
	display: flex;
}

.search__column--arrival-departure {
	flex: 0 0 270px;
}

.search__column--arrival-departure input.booking-date {
	margin: 0;
}

.search__column--persons {
	flex: 0 0 215px;
}

.search__column--submit {
	display: flex;
	margin-right: 0;
	flex: initial;
}

.fs-container section.search .mobile-filter-buttons,
.search--sticky .mobile-filter-buttons {
	display: none;
}

.search--sticky {
	position: sticky;
	top: 50px;
	left: 0;
	right: 0;
	z-index: 102;
}

.search-result-search-section--sticky {
	top: 0;
}

.fs-container .fs-listings {
	padding: 0 25px;
	margin: 0;
}

.fs-container .fs-listings--no-results {
	min-height: 30vh;
}

.no-results-listing {
	margin: 10px 0 40px 0;
}

.no-results-listing--loading:before {
	content: '';
	position: absolute;
	top: 0;
	left: 15px;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, .75);
	width: calc(100% - 30px);
	height: 100%;
	z-index: 101;
}

.no-results-listing--loading:after {
	content: '';
	background: url('/assets/img/svg/spinner.svg') no-repeat center center;
	width: 100px;
	height: 53px;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 101;
}

.no-results-listing__heading {
	margin-top: 15px;
	margin-bottom: 5px;
	font-size: 1.44rem;
	font-weight: 600;
}

.no-results-listing__content {
	margin-bottom: 0;
	font-size: 0.938rem;
	line-height: 1.2;
}

.no-results-listing__help {
	margin-top: 10px;
}

.no-results-listing__icon {
	margin-right: 4px;
}

.no-results-listing__help-button {
	margin-right: 5px;
	display: inline-flex;
	align-items: center;
}

.fs-switcher {
	padding: 0 25px;
	margin: 0;
}


/* Inner Containers */
.fs-inner-container {
	width: 50%;
	float: left;
	display: inline-block;
}

.fs-inner-container.content {
	width: 60%;
	z-index: 200;
	box-shadow: 0 0 12px 0 rgba(0,0,0, .12);
	position: relative;
	padding-top: 100px;
}

.wrapper--expanded .fs-inner-container.content {
	padding-top: 0;
}

.fs-inner-container {
	width: 40%;
	background-color: #fff;
	z-index: 10;
}

.login-anchor {
	border: 1px solid #D0D5DD;
	border-radius: 6px;
	background-color: #fff;
	padding: 2px 6px;
	color: #101828;
	transition: all .2s;
}

.login-anchor:hover {
	text-decoration: none;
	border-color: #101828;
}

.login-anchor--header {
	font-size: .875rem;
	margin-left: 12px;
}

.login-anchor--sidebar {
	width: fit-content;
	margin: 15px;
	padding: 6px;
	gap: 6px;
	display: none;
}

.header-container.fixed {
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 1000;
	background-color: #fff;
}

.fs-inner-container.map-fixed {
	height: 0;
	position: fixed;
	top: 0;
	padding-top: 100px;
}

.wrapper--expanded .fs-inner-container.map-fixed {
	padding-top: 0;
}

.fs-inner-container.map-not-fixed {
	position: absolute;
	height: auto;
}

.fs-inner-container.map-not-fixed #map-container {
	height: 100vh;
}

button.fs-map-btn.button {
	position: relative;
	float: right;
	top: 6px;
	padding: 10px 40px;
	margin: 0;
}

.button.filter-results {
	padding: 9px 20px;
	width: 100%;
	font-size: 0.938rem;
	height: 49px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
}

.showing-results {
	color: #333;
	margin: 0 0 15px 0;
	font-size: 1.25rem;
	font-family: var(--font-primary);
}

.showing-results--no-margin {
	margin: 0;
}

.showing-results__content {
	font-size: 1.125rem;
	color: #252525;
	font-weight: 400;
	line-height: 1.4;
	display: block;
}

.showing-results__content strong {
    font-weight: 600;
}

.listings-container.fs-listings {
	left: 0;
	width: 100%;
	position: relative;
}

.listings-container.fs-listings.grid-layout {
	left: 0;
	width: 100%;
	position: relative;
}

.listings-container.fs-listings.list-layout {
	padding: 0 40px;
}

.listings-container.fs-listings.list-layout .listing-content {
	flex: 4;
}

.listings-container.fs-listings.list-layout .listing-img-container {
	flex: 0 auto;
	max-width: 300px;
}

/* Grid layout below 1366px screens */
@media (max-width: 1365px) {
	.listings-container.fs-listings.grid-layout .listing-item {
		width: 100%;
		margin: 0 0 30px 0;
	}

	.listings-container.fs-listings.grid-layout {
		padding: 0 40px;
	}

	.listings-container.fs-listings.list-layout .listing-item {
		display: block;
	}

	.listings-container.fs-listings.list-layout .details.button.border {
		display: none;
	}

	.listings-container.fs-listings.list-layout .listing-img-container {
		max-width: 100%;
	}
}

/* Forms Grid */
.col-fs-12,
.col-fs-11,
.col-fs-10,
.col-fs-9,
.col-fs-8,
.col-fs-7,
.col-fs-6,
.col-fs-5,
.col-fs-4,
.col-fs-3,
.col-fs-2,
.col-fs-1   {
	float: left;
	padding: 0 10px;
}

.brands-footer__gaveia-group  {
	display: inline;
}

.col-fs-12  { width: 100% }
.col-fs-11  { width: 91.66666667% }
.col-fs-10  { width: 83.33333333% }
.col-fs-9   { width: 75% }
.col-fs-8   { width: 66.66666667% }
.col-fs-7   { width: 58.33333333% }
.col-fs-6   { width: 50% }
.col-fs-5   { width: 41.66666667% }
.col-fs-4   { width: 33.33333333% }
.col-fs-3   { width: 25% }
.col-fs-2   { width: 16.66666667% }
.col-fs-1   { width: 8.33333333% }

@media (max-width:1599px) {
	.col-fs-12 ,
	.col-fs-11,
	.col-fs-10,
	.col-fs-9,
	.col-fs-8,
	.col-fs-7,
	.col-fs-6   { width: 100% }

	.col-fs-5,
	.col-fs-4,
	.col-fs-3,
	.col-fs-2,
	.col-fs-1   { width: 50% }
}

@media (max-width: 992px) {
	.col-fs-12 ,
	.col-fs-11,
	.col-fs-10,
	.col-fs-9,
	.col-fs-8,
	.col-fs-7,
	.col-fs-6,
	.col-fs-5,
	.col-fs-4,
	.col-fs-3,
	.col-fs-3-2,
	.col-fs-2,
	.col-fs-1   { width: 100% }

	.overflow-hidden { overflow: hidden; }
}

.boxed-widget.summary {
	border-radius: 0 0 4px 4px;
}

.boxed-widget.summary h3 {
	padding: 0 0 25px;
}

.listing-item-container.compact.order-summary-widget .listing-item {
	cursor: default;
	height: 250px;
}

.listing-item-container.compact.order-summary-widget {
	margin-bottom: 0;
}

.listing-item-container.compact.order-summary-widget:hover {
	transform: none;
}


/* Input With icons */
.input-with-icon.medium-icons i {
	font-size: 21px;
	color: #a0a0a0;
	position: absolute;
	left: -3px;
	bottom: 0;
	top: auto;
}

.button.booking-confirmation-btn {
	padding: 12px 35px;
	font-size: 1rem;
}

/* Booking Confirmation Page */
.booking-confirmation-page {
	text-align: center;
	padding: 40px 0;
}

.booking-confirmation-page i {
	color: #23b35f;
	font-size: 160px;
}

.booking-confirmation-page h2 {
	font-size: 2.986rem;
	font-weight: 600;
	margin-bottom: 15px;
	display: block;
}

.booking-confirmation-page p {
	font-size: 20px;
	display: block;
}

.booking-confirmation-page a.button:before {
	display: none;
}

.booking-confirmation-page a.button {
	padding: 11px 35px;
	background-color: #eee;
	color: #444;
	font-weight: 600;
	font-size: 16px;
}

/* Custom Dropdown for Booking Widget
------------------------------------- */
.booking-select {
	position: relative;
	z-index: 100;
}

input#booking-date,
input#booking-time {
	z-index: 110;
}

.booking-select select {
	border: none;
	cursor: pointer;
	border-radius: 5px;
	box-shadow: 0 1px 6px 0 rgba(0, 0, 0, .1);
	font-size: 16px;
	font-weight: 600;
	height: auto;
	padding: 10px 16px;
	line-height: 30px;
	margin: 0;
	position: relative;
	background-color: #fff;
	text-align: left;
	color: #909090;
	transition: color .3s
}

.booking-select select {
	padding: 15px 16px;
}

.listing-main-title {
	color: #252525;
	font-size: 2.488rem;
	font-weight: 600;
	line-height: 1.4;
	padding: 0;
	margin: 0;
}

.listing-main-subtitle {
	font-family: var(--font-primary);
	color: #333;
	font-size: 0.938rem;
	line-height: 1.4;
	margin: 0;
	font-weight: 400;
}

.listing-main-subtitle__arrow:before {
	content: '';
	filter: invert(100) drop-shadow(1px 1px 0 rgba(0, 0, 0, .2));
	display: inline-block;
	width: 8px;
	height: 8px;
	background-image: url("/assets/img/svg/right-arrow.svg");
	background-size: 8px 8px;
	background-repeat: no-repeat;
	position: relative;
	margin: 0 3px;
	top: -1px;
}

.listing-top-text .listing-top-read-more {
	display: none;
}

.listing-top-text a {
	text-decoration: underline;
}

/* Boxed Widget */
.boxed-widget {
	background-color: #fff;
	border: 1px solid #ddd;
	padding: 15px;
	z-index: 90;
	position: relative;
	border-radius: 6px;
	box-shadow: 1px 1px 14px rgb(0 0 0 / 6%);
}

.boxed-widget h3 {
	font-size: 1.44rem;
	margin: 0 0 15px 0;
	display: block;
	font-weight: 600;
	font-family: var(--font-primary);
}

.price-available {
	font-weight: 600;
	font-family: var(--font-primary);
	font-size: 1.125rem;
	margin-bottom: 0;
	line-height: 1.4;
	color: #252525;
}

.price-available.price-per-day {
	display: none;
	color: #252525;
}

.price-available.your-price {
	font-size: 1.25rem;
	font-weight: 600;
}

.booking-process__price--line-height {
	line-height: 1;
}

.price-available-old {
	display: block;
	line-height: 1.4;
	color: #252525;
	font-size: 1rem;
	padding-left: 20px;
	font-weight: 400;
}

.price-calculate-note {
	display: block;
	margin: 5px 0;
}

.price-available-old .tooltip {
	font-size: .75rem;
	top: 0;
	cursor: pointer;
}

.price-available-old del {
	color: red;
}

.price-available-old del > span {
	color: #252525;
}

.boxed-widget .tooltip {
	font-size: .75rem;
	display: inline-block;
	position: relative;
	top: -1px;
}

.boxed-widget .tooltip + h3 {
	display: inline-block;
	letter-spacing: -.2px;
}

.boxed-widget .tooltip:before {
	color: #48a9ea;
}

.boxed-widget h3 i {
	margin-right: 4px;
}


/* Booking Widget
------------------------------------- */
#listing-booking-form,
#booking-block-processed {
	display: none;
}

.loading-booking {
	display: none;
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	right: 0;
	top: 0;
	z-index: 999;
	background: rgba(255,255,255,.75);
}

.loading-booking > div {
	padding: 40px;
	background: #fff;
	box-shadow: 0 6px 12px rgba(0, 0, 0, .16);
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #252525;
	font-weight: 600;
	font-size: 0.938rem;
	text-align: center;
	border-radius: 4px;
}

.side-sticky {
	position: sticky;
	top: 78px;
	left: 0;
	right: 0;
	z-index: 3;
}

.button_icon--email:before,
.send-inquiry__icon--email:before {
	content: '';
	display: inline-block;
	width: 17px;
	height: 17px;
	background-image: url('/assets/img/svg/email.svg');
	background-size: 17px 17px;
	background-repeat: no-repeat;
	position: relative;
	top: 3px;
}


.help-block h3 {
	font-size: 1rem;
	font-weight: 600;
	margin-bottom: 2px;
	margin-top: 10px;
	color: #666;
}

.help-block.text-danger {
	font-size: 1rem;
	display: block;
	margin-top: -10px;
	margin-bottom: 10px;
	margin-left: -5px;
}

.help-block p {
	font-size: 1rem;
	margin-bottom: 10px;
	line-height: 20px;
}

.help-block .help-block-call {
	font-size: 1.44rem;
	font-weight: 600;
	color: #252525;
	margin-top: 8px;
}

.help-block .help-block-call a {
	color: #252525;
	font-weight: 600;
}

.help-block .help-block-email {
	font-size: 15px;
	font-weight: 500;
	display: block;
	margin-top: 5px;
}

.help-block .help-block-email a {
	text-decoration: underline;
}

.help-block .help-image-hero {
	text-align: center;
	align-self: center;
	width: 90px;
}

.help-block .help-image-hero--rounded {
	border-radius: 50%;
}

/* Quantity Buttons for Booking Widget*/
.qtyButtons {
	display: flex;
	align-items: center;
	margin: 0 0 10px 0;
}

.qtyButtons--bottom-line {
	border-bottom: 1px solid #eee;
	padding-bottom: 8px;
	margin-bottom: 10px;
}

.qtyButton--no-margin {
	margin: 0 !important;
}

.qtyButtons--horizontal {
	margin-right: 20px;
	background: #fff;
	padding: 10px 15px;
	border-radius: 4px;
	border: 1px solid #333;
}

.qtyButtons--horizontal .qtyTitle {
	flex: auto;
}

.qtyButtons--horizontal .qtyInput {
	width: 33px;
}

.panel-dropdown-content .qtyButtons:last-child {
	margin-bottom: 3px;
}

.main-search-input-item .qtyButtons input {
	width: 40px;
}

.main-search-input-item .qtyTotal {
	position: absolute;
	left: auto;
	right: 40px;
	top: 50%;
	transform: translateY(-50%);
}

.search .input-with-icon .qtyTotal {
	position: absolute;
	left: auto;
	right: 40px;
	top: 50%;
	transform: translateY(-50%);
}

.search .input-with-icon.panel-dropdown {
	height: 51px;
	width: 100%;
}

.search .input-with-icon.panel-dropdown.location {
	width: 100%;
	border: 1px solid #333;
	border-radius: 4px;
}

.search .input-with-icon.panel-dropdown.location .select2-search__field {
	height: 49px;
}

.search .input-with-icon.panel-dropdown .panel-dropdown-content {
	width: 320px;
	padding: 20px;
	top: 56px;
}

.special-offer-sidebar--wrapper {
	padding: 10px 12px;
	border: 1px solid #D22E2E;
	border-radius: 6px;
	background: #FFF9F9;
	margin-bottom: 12px;
}

.special-offer-sidebar--top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	cursor: pointer;
}

.special-offer-sidebar--headline--wrapper {
	display: flex;
	align-items: center;
	gap: 12px;
}

.special-offer-sidebar--headline {
	margin-bottom: 0;
	font-size: 1rem;
	font-weight: 500;
	text-transform: uppercase;
}

.special-offer-sidebar--arrow {
	display: flex;
}

.special-offer-sidebar--arrow-rotate {
	transform: rotate(180deg);
}

.special-offer-sidebar--bottom {
	padding-top: 12px;
	border-top: 1px solid #E7E7E7;
	margin-top: 12px;
	transition: all 1.3s ease-in-out;
}

.special-offer-sidebar--text {
	font-size: .875rem;
	font-weight: 400;
	list-style-type: none;
	margin-bottom: 12px;
}

.special-offer-sidebar--dates {
	display: flex;
	gap: 4px;
	flex-wrap: wrap;
}

.special-offer-sidebar-date {
	color: #027A48;
	border: 1px solid #027A48;
	border-radius: 4px;
	font-size: .75rem;
	padding: 3px 6px;
	line-height: 100%;
	font-weight: 600;
	margin: 0;
	min-width: 107px;
}

.search .input-with-icon .qtyButtons {
	margin-top: 0;
}

.search .input-with-icon .qtyButtons > input {
	padding: 0 !important;
	width: 40px;
}

.qtyButtons input {
	font-family: var(--font-primary);
	outline: 0;
	font-size: 1rem;
	text-align: center;
	width: 50px;
	height: 33px;
	color: #252525;
	line-height: 33px;
	margin: 0 !important;
	padding: 0 5px;
	border: none;
	box-shadow: none;
	pointer-events: none;
	display: inline-block;
	font-weight: 400 !important;
}

.qtyTitle {
	font-size: .938rem;
	font-weight: 500;
	line-height: 1.4;
	padding-right: 15px;
	display: block;
	flex: 1;
}

.qtyInc,
.qtyDec {
	width: 30px;
	height: 30px;
	line-height: 30px;
	background-color: #fff;
	display: inline-block;
	text-align: center;
	cursor: pointer;
	user-select: none;
	border-radius: 50%;
}

.qtyInc:hover,
.qtyDec:hover {
	background-color: #fff;
}

.qtyInc:before, .qtyDec:before {
	content: '';
	display: inline-block;
	width: 30px;
	height: 30px;
	background-size: 30px 30px;
	background-repeat: no-repeat;
}

.qtyInc:before {
	background-image: url("/assets/img/svg/circle-plus.svg");
}

.qtyDec:before {
	background-image: url("/assets/img/svg/circle-minus.svg");
}

.qtyButton--disabled {
	pointer-events: none;
	opacity: .5;
}

.qtyTotal {
	background-color: #666;
	border-radius: 50%;
	color: #fff;
	display: inline-block;
	font-size: .75rem;
	font-weight: 600;
	line-height: 18px;
	text-align: center;
	position: relative;
	top: -2px;
	left: 2px;
	height: 18px;
	width: 18px;
}

/* Show more */
.show-more {
	height: 450px;
	overflow: hidden;
	position: relative;
	transition: margin .4s;
}

.show-more:after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 180px;
	display: block;
	background: linear-gradient(rgba(255,255,255,0), #fff 88%);
	z-index: 9;
	opacity: 1;
	visibility: visible;
	transition: .8s;
}

.show-more.visible {
	margin-bottom: 20px;
}
.show-more.visible:after {
	opacity: 0;
	visibility: hidden;
}

/* Pagination
------------------------------------- */
.pagination {
	margin: 9px 0 0 0;
	text-align: center;
	overflow: hidden;
}

.pagination-next-prev {
	position: relative;
	top: -66px;
}

.pagination > * {
	display: inline-block;
	margin: 0;
	padding: 0;
}

.pagination > a,
.pagination-next-prev > a,
.pagination > span {
	padding: 12px 0;
	border-bottom: none;
	display: inline-block;
	color: #333;
	background-color: #fff;
	font-weight: 600;
	margin:0;
	line-height: 1.2;
	transition: all 200ms ease-in-out;
	font-size: .75rem;
}

.pagination > a,
.pagination > span {
	border-radius: 50%;
	width: 35px;
	height: 35px;
	padding: 0;
	line-height: 35px;
}

.pagination > a i {
	line-height: 35px;
}

.pagination .blank {
	color: #a0a0a0;
	padding: 0 6px;
}

.pagination > a.current-page,
.pagination .current,
.pagination ul li a:hover,
.pagination-next-prev ul li a:hover,
.pagination > a:hover {
	background-color: #000;
	color: #fff;
}

.pagination-next-prev a.next {
	float: right;
}

.pagination-next-prev a.prev {
	float: left;
}

.pagination-next-prev ul li a {
	color: #333;
	padding: 12px 16px;
	text-transform: uppercase;
}

.pagination ul,
.pagination-next-prev ul {
	padding: 0;
	list-style-type: none;
}

/* Listings Pagination */
.fs-listings .pagination ul li a,
.fs-listings .pagination-next-prev ul li a {
	background-color: transparent;
}

.fs-listings .pagination ul li a.current-page,
.fs-listings .pagination .current,
.fs-listings .pagination ul li a:hover,
.fs-listings .pagination-next-prev ul li a:hover {
	background-color: #000;
	color: #fff;
}

.language-switcher__arrow {
	transform: rotate(90deg);
	width: 10px;
	margin-left: 8px;
}

.header-navigation__submenu-sub {
	display: flex;
	flex-direction: column;
}

.form-headline {
	color: #444050;
	font-size: 1.3rem;
	font-weight: 600;
	margin-bottom: 0;
}

.form-subtitle {
	color: #6D6B77;
	font-size: 1rem;
	font-weight: 400;
	margin-bottom: 40px;
}

.evisitor-grid-wrapper {
	display: grid;
	grid-template-columns: repeat(12, 1fr);
	grid-template-rows: repeat(1, 1fr);
	margin-top: 60px;
	margin-bottom: 60px;
}

.evisitor-guest-list {
	grid-area: 1 / 1 / 1 / 3;
	margin-right: 24px;
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.evisitor-content {
	grid-area: 1 / 3 / 1 / 13;
	display: grid;
	grid-template-columns: repeat(12, 1fr);
	grid-template-rows: repeat(1, 1fr);
	grid-column-gap: 0px;
	grid-row-gap: 0px;
	padding: 24px;
	border-radius: 6px;
	background: #FFF;
	box-shadow: 0 3px 12px 0 rgba(47, 43, 61, 0.14);
}
.forms {
	grid-area: 1 / 1 / 2 / 8;
}

.evisitor-thankyou {
	grid-area: 1 / 3 / 1 / 13;
	border-radius: 6px;
	background: #FFF;
	box-shadow: 0 3px 12px 0 rgba(47, 43, 61, 0.14);
	padding: 24px;
	display: flex;
	justify-content: center;
	flex-direction: column;
	align-items: center;
}

.form-details {
	grid-area: 1 / 9 / 2 / 13;
	padding-left: 24px;
	border-left: 1px solid #CCCCCC;
}

.evisitor-guest-list-item {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 12px 16px;
	border: 1px solid transparent;
	border-radius: 10px;
	gap: 16px;
}

.evisitor-guest-list-item--active {
	border: 1px solid #D0D5DD;
	background: #F2F4F7;
}

.visitor-guest-list-item--done,
.visitor-guest-list-item--done-last {
	border: 1px solid #77A372;
	background: #DDF2D1;
}

.visitor-guest-list-item--done-last {
	width: 100%;
}

.evisitor-thankyou-icon-wrapper {
	position: relative;
	width: fit-content;
	margin-bottom: 20px
}

.evisitor-thankyou-headline {
	font-size: 1.38rem;
	color: #3E8429;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 20px;
}

.evisitor-thankyou-subheadline {
	font-size: 1.2rem;
	color: #252525;
	font-weight: 400;
	margin-bottom: 0;
}

.evisitor-thankyou-text {
	font-size: .925rem;
	color: #666;
	font-weight: 400;
	margin-bottom: 30px;
	width: 45%;
	text-align: center;
}

.evisitor-thankyou-text-last {
	margin-bottom: 0;
	font-size: 1.2rem;
	font-weight: 600;
}

.evisitor-thankyou-button {
	border-radius: 6px;
	border: 1px solid #101828;
	background: #101828;
	color: #fff;
	padding: 6px 21px;
}

.evisitor-modal-headline {
	display: flex;
	align-items: center;
	gap: 8px;
}

.evisitor-modal-trigger-in-form {
	display: none;
}

.evisitor-modal-list {
	color: #444;
	font-size: 1rem;
	margin-top: 20px;
}

.evisitor-modal-list li {
	list-style: none;
}

.evisitor-modal-list li strong {
	margin-left: -14px;
}

.evisitor-modal-help-label {
	color: #444050;
	font-size: 1.1rem;
	font-weight: 600;
	margin-bottom: 15px;
	margin-top: 40px;
}

.evisitor-modal-help-cta {
	padding: 8px 16px;
	border-radius: 6px;
	border: 1px solid #ccc;
	display: flex;
	width: fit-content;
	gap: 10px;
}

.evisitor-modal-help-cta-label {
	color: #252525;
	font-size: 1rem;
	line-height: normal;
	font-weight: 600;
}

.evisitor-modal-list li:not(:last-child) {
  margin-bottom: 20px;
}

.evisitor-modal-headline-icon {
	fill: #9A8800;
}

.evisitor-modal-label {
	margin-bottom: 0;
	color: #444050;
	font-size: 1.3rem;
	font-weight: 600;
	margin-top: -1px;
}

.evisitor-thankyou-icon-check {
	position: absolute;
	right: -1px;
	bottom: 13px;
}

.evisitor-checkmark-svg {
	display: none;
}

.visitor-guest-list-item--done .evisitor-checkmark-svg {
	display: block;
	position: absolute;
	bottom: -6px;
	right: -6px;
}

.visitor-guest-list-item--done .evisitor-checkmark-svg.evisitor-checkmark-svg--child {
	right: -5px;
	bottom: 1px;
}

.evisitor-guest-list-img-wrapper {
	position: relative;
	display: flex;
	align-items: center;
}
.kids-svg {
	margin-right: -8px;
}

.evisitor-layout {
	background: #F5F5F5;
}

#settlement_template {
	display: none;
}

.evisitor-html {
	height: 100%;
	background: #F5F5F5;
}

.evisitor-layout .select2-container--default .select2-selection--single {
	height: 100%;
	border-radius: 6px;
	border: 1px solid #D1D0D4;
	height: 38px;
	padding: 4px;
	font-size: 1rem;
}

.evisitor-layout .select2-container--default.select2-container--open .select2-selection--single {
	border-color: #8e8e8f;
}

.evisitor-layout .select2-container--default .select2-selection--single .select2-selection__arrow {
	top: 5px;
}

.evisitor-layout .select2-container--default .select2-selection--single .select2-selection__arrow b {
	border: none;
	background-image: url('/assets/img/svg/arrow-down-bold.svg');
	background-size: 10px 10px;
	width: 10px;
	height: 10px;
	opacity: .9;
}

.evisitor-guest-list-item-title {
	color: #252525;
	font-size: 1rem;
	font-weight: 600;
	margin: 0;
}

.evisitor-guest-list-item-subtitle {
	color: #444;
	font-size: .95rem;
	font-weight: 400;
	margin: 0;
}

.evisitor-guest-list-item[data-initialized="false"],
.form-wrapper[data-initialized="false"] {
	display: none;
}

.form-wrapper[data-initialized="true"] {
	display: none;
}

.form-wrapper.active[data-initialized="true"] {
	display: block;
}

.evisitor-button-submit {
	border-radius: 6px;
	border: 1px solid #101828;
	background: #101828;
	color: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 8px;
	padding: 8px 16px;
	width: fit-content;
}

.evisitor-button-submit-wrapper {
	grid-column: 1 / -1
}

select#identity_document_0 option:not([disabled]),
select#country_of_residence_0 option:not([disabled]),
select#country_of_birth_0 option:not([disabled]) {
  font-size: 1rem;
  color: #000000;
}

/* Contact Page
------------------------------------- */
#contact textarea {
	min-height: 200px;
	margin: 15px 0 25px 0;
}

#contact input {
	margin-bottom: 25px;
}

.loader {
	margin-left: 15px;
}

.submit.disabled:hover,
.submit.disabled {
	background-color: #e8e8e8;
	color: #333;
}

#contact input.button.submit {
	margin-bottom: 10px;
	line-height: 18px;
	height: 49px;
	transition: all .25s !important;
}

#contact input[type="submit"].submit:hover {
	opacity: .92;
}

/* Search Widget */
#contact textarea,
#contact input {
	transition: box-shadow .2s !important;
}

#contact textarea:focus,
#contact input:focus {
	transition: box-shadow .2s !important;
	box-shadow: 0 0 10px 0 rgba(0,0,0,.07);
}

/* ------------------------------------------------------------------- */
/*  03. Shortcodes
---------------------------------------------------------------------- */

/* Buttons
------------------------------------- */
button.button,
input[type="button"],
input[type="submit"],
.button.border,
.hs_submit input[type="submit"],
.button,
a.button,
.button.flat {
	background-color: #252525;
	top: 0;
	padding: 10px 20px;
	color: #fff;
	position: relative;
	font-size: .938rem;
	line-height: 1.6;
	font-weight: 500;
	transition: all .2s ease-in-out;
	cursor: pointer;
	overflow: hidden;
	border: none;
	text-transform: initial;
	border-radius: 6px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.button.border:hover {
	background: #f1f1f1;
}

.button.button--with-icon {
	display: inline-flex;
	justify-content: center;
	align-items: center;
}

.button__icon--right-arrow {
	margin-left: 12px;
}

.button__icon--left-arrow {
	margin-right: 12px;
	transform: scaleX(-1);
}

.button__icon--down-arrow:after {
	opacity: .9;
	position: relative;
	display: inline-block;
	border: solid;
	border-width: 0 1px 1px 0;
	content: '';
	margin-left: 10px;
	padding: 3px;
	top: -3px;
	transform: rotate(45deg);
}

/* Stop the double-tap zoom on button */
button {
	touch-action: manipulation;
}

.hs_submit input[type="submit"] {
	text-shadow: none;
	background-image: none;
	box-shadow: none;
	height: auto !important;
	line-height: 26px;
}

.hs_submit input[type="submit"]:hover,
.hs_submit input[type="submit"]:focus,
.hs_submit input[type="submit"]:visited {
	border: none !important;
	box-shadow: none !important;
}

.button.button--larger {
	font-size: 1rem;
	padding: 12px 50px;
}

.button.button--wider {
	padding: 10px 40px;
}

.button.button--fullwidth {
	display: block;
	width: 100%;
	text-align: center;
}

.button.button--proceed:after {
	content: '';
	display: inline-block;
	width: 17px;
	height: 17px;
	background-image: url('/assets/img/svg/arrow-right-button.svg');
	background-size: 17px 17px;
	background-repeat: no-repeat;
	filter: invert(1);
	position: relative;
	top: 3px;
	margin-left: 8px;
}

.button.button--apply {
	padding: 3px 36px;
}

.button.button--apply:after {
	content: none;
}

.button.button--smaller {
	padding: 8px 20px;
	min-height: 41px;
}

.button.button--tiny {
	padding: 3px 20px;
}

.button.button--flex {
	display: flex;
	align-items: center;
}

.button.border.button--smaller {
	padding: 5px 20px;
}

.button.button--wider {
	padding: 9px 30px;
}

.button.flat {
	font-size: .938rem;
	background-color: #fff;
	border: none;
	color: #333;
}

.button.flat:hover {
	background-color: #eee;
}

.button.flat--smaller {
	font-size: .875rem;
	padding: 9px 18px;
}

.button.flat.flat--bigger {
	font-size: 0.938rem;
	padding: 16px 20px;
}

.show-map__icon {
	margin-right: 8px;
}

.show-map__icon--list {
	display: none;
}

.button-loader {
	display: none;
	position: absolute;
	top: calc(50% - 12px);
	left: calc(50% - 12px);
	width: 25px;
	height: 25px;
	border: 2px solid #000;
	border-radius: 100%;
	border-top: 3px solid transparent;
	animation: load-button-animate infinite linear 1s;
}

.button--append-loader-light .button-loader {
	border-color: #fff;
	border-top: 3px solid transparent;
}

.button-disabled {
	pointer-events: none;
	cursor: not-allowed;
}

.button-hidden-content {
	opacity: 0;
}

@keyframes load-button-animate {
	0% {
		transform: rotate(0deg);
	}
	50% {
		transform: rotate(180deg);
		opacity: .35;
	}
	100% {
		transform: rotate(360deg);
	}
}

input[type="button"],
input[type="submit"] {
	line-height: 32px;
}

button.button:before,
.button:before {
	width: 100%;
	height: 100%;
	content: '';
	display: block;
	background-color: #fff;
	position: absolute;
	left: 0;
	top: 0;
	opacity: 0;
}

button.button i:before,
.button i:before {
	transition: all .2s;
}

button.button:hover:before,
.button:hover:before {
	opacity: .1;
}

.button.white {
	background-color: #fff;
	color: #333;
}

.button.border:before {
	display: none;
}

.button.border,
button.button.border {
	background-color: transparent;
	color: #252525;
	border: 1px solid #252525;
}

.button.border--rounded,
button.button.border--rounded {
	border-radius: 25px;
}

.button.border:hover,
button.button.border:hover {
	opacity: 1;
}

.button.border.white,
button.button.border.white {
	border-color: #fff;
	color: #fff;
}

.button.border.white:hover,
button.button.border.white:hover {
	background-color: #fff;
	color: #252525;
}

button.button.border--neutral,
.button.button.border--neutral {
	border:2px solid #e6e6e6;
	background-color: #fff;
	color: #252525;
}

button.button.border--neutral:hover,
.button.button.border--neutral:hover {
	background: #f7f7f7;
}

button.button.border--gray,
.button.button.border--gray {
	border: 1px solid #dbdbdb;
	background-color: #fff;
	color: #666;
}

button.button.border--gray:hover,
.button.button.border--gray:hover,
.button.button.border--gray:hover i:before {
	color: #252525;
}

button.button.fullwidth,
.button.fullwidth {
	width: 100%;
	text-align: center;
	justify-content: center;
}

.button.white.border:hover {
	color: #333;
}

.button i {
	padding-right: 6px;
}

.centered-content {
	text-align: center;
}

.centered-content .button {
	min-width: 130px;
}

button.button.button--color-anchor,
.button.button--color-anchor,
button.button--color-action,
.button.button--color-action {
	background: linear-gradient(to bottom, rgba(248,218,112,1) 0%, rgba(244,208,76,1) 100%);
	transition: background .5s;
	color: #252525;
	z-index: 1;
}

button.button.button--color-anchor:after,
.button.button--color-anchor:after,
button.button--color-action:after,
.button.button--color-action:after {
	content: '';
	width: 100%;
	height: 100%;
	border-radius: 2px;
	background: linear-gradient(to top, rgba(244,208,76,1) 0%, rgba(248,218,112,1) 100%);
	position: absolute;
	z-index: -1;
	top: 0;
	left: 0;
	opacity: 0;
	transition: opacity .4s ease-in-out;
}

.gold-btn-right-arrow {
	position: relative;
	margin-left: 8px;
}

button.button.button--color-anchor:hover:after,
.button.button--color-anchor:hover:after {
	opacity: 1;
}

button.button.button--color-important,
.button.button--color-important {
	background-color: #000;
}

button.button.button--color-anchor i:before,
.button.button--color-anchor i:before {
	color: #fff !important;
}

button.button--color-main,
.button.button--color-main {
	background-color: #000;
}

button.button--color-main:hover:before,
.button.button--color-main:hover:before {
	opacity: .2;
}

button.button--color-white,
.button.button--color-white {
	color: #252525 !important;
	background-color: #fff;
}

button.button--color-white:hover,
.button.button--color-white:hover {
	background-color: #fafafa;
}


button.button--color-action:hover:after,
.button.button--color-action:hover:after {
	opacity: 1;
}

button.button--color-action:hover:before,
.button.button--color-action:hover:before {
	opacity: 0;
}

button.button.button--color-light-main,
.button.button--color-light-main {
	background-color: #e1e2e6;
	color: #252525;
}

button.button.button--color-light-main:hover,
.button.button--color-light-main:hover {
	background-color: #cccfdd;
}

button.button--upper,
.button.button--upper {
	text-transform: uppercase;
}

button.button--disabled,
.button.button--disabled {
	background: #ddd;
	cursor: default;
}

.button--disabled:hover,
.button--disabled:hover:after {
	background: #ddd;
	cursor: default;
	content: none
}

button.button--font-size-default,
.button.button--font-size-default {
	font-size: 1rem;
}

button.border.button--tiny,
.button.border.button--tiny {
	font-size: 0.938rem;
	padding: 6px 20px;
}

.button--text-shadow {
	text-shadow: 1px 1px 0 rgba(0, 37, 60, .2);
}

button.border.button--show-season-overview-active,
button.border.button--show-season-overview-active:hover {
	background-color: #252525;
	color: #fff;
}

.button .icon:before {
	vertical-align: middle;
}

.button .icon.icon-black-mail:before {
	color: #666;
	font-size: .75rem;
	position: relative;
	top: -1px;
}

.reporttitle {
	margin-top: 32px;
	margin-bottom: 12px;
	font-size: 1rem;
}

.cookie-tables-wrapper {
	overflow-x: auto;
}

.cookie-tables-wrapper .table-striped > tbody > tr:nth-child(odd) {
	background: #f8f8f8;
}

.cookie-tables-wrapper th,
.cookie-tables-wrapper td {
	padding: 4px 8px;
	min-width: 150px;
	text-align: left;
	font-size: .875rem;
}

.cookie-tables-wrapper .cookie-description {
	min-width: 400px;
}

.cookie-tables-wrapper thead {
	background: #3A98D7;
	color: #fff;
	padding: 10px 0;
}

/* Loaders
------------------------------------- */
.load-circle,
.load-circle:after {
	border-radius: 50%;
	width: 5em;
	height: 5em;
}

.load-circle {
	margin: 0 auto 10px auto;
	font-size: 8px;
	position: relative;
	text-indent: -9999em;
	border-top: .6em solid rgba(0, 0, 0, .2);
	border-right: .6em solid rgba(0, 0, 0, .2);
	border-bottom: .6em solid rgba(0, 0, 0, .2);
	border-left: .6em solid #252525;
	transform: translateZ(0);
	animation: load8 1.1s infinite linear;
}

@keyframes load8 {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* Static Pages
------------------------------------- */

.rounded i:before {
	position: relative;
	z-index: 11;
}

.flex {
	display: flex;
}

.flex.align-center {
	align-items: center;
}

.flex.space-between {
	justify-content: space-between;
}

.flex--column {
	flex-direction: column;
}

.flex-1 {
	flex: 1;
}

.flex-1-5 {
	flex: 1.5;
}

.flex-2 {
	flex: 2;
}

/* ---------------------------------- */
/* Section blocks
------------------------------------- */
.block-grid--regions {
	padding-bottom: 75px;
}

.block-grid-random {
	padding-bottom: 105px;
}

.block-featured {
	padding-bottom: 95px;
}

.block-featured .col-md-4 {
	width: 31%;
	padding-right: 70px;
}

.block-featured .col-md-8 {
	width: 69%;
}

.block-insurance {
	padding-bottom: 105px;
}

.block-insurance .col-md-4 {
	padding-right: 50px;
}

.block-insurance__image-container {
	position: relative;
}

.block-insurance__image-container:first-of-type {
	margin-right: 20px;
}

.block-insurance__image-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100%;
	width: 100%;
	background: linear-gradient(to top, rgba(35, 35, 37, .9) 0%, rgba(35, 35, 37, .45) 35%, rgba(22, 22, 23, 0) 60%, rgba(0, 0, 0, 0) 100%);
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
}

.block-insurance__images {
	display: flex;
	justify-content: space-between;
}

.block-insurance__image {
	object-fit: cover;
	object-position: 50% 50%;
	background-position: 50%;
	background-size: cover;
	border-radius: 6px;
	flex: 0 1 auto;
}

.block-insurance__image-content {
	color: #fff;
	position: absolute;
	bottom: 15px;
	left: 15px;
}

.block-insurance__content-title {
	font-size: 1.25rem;
	font-weight: 500;
	margin-bottom: 6px;
}

.block-insurance__content-subtitle {
	margin-bottom: 6px;
	display: flex;
}

.block-insurance__content-subtitle-span {
	text-indent: -2px;
	display: inline-block;
	padding-left: 5px;
}

.block-insurance__image-symbol {
	margin-right: 5px;
	position: relative;
	top: 4px;
}


/* ---------------------------------- */
/* Testimonials
------------------------------------- */
.block-main-testimonials {
	padding-bottom: 50px;
	background-color: #fff;
}

.block-main-testimonials .col-sm-12 {
	padding-right: 0;
}

.testimonials-block {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
}

.testimonial-block {
	display: flex;
	flex-direction: column;
	min-height: 195px;
	padding: 60px 22px 22px 22px;
	background: #fff;
	position: relative;
	border-radius: 3px;
	text-align: left;
	line-height: 21px;
	flex: 1 1 260px;
	margin-right: 15px;
	margin-bottom: 15px;
}

.testimonial-block:before {
	content: '';
	display: inline-block;
	position: absolute;
	top: 34px;
	left: 5px;
	width: 20px;
	height: 17px;
	background-size: 20px 17px;
	background-repeat: no-repeat;
	background-image: url('/assets/img/svg/quote.svg');
}

.testimonials-title {
	font-size: 2.488rem;
	line-height: 1.2;
	color: #252525;
	margin: 0 0 5px 0;
}

.testimonials-subtitle {
	font-family: var(--font-primary);
	font-size: 1rem;
	line-height: 1.4;
	color: #333;
	margin: 0 0 45px 0;
}

.testimonial-block__all {
	margin-top: 15px;
}

.testimonial-block--hidden, .testimonial-block__more-text {
	display: none;
}

.testimonial-block__content {
	margin-bottom: 5px;
	font-size: .875rem;
	line-height: 1.6;
	color: #000;
	min-height: 60px;
}

.testimonial-block__show-more {
	text-decoration: underline;
}

.testimonial-block__guest-name {
	font-size: .875rem;
	color: #545454;
	position: relative;
	margin: 5px 0 0 0;
}

.testimonial-block--hidden {
	display: none;
}

.testimonial-block--hidden-visible {
	display: flex;
}

.testimonial-block__stars {
	position: relative;
	height: 15px;
	width: 75px;
	margin-top: 5px;
}

.testimonial-block__star-score {
	font-size: .75rem;
	display: flex;
	align-items: center;
}

.testimonial-block__star-icon {
	margin-right: 4px;
	margin-top: -3px;
}

.testimonial-block__star {
	position: absolute;
	top: 0;
	left: 0;
	height: 15px;
	width: 75px;
}

.testimonial-block__star--empty {
	background: url('/assets/img/stars-gray.png') no-repeat;
	background-size: 67px 12px;
}

.testimonial-block__star--filled {
	background: url('/assets/img/stars-yellow.png') no-repeat;
	background-size: 67px 12px;
	width: 0;
	z-index: 1;
}

.testimonial-block__country-flag {
	content: '';
	display: inline-block;
	vertical-align: middle;
	margin-left: 5px;
	position: relative;
	top: -2px;
	width: 22px;
	height: 22px;
	background-size: 22px 22px;
	background-repeat: no-repeat;
}

.testimonial-block__country-flag--5 {
	background-image: url('/assets/img/svg/flags/5.svg');
}

.testimonial-block__country-flag--6 {
	background-image: url('/assets/img/svg/flags/6.svg');
}

.testimonial-block__country-flag--7 {
	background-image: url('/assets/img/svg/flags/7.svg');
}

.testimonial-block__country-flag--8 {
	background-image: url('/assets/img/svg/flags/8.svg');
}

.testimonial-block__country-flag--9 {
	background-image: url('/assets/img/svg/flags/9.svg');
}

/* Tables
------------------------------------- */
table.basic-table {
	width: 100%;
	border-collapse: separate;
	border-spacing: 0;
	border:none;
	margin-bottom: 15px;
}

table.basic-table th {
	background-color: #66676b;
	text-align: left;
	color: #fff;
	vertical-align: top;
	font-weight: 400;
}

table.basic-table th:first-child {
	border-radius: 4px 0 0 4px;
}
table.basic-table th:last-child {
	border-radius: 0 4px 4px 0;
}

table.basic-table th,
table.basic-table td {
	padding: 15px 28px;
}

table.basic-table tr:nth-child(odd) {
	background-color: #f6f6f6
}

table.basic-table {
	margin-bottom: 0;
}


@media screen and (max-width: 600px) {
	table {
		border: 0;
	}

	table tr {
		display: block;
		margin-bottom: 0;
	}

	.ui-datepicker-calendar tr {
		display: table-row;
	}
	.ui-datepicker-calendar td {
		display: table-cell;
	}
	table td {
		border-bottom: 1px solid #ddd;
		display: block;
		font-size: 1rem;
		text-align: right;
	}

	.cookie-tables-wrapper td {
		display: table-cell;
	}

	table td:before {
		content: attr(data-label);
		float: left;
		font-weight: 600;
	}

	table td:last-child {
		border-bottom: 0;
	}
}

/* Accordion / Toggles
------------------------------------- */
.ui-accordion .ui-accordion-header,
.trigger a {
	display: block;
	cursor: pointer;
	position: relative;
	line-height: 26px;
	outline: none;
	color: #333;
	font-size: .833rem;
	background-color: #fff;
	border: 1px solid #e0e0e0;
	transition: background-color .2s, color .2s;
	padding: 16px 27px;
	margin: -1px 0 0 0;
}

.ui-accordion .ui-accordion-header i,
.trigger a i {
	font-size: 0.938rem;
	top: 1px;
	position: relative;
	padding-right: 3px;
}

.trigger.active a,
.ui-accordion .ui-accordion-header-active:hover,
.ui-accordion .ui-accordion-header-active {
	background-color: #666;
	border-color: #666;
	color: #fff;
}

.ui-accordion .ui-accordion-header i,
.trigger a i {
	margin: 0 4px 0 0;
	position: relative;
}

.ui-accordion .ui-accordion-content {
	padding: 28px 30px;
	border: 1px solid #e0e0e0;
	border-top: none;
}

.ui-accordion .ui-accordion-content p {
	margin: 0;
}

mark.color {
	background-color: #666;
	border-radius: 4px;
}

mark {
	border-radius: 4px;
}

/* Tooltips
------------------------------------- */
[data-tooltip] {
	position: relative;
	z-index: 2;
	cursor: pointer;
}

/* Hide the tooltip content by default */
[data-tooltip]:before,
[data-tooltip]:after {
	visibility: hidden;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: progid: DXImageTransform.Microsoft.Alpha(Opacity=0);
	opacity: 0;
	pointer-events: none;
}

/* Position tooltip above the element */
[data-tooltip]:before {
	position: absolute;
	bottom: 100%;
	left: -140px;
	margin-bottom: 5px;
	padding: 8px 5px;
	width: 190px;
	border-radius: 3px;
	font-weight: 400;
	background-color: #fff;
	color: #333;
	content: attr(data-tooltip);
	text-align: center;
	font-size: .75rem;
	line-height: 1.2;
	box-shadow: 0 2px 8px rgba(0, 0, 0, .25);
	border: 1px solid #ccc;
}

/* Show tooltip content on hover */
[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
	visibility: visible;
	filter: progid: DXImageTransform.Microsoft.Alpha(Opacity=100);
	opacity: 1;
}


/* Info Box
------------------------------------- */
.info-box {
	background-color: #fff;
	display: inline-block;
	width: 100%;
	border-radius: 3px;
	padding: 23px 27px;
	border-top: 2px solid #666;
	background: linear-gradient(to bottom, rgba(255,255,255,.98), rgba(255,255,255,.95));
	background-color: #666;
	color: #666;
}

.info-box.no-border {
	border: none;
	background: #666 linear-gradient(to bottom, rgba(255, 255, 255, .96), rgba(255, 255, 255, .93));
}

.info-box h4 {
	margin: 0;
	font-size: 0.938rem;
	color: #666;
	margin-bottom: 20px;
}

.info-box p {
	margin-bottom: 0;
	font-size: 1rem;
	line-height: 26px;
}

.info-box.large {
	padding: 36px;

}

/* Image Hovers
------------------------------------- */
.img-box {
	height: 270px;
	display: block;
	position: relative;
	width: 100%;
	z-index: 90;
	margin: 10px 0 25px 0;
	border-radius: 3px;
}

.img-box-background {
	object-fit: cover;
	object-position: 50% 50%;
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background-position: 50%;
	background-size: cover;
	transition: transform .35s ease-out;
	border-radius: 3px;
}

.block-grid-random__item-box {
	margin: 0 0 12px;
}

.block-grid-random__row,
.grid-thumbnail-custom {
	margin-left: -6px;
	margin-right: -6px;
}

.block-grid-random__item,
.grid-thumbnail-custom__block {
	padding-left: 6px;
	padding-right: 6px;
}

.img-box:hover:before {
	opacity: 0;
}

.img-box:before {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: block;
	content: "";
	background-color: #222;
	opacity: .1;
	z-index: 99;
	transition: .3s ease-out;
}

.img-box-content {
	position: absolute;
	z-index: 101;
	top: 50%;
	margin: 0 auto;
	text-align: center;
	width: 100%;
	transform: translate(0,-50.5%);
}

.img-box-content--not-centered {
	top: auto;
	right: 0;
	bottom: 0;
	transform: translate(0, 0);
	margin: auto;
	text-align: left;
	padding: 15px 20px;
}

.img-box-content--bellow {
	background-color: #fff;
	padding: 10px 20px;
	border-radius: 6px;
	margin: 15px;
	bottom: 0;
	width: auto;
	font-size: .875rem;
}

.img-box-content h4 {
	font-family: var(--font-primary);
	font-size: .875rem;
	font-weight: 400;
	line-height: 140%;
	text-transform: uppercase;
	color: #252525;
	margin: 0;
}

.img-box-content h4:after {
	content: '';
	display: inline-block;
	width: 18px;
	height: 18px;
	background-image: url('/assets/img/svg/arrow-right-button.svg');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	position: relative;
	top: 3px;
	margin-left: 8px;
}

.img-box-content--bellow h4 {
	text-transform: inherit;
}

.img-box-content span {
	font-size: 15px;
	color: #f7f7f7;
	text-shadow: 1px 1px 0 rgba(0, 0, 0, .3);
}

.top-1 {
	position: relative;
	top: -1px;
}

.top-0 {
	position: relative;
}

.listing-features {
	font-size: .875rem;
	line-height: 1.8;
	width: 100%;
	display: block;
	list-style: none;
	margin: 0;
	padding: 0;
	columns: 2;
}

.listing-features.one-column {
	columns: 1;
}

/* Custom Option tags */
.option-tags,
.option-tags > fieldset {
	display: flex;
}

.option-tags > fieldset {
	margin-bottom: 8px;
}

.option-tags .custom-radio {
	display: flex;
}

.option-tags .custom-radio input[type="radio"] {
	position: absolute;
	clip: rect(0, 0, 0, 0);
	pointer-events: none;
}

.option-tags .custom-radio input[type="radio"] + label {
	display: inline-block;
	text-align: center;
	user-select: none;
	border: 1px solid #252525;
	padding: 3px 12px;
	font-size: .875rem;
	border-radius: 6px;
	color: #252525;
	margin-right: .4rem;
	cursor: pointer;
	min-width: 15px;
}

.option-tags .custom-radio input[type="radio"]:checked + label,
.option-tags .custom-radio input[type="radio"] + label:hover {
	background-color: #252525;
	color: #fff;
}

.review-stars__icon.star-gold:before {
	content: '';
	display: inline-block;
	background-image: url(/assets/img/svg/review-star.svg);
	width: 13px;
	height: 13px;
	background-size: 13px 13px;
	background-repeat: no-repeat;
	margin-right: 1px;
}

.review-badge-similar-villas {
	margin-top: -8px;
	margin-bottom: 8px;
}

/* Custom Checkboxes
------------------------------------- */
.checkboxes {
	list-style: none;
	padding: 0;
}

.checkboxes--inline {
	display: inline-block;
}

.checkboxes.in-row label {
	margin-top: 9px;
}

.checkboxes.one-in-row label {
	margin-top: 9px;
	width: 100%;
}

.checkboxes li {
	padding: 4px 0;
}

.checkboxes li:last-child {
	margin-bottom: -8px;
}

.checkboxes label {
	display: inline-block;
	cursor: pointer;
	position: relative;
	padding-left: 28px;
	margin-right: 20px;
	margin-bottom: 0;
	line-height: 1.4;
	font-size: 0.938rem;
	color: #252525
}

.checkboxes label span {
	color: #909090;
}

.checkboxes label span.filtering-modal__discount-label {
	color: #fff;
	background: #D22E2E;
	display: inline-block;
	margin-left: 8px;
	vertical-align: middle;
	border-radius: 6px;
	text-align: center;
	font-size: .75rem;
	padding: 1px 10px;
}

.checkboxes input[type=checkbox],
.checkboxes input[type=radio] {
	display: none;
}

.checkboxes label:before {
	content: '';
	border: 1px solid #666;
	border-radius: 6px;
	background: #fff;
	color: #fff;
	clear: none;
	cursor: pointer;
	display: inline-block;
	line-height: 0;
	height: 19px;
	outline: 0;
	text-align: center;
	vertical-align: middle;
	width: 19px;
	-webkit-appearance: none;
	padding: 4px;
	transition: all .25s;
	position: absolute;
	left: 0;
}

.checkboxes label:hover:before {
	border-color: #444;
}

.checkboxes input[type=checkbox]:checked + label:before,
.checkboxes input[type=radio].checked + label:before {
	content: '';
	display: inline-block;
	width: 19px;
	height: 19px;
	background-image: url('/assets/img/svg/check-white.svg');
	background-size: 11px 11px;
	background-repeat: no-repeat;
	background-position: center center;
	transition: none;
	background-color: #344054;
	border: 2px solid #344054;
}

.disabled-checkboxes input[type=checkbox]:checked + label:before {
	opacity: .4;
	filter: invert(1);
	border: 1px solid #777;
}

.check-special-offer {
	font-weight: 600;
}

.checkboxes--simple {
	margin-bottom: 15px;
}

.checkboxes--simple li:before {
	content: '';
	filter: invert(64%) sepia(38%) saturate(6675%) hue-rotate(105deg) brightness(96%) contrast(80%);
	display: inline-block;
	width: 14px;
	height: 14px;
	background-image: url('/assets/img/svg/check.svg');
	background-size: 14px 14px;
	background-repeat: no-repeat;
	margin-right: 6px;
	vertical-align: middle;
	top: -1px;
	position: relative;
}

.own-review-badge {
	width: fit-content;
}

.own-review-badge-wrapper {
	background: #FBD960;
	background: linear-gradient(180deg, #F8DA70 0%, #F4D04C 100%);
	border-radius: 6px;
	padding: 1px;
	display: flex;
	width: fit-content;
	box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.04);
}

.own-review-badge-left-side {
	background: #818181;
	background: linear-gradient(180deg, #F8DA70 0%, #F4D04C 100%);
	border-radius: 6px 2px 2px 6px;
	padding: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.own-review-badge-right-side {
	background: #fff;
	padding: 3px 10px;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	border-radius: 0 5px 5px 0;
	line-height: 110%;
}

.own-review-badge-score {
	font-size: 0.938rem;
	font-weight: 600;
	line-height: 1;
}

.own-review-badge-label {
	font-size: 0.938rem;
	font-weight: 600;
	width: max-content;
}

.own-review-check-icon {
	min-width: 10px;
	min-height: 10px;
	filter: invert(43%) sepia(17%) saturate(2619%) hue-rotate(47deg) brightness(100%) contrast(82%);
}

.own-review-badge-right-side-upper {
	display: flex;
	gap: 4px;
	align-items: center;
}

.own-review-badge-review-count,
.own-review-badge-review-count-link {
	font-size: .75rem;
	margin-top: 4px;
	margin-bottom: 0;
	text-align: center;
	line-height: 1;
}

.own-review-badge-review-count {
	color: #101828;
}

.own-review-badge-review-count-link {
	color: #337AB7;
	text-decoration: underline;
	display: block;
}

.own-review-comment-wrapper {
	border: 1px solid #D0D5DD;
	border-radius: 6px;
	padding: 10px;
}

.own-review-comment-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.own-review-comment {
	color: #0C111D;
	font-size: .875rem;
	line-height: 18px;
}

.own-review-comment-rating-wrapper {
	display: flex;
	align-items: center;
	gap: 6px;
}

.own-review-comment-score {
	font-size: .875rem;
	font-weight: 500;
	color: #0C111D;
	line-height: 15px;
}

.own-review-comment-location {
	margin-top: 6px;
	display: flex;
	align-items: center;
	gap: 6px;
}

.own-review-comment-location-country-flag {
	width: 17px!important;
	height: 13px!important;
	background-repeat: no-repeat;
	border-radius: 5px;
}

.own-review-comment-location-country-name {
	color: #98A2B3;
	font-size: .813rem;
	line-height: 13px;
}

.own-review-comment-text {
	margin-top: 10px;
	margin-bottom: 0;
	color: #0C111D;
	font-size: .875rem;
	line-height: 1.6;
}

.own-review-comment-read-more-btn {
	display: block;
	background: none;
	border: none;
	color: #337AB7;
	cursor: pointer;
	font-size: .875rem;
	line-height: 120%;
	text-decoration: underline;
	padding: 0;
	margin-top: 10px;
	margin-bottom: 8px;
}

.own-review-comment-date {
	display: block;
	color: #475467;
	font-size: .875rem;
	line-height: 120%;
	margin-top: 10px;
}

.own-reviews-block-section {
	margin-top: 64px;
}

.own-review-bars {
	display: flex;
	flex-wrap: wrap;
	margin: 16px 0;
}

.own-review-bar-grid {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 12px;
	align-items: center;
}

.own-review-bar-wrapper {
	width: 50%;
}

.own-review-bar-total-reviews {
	font-size: .875rem;
	color: #333;
	line-height: 1.4;
	font-weight: 500;
	margin-bottom: 32px;
}

.own-review-bar__label {
	font-size: .875rem;
	color: #252525;
	line-height: 22px;
	font-weight: 500;
	margin-top: 16px;
	margin-bottom: 2px;
	display: block;
}

.own-review-bar {
	background: #F4EFDC;
	border-radius: 6px;
	height: 10px;
	grid-area: 1 / 1 / 2 / 5;
	overflow: hidden;
}

.own-review-bar__fill {
	background: #EAC545;
	background: linear-gradient(to right, rgb(250 216 96) 0, rgb(192 168 79) 100%);
	border-radius: 6px;
	height: 10px;
}

.own-review-bar__value {
	grid-area: 1 / 5 / 2 / 6;
	line-height: 1;
	font-weight: 600;
	font-size: .94rem;
}

.how-reviews-work-block {
	display: flex;
	align-items: center;
	gap: 24px;
	padding-bottom: 2px;
}

.own-review-modal-badge-wrapper {
	display: flex;
	gap: 24px;
}


.own-reviews-block-headline {
	font-family: var(--font-primary);
	font-size: 1.728rem;
	font-weight: 400;
	color: #252525;
	margin-bottom: 16px;
}

.own-reviews-block-subheadline {
	font-family: var(--font-primary);
	font-size: 1rem;
	font-weight: 400;
	margin-bottom: 0;
	margin-top: 16px;
}

.own-review-badge-simple {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 12px 0 4px;
}

.own-review-badge-simple__score {
	background: #818181;
	background: linear-gradient(180deg, #F8DA70 0%, #F4D04C 100%);
	border-radius: 6px;
	padding: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: .875rem;
	font-weight: 600;
	height: 34px;
}

.own-review-dot {
	width: 2px;
	min-width: 2px;
	height: 2px;
	background: #252525;
	border-radius: 50%;
}

.own-review-badge-simple__reviews {
	font-size: .75rem;
	color: #505050;
}

.own-review-check-icon--simple {
	min-width: 10px;
	min-height: 10px;
	filter: invert(43%) sepia(17%) saturate(2619%) hue-rotate(47deg) brightness(100%) contrast(82%);
	margin-bottom: -3px;
	margin-left: 2px;
}

.own-review-badge-simple__stars--label {
	font-size: .938rem;
	color: #1D2939;
	font-weight: 500;
	line-height: 1.2;
}

.own-review-badge-simple-content {
	display: flex;
	flex-direction: column;
}

.read-all-reviews-btn-wrapper {
	display: flex;
	justify-content: flex-start;
	margin-top: 20px;
}

.new-object-label--so {
	margin-top: 12px;
	margin-bottom: -4px;
}

.own-review-badge-simple--so {
	margin: 0 0 11px 0;
}

.star-black {
	filter: brightness(0);
}

.align-self-end {
	align-self: flex-end;
}

.review-badge-listing {
	position: absolute;
	right: 20px;
	top: 20px;
	z-index: 101;
}

.review-badge-listing > .own-reviews-block-section {
	margin-top: 0;
}

.review-badge-listing.mobile {
	display: none;
}

.own-review-comment__show-translation {
	color: #337AB7;
	cursor: pointer;
	font-size: .75rem;
	line-height: 1.4;
}

.own-review-comment__show-translation:hover,
.own-review-comment__show-translation:focus,
.own-review-comment__show-translation:active {
	color: #337AB7;
}

.how-reviews-work-wrapper {
	display: flex;
	align-items: center;
	gap: 6px;
}

.how-reviews-work-headline {
	color: #598230;
	font-size: .875rem;
	font-weight: 500;
	text-decoration: underline;
	cursor: pointer;
	line-height: 1.4;
	margin: 0;
}
.how-reviews-work-icon {
	min-height: 22px;
	min-width: 22px;
}

.own-review-comment__translated-label {
	color: #888;
	font-size: .75rem;
	line-height: 1.4;
	display: inline-flex;
	align-items: center;
	gap: 4px;
}

.own-review-translation-wrapper {
	display: flex;
	align-items: center;
	gap: 4px;
}

.how-reviews-work-headline:hover {
	color: #598230;
	text-decoration: underline;
}

.how-reviews-work-block-part {
	margin-top: 16px;
}

.own-review-modal-filter-block {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	border-radius: 6px;
	background: #F2F2F2;
	padding: 10px 11px;
}

.own-review-modal-filter-block--headline {
	font-size: 1.125rem;
	font-weight: 500;
	line-height: 1.4;
	margin-top: 40px;
}

.own-review-modal-filter-select-block {
	display: flex;
	align-items: center;
	gap: 8px;
}

.own-review-modal-filter-block-info {
	color:#0C111D;
	font-size: .875rem;
	margin: 0;
}

.reviews-filter-label {
	color: #101828;
	font-size: .875rem;
	margin: 0;
}

.reviews-filter {
	width: auto;
	padding: 2px 12px;
	height: 37px;
	line-height: 1;
	margin: 0;
	border: 1px solid #D0D5DD;
	border-radius: 6px;
}

.read-all-reviews-btn {
	background: none;
	border: 1px solid #333;
	border-radius: 6px;
	padding: 12px 16px;
	line-height: 1.4;
	font-size: .938rem;
	font-weight: 500;
}

.my-mfp-zoom-in.mfp-ready .zoom-anim-dialog-reviews {
	background: #fff;
	padding: 16px;
	border-radius: 6px 0 0 6px;
	right: 0;
	animation: all .3s ease-in-out;
}

.own-review-modal-overlay {
	position: fixed;
	top: 0;
	right: 18px;
	bottom: 0;
	width: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 999;
}

.own-review-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	width: 40%;
	background: #FFF;
	z-index: 1000;
	padding: 0 15px 25px;
	overflow: auto;
	box-shadow: 0 1px 25px 0 rgba(0, 0, 0, .30);
}

.review-badge-header {
	align-self: flex-end;
}

.review-badge-header-top {
	position: absolute;
	right: 15px;
	top: 0;
}

.listing-main-stars-container-review {
	display: flex;
}

.review-modal-on-listing > .review-block-header {
	display: none;
}

.d-none {
	display: none;
}

.own-review-modal--opening {
	animation: slideFromRight .3s cubic-bezier(0.39, 0.575, 0.565, 1);
}

.own-review-modal--closing {
	animation: slideFromRight .3s cubic-bezier(0.39, 0.575, 0.565, 1) reverse backwards;
}

.own-review-modal-overlay--opening {
	animation: overlayAnimation .3s cubic-bezier(0.39, 0.575, 0.565, 1);
}

.own-review-modal-overlay--closing {
	animation: overlayAnimation .3s cubic-bezier(0.39, 0.575, 0.565, 1) reverse backwards;
}

.own-review-collecting-modal-overlay--opening {
	animation: overlayAnimation .2s cubic-bezier(0.39, 0.575, 0.565, 1);
}

.own-review-collecting-modal--opening {
	animation: overlayAnimation .2s cubic-bezier(0.39, 0.575, 0.565, 1);

}

.own-review-collecting-modal-overlay--closing {
	animation: overlayAnimation .2s cubic-bezier(0.39, 0.575, 0.565, 1) reverse backwards;
}

.own-review-collecting-modal--opening--closing {
	animation: overlayAnimation .2s cubic-bezier(0.39, 0.575, 0.565, 1) reverse backwards;
}

@-webkit-keyframes overlayAnimation {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@-moz-keyframes overlayAnimation {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@-ms-keyframes overlayAnimation {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@keyframes overlayAnimation {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@-webkit-keyframes slideFromBottom {
	0% {
		top: 100%;
	}

	100% {
		top: 0;
	}
}

@-moz-keyframes slideFromBottom {
	0% {
		top: 100%;
	}

	100% {
		top: 0;
	}
}

@-ms-keyframes slideFromBottom {
	0% {
		top: 100%;
	}

	100% {
		top: 0;
	}
}

@keyframes slideFromBottom {
	0% {
		top: 100%;
	}

	100% {
		top: 0;
	}
}


@-webkit-keyframes slideFromRight {
	0% {
		right: -100%;
	}

	100% {
		right: 0;
	}
}

@-moz-keyframes slideFromRight {
	0% {
		right: -100%;
	}

	100% {
		right: 0;
	}
}

@-ms-keyframes slideFromRight {
	0% {
		right: -100%;
	}

	100% {
		right: 0;
	}
}

@keyframes slideFromRight {
	0% {
		right: -100%;
	}

	100% {
		right: 0;
	}
}

.review-modal-open {
	overflow: hidden;
	overflow: hidden;
	position: fixed;
	width: 100%;
	padding-right: 17px;
}

.own-review-collecting-modal-overlay {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1001;
}

.own-review-collecting-modal {
	position: fixed;
	top: 50%;
	background: #FFF;
	z-index: 1002;
	padding: 0 30px;
	overflow: auto;
	left: 50%;
	transform: translate(-50%, -50%);
	max-width: 600px;
	max-height: 600px;
	border-radius: 6px;
	box-shadow: 0 4px 25px 0 rgba(0, 0, 0, .30);
}

.own-review-collecting-modal__header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: sticky;
	top: -1px;
	background: #FFF;
	padding: 20px 0 10px;
}

.own-review-collecting-modal__header-title {
	font-family: var(--font-primary);
	font-size: 1.5rem;
	color: #252525;
	font-weight: 600;
	line-height: 24px;
	margin-right: 20px;
}

.own-review-modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;

}

.own-review-collecting-modal__content {
	margin-bottom: 20px;
}

.own-review-collecting-modal__content-subtitle {
	color:#0C111D;
	font-size: 0.938rem;
	font-weight: 600;
	line-height: 24px;
	margin: 0;
}

.own-review-modal-close-btn {
	padding: 0;
	background: none;
	border: none;
	border-radius: 50%;
	width: 35px;
	min-width: 35px;
	height: 35px;
	padding-top: 6px;
}

.own-review-collecting-modal-close-btn {
	padding: 0;
	background: none;
	border: none;
	width: 35px;
	min-width: 35px;
	height: 35px;
	padding-top: 12px;
}

.own-review-collecting-modal__divider {
	width: 100%;
    height: 30px;
    background: #fff;
    position: sticky;
    bottom: -2px;
}

.review-comment-modal {
	margin-bottom: 16px;
}

.how-reviews-work-block-part-modal {
	margin-top: 7px;
}

/* Listing Item Layout Style
------------------------------------- */
.listing-controls.listing-controls--flex {
	display: flex;
	justify-content: flex-end;
	flex-direction: row;
	align-items: end;
}


.listing-items-container .listing-items-found {
	font-weight: 600;
}

.listing-items-container .listing-items-found > strong {
	color: #252525;
}

.listing-items-grid__title {
	font-size: 1rem;
	font-weight: 500;
	margin-bottom: 15px
}

.listing-lazy-images .keen-prev:before,
.listing-lazy-images .keen-next:before,
.listing-items-carousel .keen-prev:before,
.listing-items-carousel .keen-next:before,
.grid-thumbnail .keen-prev:before,
.grid-thumbnail .keen-next:before {
	margin: 10px;
}

.listing-items-carousel .keen-disabled,
.listing-items-carousel:not(.keen-initialized) .keen-disabled,
.grid-thumbnail .keen-disabled {
	opacity: 0;
	display: none;
}

.grid-thumbnail--regions.grid-thumbnail .keen-disabled {
	display: block;
	opacity: .4;
}

.listing-items-carousel .keen-prev:before,
.listing-items-carousel .keen-next:before,
.grid-thumbnail .keen-prev:before,
.grid-thumbnail .keen-next:before {
	filter: invert(0);
}

.grid-thumbnail .keen-prev,
.listing-items-carousel .keen-prev {
	left: -55px;
	transform: translateY(-50%) rotate(180deg);
	opacity: 1;
	outline: 0;
	background: #fff;
}

.grid-thumbnail .keen-next,
.listing-items-carousel .keen-next {
	right: -55px;
	transform: translate(0, -50%) rotate(0deg);
	opacity: 1;
	outline: 0;
	background: #fff;
}

.block-featured .listing-items-carousel .keen-prev,
.block-featured .listing-items-carousel .keen-next {
	top: calc(50% - 12px);
}

.block-featured .listing-item-container .listing-item-image .keen-prev,
.block-featured .listing-item-container .listing-item-image .keen-next {
	opacity: 0;
	display: none;
}

.block-featured .listing-item-container .listing-item-image:hover .keen-prev:not(.keen-disabled),
.block-featured .listing-item-container .listing-item-image:hover .keen-next:not(.keen-disabled) {
	opacity: 1;
	display: block;
}

.grid-thumbnail--regions.grid-thumbnail .keen-prev {
	left: auto;
	right: 50px;
	top: -80px;
}

.grid-thumbnail--regions.grid-thumbnail .keen-next {
	left: auto;
	right: 0;
	top: -80px;
}

.grid-thumbnail--reviews.grid-thumbnail .keen-prev {
	left: auto;
	right: 50px;
	top: -10px;
	z-index: 1;
}

.grid-thumbnail--reviews.grid-thumbnail .keen-next {
	left: auto;
	right: 0;
	top: -10px;
	z-index: 1;
}

.grid-thumbnail--reviews.grid-thumbnail .keen-disabled {
	display: block;
	opacity: .4;
}

.listing-lazy-images .keen-prev,
.listing-lazy-images .keen-next {
	border: 0;
}

.listing-items-grid .listing-item-container.list-layout .listing-item {
	flex-direction: column;
	height: auto;
	display: block;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-image {
	flex: 100%;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner .listing-item-inner-top {
	max-width: 100%;
	white-space: normal;
	overflow: visible;
	margin-top: 4px;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner .listing-item-inner-top--so {
	width: 100%;
	margin-bottom: -6px;
}


.listing-items-grid .listing-item-container.list-layout .listing-item-inner .listing-item-title {
	font-weight: 500;
	font-size: 1rem;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 0;
	color: #333;
	line-height: 1.6;
	margin-top: 10px;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner .listing-item-title--so {
	display: inline-block;
	width: unset;
	max-width: 80%;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner-top .listing-item-location,
.listing-items-grid .listing-item-container.list-layout .listing-item-inner-top .listing-item-price-info {
	display: block;
	font-size: .875rem;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner-top .listing-item-location {
	margin-top: -10px;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner {
	padding: 10px 15px;
}

.listing-items-similar.listing-items-grid .listing-item-container.list-layout .listing-item-inner {
	padding: 0;
}

.listing-items-reviews.listing-items-grid .listing-item-container.list-layout .listing-item-inner {
	padding: 16px 0 0 0;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner-middle {
	display: flex;
}

.listing-items-carousel--spo .listing-item-content {
	margin-top: -20px;
}

.listing-items-carousel--spo .listing-item-content.new-object {
	margin-top: -8px;
}

.grid-thumbnail--reviews .keen-prev:before, .grid-thumbnail--reviews .keen-next:before {
	height: 18px;
	background-size: 18px;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner-middle .main-features {
	width: 100%;
	column-count: auto;
	flex: 0 0 140px;
	margin-top: 0;
}

.listing-item--reviews,
.listing-item-content--reviews,
.listing-item-inner--reviews,
.listing-item-inner--reviews > .own-review-comment-wrapper {
	height: 100%!important;
}

.search-result-sort-filter {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	gap: 20px;
	margin-bottom: 15px;
}

.listing-item-inner--reviews > .own-review-comment-wrapper {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.listing-item-inner--reviews > .own-review-comment-wrapper  > .own-review-comment-text {
	margin-bottom: auto;
}

.listing-item-container--reviews {
	height: auto!important;
	z-index: 1;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner-middle .main-features + .main-features {
	flex: 100%;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-image .listing-item-price {
	color: #fff;
	position: absolute;
	bottom: 0;
	left: 0;
	font-size: 1.25rem;
	line-height: 1.2;
	display: block;
	width: 100%;
	z-index: 999;
	padding: 5px 15px 10px 15px;
	margin-bottom: 0;
	margin-top: 0;
	text-shadow: 0 1px 5px rgba(0, 0, 0, .4);
}

.listing-items-grid .listing-item-container.list-layout .listing-item-image .listing-item-price > a {
	color: #fff;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-image .listing-item-price .price-from {
	font-size: 1rem;
	display: block;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-image .listing-item-price .price-value {
	font-size: 1.25rem;
	font-weight: 600;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-image .listing-item-price .price-per-week {
	font-weight: normal;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner-bottom {
	position: relative;
	bottom: 0;
	right: 0;
	text-align: center;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner-bottom .button {
	width: 100%;
	height: auto;
}

.listing-items-similar.listing-items-grid .listing-item-container {
	padding: 0 10px;
}

.listing-item-container.list-layout--loading:before {
	content: '';
	position: absolute;
	top: 0;
	left: 15px;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, .75);
	width: calc(100% - 30px);
	height: 100%;
	z-index: 101;
}

.listing-item-container.list-layout--loading:after {
	content: '';
	background: url('/assets/img/svg/spinner.svg') no-repeat center center;
	width: 100px;
	height: 53px;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 101;
}

.listing-item-container.list-layout .listing-item {
	margin-bottom: 25px;
	box-shadow: 0 2px 7px rgba(0, 0, 0, .06);
	position: relative;
	border-radius: 6px;
	display: flex;
	height: 280px;
	background-color: #fff;
	border: 1px solid #ddd;
}

.listing-item-container.list-layout .listing-item:hover {
	box-shadow: 0 0 8px 0 rgba(0, 0, 0, .2);
}

.listing-item-container.list-layout .listing-item:before {
	display: none;
}

.listing-item-container.list-layout .listing-item img {
	border-radius: 0;
}

.listing-item-container.list-layout .listing-item-content {
	flex: 5;
	position: relative;
	bottom: 0;
	left: 0;
	padding: 0;
	width: 100%;
	z-index: 50;
	box-sizing: border-box;
}

.listing-item-content__anchor {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
}

.listing-item-container.list-layout .listing-item-image {
	flex-basis: 400px;
	height: 280px;
	overflow: hidden;
	position: relative;
	min-height: 270px;
}

.listing-item__reviews {
	position: absolute;
	top: 10px;
	right: 10px;
	z-index: 101;
}

.listing-item-container.list-layout .listing-item-image a {
	position: relative;
	z-index: 1;
	display: block;
	height: 100%;
}

.villa-link-on-image {
	width: 100%;
	height: 100%;
}

.listing-item-container.list-layout .listing-item-image .listing-item-image-anchor {
	width: 100%;
	position: absolute;
	width: 100%;
	z-index: 3;
}

.listing-item-image--grid .listing-item-price {
	background: linear-gradient(180deg, transparent, rgba(0,0,0,.7));
}

.listing-item-container.list-layout .listing-item-inner {
	padding: 20px 25px;
	height: 100%;
	position: relative;
}
.listing-item-container.list-layout .listing-item-inner .listing-item-inner-top {
	max-width: 80%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price {
	position: absolute;
	right: 25px;
	top: 130px;
	font-size: .75rem;
	color: #252525;
	text-align: right;
	line-height: 1.2;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price--specific-price {
	top: 184px;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price--with-discount {
	top: 142px;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price .price-from,
.listing-item-container.list-layout .listing-item-inner-top .listing-item-price .price-per-day,
.listing-item-container.list-layout .listing-item-inner-top .listing-item-price .price-per-week {
	display: block;
	font-size: .875rem;
	color: #252525;
	font-weight: 400;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price .price-from--smaller {
	font-size: .875rem;
	line-height: 1.4;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price .price-discount,
.booking-process .price-discount {
	color: #8a8a8a;
	font-weight: 500;
	font-size: .938rem;
	display: block;
}

.listing-item-container .listing-item-price .price-discount del,
.booking-process .price-discount del {
	color: red;
}

.listing-item-container .listing-item-price .price-discount del > span,
.booking-process .price-discount del > span {
	color: #8a8a8a;
}

.booking-process .price-discount {
	text-align: right;
	display: block;
	line-height: 23px;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price.listing-item-price--sold-out > span {
	font-size: 1.25rem;
	letter-spacing: 0;
}

.listing-item-container.list-layout .listing-item-inner-top > span a {
	color: #333;
	font-size: 1.25rem;
	font-weight: 500;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price > span {
	color: #333;
	font-size: 1.44rem;
	font-weight: 600;
	display: block;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price > span i {
	height: 28px;
	width: 38px;
	display: inline-block;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price > span i:before {
	font-size: 15px;
	color: #48a9ea;
	position: relative;
	top: -3px;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price .listing-item-old-price {
	display: block;
	font-size: 18px;
	color: #8a8a8a;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price .listing-item-old-price del {
	color: red;
}

.listing-item-container.list-layout .listing-item-inner-top .listing-item-price .listing-item-old-price del > span {
	color: #8a8a8a;
	font-weight: 600;
}

.listing-item-container.list-layout .listing-item-inner-middle .main-features {
	width: 400px;
	list-style: none;
	margin: 10px 0;
	padding-left: 0;
	column-count: 2;
	column-gap: 0;
}

.listing-item__spacer {
	display: block;
	width: 70px;
	height: 1px;
	background: #dedede;
	margin: 15px 0 10px 0;
}

.listing-item-container.list-layout .listing-item-inner-middle .main-features li {
	display: block;
	margin-bottom: 4px;
	font-size: .875rem
}

.listing-item-inner-middle--search-result {
	font-size: 1rem;
}

.listing-outdoor-pool--elipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 200px;
}

.listing-item-title-wrapper,
.listing-item-title-elipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.listing-item-title--search-result {
	display: flex;
	gap: 8px;
}

.flexible-date-label-wrapper {
	position: absolute;
    right: 25px;
	top: 151px;
}

.flexible-date-label-wrapper--with-discount {
	top: 107px;
}

.flexible-date-label-wrapper.mobile {
	display: none;
	position: static;
	margin-bottom: 10px;
	margin-top: 15px;
}

.flexible-date-label {
	font-size: .75rem;
	background: #ffd95029;
	border-radius: 5px;
	padding: 3px 6px;
	line-height: normal;
	border: 1px solid #ccb153;
	color: #101828;
	font-weight: 600;
}

.listing-items-grid .listing-item-container.list-layout .listing-item-inner-middle .main-features li {
	font-size: .75rem;
}

.listing-item-container.list-layout .listing-item-inner-middle .main-features li .featured-text {
	font-weight: normal;
}

.listing-item-container.list-layout .listing-item-inner-bottom {
	text-align: right;
	position: absolute;
	bottom: 15px;
	right: 15px;
}

.listing-item-container.list-layout .listing-item-inner-bottom > a > i:before {
	content: '';
	display: inline-block;
	width: 15px;
	height: 15px;
	background-image: url('/assets/img/svg/email.svg');
	background-size: 15px 15px;
	background-repeat: no-repeat;
	position: relative;
	top: 2px;
	transition: all .2s ease-in-out;
}

.listing-item-container.list-layout .listing-item-inner h3 {
	color: #252525;
	font-weight: 400;
	font-size: 1.728rem;
	margin-bottom: 0;
}

.listing-stars {
	display: inline-block;
	position: relative;
	top: 0;
}

.listing-start--so {
	display: inline-block;
	top: -12px;
	margin-left: 5px;
}

.listing-stars .listing-stars__icon--star:before {
	content: '';
	display: inline-block;
	background-image: url('/assets/img/svg/star.svg');
	width: 13px;
	height: 13px;
	background-size: 13px 13px;
	background-repeat: no-repeat;
	margin-left: -5px;
}

.listing-item-container.list-layout .listing-item-details {
	position: absolute;
	bottom: 25px;
	right: 35px;
	left: auto;
	top: auto;
	padding: 0;
	width: auto;
	z-index: 50;
	color: #888;
	box-sizing: border-box;
	font-weight: 500;
	font-size: 14px;
}

.date-with-underline-bold {
	text-decoration: underline;
	font-weight: 600!important;
}

.listing-items-similar .listing-item-container.list-layout .listing-item {
	box-shadow: none;
	border: none;
	margin-bottom: 0;
}

.listing-items-similar .listing-item img {
	height: 270px;
}

.listing-items-similar .listing-item-container.list-layout .listing-item img {
	border-radius: 3px;
}

.list-layout-not-available-content {
	color: #252525;
	font-size: 15px;
	border-top: 1px solid #eaeaea;
	padding-top: 10px;
	margin-bottom: 20px;
}

.list-layout-not-available-content i {
	margin-right: 5px;
}

.listing-destinations {
	border-bottom: 1px solid #eee;
}

.listing-destinations {
	margin-bottom: 30px;
}

.listing-top-content--borderless {
	border-bottom: 0;
}

.listing-top-content .listing-top-title {
	font-size: 2.488rem;
	font-weight: 600;
	color: #252525;
	letter-spacing: 1px;
	margin: 15px 0 10px 0;
	line-height: 1.4;
}

.listing-top-content .listing-top-subtitle,
.listing-top-content .heading-3 {
	margin-top: 15px;
	font-size: 1.25rem;
	line-height: 1.3;
	font-family: var(--font-primary);
	font-weight: 600;
}

.listing-top-text__content-shrink {
	max-height: 115px;
	overflow: hidden;
	position: relative;
}

.listing-top-text__content-shrink:after {
	content: '';
	position: absolute;
	bottom: -5px;
	left: -15px;
	right: -15px;
	box-shadow: inset white 0 -20px 20px;
	width: 100%;
	height: 25px;
}

.listing-top-text__content-shrink--collapsed:after {
	content: none;
}

.listing-top-text__content-shrink--collapsed {
	max-height: 100%;
}

.listing-top-text__read-more.button.flat {
	cursor: pointer;
	font-weight: 500;
	color: var(--color-anchor);
	margin-top: 5px;
	display: inline-flex;
	align-items: center;
	padding: 6px 10px;
}

.listing-top-text__read-more-arrow {
	margin-left: 6px;
}

.listing-top-text__content-shrink--collapsed + .listing-top-text__read-more .listing-top-text__read-more-arrow {
	transform: rotate(180deg);
}

.listing-top-text__read-more-arrow path {
	stroke: var(--color-anchor);
}

.listing-top-content .listing-top-text,
.listing-top-content p {
	font-size: .875rem;
}

.listing-top-content .listing-top-text br,
.listing-top-content p br {
	margin-bottom: 15px;
	display: block;
	content: '';
}

.listing-top-content .listing-top-text:last-child {
	margin-bottom: 0;
}

/* Listing Item
------------------------------------- */
.listing-item-container {
	display: block;
	height: 100%;
	width: 100%;
	margin-bottom: 30px;
}

.block-featured .listing-item-container,
.listing-items-similar .listing-item-container{
	margin-bottom: 0;
}


.listing-item-container.listing-item--grid {
	margin-bottom: 0;
}

.listing-item-container.listing-item--grid .listing-item-price {
	color: #fff;
	position: absolute;
	bottom: 5px;
	right: 15px;
	margin: 0;
	font-weight: 600;
	font-size: 24px;
	text-align: right;
	line-height: 20px;
}

.listing-item-container.listing-item--grid .listing-item-price span {
	display: block;
	font-size: 14px;
	color: #fff;
}

.listing-item-container.listing-item--grid .listing-item-price .listing-item-old-price {
	font-size: 15px;
	display: block;
}

.listing-item-container.listing-item--grid + .listing-item-bottom {
	border: 1px solid #e6e6e6;
	background: #f9f9f9;
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
}

.listing-item-container.listing-item--grid + .listing-item-bottom ul {
	list-style-type: none;
	padding: 0;
	margin: 0;
}

.listing-item-container.listing-item--grid + .listing-item-bottom .main-features ul {
	display: flex;
	justify-content: space-around;
	padding: 20px 20px 10px 20px;
}

.listing-item-container.listing-item--grid + .listing-item-bottom .main-features ul li {
	flex: 1;
	text-align: center;
	color: #252525;
	text-transform: uppercase;
	font-size: 13px;
	font-weight: 500;
}

.listing-item-container.listing-item--grid + .listing-item-bottom .main-features ul li i {
	display: block;
	height: 37px;
}

.listing-item-container.listing-item--grid + .listing-item-bottom .main-features ul li i:before {
	font-size: 30px;
	color: #252525;
}

.listing-item-container.listing-item--grid + .listing-item-bottom .main-features ul li i.icon-people-linear:before {
	font-size: 32px;
}

.listing-item-container.listing-item--grid + .listing-item-bottom .actions {
	border-top: 1px solid #e6e6e6;
	background: #fff;
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
	padding: 15px;
	display: flex;
	justify-content: space-evenly;
	margin-top: 15px;
}

.listing-item-container.listing-item--grid + .listing-item-bottom .actions a.button {
	padding: 5px 25px;
	min-width: 140px;
	text-align: center;
}

.listing-item-container.listing-item--grid + .listing-item-bottom .actions a.action-book {
	line-height: 29px;
}

section.fullwidth .listing-item-container,
.fs-content .listing-item-container {
	background-color: #fff;
}

section.fullwidth .listing-item-container.list-layout {
	box-shadow: none;
}

.listing-item {
	display: block;
	position: relative;
	background-size: cover;
	background: #ccc no-repeat 50%;
	height: 265px;
	z-index: 100;
}

#snippet-searchResultsControl-searchResults .listing-item {
	z-index: 0;
}

.listing-item img {
	object-fit: cover;
	height: 280px;
	width: 100%;
	border-radius: 4px 4px 0 0;
}

.listing-item:before {
	content: "";
	top: 0;
	position: absolute;
	height: 100%;
	width: 100%;
	z-index: 9;
	background: linear-gradient(to top, rgba(35,35,37,.9) 0%, rgba(35,35,37,.45) 35%, rgba(22,22,23,0) 60%, rgba(0,0,0,0) 100%);
	border-radius: 4px 4px 0 0;
	opacity: 1;
}

.listing-item-content {
	position: absolute;
	bottom: 8px;
	left: 0;
	padding: 0 15px;
	width: 100%;
	z-index: 50;
	box-sizing: border-box;
}

.listing-item--grid .listing-item-content h2 {
	font-weight: 400;
	font-size: 1.728rem;
	color: #fff;
	text-shadow: 1px 1px 0 rgba(0, 0, 0, .33);
	padding-right: 100px;
	margin-bottom: 5px;
}

.listing-item--grid .listing-item-content h2 + span {
	color: #f7f7f7;
	font-size: .75rem;
	display: block;
	padding-right: 80px;
	line-height: 1.4;
}

.listing-item-content h3 {
	color: #fff;
	font-size: 1.25rem;
	position: relative;
	font-weight: 600;
	margin: 0;
	line-height: 1.4;
	font-family: var(--font-primary);
}

.listing-item-container.list-layout span.tag,
.listing-item-content span.tag {
	text-transform: uppercase;
	font-size: 9px;
	letter-spacing: 2.5px;
	background: rgba(255,255,255,.3);
	border-radius: 50px;
	padding: 4px 16px;
	line-height: 20px;
	color: #fff;
	font-weight: 400;
	margin-bottom: 9px;
}

.listing-item-container.list-layout span.tag {
	position: absolute;
	bottom: 20px;
	left: 20px;
	background: #666;
	margin: 0;
	z-index: 10;
	line-height: 14px;
	padding: 7px 16px;
	margin-right: 20px;
	text-align: center;
}

.listing-lead-block {
	background: #fff;
	margin-bottom: 25px;
	padding: 15px;
	display: flex;
	align-items: center;
	border: 1px solid #dedede;
	border-radius: 3px;
}

.listing-lead-block label {
	margin-bottom: 0;
}

.listing-lead-block .listing-lead-block-image {
	float: left;
	border-radius: 50%;
	width: 80px;
	height: 80px;
}

.listing-lead-block .listing-lead-block-phone {
	color: #333;
	float: left;
	line-height: 1.4;
	position: relative;
	margin-left: 20px;
	padding-left: 50px;
}

.map-action-header {
	display: none;
}

.listing-lead-block .listing-lead-block-phone:before {
	content: '';
	display: inline-block;
	width: 32px;
	height: 32px;
	background-image: url('/assets/img/svg/phone.svg');
	background-size: 32px 32px;
	background-repeat: no-repeat;
	vertical-align: middle;
	margin-right: 8px;
	font-weight: normal;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	left: 0;
}

.listing-lead-block .listing-lead-block-phone strong {
	font-size: 0.938rem;
	display: block;
}

.listing-lead-block .listing-lead-block-left {
	margin-right: 35px;
	border-right: 1px solid #ccc;
	padding-right: 35px;
}

.listing-lead-block .listing-lead-block-right {
	flex: 1;
}

.listing-lead-block .listing-lead-block-right .listing-lead-block-form input {
	display: inline-block;
	width: 300px;
	height: 44px;
	margin-right: 10px;
}

.listing-lead-block .listing-lead-block-right .listing-lead-block-form > p {
	margin-bottom: 0;
}

.listing-capacity__icon {
	display: inline-block;
	vertical-align: middle;
}

.listing-capacity__icon:before {
	margin-right: 8px;
	content: '';
	display: inline-block;
	width: 22px;
	height: 22px;
	background-size: 22px 22px;
	background-repeat: no-repeat;
}

.listing-capacity__icon--group:before {
	background-image: url('/assets/img/svg/people.svg');
}

.listing-capacity__icon--bed:before {
	background-image: url('/assets/img/svg/bed-2.svg');
}

.listing-capacity__icon--concierge:before {
	background-image: url('/assets/img/svg/concierge.svg');
}

.listing-main-features__icon--bathroom:before,
.listing-capacity__icon--bathroom:before {
	background-image: url('/assets/img/svg/bath.svg');
}

.listing-capacity__icon--pool:before {
	background-image: url('/assets/img/svg/swimming-pool.svg');
}

.listing-capacity__icon--home:before {
	background-image: url('/assets/img/svg/home-2.svg');
}

.listing-capacity__icon--pets-allowed:before {
	background-image: url('/assets/img/svg/pets-allowed.svg');
}

.listing-capacity__icon--pets-not-allowed:before {
	background-image: url('/assets/img/svg/pets-not-allowed.svg');
}

.listing-item-location {
	font-size: .875rem;
}

.search-listing-block {
	display: flex;
	width: 100%;
	margin-top: 20px;
	background: #fff;
	padding: 20px;
	box-shadow: 1px 1px 20px 1px rgba(143, 143, 143, .28);
	border-radius: 4px;
}

.search-listing-block input {
	margin-bottom: 0;
}

.search-listing-block__item {
	margin-right: 12px;
}

.search-listing-block__item--calendar {
	width: 300px;
}

.search-listing-block__icon {
	position: absolute;
	left: 15px;
	top: 50%;
	transform: translateY(-50%);
}

a.button.search-listing-anchor-filter,
a.button.search__anchor-filter {
	padding-left: 40px !important;
	overflow: visible
}

a.search-listing-anchor-filter:after,
a.button.search__anchor-filter:after {
	content: none;
}

.search-listing-block__filters-container {
	display: flex;
	justify-content: flex-end;
}

.search-notice {
	display: none;
	border-radius: 50%;
	background: #252525;
	width: 20px;
	height: 20px;
	position: absolute;
	top: -5px;
	right: -5px;
	color: #fff;
	justify-content: center;
	align-items: center;
	font-size: .75rem;
	z-index: 10;
	font-weight: 600;
}

.search-notice--visible {
	display: flex;
}

.search-listing-block__note {
	color: #333;
	margin: 10px 0 0;
	font-size: .875rem;
}

.search-listing-block .row input {
	margin-bottom: 0;
}

.search-listing-block a.button.border.more-filters {
	background-color: #fff;
	height: 51px;
}

.search-listing-anchor-filter {
	height: 51px;
}

.search-listing-block .more-filters:hover:after {
	filter: invert(100%);
}

.search-listing-block .button.filter-results {
	vertical-align: top;
	height: 51px;
	margin-left: 10px;
	padding: 9px 30px;
	width: auto;
	font-size: 0.938rem;
}

.search-listing-block a.button.border.more-filters:hover {
}

.search-listing-block .input-with-icon.panel-dropdown {
	width: 100%;
}

.search-listing-block .more-filters + .panel-dropdown-content {
	left: auto;
	right: -15px;
	width: 1180px;
	top: calc(100% + 40px);
}

.listing-item-container.compact .listing-item-content span.tag {
	background-color: #666;
}

.rating:after {
	content: '.';
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}

/* Badges */
.listing-item {
    overflow: hidden;
}

.listing-load-more {
	margin: 40px auto 50px auto;
	text-align: center;
}

.listing-pagination {
	margin: 20px 0 40px 0;
	padding: 0 20px;
	background: #fff;
	display: flex;
	justify-content: space-between;
	border-radius: 3px;
}

.listing-pagination-items--show-mobile {
	display: none;
}

.listing-pagination-item-content {
	display: none;
}

.listing-pagination-item {
	display: block;
	float: left;
	width: 30px;
	height: 30px;
	font-weight: 500;
	text-align: center;
	line-height: 28px;
	border: 1px solid transparent;
	margin: 0 3px;
	border-radius: 3px;
	color: #252525;
}

.listing-pagination-item--active,
.listing-pagination-item--active:hover {
	background: #efefef;
}

.listing-pagination-item:hover {
	background: #f4f4f4;
}

.listing-pagination-item--dots:hover {
	background: transparent;
}

.listing-pagination-page-mobile {
	display: none;
}

.listing-pagination-previous:before,
.listing-pagination-next:before {
	content: '';
	filter: invert(12%) sepia(66%) saturate(1226%) hue-rotate(205deg) brightness(94%) contrast(89%);
	display: inline-block;
	width: 11px;
	height: 11px;
	background-size: 11px 11px;
	background-repeat: no-repeat;
}

.listing-pagination-previous:before {
	background-image: url('/assets/img/svg/left-arrow.svg');
}

.listing-pagination-next:before {
	background-image: url('/assets/img/svg/right-arrow.svg');
}

.listing-pagination-previous,
.listing-pagination-next {
	line-height: 26px;
}

.load-more-spinner {
	display: none;
	background: url('/assets/img/loader/load-more.gif') no-repeat center center;
	width: 100px;
	height: 53px;
	margin: 0 auto;
}

.single-stickybar {
	position: fixed;
	display: flex;
	bottom: -63px;
	width: 100%;
	background: #fff;
	z-index: 999;
	padding: 10px;
	justify-content: space-between;
	transition: all 400ms;
	box-shadow: 0 0 6px rgba(0,0,0,.12);
}

.single-stickybar--space-around {
	justify-content: space-around;
}

.single-stickybar--single-villa {
	padding: 0 10px;
	bottom: -69px
}

.single-stickybar--visible {
	bottom: 0;
}

.single-stickybar .book-now {
	font-size: 16px;
}

.single-stickybar .book-now-scrollto,
.single-stickybar .send-inquiry {
	padding: 8px 10px;
	flex: 1 0 auto;
	text-align: center;
	margin: 0 5px;
}

.single-stickybar .book-now-scrollto {
	width: 50%;
}

/* USP Block
------------------------------------- */
.block-main-usps-container {
	display: flex;
	margin: 20px 0;
	padding: 50px 0;
	height: 350px;
}

.block-main-usps .main-usps__content {
	display: flex;
	flex-flow: column;
	justify-content: center;
	flex: 0 0 490px;
	margin-right: 120px;
}

.block-main-usps .usp-content__title {
	font-size: 2.6rem;
	line-height: 1.2;
	margin: 0 0 18px;
}

.block-main-usps .usp-content__subtitle {
	margin-bottom: 0;
	max-width: 380px;
}

.block-main-usps .main-usps__items {
	display: flex;
	flex: 2 1 0;
}

.block-main-usps .usp-item {
	display: flex;
	flex-direction: column;
	flex: 0 0 220px;
	justify-content: flex-start;
	margin: 50px 40px 0 0;
}

.block-main-usps .usp-item__button {
	text-align: center;
	margin: 50px 0 0;
	width: 100%;
}

.block-main-usps .usp-item__title {
	color: #252525;
	font-family: var(--font-primary);
	font-size: 16px;
	font-weight: 600;
	position: relative;
	line-height: 1.4;
	letter-spacing: 1px;
	text-transform: uppercase;
	margin: 0 0 12px;
}

.block-main-usps .usp-item__text {
	color: #252525;
	margin: 0;
}

.block-main-usps .usp-item__icon-container {
	width: 40px;
	height: 40px;
	max-width: 40px;
	margin-bottom: 25px;
	position: relative;
}

.block-main-usps .usp-item__icon {
	position: absolute;
}

.block-main-usps .usp-item__icon--verified {
	top: 0;
}

.side-usps {
	list-style: none;
	padding-left: 0;
}

.side-usps .side-usp {
	display: flex;
	margin-bottom: 12px;
}

.side-usps .side-usp__icon {
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	background: #73C850;
	padding: 6px;
	border-radius: 50%;
	margin-right: 10px;
	width: 24px;
	height: 24px;
	display: inline-block;
}
.side-usps .side-usp__icon:before {
	content: '';
	filter: invert(100%);
	display: inline-block;
	width: 12px;
	height: 12px;
	background-image: url('/assets/img/svg/check.svg');
	background-size: 12px 12px;
	background-repeat: no-repeat;
	position: relative;
	top: -3px;
}

/* Sidebar blocks
------------------------------------- */
.side-company-details li {
	margin-bottom: 10px;
}

.side-company-details p.title {
	margin-bottom: 0;
	font-weight: 600;
}

.page-breadcrumb {
	color: #bbb;
	font-size: .875rem;
}

.page-breadcrumb--single-villa {
	font-size: .875rem;
	line-height: 1.2;
}

.page-breadcrumb__item {
	color: #585858;
}

.page-breadcrumb__item:hover,
.page-breadcrumb__item.active {
	color: #252525;
}

.page-breadcrumb__item.active {
	font-weight: 500;
}

.page-breadcrumb__item:after {
	content: ' - ';
	color: #666;
	width: 6px;
	height: 6px;
	display: inline-block;
	margin-left: 5px;
}

.page-breadcrumb__item:last-child:after {
	content: none;
}

/* Hubspot parts
------------------------------------- */
.hubspot-form .text-danger {
	font-size: 1rem;
	display: inline-block;
	position: relative;
	top: -10px;
}

.hubspot-form .help-block.text-danger {
	display: none;
}

.hubspot-form input.has-error,
.hubspot-form textarea.has-error,
.hubspot-inline-form input.has-error,
.hubspot-inline-form textarea.has-error,
.hubspot-form .flatpickr-input.has-error + input {
	border-color: red;
}

.flatpickr-wrapper .text-danger {
	display: none;
}

#snippet--lead-gen-form-response .hubspot-thankyou-message {
	font-size: 0.938rem;
	font-weight: 600;
	margin-top: 10px;
}

#snippet--contactus-form-response .hubspot-thankyou-message {
	background: #fff;
	padding: 15px;
}

input[type="number"].inquiry-form--input-pets-number {
	width: 80px;
}

.hubspot-thankyou-message .note {
	background: #f5f5f5;
	margin: 15px 0;
	padding: 15px;
	text-indent: -1px;
	padding-left: 46px;
}

.hubspot-thankyou-message .note:before {
	content: '';
	display: inline-block;
	width: 18px;
	height: 18px;
	background-image: url('/assets/img/svg/exclamation.svg');
	background-size: 18px 18px;
	background-repeat: no-repeat;
	position: absolute;
	margin-left: -27px;
	margin-top: 5px;
}

.hubspot-thankyou-message strong {
	color: #252525;
}

.price-includes-information ul li,
.price-not-included-information ul li {
	display: inline-block;
	margin-right: 10px;
}

.price-includes-information ul li:before {
	content: '';
	filter: invert(64%) sepia(38%) saturate(6675%) hue-rotate(105deg) brightness(96%) contrast(80%);
	display: inline-block;
	width: 14px;
	height: 14px;
	background-image: url('/assets/img/svg/check.svg');
	background-size: 14px 14px;
	background-repeat: no-repeat;
	margin-right: 8px;
	position: relative;
	top: 2px;
}

/* Loaders
------------------------------------- */
.booking-loader,
.main-loader,
.form-loader,
.page-loader {
	background: rgba(255, 255, 255, .8);
	height: 100%;
	left: 0;
	margin: auto;
	position: absolute;
	right: 0;
	top: 0;
	width: calc(100% - 30px);
	z-index: 9999;
	border-radius: 3px;
	border: 1px solid #dcdbdb;
	display: none;
}

.booking-loader__spinner {
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

@keyframes spinning-circle {
	0% {
		stroke-dashoffset: 0;
		stroke-dasharray: 150.6 100.4;
	}
	50% {
		stroke-dasharray: 1 250;
	}
	100% {
		stroke-dashoffset: 502;
		stroke-dasharray: 150.6 100.4;
	}
}

.spinner__circle {
	animation: 2s linear spinning-circle infinite;
}

.form-loader {
	border: none;
	border-radius: 0;
}

.form-loader:before {
	content: '';
	background: url('/assets/img/svg/spinner.svg') no-repeat center center;
	width: 46px;
	height: 46px;
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.main-loader {
	background: rgba(255, 255, 255, .7);
	border: none;
	z-index: 120;
}

.main-loader > div,
.page-loader > div {
	border: 1px solid #dcdcdc;
	padding: 30px 40px;
	background: #fff;
	box-shadow: 0 0 25px rgba(0, 0, 0, .1);
	position: fixed;
	top: 220px;
	left: 50%;
	transform: translateX(-50%);
	color: #252525;
	text-align: center;
	border-radius: 3px;
}

.main-loader--search-results > div {
	left: 30%;
	transform: translateX(-30%);
}

.page-loader--filtering-results {
	position: fixed;
	z-index: 999;
	width: 100%;
}

.page-loader--filtering-results > div {
	top: 50%;
}

/* Custom Blocks
------------------------------------- */
.tag-cloud {
	margin-top: 25px;
}

.tag-cloud a {
	color: #252525;
	border: 1px solid #333;
	border-radius: 16px;
	padding: 3px 15px;
	display: inline-block;
	margin: 5px 0;
}

.block-content {
	border: 1px solid #eaeaea;
	border-radius: 3px;
	padding: 20px;
	margin-bottom: 10px;
}

.block-content--gray-bg {
	background: #f1f1f1;
}

.block-content .title {
	font-size: 18px;
}

.content-block {
    position: relative;
}

.content-block__title {
	font-size: 2.488rem;
	font-weight: 600;
	letter-spacing: 1px;
	color: #252525;
	margin: 15px 0 10px 0;
	line-height: 1.4;
}

.content-block .heading-2 {
	margin-top: 15px;
	font-size: 1rem;
	line-height: 1.6;
	font-family: var(--font-primary);
	font-weight: 500;
}

.listing-top-text--country p {
	display: none;
}

.listing-top-text--country p:first-child {
	display: block;
}

.listing-bottom-text--country p:first-child {
	display: none;
}

.content-block p {
	font-size: 0.938rem;
}

.section-block {
	padding-bottom: 40px;
}

.section-block--bordered {
	border-bottom: 1px solid #eee;
	margin-bottom: 50px;
}

.section-block__titles {
	margin-bottom: 30px;
}

.section-title {
	font-size: 2.488rem;
	font-weight: 600;
	line-height: 1.2;
	margin-bottom: 8px;
}

.section-title strong {
    font-weight: 600;
}

.section-subtitle {
	color: #252525;
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.4;
	margin-top: 0;
	font-family: var(--font-primary);
}

.button-ml-md {
	margin-left: 30px;
}

.grid-thumbnail {
	position: relative;
}

.grid-thumbnail--reviews {
	margin-left: 4px;
}

.grid-thumbnail-custom__anchor {
	display: block;
	position: relative;
	width: 100%;
	z-index: 90;
	margin: 0 0 12px 0;
	border-radius: 3px;
	min-height: 270px;
}

.grid-thumbnail-custom__anchor:hover {
	text-decoration: none;
}

.grid-thumbnail-custom__block--hidden {
	display: none;
}

.grid-thumbnail-custom__block--overlayed .grid-thumbnail-custom__anchor {
	height: 240px;
	min-height: 0;
}

.grid-thumbnail-custom__block--over .grid-thumbnail-custom__anchor {
	min-height: 0;
}

.listing-item-image-container {
	width: 100%;
}

.grid-thumbnail-custom__content--centered {
	top: 50%;
	margin: 0 auto;
	text-align: center;
	transform: translate(0,-50.5%);
}

.grid-thumbnail-custom__content {
	background-color: #fff;
	padding: 10px 20px;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	box-shadow: 0 2px 10px rgb(0, 0, 0, .06);
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.grid-thumbnail-custom__block--overlayed .grid-thumbnail-custom__content {
	display: block;
	position: absolute;
	z-index: 100;
	top: auto;
	left: 0;
	bottom: 0;
	margin: auto;
	text-align: left;
	transform: translate(0, 0);
	width: 100%;
	padding: 15px 20px;
	background: linear-gradient(to top, rgba(0, 0, 0, .6) 0, rgba(0, 0, 0, 0) 100%);
}

.grid-thumbnail-custom__block--over .grid-thumbnail-custom__content {
	display: block;
	position: absolute;
	z-index: 100;
	padding: 10px 20px;
	border-radius: 3px;
	margin: 15px;
	top: auto;
	bottom: 0;
	right: 0;
	width: auto;
}

.grid-thumbnail-custom__content-title {
	color: #333;
	font-size: 0.938rem;
	font-weight: 600;
	line-height: 22px;
	margin-top: 0;
	margin-bottom: 0;
	font-family: var(--font-primary);
}

.grid-thumbnail-custom__block--overlayed .grid-thumbnail-custom__content-title {
	color: #fff;
	font-size: 1.25rem;
	margin-top: 0;
	text-shadow: 0 1px 1px rgba(0, 0, 0, .4)
}

.grid-thumbnail-custom__block--over .grid-thumbnail-custom__content-title {
	color: #333;
	font-size: 0.938rem;
	text-transform: capitalize;
	text-shadow: none;
	line-height: 1.2;
	margin: 0;
}

.grid-thumbnail-custom__block--over .grid-thumbnail-custom__content-title:after {
	content: '';
	display: inline-block;
	width: 16px;
	height: 12px;
	background-image: url('/assets/img/svg/arrow-right-button.svg');
	background-size: 14px 14px;
	background-repeat: no-repeat;
	position: relative;
	margin-left: 6px;
}

.grid-thumbnail-custom__content-text {
	color: #333;
	line-height: 1.2;
}

.grid-thumbnail-custom__block--overlayed .grid-thumbnail-custom__content-text {
	color: #fff;
	font-size: .1rem;
	font-weight: 400;
	text-shadow: 0 1px 1px rgba(0, 0, 0, .4)
}

.grid-thumbnail-custom__block--over .grid-thumbnail-custom__content-text {
	line-height: 1.6;
	font-size: .875rem;
	display: block;
}

.grid-thumbnail-custom__block--overlayed:hover .grid-thumbnail-custom__content {
	background: linear-gradient(to top, rgba(0, 0, 0, .7) 0, rgba(0, 0, 0, .35) 50%, rgba(0, 0, 0, 0) 100%);
}

.grid-thumbnail-custom__block--hidden {
	display: none;
}

.grid-thumbnail-custom__image {
	object-fit: cover;
	object-position: 50% 50%;
	background-position: 50%;
	background-size: cover;
	width: 100%;
	height: 270px;
}

.grid-thumbnail-custom__block--overlayed .grid-thumbnail-custom__image {
	position: absolute;
	top: 0;
	left: 0;
}

.grid-thumbnail-custom__block--over .grid-thumbnail-custom__image {
	border-radius: 3px;
}

.grid-thumbnail-view-all {
	margin-top: 30px;
	margin-bottom: 10px;
	text-align: center;
}

.grid-thumbnail-carousel {
	margin-left: -10px;
	margin-right: -10px;
	display: flex;
	flex-direction: row;
}

.grid-thumbnail-carousel--one-in-a-row,
.grid-thumbnail-carousel--four-in-a-row,
.grid-thumbnail-carousel--five-in-a-row,
.grid-thumbnail-carousel--six-in-a-row,
.listing-items-similar {
	margin-left: -10px;
	margin-right: -10px;
}

.grid-thumbnail--regions .grid-thumbnail-carousel--five-in-a-row {
	margin-left: -5px;
	margin-right: -5px;
}

.grid-thumbnail-carousel .grid-thumbnail-block {
	padding-left: 10px;
	padding-right: 10px;
	width: 100%;
	position: relative;
}

.grid-thumbnail-carousel .grid-thumbnail-block--testimonials {
	padding-left: 15px;
	padding-right: 15px;
}

.grid-thumbnail-carousel--six-in-a-row .grid-thumbnail-block,
.grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block {
	min-width: 210px;
	padding-left: 10px;
	padding-right: 10px;
	width: auto;
}

.grid-thumbnail-carousel--three-in-a-row .grid-thumbnail-block {
	padding-left: 10px;
	padding-right: 10px;
	min-width: 466.5px;
}

.grid-thumbnail--regions .grid-thumbnail-carousel--one-in-a-row .grid-thumbnail-block {
	padding-left: 10px;
	padding-right: 10px;
	min-width: 100%;
}

.grid-thumbnail--regions .grid-thumbnail-carousel--four-in-a-row .grid-thumbnail-block {
	padding-left: 10px;
	padding-right: 10px;
	min-width: 350px;
}

.grid-thumbnail--regions .grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block {
	padding-left: 5px;
	padding-right: 5px;
	min-width: 278px;
}

.grid-thumbnail-carousel--six-in-a-row .grid-thumbnail-block:nth-child(n+7),
.grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block:nth-child(n+6)
.grid-thumbnail-carousel--three-in-a-row .grid-thumbnail-block:nth-child(n+4) {
	display: none;
}

.grid-thumbnail-carousel--six-in-a-row.keen-initialized .grid-thumbnail-block:nth-child(n+7),
.grid-thumbnail-carousel--five-in-a-row.keen-initialized .grid-thumbnail-block:nth-child(n+6),
.grid-thumbnail-carousel--three-in-a-row.keen-initialized .grid-thumbnail-block:nth-child(n+4) {
	display: block;
}

.grid-thumbnail-carousel--three-in-a-row .grid-thumbnail-image--180h img {
	width: 450px;
	max-width: 100%;
}

.grid-thumbnail-block.content-over-image {
	position: relative;
}

.grid-thumbnail-block--top-spacing {
	margin-top: 20px;
}

.grid-thumbnail-block img {
	border-radius: 3px;
	min-height: 300px;
	object-fit: cover;
}

.camp-grid .grid-thumbnail-block img {
	min-height: 0;
	object-fit: none;
}

.grid-thumbnail-block.content-over-image a {
	color: #fff;
}

.grid-thumbnail-block.content-over-image .grid-thumbnail-content {
	position: relative;
	padding: 0;
	color: #252525;
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
}

.grid-thumbnail-block.content-over-image .grid-thumbnail-content h2 {
	font-weight: 600;
	font-size: 18px;
	line-height: 25px;
	color: #333;
}

.grid-thumbnail-block.content-over-image .grid-thumbnail-content p {
	margin: 0;
}

.grid-thumbnail-block.content-over-image .grid-thumbnail-content p.grid-thumbnail-content__title {
	margin-bottom: 5px;
}

.grid-thumbnail-block .grid-thumbnail-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: block;
	transition: background-color .1s ease-in-out;
}

.grid-thumbnail-content {
	margin-top: 10px;
	color: #252525;
}

.grid-thumbnail-content__title {
	font-size: 1rem;
	color: #252525;
	font-weight: 500;
	line-height: 1.4;
	margin-top: 0;
	margin-bottom: 0;
}

.grid-thumbnail-content__title--smaller {
	font-size: .875rem;
	line-height: 1.2;
}

.grid-thumbnail-content__title--xsmaller {
	font-size: .75rem;
}

.grid-thumbnail-content__info {
	color: #333;
	font-size: .938rem;
	line-height: 140%;
}

.grid-thumbnail-content__number-of-objects {
	color: #333;
	line-height: 1.2;
	font-weight: 400;
	font-size: .875rem;
	margin-top: 0;
}

.grid-thumbnail-block:hover .grid-thumbnail-overlay {
	background: rgba(255, 255, 255, .15);
}

.grid-thumbnail-block:hover a {
	text-decoration: none;
}

.grid-thumbnail-image--320h img {
	height: 320px;
	object-fit: cover;
	width: 100%;
}

.grid-thumbnail-image--180h img {
	height: 180px;
	object-fit: cover;
	width: 100%;
	min-height: 180px;
}

.grid-thumbnail-image--160h img {
	height: 160px;
	object-fit: cover;
	width: 100%;
	min-height: 160px;
	max-width: 215px;
}

.grid-thumbnail-image--300h img {
	height: 270px;
	object-fit: cover;
	object-position: center;
	width: 270px;
	min-height: 270px;
	max-width: 100%;
}

.grid-thumbnail-content .checks {
	padding-left: 0;
	margin-top: 5px;
	color: #555;
}

.grid-thumbnail-content .checks li {
	text-indent: -14px;
	padding-left: 15px;
}

.grid-thumbnail-content .checks li:before {
	content: ' • ';
	margin-right: 0;
	vertical-align: baseline;
	color: #bdbdbd;
	font-size: 16px;
}

.block-grid-random__item--hidden {
	display: none;
}

.objects-listing--featured,
.grid-tags {
	padding-top: 40px;
	padding-bottom: 40px;
}

.grid-item-tags {
	display: flex;
	flex-wrap: wrap;
	margin-top: 10px;
}

.grid-item-tag {
	display: block;
	background: #fff;
	border-radius: 3px;
	padding: 0 40px;
	margin-right: 15px;
	margin-bottom: 15px;
}

.grid-item-tag:hover {
	text-decoration: underline;
}

.grid-item-tag:last-of-type {
	margin-right: 0;
}

.grid-item-tag__title {
	color: #252525;
	font-size: 14px;
	font-weight: 600;
}

.grid-item-tag__text {
	color: #252525;
	font-size: 12px;
}


/* ------------------------------------------------------------------- */
/*  05. Script Styles
---------------------------------------------------------------------- */

/* Google Maps
------------------------------------- */
.infoBox {
	border-radius: 4px;
}

.show-if-map-visible {
	display: none;
}

.custom-infobox-container,
.map-container__close-button {
	display: none;
}

.custom-infobox {
	background-color: #fff;
	margin-bottom: 10px;
	box-shadow: 0 3px 5px 0 rgba(0, 0, 0, .25);
	width: calc(100% - 20px);
	margin-left: 10px;
	z-index: 1000;
	position: absolute;
	bottom: 0;
	display: flex;
	border-radius: 8px;
}

.custom-infobox .listing-img-container {
	width: 180px;
	background: #eee;
	border-radius: 8px;
}

.custom-infobox .listing-img-container img {
	height: 180px;
	object-fit: cover;
	width: 180px;
	border-top-left-radius: 8px;
	border-bottom-left-radius: 8px;
	min-width: 130px;
}

.custom-infobox .listing-content {
	position: relative;
	display: flex;
	padding: 10px;
	flex: 1;
}

.custom-infobox .listing-title {
	width: 100%;
	padding-left: 0;
	display: flex;
	flex-direction: column;
}

.custom-infobox .listing-item-location {
	line-height: 1.2;
	margin-bottom: 10px;
}

.custom-infobox .listing-content h3 {
	font-size: 1rem;
	font-weight: 600;
	line-height: 1.4;
	margin: 0;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	width: 97%;
	font-family: var(--font-primary);
}

.custom-infobox .listing-content h3 a {
	color: #333;
}

.custom-infobox .listing-content .listing-price {
	font-size: 0.938rem;
	font-weight: 600;
	line-height: 1.2;
	margin-top: auto;
	margin-bottom: 0;
}

.custom-infobox .listing-content .map-features {
	margin-bottom: 0;
	list-style: none;
	display: flex;
	flex-direction: column;
	padding: 0;
}

.custom-infobox .listing-content .map-features li {
	display: inline-block;
	font-size: .75rem;
	line-height: 1.2;
}

.custom-infobox .listing-single-page {
	position: absolute;
	right: 5px;
	bottom: 10px;
	padding: 0 6px;
}

.custom-infobox .listing-single-page:after {
	content: '';
	display: inline-block;
	width: 16px;
	height: 16px;
	background-image: url('/assets/img/svg/right-arrow.svg');
	background-size: 16px 16px;
	background-repeat: no-repeat;
	position: relative;
	vertical-align: middle;
}

.map-box {
	background-color: #fff;
	margin-bottom: 15px;
	box-shadow: 0 9px 15px 0 rgba(0,0,0,.2);
	position: relative;
	border-radius: 8px;
}

.map-box .listing-img-container img {
	width: 100%;
	min-height: 160px;
	border-top-right-radius: 8px;
	border-top-left-radius: 8px;
}

.map-box .listing-content {
	position: initial;
}

.map-box h4 {
	margin: 0;
	padding: 0;
}

.map-box h4 a {
	padding: 0 0 2px 0;
	font-size: 1rem;
	line-height: 25px;
	transition: all .2s ease-in-out;
	display: inline-block;
}

.listing-img-container {
	position: relative;
	height: 100%;
	display: block;
}

.map-box h4 a:hover {
	color: #666;
}

.map-box p {
	padding: 0;
	line-height: 25px;
	margin: 0;
}

.map-box .listing-title {
	padding: 10px;
}

.map-box .listing-img-content {
	padding: 18px 22px;
}

.map-box .listing-img-content span {
	font-size: 1.25rem;
}

.map-box .listing-img-content .listing-price i {
	font-size: 1rem;
	margin: -7px 0 0 0;
}

.map-box .listing-title .map-features {
	padding: 0;
	margin: 5px 0;
	list-style: none;
	display: flex;
	flex-wrap: wrap;
}

.map-box .listing-title .map-features li {
	font-size: .75rem;
	margin-right: 6px;
	line-height: 1.6;
}

.map-box .listing-price {
	font-weight: 600;
	font-size: .938rem;
	margin-top: 5px;
}

.infoBox > img {
	position: absolute !important;
	right: 0;
	top: 0;
}

.map-box .listing-item-content {
	position: relative;
	padding: 0;
	width: 100%;
	z-index: 50;
	display: flex;
	flex-direction: column;
	bottom: auto;
}

.map-box .listing-item-location {
	font-size: .75rem;
	line-height: 1.4;
}

.map-box .listing-item-content h3 {
	color: #252525;
	font-size: 0.938rem;
	font-weight: 600;
	margin: 0;
	line-height: 1.2;
	flex: 2;
}

.map-box .listing-item-content h3 a {
	color: #252525;
}

.map-box .listing-item-content h3 a:hover {
	text-decoration: underline;
}

.map-box .listing-item-content .listing-price {
	font-size: 21px;
	font-weight: 600;
	flex: 1;
	text-align: right;
	line-height: 21px;
	color: #fff;
}

.map-box .listing-item-content .listing-price-from {
	font-size: 14px;
	font-weight: 500;
	line-height: 19px;
	position: relative;
	top: 2px;
}

.map-box .listing-item-content span {
	font-size: 13px;
	display: inline-block;
	color: rgba(255,255,255,.9);
}


/* Close Button */
.infoBox-close {
	background-color: #fff;
	position: absolute;
	top: 8px;
	right: 8px;
	display: inline-block;
	z-index: 999;
	text-align: center;
	line-height: 29px;
	cursor: pointer;
	height: 27px;
	width: 27px;
	transition: all .2s ease-in-out;
	border-radius: 50%;
}

.infoBox-close:before {
	content: '';
	display: inline-block;
	width: 15px;
	height: 15px;
	background-image: url('/assets/img/svg/close.svg');
	background-size: 15px 15px;
	background-repeat: no-repeat;
	position: relative;
	top: 3px;
}

.infoBox {
	animation: fadeIn .3s;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

#map {
	height: 100%;
	width: 100%;
	margin: 0;
	z-index: 990;
	border-radius: 3px;
}

#map-container {
	position: relative;
	top: 0;
	left: 0;
	height: 520px;
	width: 100%;
	z-index: 990;
	margin-bottom: 60px;
}

#map-container.map-in-viewport {
	height: 90vh;
}

/* Cluster styles */
.cluster img {
	display:none
}

.cluster-visible {
	text-align: center;
	font-size: 15px !important;
	color: #fff !important;
	font-weight: 500;
	border-radius: 50%;
	width: 36px !important;
	height: 36px !important;
	line-height: 36px !important;
	background-color: #66676b;
}

.cluster-visible:before {
	border: 7px solid #66676b;
	opacity: .2;
	box-shadow: inset 0 0 0 4px #66676b;
	content: '';
	position:absolute;
	border-radius: 50%;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	animation: cluster-animation 2.5s infinite;
}

@keyframes cluster-animation {
	0%,100% {
		transform:scale(1.3) rotate(0deg)
	}
	50% {
		transform:scale(1.5) rotate(90deg)
	}
}

/* Custom Marker */
.map-marker-container {
	position: absolute;
	transform: translate3d(-50%,-100%, 0);
	margin-left: -20px;
}

.map-marker-container:hover {
	z-index: 100;
}

.map-marker-container.clicked {
	z-index: 100 !important;
}

.marker-container {
	position: relative;
	width: 100%;
	height: 24px;
	z-index: 1;
	cursor: pointer;
}

.marker-container--no-cursor {
	cursor: default;
}

.brands-footer__gaveia-group-wrapper {
	display: flex;
	width: 100%;
	justify-content: flex-end;
	font-size: .875rem;
}

.marker-container .marker-card .icon-box {
	min-width: 40px;
	height: 24px;
	background: #fff;
	border-radius: 6px;
	padding: 3px 6px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, .3);
	font-size: .875rem;
	font-weight: 500;
	color: #252525;
	font-style: normal;
	line-height: 1.4;
	display: inline-block;
	position: relative;
	font-family: var(--font-primary);
}

.marker-container .marker-card .icon-house-map-pin {
	padding: 10px;
	box-shadow: 0 1px 5px rgba(0, 0, 0, .3);
	background: #fff;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	display: flex;
}

.marker-container .marker-card .icon-house-map-pin:before {
	content: '';
	display: inline-block;
	width: 20px;
	height: 20px;
	background-image: url('/assets/img/svg/home.svg');
	background-size: 20px 20px;
	background-repeat: no-repeat;
}

.marker-container .marker-card .icon-house-map-pin:after {
	content: '';
	position: absolute;
	top: 39px;
	left: 20px;
	margin-left: -6px;
	border-width: 6px;
	border-style: solid;
	border-color: #fff transparent transparent transparent;
	filter: drop-shadow(0 2px 1px rgba(0, 0, 0, .2));
}

.marker-container .marker-card .icon-box:after {
	content: '';
	position: absolute;
	top: 100%;
	left: 50%;
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: #fff transparent transparent transparent;
	filter: drop-shadow(0 2px 1px rgba(0, 0, 0, .2));
}

.marker-container {
	perspective: 1000;
}

.marker-card {
	width: 100%;
	height: 100%;
	transform-style: preserve-3d;
	transition: all .4s ease-in-out;
	position: absolute;
	z-index: 1;
}

.marker-container:hover .marker-card .icon-box,
.map-marker-container.clicked .marker-card .icon-box {
	background: #252525;
	color: #fff;
	position: relative;
	left: -1px;
}

.marker-container:hover .marker-card .icon-box:after,
.map-marker-container.clicked .marker-card .icon-box:after {
	border-color: #252525 transparent transparent transparent;
}

.marker-container .marker-card .icon-box.icon-smaller {
	width: 28px;
	height: 20px;
}

.marker-arrow {
	width: 0;
	content: '';
	height: 0;
	border-style: solid;
	border-width: 11px 11px 0;
	border-color: #66676b transparent transparent;
	top: 36px;
	left: 50%;
	transform: translateX(-50%);
	position: absolute;
	border-radius: 50%;
	z-index: 1;
}

/* Custom Zoom Buttons
------------------------------------- */
.custom-zoom-in,
.custom-zoom-out {
	background-color: #fff;
	cursor: pointer;
	border-radius: 50%;
	margin: 5px 15px;
	transition: color .3s, background-color .3s;
	box-shadow: 0 1px 4px -1px rgba(0, 0, 0, .2);
	text-align: center;
	height: 34px;
	width: 34px;
}

.custom-zoom-in:hover,
.custom-zoom-out:hover {
	background-color: #66676b;
	color: #fff;
}

.custom-zoom-in:before,
.custom-zoom-out:before  {
	content: '';
	display: inline-block;
	width: 15px;
	height: 15px;
	background-size: 15px 15px;
	background-repeat: no-repeat;
	position: relative;
	top: 9px;
}

.zoomControlWrapper {
	position: absolute;
	left: 0;
	right: auto;
	width: 70px;
}

.custom-zoom-in:before {
	background-image: url('/assets/img/svg/plus.svg');
}
.custom-zoom-out:before  {
	background-image: url('/assets/img/svg/minus.svg');
}

.custom-zoom-in:hover:before,
.custom-zoom-out:hover:before {
	filter: invert(100%);
}

#scrollEnabling {
	color: #333;
	background-color: #fff;
	padding: 7px 18px;
	padding-top: 9px;
	transition: all .2s ease-in-out;
	box-sizing: border-box;
	display: inline-block;
	border-radius: 50px;
	box-shadow: 0 1px 4px -1px rgba(0, 0, 0, .2);
}

#scrollEnabling:hover,
#scrollEnabling.enabled {
	background-color: #66676b;
	color: #fff;
}

#scrollEnabling {
	position: absolute;
	top: 10px;
	right: 55px;
	z-index: 999;
	font-size: .875rem;
	line-height: 1.2;
}

.new-object-label {
	border-radius: 6px;
	background: #F9D75F;
	color: #252525;
	padding: 0 10px;
	font-size: .75rem;
	width: fit-content;
	margin-bottom: 4px;
	font-weight: 500;
}

.new-object-label--single-villa {
	margin-top: 12px;
	margin-bottom: -13px;
}

/* Magnific Popup
------------------------------------- */
.modal-general-opened {
	overflow: hidden;
	position: fixed;
	width: 100%;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.mfp-bg {
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 10000;
	overflow: hidden;
	position: fixed;
	background: #000;
	opacity: .96;
}

.mfp-wrap {
	top: -10px;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 10001;
	position: fixed;
	outline: none !important;
	-webkit-backface-visibility: hidden;
}

.mfp-container {
	text-align: center;
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	padding: 0 15px;
}

.mfp-container:before {
	content: '';
	display: inline-block;
	height: 100%;
	vertical-align: middle;
}

.mfp-align-top .mfp-container:before {
	display: none;
}

.mfp-content {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin: 0 auto;
	text-align: left;
	z-index: 505;
}

.mfp-inline-holder .mfp-content,
.mfp-ajax-holder .mfp-content {
	width: 100%;
	cursor: auto;
}

.mfp-ajax-cur {
	cursor: progress;
}

.mfp-zoom-out-cur,
.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
	cursor: zoom-out;
}

.mfp-zoom {
	cursor: zoom-in;
}

.mfp-auto-cursor .mfp-content {
	cursor: auto;
}

.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
}

.mfp-loading.mfp-figure {
	display: none;
}

.mfp-hide {
	display: none !important;
}

.mfp-preloader {
	color: #aaa;
	position: absolute;
	top: 50%;
	width: auto;
	text-align: center;
	margin-top: -.8em;
	left: 8px;
	right: 8px;
	z-index: 504;
}

.mfp-preloader a {
	color: #cccccc;
}

.mfp-preloader a:hover {
	color: #fff;
}

.mfp-s-ready .mfp-preloader {
	display: none;
}

.mfp-s-error .mfp-content {
	display: none;
}

.mfp-s-loading .mfp-arrow {
	display: none;
}

button.mfp-close,
button.mfp-arrow {
	overflow: visible;
	cursor: pointer;
	border: 0;
	-webkit-appearance: none;
	display: block;
	padding: 0;
	z-index: 506;
}

.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
	right: 0;
	top: -40px;
}

/* Close Button */
.og-close,
button.mfp-close {
	position: absolute;
	width: 45px;
	height: 45px;
	top: -20px;
	display: block;
	right: -12px;
	cursor: pointer !important;
	z-index: 9999;
	color: #fff;
	transition: all .2s ease-in-out;
	border-radius: 50%;
	margin: 0;
	background-color: transparent;
	outline: none;
	transform: translate3d(0,0,0);
}

.mfp-iframe-holder .mfp-close {
	top: -50px;
	right: -12px;
	transform: translate3d(0,0,0);
}

.mfp-gallery {
	cursor: pointer;
}

.mfp-gallery .mfp-close {
	top: 20px;
	right: 10px;
}

.mfp-gallery .mfp-content .mfp-close {
	display: none;
}

.mfp-gallery .mfp-close:before {
	top: 4px;
	left: 50%;
	position: absolute;
	height: 10px;
	transition: .3s;
}

.mfp-close:before {
	top: 0;
}

.og-close {
	margin: 17px;
	right: 10px;
	background: transparent;
	color: #252525;
}

.og-close:before,
.mfp-close:before {
	content: '';
	display: inline-block;
	width: 20px;
	height: 20px;
	background-image: url('/assets/img/svg/close.svg');
	background-size: 20px 20px;
	background-repeat: no-repeat;
	position: relative;
}

.og-close:before {
	top: 7px;
	font-size: 29px;
}

#small-dialog .mfp-close,
.inquiry-modal .mfp-close,
.mfp-close:hover {
	color: #fff;
}

/* Popup close button */
#small-dialog .mfp-close,
.inquiry-modal .mfp-close,
.informational-modal .mfp-close,
.filtering-modal .mfp-close {
	top: 20px;
	right: 20px;
	width: 40px;
	height: 40px;
}

#small-dialog .mfp-close:after,
#small-dialog .mfp-close:before,
.inquiry-modal .mfp-close:after,
.inquiry-modal .mfp-close:before,
.informational-modal .mfp-close:after,
.informational-modal .mfp-close:before,
.filtering-modal .mfp-close:after,
.filtering-modal .mfp-close:before {
	transition: none;
}

.small-dialog-content input[type=submit] {
	width: 100%;
	margin-top: 26px;
	margin-bottom: 10px;
}

.small-dialog-content .divider {
	display: inline-block;
	width: 100%;
	margin-top: 15px;
	padding-bottom: 0;
}

.small-dialog-header {
	font-size: 1.25rem;
	width: calc(100% + 80px);
	position: relative;
	left: -40px;
	display: inline-block;
	padding: 25px 25px 25px 40px;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
}

.small-dialog-header--fixed {
	border-bottom: 1px solid #ddd;
}

.small-dialog-header h3 {
	padding: 0;
	margin: 0;
	font-size: 1.33rem;
	font-weight: 500;
	width: 94%;
	font-family: var(--font-primary);
}

.mfp-counter {
	position: absolute;
	top: 0;
	right: 0;
	color: #aaa;
	font-size: 1rem;
	line-height: 1.4;
}

.mfp-arrow {
	opacity: 1;
	margin: 0 20px;
	top: 50%;
	transform: translateY(-50%);
	color: #fff;
	transition: all .2s ease-in-out;
	cursor: pointer;
	width: 60px;
	height: 60px;
	position: absolute;
	display: block;
	z-index: 100;
	overflow: hidden;
	background: rgba(255,255,255,.15);
	border-radius: 50%;
	outline: none;
}

.mfp-arrow:hover {
	background: #66676b;
	color: #fff;
}

.mfp-arrow.mfp-arrow-left,
.mfp-arrow.mfp-arrow-right {
	font-size: 32px;
	line-height: 1;
	opacity: 1;
	color: #fff;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	transition: all .15s;
}

.mfp-arrow.mfp-arrow-right {
	right: 15px;
}

.mfp-arrow.mfp-arrow-left:before,
.mfp-arrow.mfp-arrow-right:before {
	content: "\e906";
	top: 0;
	position: relative;
}

.mfp-arrow.mfp-arrow-right {
	transform: translateY(-50%) rotate(-90deg);
}

.mfp-arrow.mfp-arrow-left {
	transform: rotate(90deg);
}

.mfp-content:hover .mfp-arrow {
	opacity: 1;
}

.mfp-iframe-holder {
	padding-top: 40px;
	padding-bottom: 40px;
}

.mfp-iframe-holder .mfp-content {
	line-height: 0;
	width: 100%;
	max-width: 1180px;
}

.mfp-iframe-scaler {
	width: 100%;
	height: 0;
	overflow: hidden;
	padding-top: 54%;
}

.mfp-iframe-scaler iframe {
	position: absolute;
	display: block;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	box-shadow: 0 0 8px rgba(0, 0, 0, .6);
	background: #252525;
}

/* Popup */
#small-dialog,
.inquiry-modal,
.informational-modal,
.filtering-modal {
	background: #fff;
	padding: 40px;
	padding-top: 0;
	text-align: left;
	max-width: 610px;
	margin: 40px auto;
	position: relative;
	border-radius: 4px;
}

.inquiry-modal {
	max-width: 645px;
}

.informational-modal {
	max-height: 80vh;
	height: 80vh;
	margin: 0 auto;
	max-width: 650px;
}

.filtering-modal {
	margin: 0 auto;
	max-width: 720px;
	height: 80vh;
	max-height: 80vh;
}

.filtering-modal__container {
	overflow: visible;
	overflow-y: auto;
	height: calc(100% - 115px);
	margin-top: 15px;
}

.filtering-modal__section {
	margin-bottom: 25px;
}

.filtering-modal__section--special-offer {
	margin-top: 15px;
}

.filtering-modal__block {
	margin-bottom: 10px;
}

.filtering-modal__block--double-space {
	margin-bottom: 25px;
}

.filtering-modal__block--hidden-content label:nth-child(n+14) {
	display: none;
}

.filtering-modal__show-more {
	font-weight: 600;
	text-decoration: underline;
	display: inline-block;
	margin-top: 15px;
	cursor: pointer;
}

.filtering-modal__section-title {
	font-weight: 500;
	font-size: 1.125rem;
	border-bottom: 1px solid #a6a6a6;
	padding-bottom: 10px;
}

.filtering-modal__section-subtitle {
	font-size: 0.938rem;
}

.filtering-modal__section-row {
	display: flex;
	justify-content: space-between;
}

.filtering-modal__section-column {
	flex: 0 1 40%;
}

.filtering-modal__checkboxes {
	column-count: 2;
}

.filtering-modal__checkboxes label {
	display: flex;
	margin-bottom: 13px;
	align-items: center;
	width: 100%;
}

#small-dialog span.line,
.inquiry-modal span.line {
	padding-top: 0;
}

.my-mfp-zoom-in .zoom-anim-dialog {
	opacity: 0;
	transition: all .2s ease-in-out;
	transform: scale(.8);
}

/* animate in */
.my-mfp-zoom-in.mfp-ready .zoom-anim-dialog {
	opacity: 1;
	transform: scale(1);
}

/* animate out */
.my-mfp-zoom-in.mfp-removing .zoom-anim-dialog {
	transform: scale(.8);
	opacity: 0;
}

/* Dark overlay, start state */
.my-mfp-zoom-in.mfp-bg {
	opacity: .001;
	transition: opacity .3s ease-out;
}
/* animate in */
.my-mfp-zoom-in.mfp-ready.mfp-bg {
	opacity: .6;
}
/* animate out */
.my-mfp-zoom-in.mfp-removing.mfp-bg {
	opacity: 0;
}


/* Main image in popup */
img.mfp-img {
	width: auto;
	max-width: 100%;
	height: auto;
	display: block;
	line-height: 0;
	box-sizing: border-box;
	margin: 40px auto;
}

/* The shadow behind the image */
.mfp-figure:after {
	content: '';
	position: absolute;
	left: 0;
	top: 40px;
	bottom: 40px;
	display: block;
	right: 0;
	width: auto;
	height: auto;
	z-index: -1;
	box-shadow: 0 0 18px rgba(11,11,11, .6);
}

.mfp-figure {
	line-height: 0;
}

.mfp-bottom-bar {
	margin-top: -30px;
	position: absolute;
	top: 100%;
	left: 0;
	width: 100%;
	cursor: auto;
}

.mfp-title {
	text-align: left;
	line-height: 18px;
	color: #f3f3f3;
	word-break: break-word;
	padding-right: 36px;
}

.mfp-figure small {
	color: #bdbdbd;
	display: block;
	font-size: 12px;
	line-height: 14px;
}

.mfp-image-holder .mfp-content {
	max-width: 100%;
}

.mfp-gallery .mfp-image-holder .mfp-figure {
	cursor: pointer;
}

/* Media Queries for Magnific Popup */
@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {
	.mfp-img-mobile .mfp-image-holder {
		padding-left: 0;
		padding-right: 0;
	}

	.mfp-img-mobile img.mfp-img {
		padding: 0
	}

	.mfp-img-mobile .mfp-figure:after {
		top: 0;
		bottom: 0;
	}

	.mfp-img-mobile .mfp-bottom-bar {
		background: rgba(0, 0, 0, .6);
		bottom: 0;
		margin: 0;
		top: auto;
		padding: 3px 5px;
		position: fixed;
		box-sizing: border-box;
	}

	.mfp-img-mobile .mfp-bottom-bar:empty {
		padding: 0;
	}

	.mfp-img-mobile .mfp-counter {
		right: 5px;
		top: 3px;
	}

	.mfp-img-mobile .mfp-close {
		top: 0;
		right: 0;
		width: 35px;
		height: 35px;
		line-height: 35px;
		background: rgba(0, 0, 0, .6);
		position: fixed;
		text-align: center;
		padding: 0;
	}

	.mfp-img-mobile .mfp-figure small {
		display: inline;
		margin-left: 5px;
	}
}

.mfp-fade.mfp-bg {
	opacity: 0;
	transition: all .2s ease-out;
}

.mfp-fade.mfp-bg.mfp-ready {
	opacity: .92;
	background-color: #111;
}

.mfp-fade.mfp-bg.mfp-removing {
	opacity: 0;
}

.mfp-fade.mfp-wrap .mfp-content {
	opacity: 0;
	transition: all .2s ease-out;
}

.mfp-fade.mfp-wrap.mfp-ready .mfp-content {
	opacity: 1;
}

.mfp-fade.mfp-wrap.mfp-removing .mfp-content {
	opacity: 0;
}

/* fullscreen setup */
.fullscreen,
.content-a {
	width:100%;
	height:100%;
	overflow:hidden;
}

.fullscreen.overflow,
.fullscreen.overflow .content-a {
	height:auto;
	min-height:100%;
}

/* content centering styles */
.centered-content {
	position:relative;
	vertical-align:middle;
	text-align:center;
}

/* keen-slider */
.keen-slider {
	display: flex;
	overflow: hidden;
	position: relative;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	touch-action: pan-y;
	-webkit-tap-highlight-color: transparent;
}
.keen-slider__slide {
	position: relative;
	overflow: hidden;
	width: 100%;
	min-height: 100%;
}

.keen-slider__slide--fade {
	position: absolute;
	overflow: hidden;
	width: 100%;
	min-height: 100%;
	top: 0;
	opacity: 0;
	z-index: 0;
	transition: all 300ms;
}

.keen-slider__slide--fade[data-slide="1"] {
	opacity: 1;
	z-index: 1;
}

.keen-slider[data-keen-slider-v] {
	flex-wrap: wrap;
}

.keen-slider[data-keen-slider-v] .keen-slider__slide {
	width: 100%;
}

.keen-slider[data-keen-slider-moves] * {
	pointer-events: none;
}

.keen-dots {
	display: none;
	padding: 10px 0;
	justify-content: center;
	position: relative;
	width: 100%;
	height: 35px;
}

.keen-block--enable-dots-line .keen-dots {
	display: flex;
}

.keen-dots--top-spacing {
	margin-top: 15px;
}

.keen-dot {
	font-size: 0;
	line-height: 0;
	display: block;
	width: 15px;
	height: 15px;
	padding: 7.5px;
	cursor: pointer;
	color: transparent;
	border: 0;
	background: #fff;
	box-shadow: inset 0 0 0 2px silver;
	transition: all .3s;
	border-radius: 50%;
	margin: 0 3px;
}

.keen-dots--line .keen-dot {
	font-size: 0;
	line-height: 0;
	display: block;
	width: 100%;
	height: 2px;
	padding: 0;
	cursor: pointer;
	color: transparent;
	border: 0;
	background: #dfdfdf;
	transition: all .3s;
	margin: 0;
	box-shadow: none;
	border-radius: 0;
}

.keen-dot--hide {
	display: none;
}

.keen-dot:focus {
	outline: none;
}

.keen-dot--active {
	background: #333;
	box-shadow: inset 0 0 0 0 #333;
}

.keen-dots--line .keen-dot--active {
	background: #252525;
	height: 2px;
	box-shadow: none;
}

/* Arrows */
.keen-block .keen-arrow {
	display: none;
}

.keen-prev,
.keen-next {
	font-size: 0;
	line-height: 0;
	opacity: 0;
	position: absolute;
	top: 50%;
	z-index: 100;
	display: block;
	width: auto;
	height: auto;
	padding: 0;
	transform: translate(0, -50%);
	cursor: pointer;
	color: transparent;
	border: none;
	outline: none;
	background: transparent;
}

.keen-disabled {
	display: none;
	opacity: 0;
}

.keen-prev:hover,
.keen-next:hover {
	color: transparent;
	outline: none;
	background: transparent;
}

.keen-prev:hover:before,
.keen-next:hover:before {
	color: #999;
}

.keen-prev:before,
.keen-next:before {
	content: '';
	display: inline-block;
	opacity: 1;
	width: 24px;
	height: 24px;
	background-size: 24px 24px;
	background-repeat: no-repeat;
	filter: invert(1);
}

.keen-prev {
	left: 3px;
	transform: translate3d(-90px,-50%,0);
}

.keen-next {
	right: 3px;
	transform: translate3d(90px,-50%,0) rotate(180deg);
}

.keen-prev:before,
.keen-next:before {
	background-image: url('/assets/img/svg/arrow-thin-right.svg');
}

.listing-lazy-images {
	padding: 0;
	margin: 0;
	height: 100%;
	z-index: 100;
}

.listing-lazy-images .gallery-object {
	background: #e8eaf1;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #252525;
}

.listing-lazy-images .gallery-trigger-object {
	padding: 15px;
	text-align: center;
}

.listing-lazy-images .gallery-trigger-object > a {
	font-weight: 500;
	color: #b3a351;
	text-decoration: underline;
}

.listing-lazy-images .keen-prev,
.listing-lazy-images .keen-next {
	transition: opacity .15s;
	top: calc(50% - 20px);
}

.listing-lazy-images .keen-prev,
.listing-lazy-images .keen-next {
	opacity: 1;
}

.listing-lazy-images .keen-prev {
	left: 15px;
	transform: none;
	background: rgba(255,255,255,.85);
	margin: 0;
	border-radius: 50%;
}

.listing-lazy-images .keen-next {
	right: 15px;
	transform: none;
	background: rgba(255,255,255,.85);
	margin: 0;
	border-radius: 50%;
}

.listing-lazy-images .keen-prev:before,
.listing-lazy-images .keen-next:before {
	content: '';
	display: inline-block;
	opacity: 1;
	width: 16px;
	height: 16px;
	background-size: 16px 16px;
	background-repeat: no-repeat;
	filter: invert(28%) sepia(9%) saturate(0%) hue-rotate(230deg) brightness(93%) contrast(101%);
}

.listing-lazy-images .keen-prev:before {
	background-image: url('/assets/img/svg/left-arrow.svg');
}

.listing-lazy-images .keen-next:before {
	background-image: url('/assets/img/svg/right-arrow.svg');
}

.listing-lazy-images .keen-prev:hover,
.listing-lazy-images .keen-next:hover {
	background: rgba(255,255,255,1);
}

.listing-lazy-images .keen-prev:hover:before,
.listing-lazy-images .keen-next:hover:before {
	filter: invert(0%) sepia(74%) saturate(4%) hue-rotate(262deg) brightness(105%) contrast(71%);
}


/* Dots */
.slick-dotted.slick-slider {
	margin-bottom: 30px;
}

.slick-dots {
	display: block;
	width: 100%;
	padding: 0;
	margin: 0;
	list-style: none;
	text-align: center;
	margin-top: 50px;
}

.slick-dots li {
	position: relative;
	display: inline-block;
	padding: 0;
	margin: 0 3px;
	cursor: pointer;
}

.slick-dots li {
	font-size: 0;
	cursor: pointer;
	color: transparent;
	border: 0;
	position: relative;
	outline: none;
	display: inline-block;
	width: 15px;
	height: 15px;
	margin: 0 3px;
	opacity: 1;
	border-radius: 50%;
	line-height: 0;
	box-shadow: inset 0 0 0 2px #c0c0c0;
	transition: all .3s;
}

.slick-dots li.slick-active {
	box-shadow: inset 0 0 0 6px #252525;
	transition: all .3s;
}

.slick-dots li:after {
	display: block;
	height: 13px;
	width: 13px;
	content: "";
	position: absolute;
	top: 1px;
	left: 1px;
	border-radius: 50%;
	transition: all .3s;
	opacity: 0;
	background-color: #252525;
}

.slick-dots li.slick-active:after {
	opacity: 1;
}

.slick-dots li button {
	display: none;
}

.slick-dots li button:hover,
.slick-dots li button:focus {
	outline: none;
}

.slick-slide {
	outline: none;
}

.testimonial-carousel .slick-slide.slick-active {
	opacity: 1;
	filter: none;
	pointer-events: all;
}

.fullwidth-carousel-container {
	overflow: hidden;
	width: 100%;
}

.fw-carousel-item {
	position: relative;
	height: 100%;
}


/* Select2
------------------------------------- */
.select2-results {
	border: 1px solid #ddd;
	border-radius: 2px;
}

.select2-results__option {
	font-size: 1rem;
	padding: 12px 15px !important;
}

.select2-results__options {
	border-radius: 4px;
}

.select2-rendered__match {
	color: #252525;
	font-weight: 600;
}

.select2-dropdown {
	box-shadow: 0 3px 7px rgba(0, 0, 0, .16);
	top: 12px;
	border: none;
	min-width: 320px;
}

.select2-container--default.select2-container--focus .select2-selection--multiple,
.select2-container--default .select2-selection--multiple {
	border: none !important;
}

.main-search-input .select2-container--default .select2-selection--multiple {
	border-radius: 4px;
	min-height: 40px;
}

.select2-container .select2-search--inline {
	padding-left: 12px !important;
	float: none !important;
}

.select2-container .select2-selection__choice + .select2-search--inline {
	padding-left: 0 !important;
	width: 10px;
	float: left !important;
}

.select2-container .select2-search--inline .select2-search__field {
	margin-top: 0 !important;
	font-size: .875rem;
	height: 40px;
}

.select2-container--default .select2-search--inline .select2-search__field {
	margin-bottom: 0 !important;
	width: 100% !important;
}

.select2-container .select2-selection--multiple .select2-selection__rendered {
	display: flex !important;
	padding: 0 0 0 5px!important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
	background-color: transparent !important;
	border-radius: 0 !important;
	border: none !important;
	color: #252525;
	font-size: .875rem;
	line-height: 1.4;
	padding: 0 !important;
	margin: 7px 0 0 12px !important;
	font-weight: 400;
	flex-direction: row-reverse;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
}

.panel-dropdown--location .select2-container--default .select2-selection--multiple .select2-selection__choice {
	margin-top: 12px !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice+.select2-selection__choice {
	margin: 8px 0 0 0 !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
	color: #252525 !important;
	margin-left: 8px;
	margin-right: 0 !important;
	display: none !important;
}

.select2-container--focus .select2-selection__choice+.select2-search input {
	opacity: 0;
}

.select2.country-select .select2-selection--single {
	background: #fff;
	border: 1px solid #dbdbdb;
	border-radius: 0;
	height: 51px;
	line-height: 51px;
}

.select2.country-select.select2-container--open.select2-container--below .select2-selection--single {
	background: #fff none;
}

.select2.country-select .select2-selection--single .select2-selection__rendered {
	line-height: 51px;
	padding-left: 20px;
	color: #232d54;
}

.country-select .select2-search--dropdown {
	padding: 0 10px;
}

.select2.country-select .select2-selection--single .select2-selection__arrow {
	background-image: none;
	background-color: #fff;
	border-radius: 0;
	height: 49px;
	width: 35px;
	border-left: 1px solid #dbdbdb;
}

.select2.country-select .select2-selection--single:focus {
	border: 1px solid #252525;
}

.select2-container--classic.country-select .select2-search--dropdown .select2-search__field {
	border: 1px solid #dbdbdb;
	padding: 0 10px;
	height: 40px;
	line-height: 40px;
}

.select2-container--classic.country-select.select2-container--open .select2-dropdown {
	border-color: #252525;
	border-radius: 0;
}

.select2-container--classic.country-select.select2-container--open .select2-selection--single {
	border: 1px solid #252525;
}

.select2-container--classic.country-select .select2-results__option--highlighted[aria-selected],
.select2-container--default .select2-results__option--highlighted[aria-selected] {
	background-color: #ececec !important;
	color: #252525 !important;
}

.select2-container--classic.country-select .select2-results__option {
	font-size: 1rem;
}

.select2-container--classic.country-select .select2-results>.select2-results__options {
	border-top: 1px solid #dbdbdb;
}

.select2-container--default .select2-results>.select2-results__options {
	max-height: 300px;
}

.select2-results__suggestion-title {
	font-size: .875rem;
	font-weight: 500;
	margin-bottom: 10px;
}

.select2-results__suggestion-item {
	font-weight: 500;
	font-size: .875rem;
	cursor: pointer;
	padding-left: 28px;
	position: relative;
	line-height: 1.4;
	margin-bottom: 8px;
	padding-top: 5px;
	padding-bottom: 5px;
}

.select2-results__suggestion-item:before {
	content: '';
	display: inline-block;
	width: 20px;
	height: 20px;
	background-image: url('/assets/img/svg/pin-2.svg');
	background-size: 20px 20px;
	background-repeat: no-repeat;
	top: 28%;
	left: 0;
	position: absolute;
}

.select2-results__suggestion-item > span {
	display: block;
	font-weight: 400;
	color: #666;
	font-size: .75rem;
}

.select2-results__suggestion-item:hover {
	background: #f9f9f9;
}

.filtering-button-wrapper {
	display: flex;
	justify-content: flex-end;
	margin-top: 3px;
}

a.filtering-button {
	padding: 4px 18px!important;
}

.show-filters-map-button {
	display: none!important;
	padding: 4px 12px!important;
}

/* NoUiSlider
------------------------------------- */
.nouislider-container {
	margin: 11px 14px;
	position: relative;
	padding-bottom: 30px;
}

.nouislider-container .nouislider-min-value,
.nouislider-container .nouislider-max-value {
	font-weight: 600;
	font-size: .75rem;
	position: absolute;
	color: #252525;
	top: 16px;
}

.nouislider-container .nouislider-min-value {
	left: -11px;
}

.nouislider-container .nouislider-max-value {
	right: -11px;
}

.noUi-target,.noUi-target *{-webkit-touch-callout:none;-webkit-tap-highlight-color:transparent;-webkit-user-select:none;-ms-touch-action:none;touch-action:none;-ms-user-select:none;-moz-user-select:none;user-select:none;box-sizing:border-box}.noUi-target{position:relative;direction:ltr}.noUi-base,.noUi-connects{width:100%;height:100%;position:relative;z-index:1}.noUi-connects{overflow:hidden;z-index:0}.noUi-connect,.noUi-origin{will-change:transform;position:absolute;z-index:1;top:0;left:0;height:100%;width:100%;transform-origin:0 0}html:not([dir=rtl]) .noUi-horizontal .noUi-origin{left:auto;right:0}.noUi-vertical .noUi-origin{width:0}.noUi-horizontal .noUi-origin{height:0}.noUi-handle{position:absolute}.noUi-state-tap .noUi-connect,.noUi-state-tap .noUi-origin{transition:transform .3s}.noUi-state-drag *{cursor:inherit!important}.noUi-horizontal{height:4px}.noUi-horizontal .noUi-handle{cursor: pointer; width:25px;height:25px;left:-17px;top:-11px}.noUi-vertical{width:18px}.noUi-vertical .noUi-handle{width:28px;height:28px;left:-6px;top:-17px}html:not([dir=rtl]) .noUi-horizontal .noUi-handle{right:-10px;left:auto}.noUi-target{background:#A2A2A2;border-radius:16px;}.noUi-connect{background:#000}.noUi-draggable{cursor:ew-resize}.noUi-vertical .noUi-draggable{cursor:ns-resize}.noUi-handle{border:1px solid #1F2732;border-radius:12px;background:#fff;cursor:default;box-shadow:0 2px 8px rgb(0 0 0 / 12%)}.noUi-active{box-shadow:inset 0 0 1px #FFF,inset 0 1px 7px #ddd,0 3px 6px -3px #BBB}.noUi-handle:after{left:13px}.noUi-vertical .noUi-handle:after,.noUi-vertical .noUi-handle:before{width:14px;height:1px;left:6px;top:14px}.noUi-vertical .noUi-handle:after{top:17px}[disabled] .noUi-connect{background:#B8B8B8}[disabled] .noUi-handle,[disabled].noUi-handle,[disabled].noUi-target{cursor:not-allowed}.noUi-pips,.n.noUi-handleoUi-pips *{box-sizing:border-box}.noUi-pips{position:absolute;color:#999}.noUi-value{position:absolute;white-space:nowrap;text-align:center}.noUi-value-sub{color:#ccc;font-size:10px}.noUi-marker{position:absolute;background:#ccc}.noUi-marker-sub{background:#aaa}.noUi-marker-large{background:#aaa}.noUi-pips-horizontal{padding:10px 0;height:80px;top:100%;left:0;width:100%}.noUi-value-horizontal{transform:translate(-50%,50%)}.noUi-rtl .noUi-value-horizontal{transform:translate(50%,50%)}.noUi-marker-horizontal.noUi-marker{margin-left:-1px;width:2px;height:5px}.noUi-marker-horizontal.noUi-marker-sub{height:10px}.noUi-marker-horizontal.noUi-marker-large{height:15px}.noUi-pips-vertical{padding:0 10px;height:100%;top:0;left:100%}.noUi-value-vertical{transform:translate(0,-50%);padding-left:25px}.noUi-rtl .noUi-value-vertical{transform:translate(0,50%)}.noUi-marker-vertical.noUi-marker{width:5px;height:2px;margin-top:-1px}.noUi-marker-vertical.noUi-marker-sub{width:10px}.noUi-marker-vertical.noUi-marker-large{width:15px}.noUi-tooltip{display:none;position:absolute;border:1px solid #D9D9D9;border-radius:3px;background:#fff;color:#000;padding:5px;text-align:center;white-space:nowrap}.noUi-horizontal .noUi-tooltip{transform:translate(-50%,0);left:50%;bottom:120%}.noUi-vertical .noUi-tooltip{transform:translate(0,-50%);top:50%;right:120%}

@media (max-width: 767px) {
	.noUi-tooltip {
		display: block;
		line-height: 1.2;
		font-size: .875rem
	}

	.nouislider-container .nouislider-min-value,
	.nouislider-container .nouislider-max-value {
		display: none;
	}

	.own-review-modal-overlay {
		right: 0;
	}

	.own-review-modal--opening {
		animation: slideFromBottom .3s cubic-bezier(0.39, 0.575, 0.565, 1);
	}

	.own-review-modal--closing {
		animation: slideFromBottom .3s cubic-bezier(0.39, 0.575, 0.565, 1) reverse backwards;
	}

	.nouislider-title {
		margin: 0 auto;
		display: block;
		text-align: center;
		margin-top: 10px;
		font-size: 0.938rem;
	}

	.section-faq {
		margin: 60px 0;
	}

	.faq-title {
		font-size: 1.728rem;
	}
}

#frm-homepageSearchForm-destination,
#location-search-input {
	display: none;
}

/* Light Gallery
------------------------------------- */
.lg-outer {
	z-index: 1102;
}

.lg-backdrop {
	z-index: 1101;
}

.lg-backdrop.in {
	opacity: .9;
}

.lg-on {
	overflow: hidden;
}

/* ------------------------------------------------------------------- */
/*  05. Others
---------------------------------------------------------------------- */

/* Common Styles
------------------------------------- */

html {
	font-size: 16px;
}

body {
	color: #333;
	font-size: 0.938rem; /* 15px */
	line-height: 1.6;
	overflow-x: hidden;
}

.background-white {
	background-color: #fff;
}

.background-gray {
	background-color: #f7f7f7;
}

.background-gray--with-border {
	border: 1px solid #ececec;
}

.section-border-top {
	border-top: 1px solid #ececec;
}

.section-border-bottom {
	border-bottom: 1px solid #ececec;
}

a {
	color: #20399d;
}

ul.no-style {
	list-style: none;
	padding-left: 0;
}

.cursor-pointer {
	cursor: pointer;
}

/* Main Font */
body, h5, h6, .map-box p, .map-box div,
body .menu-responsive i.menu-trigger:after {
	font-family: var(--font-primary);
	font-weight: normal;
	text-transform: none;
}

/* Headlines */
h1, h2, h3, h4, h5, h6 {
	color: #252525;
}

h1, h2, h3, h4 {
	font-family: 'Butler', sans-serif;
	font-weight: 600;
}

h1.default-font,
h2.default-font,
h3.default-font,
h4.default-font {
	font-weight: normal;
}

h1 strong,
h2 strong,
h3 strong,
h4 strong {
	font-weight: 600;
}

h1.upper,
h2.upper,
h3.upper,
h4.upper {
	text-transform: uppercase;
}

h1.white,
h2.white,
h3.white,
h4.white,
h5.white,
h6.white {
	color: #fff;
}

h3.headline {
	font-weight: 600;
	font-size: 2.986rem;
	line-height: 120%;
	margin: 0 0 30px 0;
}

h3.headline.smaller {
	font-size: 2.488rem;
	line-height: 120%;
}

h3.headline strong {
	font-weight: 600;
}

h2.headline {
	font-size: 34px;
	line-height: 40px;
	margin: 0 0 30px 0;
}

h2.headline span {
	font-size: 1rem;
	line-height: 1.4;
}

.headline span {
	font-family: var(--font-primary);
	font-size: 1rem;
	line-height: 1.4;
	margin-top: 5px;
	color: #252525;
	font-weight: 400;
	display: block;
	text-transform: initial;
	letter-spacing: 0;
}

.headline.upper {
	text-transform: uppercase;
}

h4.headline {
	font-size: 22px;
	line-height: 32px;
	margin: -5px 0 30px 0;
}

.headline.centered {
	text-align: center;
}

.headline-top-subtitle {
	color: #333;
	margin-bottom: 5px;
	line-height: 1.4;
	font-size: .875rem;
}

.border-top {
	border-top: 1px solid #e8e8e8;
}

mark {
	font-weight: 500;
}

hr.double-line {
	border-top-width: 3px;
}

hr.darken {
	border-top-color: #ddd;
}

a.main-color {
	color: #252525;
}

.block-page h1 {
	font-weight: 600;
	font-size: 2.074rem;
}

.block-page h3 {
	line-height: 1.2;
	font-size: 1.25rem;
	font-family: var(--font-primary);
}

.special-offer {
	position: relative;
}

.special-offer-indicator {
	position: absolute;
	top: 11%;
	left: 50%;
	width: .3125rem;
	height: .3125rem;
	transform: translateX(-50%);
	background: #007AFF;
	border-radius: 100%;
}


/* Offsets
------------------------------------- */

/* Margin Top */
.margin-top-0  { margin-top: 0 !important; }
.margin-top-1  { margin-top: 1px !important; }
.margin-top-2  { margin-top: 2px !important; }
.margin-top-3  { margin-top: 3px !important; }
.margin-top-4  { margin-top: 4px !important; }
.margin-top-5  { margin-top: 5px !important; }
.margin-top-6  { margin-top: 6px !important; }
.margin-top-7  { margin-top: 7px !important; }
.margin-top-8  { margin-top: 8px !important; }
.margin-top-9  { margin-top: 9px !important; }
.margin-top-10 { margin-top: 10px !important; }
.margin-top-15 { margin-top: 15px !important; }
.margin-top-20 { margin-top: 20px !important; }
.margin-top-25 { margin-top: 25px !important; }
.margin-top-30 { margin-top: 30px !important; }
.margin-top-35 { margin-top: 35px !important; }
.margin-top-40 { margin-top: 40px !important; }
.margin-top-45 { margin-top: 45px !important; }
.margin-top-50 { margin-top: 50px !important; }
.margin-top-55 { margin-top: 55px !important; }
.margin-top-60 { margin-top: 60px !important; }
.margin-top-65 { margin-top: 65px !important; }
.margin-top-70 { margin-top: 70px !important; }
.margin-top-75 { margin-top: 75px !important; }
.margin-top-80 { margin-top: 80px !important; }
.margin-top-85 { margin-top: 85px !important; }
.margin-top-90 { margin-top: 90px !important; }
.margin-top-95 { margin-top: 95px !important; }

/* Margin Bottom */
.margin-bottom-0  { margin-bottom: 0 !important; }
.margin-bottom-5  { margin-bottom: 5px !important; }
.margin-bottom-10 { margin-bottom: 10px !important; }
.margin-bottom-15 { margin-bottom: 15px !important; }
.margin-bottom-20 { margin-bottom: 20px !important; }
.margin-bottom-25 { margin-bottom: 25px !important; }
.margin-bottom-30 { margin-bottom: 30px !important; }
.margin-bottom-35 { margin-bottom: 35px !important; }
.margin-bottom-40 { margin-bottom: 40px !important; }
.margin-bottom-45 { margin-bottom: 45px !important; }
.margin-bottom-50 { margin-bottom: 50px !important; }
.margin-bottom-55 { margin-bottom: 55px !important; }
.margin-bottom-55 { margin-bottom: 55px !important; }
.margin-bottom-55 { margin-bottom: 55px !important; }
.margin-bottom-55 { margin-bottom: 55px !important; }
.margin-bottom-60 { margin-bottom: 60px !important; }
.margin-bottom-65 { margin-bottom: 65px !important; }
.margin-bottom-70 { margin-bottom: 70px !important; }
.margin-bottom-75 { margin-bottom: 75px !important; }
.margin-bottom-80 { margin-bottom: 80px !important; }
.margin-bottom-85 { margin-bottom: 85px !important; }
.margin-bottom-90 { margin-bottom: 90px !important; }
.margin-bottom-95 { margin-bottom: 95px !important; }

/* Margin Left */
.margin-left-0  { margin-left: 0 !important; }
.margin-left-1  { margin-left: 1px !important; }
.margin-left-2  { margin-left: 2px !important; }
.margin-left-3  { margin-left: 3px !important; }
.margin-left-4  { margin-left: 4px !important; }
.margin-left-5  { margin-left: 5px !important; }
.margin-left-6  { margin-left: 6px !important; }
.margin-left-7  { margin-left: 7px !important; }
.margin-left-8  { margin-left: 8px !important; }
.margin-left-9  { margin-left: 9px !important; }
.margin-left-10 { margin-left: 10px !important; }
.margin-left-15 { margin-left: 15px !important; }
.margin-left-20 { margin-left: 20px !important; }
.margin-left-25 { margin-left: 25px !important; }
.margin-left-30 { margin-left: 30px !important; }
.margin-left-35 { margin-left: 35px !important; }
.margin-left-40 { margin-left: 40px !important; }
.margin-left-45 { margin-left: 45px !important; }
.margin-left-50 { margin-left: 50px !important; }
.margin-left-55 { margin-left: 55px !important; }
.margin-left-60 { margin-left: 60px !important; }
.margin-left-65 { margin-left: 65px !important; }
.margin-left-70 { margin-left: 70px !important; }
.margin-left-75 { margin-left: 75px !important; }
.margin-left-80 { margin-left: 80px !important; }
.margin-left-85 { margin-left: 85px !important; }
.margin-left-90 { margin-left: 90px !important; }
.margin-left-95 { margin-left: 95px !important; }

/* Margin Right */
.margin-right-0  { margin-right: 0 !important; }
.margin-right-1  { margin-right: 1px !important; }
.margin-right-2  { margin-right: 2px !important; }
.margin-right-3  { margin-right: 3px !important; }
.margin-right-4  { margin-right: 4px !important; }
.margin-right-5  { margin-right: 5px !important; }
.margin-right-6  { margin-right: 6px !important; }
.margin-right-7  { margin-right: 7px !important; }
.margin-right-8  { margin-right: 8px !important; }
.margin-right-9  { margin-right: 9px !important; }
.margin-right-10 { margin-right: 10px !important; }
.margin-right-15 { margin-right: 15px !important; }
.margin-right-20 { margin-right: 20px !important; }
.margin-right-25 { margin-right: 25px !important; }
.margin-right-30 { margin-right: 30px !important; }
.margin-right-35 { margin-right: 35px !important; }
.margin-right-40 { margin-right: 40px !important; }
.margin-right-45 { margin-right: 45px !important; }
.margin-right-50 { margin-right: 50px !important; }
.margin-right-55 { margin-right: 55px !important; }
.margin-right-60 { margin-right: 60px !important; }
.margin-right-65 { margin-right: 65px !important; }
.margin-right-70 { margin-right: 70px !important; }
.margin-right-75 { margin-right: 75px !important; }
.margin-right-80 { margin-right: 80px !important; }
.margin-right-85 { margin-right: 85px !important; }
.margin-right-90 { margin-right: 90px !important; }
.margin-right-95 { margin-right: 95px !important; }
.margin-right-100 { margin-right: 100px !important; }


/* Padding Top */
.padding-top-0  { padding-top: 0 !important; }
.padding-top-1  { padding-top: 1px !important; }
.padding-top-2  { padding-top: 2px !important; }
.padding-top-3  { padding-top: 3px !important; }
.padding-top-4  { padding-top: 4px !important; }
.padding-top-5  { padding-top: 5px !important; }
.padding-top-6  { padding-top: 6px !important; }
.padding-top-7  { padding-top: 7px !important; }
.padding-top-8  { padding-top: 8px !important; }
.padding-top-9  { padding-top: 9px !important; }
.padding-top-10 { padding-top: 10px !important; }
.padding-top-15 { padding-top: 15px !important; }
.padding-top-20 { padding-top: 20px !important; }
.padding-top-25 { padding-top: 25px !important; }
.padding-top-30 { padding-top: 30px !important; }
.padding-top-35 { padding-top: 35px !important; }
.padding-top-40 { padding-top: 40px !important; }
.padding-top-45 { padding-top: 45px !important; }
.padding-top-50 { padding-top: 50px !important; }
.padding-top-55 { padding-top: 55px !important; }
.padding-top-60 { padding-top: 60px !important; }
.padding-top-65 { padding-top: 65px !important; }
.padding-top-70 { padding-top: 70px !important; }
.padding-top-75 { padding-top: 75px !important; }
.padding-top-80 { padding-top: 80px !important; }
.padding-top-85 { padding-top: 85px !important; }
.padding-top-90 { padding-top: 90px !important; }
.padding-top-95 { padding-top: 95px !important; }
.padding-top-100 { padding-top: 100px !important; }
.padding-top-105 { padding-top: 105px !important; }
.padding-top-110 { padding-top: 110px !important; }
.padding-top-115 { padding-top: 115px !important; }
.padding-top-120 { padding-top: 120px !important; }

/* Padding Bottom */
.padding-bottom-0  { padding-bottom: 0 !important; }
.padding-bottom-5  { padding-bottom: 5px !important; }
.padding-bottom-10 { padding-bottom: 10px !important; }
.padding-bottom-15 { padding-bottom: 15px !important; }
.padding-bottom-20 { padding-bottom: 20px !important; }
.padding-bottom-25 { padding-bottom: 25px !important; }
.padding-bottom-30 { padding-bottom: 30px !important; }
.padding-bottom-35 { padding-bottom: 35px !important; }
.padding-bottom-40 { padding-bottom: 40px !important; }
.padding-bottom-45 { padding-bottom: 45px !important; }
.padding-bottom-50 { padding-bottom: 50px !important; }
.padding-bottom-55 { padding-bottom: 55px !important; }
.padding-bottom-60 { padding-bottom: 60px !important; }
.padding-bottom-65 { padding-bottom: 65px !important; }
.padding-bottom-70 { padding-bottom: 70px !important; }
.padding-bottom-75 { padding-bottom: 75px !important; }
.padding-bottom-80 { padding-bottom: 80px !important; }
.padding-bottom-85 { padding-bottom: 85px !important; }
.padding-bottom-90 { padding-bottom: 90px !important; }
.padding-bottom-95 { padding-bottom: 95px !important; }
.padding-bottom-100 { padding-bottom: 100px !important; }

/* Padding Right */
.padding-right-0  { padding-right: 0 !important; }
.padding-right-5  { padding-right: 5px !important; }
.padding-right-10 { padding-right: 10px !important; }
.padding-right-15 { padding-right: 15px !important; }
.padding-right-20 { padding-right: 20px !important; }
.padding-right-25 { padding-right: 25px !important; }
.padding-right-30 { padding-right: 30px !important; }
.padding-right-35 { padding-right: 35px !important; }
.padding-right-40 { padding-right: 40px !important; }
.padding-right-45 { padding-right: 45px !important; }
.padding-right-50 { padding-right: 50px !important; }
.padding-right-55 { padding-right: 55px !important; }
.padding-right-55 { padding-right: 55px !important; }
.padding-right-55 { padding-right: 55px !important; }
.padding-right-55 { padding-right: 55px !important; }
.padding-right-60 { padding-right: 60px !important; }
.padding-right-65 { padding-right: 65px !important; }
.padding-right-70 { padding-right: 70px !important; }
.padding-right-75 { padding-right: 75px !important; }
.padding-right-80 { padding-right: 80px !important; }
.padding-right-85 { padding-right: 85px !important; }
.padding-right-90 { padding-right: 90px !important; }
.padding-right-95 { padding-right: 95px !important; }
.padding-right-100 { padding-right: 100px !important; }

/* Padding Left */
.padding-left-0  { padding-left: 0 !important; }
.padding-left-5  { padding-left: 5px !important; }
.padding-left-10 { padding-left: 10px !important; }
.padding-left-15 { padding-left: 15px !important; }
.padding-left-20 { padding-left: 20px !important; }
.padding-left-25 { padding-left: 25px !important; }
.padding-left-30 { padding-left: 30px !important; }
.padding-left-35 { padding-left: 35px !important; }
.padding-left-40 { padding-left: 40px !important; }
.padding-left-45 { padding-left: 45px !important; }
.padding-left-50 { padding-left: 50px !important; }
.padding-left-55 { padding-left: 55px !important; }
.padding-left-55 { padding-left: 55px !important; }
.padding-left-55 { padding-left: 55px !important; }
.padding-left-55 { padding-left: 55px !important; }
.padding-left-60 { padding-left: 60px !important; }
.padding-left-65 { padding-left: 65px !important; }
.padding-left-70 { padding-left: 70px !important; }
.padding-left-75 { padding-left: 75px !important; }
.padding-left-80 { padding-left: 80px !important; }
.padding-left-85 { padding-left: 85px !important; }
.padding-left-90 { padding-left: 90px !important; }
.padding-left-95 { padding-left: 95px !important; }
.padding-left-100 { padding-left: 100px !important; }

.hide-on-mobile {
	display: block;
}

.hide-on-mobile.hide-on-mobile--inline-block {
	display: inline-block;
}

.show-on-mobile {
	display: none;
}

.relative {
	position: relative;
}

.show-on-tablet {
	display: none;
}

/* Loaders */
.booking-loader {
	background: rgba(255, 255, 255, .8);
	height: 100%;
	left: 0;
	margin: auto;
	position: absolute;
	right: 0;
	top: 0;
	width: 100%;
	z-index: 9999;
	border-radius: 3px;
	border: 1px solid #dcdbdb;
	display: none;
}

/* Flexible dates on calendar */

.flatpickr-calendar--flexible-dates.open {
	max-height: unset;
}

.toggle-calendar {
	display: inline-flex;
	align-items: center;
	width: fit-content;
	padding: 4px;
	border-radius: 300px;
	border: 1px solid #D0D5DD;
}

.toggle-calendar-flexible-days-wrapper {
	border-radius: 0;
	border: none;
	gap: 4px;
	overflow: auto;
	width: 100%;
	justify-content: center;
}

.toggle-calendar-btn {
	border: 1px solid transparent;
	background: transparent;
	color: #101828;
	font-size: 13px;
	font-weight: 400;
	border-radius: 25px;
	padding: 4px 10px;
	min-width: 100px;
}

.toggle-calendar-btn-flexible-day {
	border-radius: 8px;
	border: 1px solid #D0D5DD;
	min-width: unset;
	white-space: nowrap;
}

.toggle-calendar-btn.active {
	color: #fff;
	background: #1D2939;
	border: 1px solid #D0D5DD;
}

.calendar-bottom-action__bottom {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	margin: 16px;
}

.calendar-bottom-action__bottom-number-of-nights {
	color:#1D2939;
	font-size: .875rem;
	font-weight: 500;
	margin-bottom: 0;
}

.calendar-bottom-action__bottom-number-of-nights-wrapper {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.calendar-bottom-action__bottom-number-of-nights-extended-info {
	color: #1D2939;
	font-size: .75;
	font-weight: 400;
	margin-bottom: 0;
}

.search-block__label--dates-box--flexible {
	position: absolute;
	left: 190px;
	top: 50%;
	transform: translate(0, -50%);
}

.search-block__label--dates-box--flexible.flexible-listing-page {
	left: 240px;
	top: 51%;
}

.btn-calendar-confirm {
	text-transform: capitalize;
	padding: 8px 34px;
}

.calendar-bottom-info {
	display: flex;
	flex-direction: row;
	gap: 8px;
	margin: 16px 0 8px;
	align-items: center;
	justify-content: center;
}

.flexible-month-label-icon {
	width: 20px;
	height: 20px;
	max-width: 20px;
	max-height: 20px;
	margin-bottom: 4px;
	background-image: url('assets/img/svg/calendar-unchecked.svg');
	background-repeat: no-repeat;
	opacity: .7;
}

.flexible-month-label.active .flexible-month-label-icon {
	background-image: url('assets/img/svg/calendar-checked.svg');
	opacity: 1;
}

.flexible-wrapper {
	margin-top: 86px;
}

.flexible-month-list {
	display: grid;
	grid-template-columns: repeat(6, 1fr);
	grid-template-rows: 1fr;
	gap: 10px;
	margin-top: 20px;
}

.calendar-bottom-action__bottom-number-of-nights-wrapper__show-on-mobile {
	display: none;
}

.flexible-month-label {
	font-size: 12px;
	font-weight: 400;
	color: #101828;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: 8px;
	border: 1px solid #D0D5DD;
	background: #FFF;
	padding: 6px;
	line-height: normal;
	cursor: pointer;
	transition: all .1s;
	user-select: none;
}

.flexible-month-label.disabled {
	opacity: .5;
	cursor: not-allowed;
}

.flexible-month-label:hover {
	background: #fafafa;
	border: 1px solid #bfc3c9;
}

.flexible-month-label.active,
.flexible-month-label.active:hover {
	border-color: #101828;
	color: #101828;
	background: #fafafa;
}

.flexible-month-label:active,
.flexible-month-label:focus {
	transform: scale(.95);
}

.flexible-month-label-month {
	font-weight: 500;
}

.toggle-calendar-main-wrapper {
	margin-bottom: 16px;
}

.calendar-bottom-info-icon {
	width: 16px;
	height: 16px;
	max-width: 16px;
}

.search-block__label:before, .search-block__dummy-label:before {
	content: '';
	display: inline-block;
	width: 22px;
	height: 22px;
	background-size: 22px 22px;
	background-repeat: no-repeat;
	vertical-align: middle;
	position: relative;
}

.flexible-months-bottom-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	margin-top: 100px;
}

.flexible-months-bottom-left {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.flexible-months-bottom-left--mobile {
	display: none;
}

.flexible-months-bottom-main-label {
	color: #1D2939;
	font-size: 18px;
	font-weight: 600;
}

.flexible-months-bottom-extended-info {
	color: #1D2939;
	font-size: 16px;
	font-weight: 400;
}

.visible-0 {
	visibility: hidden;
}

.flatpickr-calendar--flexible-dates span.flatpickr-day.flexible-date:not(.disabled) {
	background-image: url('/assets/img/svg/calendar-day-border.svg');
	background-repeat: no-repeat;
	background-position: center;
	background-size: 94%;
}

.calendar-bottom-info__icon {
	max-height: 14px;
	margin-top: 2px;
}

.flatpickr-calendar--flexible-dates .flatpickr-months .flatpickr-prev-month,
.flatpickr-calendar--flexible-dates .flatpickr-months .flatpickr-next-month {
	position: static;
	padding-bottom: 0;
	margin-top: 4px;
}

/* flexible dates END */


/* Flatpickr */
.daterange-picker + input {
	display: none;
}

.flatpickr-hidden {
	display: none;
}

.flatpickr-calendar {
	border-radius: 0 !important;
}

.flatpickr-day {
	line-height: 47px !important;
	height: 47px !important;
	color: #252525 !important;
}

.departure-only {
	color: #999999 !important;
	cursor: not-allowed !important;
}

.flatpickr-day.disabled {
	color: #a0a0a0 !important;
}

.flatpickr-day.booked {
	color: #d4d4d4 !important;
	text-decoration: line-through;
	background-color: #f1f1f1;
}

.flatpickr-day.booked:hover {
	background-color: #f1f1f1 !important;
}

.flatpickr-day.optional-arrival-bolded {
	font-weight: 500;
}

.flatpickr-day.optional-arrival.disabled.startRange,
.date-rangepicker + .flatpickr-calendar .flatpickr-day.optional-arrival.disabled.startRange {
	color: #66530E !important;
}

.flatpickr-day.only-saturday.disabled {
	cursor: pointer !important;
	background: #fff !important;
	color: #252525 !important;
}

.flatpickr-day.only-saturday.disabled:hover {
	background: #fff !important;
}

.flatpickr-day.only-saturday.disabled.inRange {
	background: #f3e8b3 !important;
}

.flatpickr-day.only-saturday.disabled.inRange:hover {
	color: #252525 !important;
}

.flatpickr-day.only-saturday.disabled.notAllowed,
.flatpickr-day.saturday.notAllowed,
.flatpickr-day.only-saturday.notAllowed:hover,
.flatpickr-day.saturday.notAllowed:hover,
.flatpickr-day.optional-arrival.notAllowed,
.flatpickr-day.optional-arrival.notAllower:hover {
	cursor: not-allowed !important;
	color: rgba(64,72,72,.1) !important;
	background: #fdfdfd !important;
}

.flatpickr-day.optional-arrival.disabled.startRange:hover,
.flatpickr-day.selected.disabled.startRange:hover {
	background: #F1DD98 !important;
}

.flatpickr-day.selected.startRange {
	color: #404848 !important;
	background: #F1DD98 !important;
	border-top-left-radius: 6px !important;
	border-bottom-left-radius: 6px !important;
}

.flatpickr-day.selected.endRange {
	color: #404848 !important;
	background: #F1DD98 !important;
	border-top-right-radius: 6px !important;
	border-bottom-right-radius: 6px !important;
}

.flatpickr-day.selected.available.startRange,
.flatpickr-day.selected.available.startRange:hover,
.flatpickr-day.selected.available.startRange.prevMonthDay {
	color: #66530E !important;
	background: #F1DD98 !important;
	border-top-left-radius: 6px !important;
	border-bottom-left-radius: 6px !important;
	font-weight: 500;
}

.flatpickr-day.selected.available.endRange,
.flatpickr-day.selected.available.endRange:hover,
.flatpickr-day.selected.disabled.endRange:hover {
	color: #404848 !important;
	background: #F1DD98 !important;
	border-radius: 6px !important;
	font-weight: 600;
}

.flatpickr-day.selected.available.departure-date-radius,
.flatpickr-day.selected.available.departure-date-radius:hover,
.flatpickr-day.inRange + .flatpickr-day.selected.available.endRange,
.flatpickr-day.inRange + .flatpickr-day.selected.available.endRange:hover {
	border-top-left-radius: 0 !important;
	border-bottom-left-radius: 0 !important;
}

.flatpickr-day[data-location="mp"]:not(.not-writeable-for-range).inRange.available + .flatpickr-day.selected.available.endRange {
	border-top-left-radius: 0 !important;
	border-bottom-left-radius: 0 !important;
}


.flatpickr-day.selected.available.startRange.hidden,
.flatpickr-day.selected.available.endRange.hidden,
.flatpickr-day.selected.available.startRange.hidden:hover,
.flatpickr-day.endRange.hidden:hover {
	background: #fdfdfd !important;
}

.flatpickr-day.startRange {
	color: #404848 !important;
	background: #f4e8b3 !important;
	font-weight: 600 !important;
}

.flatpickr-day.selected,
.flatpickr-day.inRange,
.flatpickr-day.endRange {
	font-weight: 600;
	box-shadow: none !important;
}

span.flatpickr-weekday {
	font-size: .875rem !important;
	font-weight: 400 !important;
	color: #252525 !important;
}

.flatpickr-day-price {
	font-size: .833rem;
	font-weight: 600;
	display: block;
	line-height: 1.2;
}

.date-rangepicker + .flatpickr-calendar.inline .flatpickr-days .dayContainer {
	justify-content: flex-start !important;
	border-top: 1px solid #ddd;
	border-left: 1px solid #ddd;
}

.flatpickr-months .flatpickr-month {
	color: #252525 !important;
}

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
	color: #252525 !important;
	font-weight: 600 !important;
}

.flatpickr-current-month .numInputWrapper {
	width: 5ch;
}

.flatpickr-month .numInputWrapper span {
	display: none;
}

.date-rangepicker + .flatpickr-calendar.inline {
	padding: 10px;
	border-radius: 3px;
	box-shadow: 0 1px 4px 1px rgb(0 0 0 / 6%);
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day {
	margin-top: 0 !important;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.disabled {
	color: #b0b0b0 !important;
	background: transparent;
	font-weight: 400;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.disabled.selected {
	text-decoration: none;
	color: #404848 !important;
	background: #F1DD98 !important;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.disabled.startRange {
	text-decoration: none;
}

.date-rangepicker + .flatpickr-calendar.inline,
.date-rangepicker + .flatpickr-calendar.inline .flatpickr-days {
	width: 100% !important;
}

.dayContainer + .dayContainer {
	box-shadow: none !important;
}

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
	box-shadow: none !important;
}

.numInputWrapper:hover,
.flatpickr-current-month span.cur-month:hover {
	background: transparent !important;
}

.date-rangepicker + .flatpickr-calendar.inline .flatpickr-weekdays .flatpickr-weekdaycontainer {
	padding: 0 10px;
}

.date-rangepicker + .flatpickr-calendar.inline .flatpickr-day.inRange {
	box-shadow: none !important;
}

.flatpickr-rContainer {
	width: 100%;
	padding: 10px;
}

.date-rangepicker + .flatpickr-calendar.inline .dayContainer {
	width: 50%;
	max-width: 50%;
	border-left: 1px solid #ddd;
	margin: 0 10px;
	border-top: 1px solid #ddd;
}
.flatpickr-months .flatpickr-prev-month svg,
.flatpickr-months .flatpickr-next-month svg {
	width: 16px !important;
	height: 16px !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
	height: 36px !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
	right: 15px;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
	left: 15px;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
	top: 12px !important;
	padding: 6px;
	height: 29px !important;
}

span.flatpickr-day,
span.flatpickr-day.prevMonthDay,
span.flatpickr-day.nextMonthDay {
	border: 0 !important;
}

span.flatpickr-day,
span.flatpickr-day.prevMonthDay,
span.flatpickr-day.nextMonthDay {
	border-right: 1px solid #ddd !important;
	border-bottom: 1px solid #ddd !important;
}

.flatpickr-day:hover {
	background: #F8EFD0 !important;
	box-shadow: none !important;
	border-radius: 0;
}

.flatpickr-day.inRange,
.flatpickr-day.inRange:hover,
.flatpickr-day.inRange.disabled:hover,
.date-rangepicker + .flatpickr-calendar .flatpickr-day.disabled:not(.hidden).inRange {
	color: #66530e !important;
	background: #F8EFD0 !important;
	border-radius: 0 !important;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.disabled.not-writeable-for-range.inRange {
	background: #fff !important;
	color: #b0b0b0 !important;
	text-decoration: none !important;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.booked.not-writeable-for-range.inRange {
	background: #fff !important;
	color: #b0b0b0 !important;
	text-decoration: line-through !important;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.not-writeable-for-range.inRange {
	background: #fff !important;
	color: #404848 !important;
	text-decoration: none !important;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.arrival-possible.not-writeable-for-range.inRange {
	background: #fff !important;
	color: #000 !important;
	text-decoration: underline !important;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.arrival-possible.disabled.inRange {
	color: #66530E !important;
	text-decoration: none !important;
}


.date-rangepicker + .flatpickr-calendar .flatpickr-day.arrival-possible.disabled {
	color: #b0b0b0 !important;
	/* text-decoration: none !important; */
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.departure-possible.disabled {
	color: #b0b0b0 !important;
	text-decoration: none !important;
}

.flatpickr-day.inRange.disabled:hover,
.flatpickr-day.inRange.disabled {
	color: rgba(64,72,72,.1) !important;
	text-decoration: none !important;
}

.flatpickr-day.hidden {
	display: inline-block !important;
	text-indent: -9999px;
	visibility: visible !important;
	background: transparent !important;
	cursor: not-allowed !important;
	color: rgba(64,72,72,.1) !important;
}

.flatpickr-day.hidden.only-saturday.disabled {
	background: #fdfdfd !important;
}

.flatpickr-day.hidden.only-saturday.disabled:hover {
	cursor: not-allowed !important;
	background: #fdfdfd !important;
}

.flatpickr-day.hidden.endRange,
.flatpickr-day.hidden.selected,
.flatpickr-day.hidden.selected:hover {
	background: #fdfdfd !important;
}

.flatpickr-day.selected,
.flatpickr-day.selected:hover,
.flatpickr-day.endRange,
.flatpickr-day.endRange:hover {
	color: #404848 !important;
	background: #F1DD98 !important;
}

.flatpickr-day.endRange,
.flatpickr-day.endRange:hover {
	border-top-right-radius: 6px !important;
	border-bottom-right-radius: 6px !important;
}

.flatpickr-day.disabled:hover,
.flatpickr-day.hidden:hover {
	background: #fff !important;
}

.flatpickr-day.disabled.booked {
	color: #a0a0a0 !important;
}

.flatpickr-day.hidden.disabled.booked {
	background: transparent !important;
}

.flatpickr-calendar:after {
	border-width: 9px !important;
	margin: 0 -9px !important;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
	box-shadow: none !important;
}

.restricted-range-minimal-days-of-stay {
	cursor: not-allowed !important;
	color: #b0b0b0 !important;
	text-decoration: none;
}

.flatpickr-day.today {
	color: #0071E3;
	background: #fff;
}

.flatpickr-wrapper {
	width: 100%;
}

.flatpickr-wrapper .flatpickr-input {
	margin-bottom: 0 !important;
}

.calendar--single .flatpickr-wrapper .flatpickr-input {
	margin-bottom: 16px !important;
}

.arival-departure-date-container {
	margin-bottom: 10px;
}

.flatpickr-calendar.static {
	top: 100% !important;
}

.date-mainpicker + .flatpickr-calendar.static {
	right: 0;
	width: 660px !important;
}

.flatpickr-innerContainer .object-not-available {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 80%;
	border-width: 2px;
	text-align: center;
	opacity: .9;
	z-index: 100;
	font-size: 1rem;
}

.flatpickr-innerContainer .object-not-available:hover {
	opacity: 1;
}

.flatpickr-month .numInputWrapper .numInput {
	box-shadow: none;
}

.flatpickr-content-after {
	font-size: .875rem;
	display: flex;
	color: #252525;
	margin: 5px 20px;
	padding-top: 20px;
	padding-bottom: 10px;
	border-top: 1px solid #a6a6a6;
	justify-content: space-between;
	align-items: flex-end;
	margin-top: 0;
}

.flatpickr-content-after__clear {
	cursor: pointer;
	text-decoration: underline;
	padding: 5px;
	display: none;
}

.flatpickr-content-after__left {
	display: flex;
	justify-content: flex-start;
	flex-direction: column;
	align-items: flex-start;
}

.flatpickr-content-after__information:before {
	content: '';
	display: inline-block;
	vertical-align: middle;
	width: 16px;
	height: 16px;
	margin-right: 6px;
	background-image: url('/assets/img/svg/info.svg');
	background-size: 16px 16px;
	background-repeat: no-repeat;
}

.flatpickr-content-after__legend-item--not-available {
	margin-right: 20px;
}

.flatpickr-content-after__legend-item--not-available:before,
.flatpickr-content-after__legend-item--closed-for-arrival:before {
	content: '23';
	display: inline-block;
	margin-right: 10px;
	text-decoration: line-through;
	padding: 5px;
	color: #a0a0a0;
}

.flatpickr-content-after__legend-item--closed-for-arrival:before {
	color: #a0a0a0;
	background: #fff;
	font-size: .75rem;
	line-height: 1.2;
	border: 1px solid #dddee4;
	border-radius: 6px;
	text-decoration: none;
}

.flatpickr-content-after__legend-item--special-offer {
	position: relative;
	border: 1px solid #999;
	padding: 6px 10px;
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: .75rem;
	border-radius: 4px;
	color: #333;
	line-height: 18px;
}

.flatpickr-content-after__legend-icon--discount {
	content: url("/assets/img/svg/discount.svg");
	filter: invert(100%);
	width: 10px;
	height: 10px;
}

.flatpickr-content-after--single-vila {
	padding-bottom: 20px;
	margin: 0 20px;
	align-items: center;
}

.flatpickr-content-after__clear--flex {
	display: flex;
	align-items: center;
	padding: 5px 0;
}

.flatpickr-content-after__legend-item--special-offer-dot {
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background: #007AFF;
	flex: 1 0 5px;
}

.flatpickr-content-after .legend.minimum-stay:before {
	content: '';
	width: 7px;
	height: 7px;
	border-radius: 50%;
	background: #d8c66c;
	display: inline-block;
	margin-right: 7px;
}

.flatpickr-close-icon {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	right: 20px;
	display: flex;
	padding: 5px;
	cursor: pointer;
	z-index: 101;
}

.flatpickr-close-icon:before {
	content: '';
	display: inline-block;
	vertical-align: middle;
	width: 14px;
	height: 14px;
	background-image: url('/assets/img/svg/close.svg');
	background-size: 14px 14px;
	background-repeat: no-repeat;
}

.calendar--borderless .flatpickr-calendar {
	padding: 0 !important;
	margin-top: 8px;
	border: 1px solid #ddd;
	border-radius: 4px !important;
	box-shadow: 0 4px 10px 0 rgba(0,0,0,.1);
}

.calendar--borderless .flatpickr-calendar--flexible-dates {
	padding: 0 !important;
}

.main-search-input-item.calendar--borderless .flatpickr-calendar {
	margin-top: 12px;
	width: 660px !important;
}

.search__column--arrival-departure.calendar--borderless .flatpickr-calendar,
.search-listing-block .flatpickr-calendar,
.contact-us__form-block .flatpickr-calendar {
	width: 660px !important;
}

.contact-us__form-block .flatpickr-wrapper .flatpickr-input {
	margin-bottom: 16px !important;
}

.calendar--borderless .flatpickr-calendar {
	width: calc(307.875px + 20px);
}

.block-quiz-flex-block--calendar.calendar--borderless .flatpickr-calendar,
.block-quiz-flex-block--calendar.calendar--borderless .flatpickr-days {
	width: 640px !important;
}

.calendar--borderless .flatpickr-calendar:before,
.calendar--borderless .flatpickr-calendar:after {
	content: none;
}

.calendar--borderless span.flatpickr-day,
.calendar--borderless .dayContainer {
	border: 0 !important;
	border-bottom: 0 !important;
	border-right: 0 !important;
	margin: 3px 0;
	font-weight: 400;
}

.calendar--borderless .dayContainer + .dayContainer {
	margin-left: 3px;
}

.calendar--borderless .flatpickr-months {
	margin-top: 5px;
}

.minimum-stay-day.disabled {
	position: relative;
}

.default-mainpicker + .flatpickr-calendar span.flatpickr-day,
.default-mainpicker + .flatpickr-calendar span.flatpickr-day.prevMonthDay,
.default-mainpicker + .flatpickr-calendar span.flatpickr-day.nextMonthDay {
	height: 38px !important;
	line-height: 38px !important;
}

.calendar--single .flatpickr-calendar,
.calendar--single .flatpickr-days {
	width: 660px !important;
	padding: 0;
}

.reevoo-badge--inject-css {
	display: none;
	background: rgba(255,255,255,.9);
	border-radius: 4px;
	min-height: 44px;
}

/* FAQ */

.section-faq {
	margin: 100px 0;
}

.faq-title {
	font-size: 2.986rem;
	font-weight: 600;
	margin: 5px 0 10px 0;
	line-height: 1.2;
	font-family: 'Butler', sans-serif;
}

.faq-item-question {
	font-weight: 500;
	margin: 0;
	font-size: 1rem;
	cursor: pointer;
	margin-left: 30px;
	margin-bottom: 5px;
}

.faq-item-answer {
	display: none;
	margin: 0;
	margin-left: 30px;
}

.faq-item:first-of-type .faq-item-answer {
	display: block;
}


.faq-paragraph {
	margin: 20px 0;
}

.faq-item {
	margin-bottom: 15px;
	position: relative;
}

.faq-item.close .faq-item-question:before {
	content: '';
	background-image: url('/assets/img/svg/circle-plus.svg');
	background-size: 21px 21px;
	width: 21px;
	height: 21px;
	position: absolute;
	top: 6px;
	left: 0;
}

.faq-item.open .faq-item-question:before {
	content: '';
	background-image: url('/assets/img/svg/circle-minus.svg');
	background-size: 21px 21px;
	width: 21px;
	height: 21px;
	position: absolute;
	top: 2px;
	left: 0;
}

/* FAQ END */


.listing-bottom-text p:first-child {
	display: none;
}

.listing-bottom-text .heading-2 {
	font-size: 1.5rem;
	font-weight: 500;
}

.listing-bottom-text .heading-2:not(:nth-child(2)) {
	margin-top: 50px;
}

.listing-bottom-text a {
	text-decoration: underline;
}

.listing-bottom-text .heading-3 {
	font-size: 1rem;
	font-weight: 500;
}
/* ------------------------------------------------------------------- */
/* 06. Photo gallery modal
---------------------------------------------------------------------- */
.photo-gallery-modal__opener {
	cursor: pointer;
}

.photo-gallery-modal {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 100%;
	background: white;
	z-index: 1000;
	overflow-y: scroll;
	animation: slideIn .3s;
	overflow-x: hidden;
	padding: 0 100px 50px;
}

.photo-gallery-modal--open {
	display: block;
}

.photo-gallery-modal__header {
	width: 99%;
	background: white;
	top: 0;
	padding: 50px 0 0;
	z-index: 1001;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 100px;
	transition: all .3s;
}


.photo-gallery-modal__title {
	font-size: 2.45rem;
	text-transform: capitalize;
	line-height: 39px;
	margin: 0;
	transition: all .3s;
	transition: all .3s;
	font-family: var(--font-primary);
	font-weight: 400;
}

.photo-gallery-modal__close-btn {
	background: transparent;
	border: none;
	display: flex;
	position: fixed;
	top: 50px;
	right: 100px;
	z-index: 1;
}

.photo-gallery-modal__close-btn::before {
	content: '';
	display: inline-block;
	width: 40px;
	height: 40px;
	background-image: url('/assets/img/svg/close.svg');
	background-size: 40px 40px;
	background-repeat: no-repeat;
}

.photo-gallery-modal__slide-out-animation {
	animation: slideOut .3s;
}

.photo-gallery-modal__divider {
	height: 10px;
}

.photo-gallery-modal__filter-container {
	position: sticky;
	top: 0;
	background-color: #fff;
	z-index: 1;
	transition: all .3s;
	padding: 50px 0 0;
	overflow: auto;
	width: calc(100% - 66px);
}

.photo-gallery-modal__filter-container--with-note {
	padding-top: 30px;
}

.photo-gallery-modal__filter {
	display: flex;
	align-items: center;
	justify-content: start;
	gap: 10px;
	padding-left: 0;
}

.photo-gallery-modal__photo-informative {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
}

.photo-gallery-modal__photo-informative-icon {
	margin-right: 6px;
}

.photo-gallery-modal__filter > li {
	list-style: none;
}

.photo-gallery-modal__filter-button {
	border: 1px solid #333;
	border-radius: 6px;
	font-size: .875rem;
	text-transform: capitalize;
	color: #333;
	padding: 8px 12px;
	background-color: #fff;
	cursor: pointer;
	white-space: nowrap;
}

.photo-gallery-modal__filter-button--active {
	background: #333;
	color: #fff !important;
}

@keyframes slideIn {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

@keyframes slideOut {
	from {
		transform: translateY(0);
	}
	to {
		transform: translateY(100%);
	}
}

.photo-gallery-modal__grid__wrapper {
	position: relative;
}

.photo-gallery-modal__grid__wrapper--pt {
	padding-top: 100px;
}

.photo-gallery-modal__grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	max-width: 800px;
	margin: 0 auto;
}

.photo-gallery-modal__img--big {
	min-height: 503px;
	object-fit: cover;
}

.photo-gallery-modal__img--small {
	min-height: 292.5px;
	object-fit: cover;
}

.photo-gallery-modal__grid__big-image { grid-area: 2 / 1 / 5 / 3; }


.photo-gallery-modal__grid__item {
	border-radius: 6px;
	overflow: hidden;
	height: auto;
	margin: 5px!important;
}

.photo-gallery-modal__grid__title {
	grid-area: 1 / 1 / 2 / 3;
	position: absolute;
	top: 100px;
	left: 0;
	font-size: 1.875rem;
	text-transform: capitalize;
	max-width: 250px;
	word-wrap: break-word;
}

.photo-gallery-modal__gallery__image--active {
	animation: shake .5s .5s;
}

@keyframes shake {
	0%, 100% { transform: translateX(0); }
	25%, 75% { transform: translateX(-10px); }
	50% { transform: translateX(10px); }
}

/* ------------------------------------------------------------------- */
/* 06. Media Queries
---------------------------------------------------------------------- */

@media (max-width: 1350px) {
	.filtering-modal__container {
		max-height: 75vh;
	}
}

@media (min-width: 1440px) and (max-width: 1550px) {
	.listing-item-container.list-layout .listing-item-image {
		flex-basis: 330px;
	}
}

/* Search results responsive elements */
@media (min-width: 991px) and (max-width: 1919px) {
	.fs-inner-container .listing-item-container.list-layout .listing-item-inner-middle .main-features {
		width: 80%;
		gap: 4px;
	}
}

@media (max-width: 1200px) {
	.evisitor-content {
		grid-area: 2 / 1 / 2 / 13;
	}

	.evisitor-guest-list-item {
		min-width: 167px;
	}

	.evisitor-guest-list {
		grid-area: 1 / 1 / 1 / 13;
		flex-direction: row;
		overflow: auto;
		margin-bottom: 16px;
		padding-bottom: 8px;
		margin-right: 0;
	}
}

@media (min-width: 1024px) {
	.listing-items-grid:not(.keen-initialized) .listing-item-container {
		width: 33%;
		min-width: calc(33% + 4px);
	}

	.listing-container--search-results {
		padding-top: 30px;
		min-height: 90vh;
	}

	.fs-inner-container.content {
		width: 65%;
		min-width: 880px;
	}

	.fs-inner-container.main-map {
		width: 35%;
	}
}

@media (min-width: 1024px) and (max-width: 1280px) {
	.fs-container section.search {
		margin-left: 25px;
		margin-right: 25px;
	}
}

@media (min-width: 1024px) and (max-width: 1440px) {
	.main-search-input-item--datepicker {
		min-width: 280px;
	}

	.main-search-input-item--persons {
		min-width: 200px;
	}
}

@media (min-width: 992px) and (max-width: 1440px) {
	.inquiry-calendar {
		left: -100%;
	}
}

@media (max-width: 1279px) {
	.keen-dots {
		display: flex;
	}

	.fs-container .fs-listings {
		padding: 0 10px;
	}
}

@media (max-width: 1259px) {

	.menu-separator {
		margin: 0 20px;
	}

	.row-flex {
		display: block;
	}

	.photo-gallery-modal__grid {
		max-width: 600px;
	}

	.photo-gallery-modal__grid__wrapper--first {
		padding-top: 150px;
	}

	.photo-gallery-modal__header {
		padding: 0;
		padding-top: 16px;
		align-items: unset;
		height: auto;
		top: 0;
		position: fixed;
		width: 96%;
		align-items: center;
	}

	.photo-gallery-modal__filter-button {
		line-height: 100%;
	}

	.photo-gallery-modal__close-btn {
		position: sticky;
	}

	.photo-gallery-modal__filter-container {
		padding: 16px 0;
		width: calc(100% - 25px);
		margin: 0;
		top: 54px;
		position: fixed;
	}

}

@media (min-width: 768px) and (max-width: 1239px) {
	.show-on-tablet {
		display: block;
	}

	.hide-on-tablet {
		display: none;
	}

	.listing-section--tablet {
		display: block;
	}

	.listing-section--desktop {
		display: none;
	}
}

@media (min-width: 1024px) and (max-width: 1239px) {
	.main-search-input-item--location {
		flex: 1.4;
	}
}

@media (min-width: 992px) and (max-width: 1239px) {
	.grid-thumbnail-carousel--six-in-a-row .grid-thumbnail-block,
	.grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block {
		min-width: 160px;
	}

	.grid-thumbnail--regions .grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block {
		min-width: 190px;
	}
}

@media (min-width: 1024px) and (max-width: 1680px) {
	.main-search-input--search-results {
		padding: 12px;
		padding-bottom: 5px;
		padding-top: 5px;
	}

	.main-search-input--search-results .main-search-input-item {
		margin-left: 10px;
	}

	.main-search-input-item--filters {
		margin-left: 0;
		flex: 0;
	}

	.main-search-input--search-results .main-search-input-item--persons:after {
		display: none;
	}

	.main-search-input--search-results .main-search-input-item--location {
		min-width: 0;
	}
}

@media (min-width: 1024px) and (max-width: 1279px) {
	.listing-item-container.list-layout .listing-item-inner-middle .main-features {
		column-count: 1;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li {
		display: inline-block;
		margin-right: 5px;
	}

	.listing-outdoor-pool--elipsis {
		overflow: unset;
		width: auto;
	}
}

@media (min-width: 1024px) and (max-width: 1366px) {
	.listing-item-container.list-layout .listing-item-inner-top {
		width: 80%;
	}

	.listing-nav li {
		margin-right: 5px;
	}

	.block-main-usps .usp-item {
		flex: 0 0 30%
	}
}

@media (min-width: 768px) and (max-width: 991px) {
	.row-flex--tablet {
		display: flex;
	}

	.col-hide-tablet {
		display: none;
	}

	.block-main-usps .usp-item {
		flex: 0 0 42%;
	}

	.block-main-usps .usp-content__title,
	.block-main-usps .usp-content__subtitle {
		max-width: 100% !important;
	}

	.row-flex--tablet-column-reverse {
		flex-direction: column-reverse;
	}

	.grid-thumbnail-carousel--six-in-a-row .grid-thumbnail-block,
	.grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block {
		min-width: 247px;
	}

	.grid-thumbnail--regions .grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block {
		min-width: 243px;
	}

	#snippet--listingResults .listing-item-container.list-layout .listing-item-inner,
	#snippet--searchResultsControl .listing-item-container.list-layout .listing-item-inner {
		min-height: 290px;
	}

	.listing-item-container.list-layout .listing-item-inner h3 {
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li:before {
		content: ' · ';
		color: #757e9c;
		font-size: 1.313rem;
		display: inline-block;
		position: relative;
		top: 1px;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li {
		float: left;
		margin: 0 6px 0 0!important;
		flex: inherit!important;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li i {
		display: none;
	}
}

@media (min-width: 992px) and (max-width: 1239px) {
	.flex--footer .flex-left {
		margin-bottom: 15px;
	}

	.flex--footer-contact {
		flex-direction: column;
	}

	.own-review-modal {
		width: 60%;
	}
}

@media (max-width: 992px) {
	.own-review-modal {
		width: 80%;
	}

	.own-review-modal-filter-block-info,
	.reviews-filter-label {
		font-size: .94rem;
	}

	.own-review-modal-badge-wrapper {
		gap: 12px;
	}
}

@media (min-width: 992px) and (max-width: 1024px) {
	.date-rangepicker + .flatpickr-calendar.inline {
		padding: 10px 67px !important;
	}

	.button.filter-results {
		padding: 9px 40px;
		width: 100%;
	}

	.search-listing-block .button.filter-results {
		margin-left: 0;
	}

	.panel-dropdown {
		width: 100%;
	}

	.search-listing-block .more-filters+.panel-dropdown-content {
		left: auto;
		right: 0;
		width: 600px;
	}
}

@media (min-width: 768px) and (max-width: 1024px) {
	.main-search-container .image-container {
		height: 380px!important;
	}

	#map-container.map-in-viewport {
		height: 85vh !important;
	}

	.hide-on-mobile.hide-on-mobile--inline-block {
		display: none;
	}

	.custom-infobox .listing-img-container {
		width: auto;
	}

	.custom-infobox .listing-single-page {
		display: none;
	}

	.custom-infobox .listing-img-container img {
		height: 100%;
		width: 280px;
	}

	.custom-infobox .listing-content h3 {
		font-size: 1rem;
		line-height: 1.2;
		margin-top: 5px;
		margin-bottom: 5px;
	}

	.custom-infobox .listing-content .listing-price {
		font-size: 1rem;
		margin-bottom: 10px;
	}

	.custom-infobox .listing-content .map-features li {
		font-size: .75rem;
		margin-right: 25px;
	}

	.custom-infobox .listing-content .map-features li i:before {
		font-size: 25px;
	}

	.main-search-input-item .flatpickr-days {
		width: 100% !important;
	}
}

/* Special case with grid-list view */
@media (min-width: 1025px) {
	.listing-stars--grid-view {
		display: none !important;
	}

	.listing-item-price--grid-view {
		display: none !important;
	}
}

/* Laptops */
@media only screen and (min-width: 1240px) and (max-width: 1366px)  {
	.input-with-icon.location a i {
		padding: 15px 18px 15px 10px;
	}

	.search-listing-block .more-filters + .panel-dropdown-content {
		left: -760px;
	}

	.main-search-input-item .panel-dropdown-content--homepage {
		max-width: 280px;
	}

	.data-radius-title {
		margin-top: 7px;
		line-height: 24px;
	}

	.fs-content .search .row .col-fs-6 {
		width: 50%;
	}
}

/* Smaller than laptops */
@media (max-width: 1366px) {
	.main-search-container {
		margin-top: 0;
	}

	.fs-inner-container .listing-item-container.list-layout .listing-item-inner-middle .main-features {
		width: 100%;
	}

	#header .left-side.navigation {
		margin-left: 20px;
	}

	.prefooter-wrapper {
		width: 90%;
		border-radius: 0;
	}
}

@media (max-width: 1440px) {
	.listing-item-container.list-layout .listing-item-image {
		flex: 2.8;
	}

	.listing-item-container.list-layout .listing-item-inner h3 {
		font-size: 1.438rem;
	}

	.header__logo-anchor--bg {
		background-image: url('/assets/img/logo-symbol.svg');
		width: 62px;
		height: 47px;
	}

	.listing-items-similar.listing-items-grid .listing-item-container {
		padding: 0 6px;
	}

	.listing-item__spo-btn {
		font-size: .938rem !important;
	}

	.photo-gallery-modal__grid {
		max-width: 700px;
	}

	.photo-gallery-modal__grid__title {
		position: static;
		max-width: unset;
	}

	.photo-gallery-modal__img--big {
		height: 439px;
	}

	.photo-gallery-modal__img--small {
		height: 255px;
	}
}

/* Smaller than standard 1180 (devices and browsers) */
@media (max-width: 1239px) {
	.side-sticky {
		position: static;
		top: 0;
	}

	.fullwidth .header-navigation:before {
		background: linear-gradient(to right, #e8e8e8 50%, #fff);
	}

	#header .left-side.navigation {
		margin-left: 10px;
	}

	.header-navigation__submenu-item--usps {
		display: none;
	}
}

@media (max-width: 1024px) {
	.wrapper {
		padding-top: 66px;
	}

	.filtering-button-wrapper {
		justify-content: flex-start;
		gap: 12px;
	}

	.listing-outdoor-pool--elipsis {
		overflow: unset;
		width: auto;
	}

	.login-anchor--sidebar {
		display: block;
	}

	.wrapper--expanded {
		padding-top: 110px;
	}

	.search--sticky {
		position: static;
		top: 0;
	}

	.header-container,
	.header-container.header-container--home {
		position: fixed;
		top: 0;
		width: 100%;
		z-index: 1000;
		background-color: #fff;
		box-shadow: 0 2px 5px rgba(0, 0, 0, .12);
	}

	.header-container__single-villa {
		position: static;
	}

	.wrapper__single-villa {
		padding-top: 0;
	}

	.header-container.header-container--home #header {
		background: #fff;
	}

	#header.cloned {
		display: none;
	}

	.headline span,
	.section-subtitle {
		font-size: 0.938rem;
	}

	.main-search-input button.button {
		width: 100%;
	}

	.main-search-input-item--datepicker {
		max-width: 100%;
		min-width: auto;
	}

	.main-search-input input#location-search-input {
		width: 100%;
		padding-right: 40px;
	}

	.main-search-input .main-search-input-item.panel-dropdown {
		margin-right: 0;
		display: block;
		width: 100%;
		height: auto;
	}

	.panel-dropdown .panel-dropdown-content.filters h3 {
		margin-bottom: 25px;
	}

	.main-search-input-item {
		margin-right: 0;
	}

	.main-search-input select,
	.main-search-input select:focus {
		font-size: 1rem;
		border: 1px solid #e0e0e0;
		box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .08);
		background: #fff;
		height: 55px;
		padding: 12px 18px;
		border-radius: 4px;
	}

	.main-search-inner {
		height: auto;
		margin-top: 0;
		min-height: 380px;
	}

	.select2-container--default .select2-selection--multiple .select2-selection__rendered {
		background-color: transparent;
		width: 100% !important;
		border-radius: 2px;
		padding: 0;
	}

	.select2-container .select2-search--inline .select2-search__field {
		font-size: 1.125rem !important;
	}

	.select2-container--default .select2-selection--multiple .select2-selection__choice {
		font-size: 0.938rem;
		margin: 11px 0 0 12px!important;
	}

	.select2-container--default .select2-selection--multiple .select2-selection__choice+.select2-selection__choice {
		margin: 11px 0 0 0!important;
	}

	.main-search-input-item .select2-container--default .select2-selection--multiple {
		border: 1px solid #333!important;
		height: 46px;
	}

	.main-search-container .image-container {
		height: 360px;
		width: 100%;
		max-width: 100%;
	}

	.main-search-inner__text {
		padding-top: 70px;
		display: flex;
		flex-flow: column;
		align-items: center;
		justify-content: center;
		text-align: center;
	}

	.main-search-input__mobile {
		display: block;
		background-color: #fff;
		border-radius: 4px;
		padding: 10px;
		position: relative;
		margin-top: 30px;
	}

	.main-search-input__mobile--search-results {
		box-shadow: 1px 1px 10px 1px rgba(0, 0, 0, .14);
		display: flex;
		justify-content: space-between;
		padding-left: 45px;
	}

	.main-search-input__mobile--search-results a.button.search__anchor-filter {
		padding: 0 !important;
	}

	.main-search-input__mobile-data-secondary {
		display: flex;
		font-size: 1rem;
	}

	.main-search-input__mobile-data {
		display: inline-flex;
	}

	.main-search-input__mobile-data--destination {
		font-weight: 600;
	}

	.main-search-input__mobile-section {
		flex: 0 1 80%;
	}

	.main-search-input__mobile-section--search {
		overflow: hidden;
		white-space: nowrap;
	}

	.main-search-input__mobile-section + .main-search-input__mobile-section {
		flex: 0 1 20%;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.main-search-input__mobile-section > a {
		min-width: 32px;
		height: 32px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #252525;
		margin-left: 6px;
		position: relative;
	}

	.search__anchor-filter-text {
		margin-left: 5px
	}

	.search-notice {
		width: 16px;
		height: 16px;
	}

	.main-search-input__mobile-data--adults {
		position: relative;
		padding-left: 15px;
	}

	.main-search-input__mobile-data--adults:before {
		content: '·';
		font-size: 1.728rem;
		color: #252525;
		display: inline-block;
		position: absolute;
		top: -9px;
		left: 4px;
	}

	.main-search-input__mobile:after,
	.main-search-input__mobile.main-search-input__mobile--search-results:before {
		content: '';
		display: inline-block;
		width: 20px;
		height: 20px;
		background-size: 20px 20px;
		background-repeat: no-repeat;
		vertical-align: middle;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 15px;
		background-image: url('/assets/img/svg/search.svg');
	}

	.main-search-input__mobile.main-search-input__mobile--search-results:before {
		right: 0;
		left: 10px;
		top: 50%;
		transform: translateY(-50%);
		background-size: 24px 24px;
		width: 24px;
		height: 24px;
	}

	.main-search-input__mobile.main-search-input__mobile--search-results:after {
		content: none;
	}

	.main-search-input__placeholder-title {
		font-size: .875rem;
		line-height: 1.4;
		font-weight: 500;
		text-transform: capitalize;
	}

	.main-search-input__placeholder-content {
		color: #888;
		font-size: .75rem;
		line-height: 1.4;
		text-transform: capitalize;
	}

	.main-search-input__placeholder-content span:after {
		content: '·';
		vertical-align: top;
		font-size: .75rem;
		color: #888;
		margin: 0 3px;
	}

	.main-search-input__placeholder-content span:last-child:after {
		display: none;
	}

	.main-search-input-item__title {
		font-size: .938rem;
		line-height: 1.2;
		text-transform: capitalize;
		letter-spacing: 0;
		left: 12px;
		top: 0;
		padding-bottom: 4px;
	}

	.main-search-input-item--persons .main-search-input-item__title {
		display: none;
	}

	.main-search-input {
		border-radius: 0;
		display: none;
		padding: 0;
		max-height: 100%;
		background-color: transparent;
		box-shadow: none;
		margin-top: 25px;
	}

	.overflow-hidden {
		overflow: hidden;
	}

	.main-search-input--overlayed {
		position: fixed;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 100vh;
		background: #fff;
		z-index: 1001;
		display: block;
		align-items: flex-start;
		justify-content: flex-start;
		padding: 80px 20px 20px 20px;
		border-radius: 0;
		border: none;
		margin-top: 0;
		overflow: auto;
	}

	.main-search-input--overlayed .main-search-input-item--filters {
		display: none;
	}

	.main-search-input__close-button {
		position: absolute;
		right: 20px;
		top: 20px;
		display: block;
	}

	.gray-style .main-search-input-item,
	.main-search-input-item {
		border-color: transparent;
	}

	.main-search-input button.button {
		margin-top: 0;
	}

	.main-search-input-item {
		margin-bottom: 15px;
		margin-left: 0;
	}

	.main-search-input-item,
	.main-search-input .main-search-input-item:nth-last-child(2) {
		padding: 0;
	}

	.main-search-input-item--location {
		max-width: 100%;
		border: none;
	}

	.main-search-input input,
	.main-search-input input:focus {
		padding: 0 20px 0 12px;
		font-size: .75rem;
		line-height: 21px;
		height: 46px;
		background: #fff;
		border: 1px solid #333;
		border-radius: 4px;
	}

	.main-search-input-item--persons input {
		border: none;
	}

	.main-search-input-item__placeholder input::-webkit-input-placeholder {
		font-size: 0.938rem;
		color: #999;
		letter-spacing: 0;
	}

	.main-search-input-item:after,
	.main-search-input--search-results .main-search-input-item--persons:after {
		display: none;
	}

	.main-search-input-item__icon {
		top: 6px;
	}

	.input-persons-placeholder.input-persons-placeholder--main-search {
		top: 10px;
		letter-spacing: 0;
		left: 37px;
	}

	.main-search-input-item--persons .input-persons-placeholder {
		display: none;
	}

	.main-search-input-item__icon:before {
		width: 12px;
		height: 12px;
		background-size: 12px 12px;
	}

	.main-search-input-item .qtyButtons input {
		width: 50px;
		border: none;
		box-shadow: none;
		font-size: 0.938rem;
		height: 30px !important;
		line-height: 36px;
		padding: 0;
	}

	.main-search-input-item.panel-dropdown .panel-dropdown-content {
		width: 100%;
		max-width: 100%;
		opacity: 1;
		visibility: visible;
		top: 0;
		display: inline-block;
		position: relative;
		margin-top: -40px;
		box-shadow: none;
		border: none;
		padding: 10px;
	}

	.main-search-input-item.panel-dropdown .panel-dropdown-content .button--apply {
		display: none;
	}

	.block-featured .col-md-4,
	.block-featured .col-md-8 {
		width: 100%;
		padding-right: 15px;
	}

	.block-insurance .col-md-4 {
		padding-right: 15px;
	}

	.block-insurance {
		padding-bottom: 65px;
	}

	.block-insurance__image-logo {
		width: 150px;
	}

	.block-main-usps .container {
		padding-left: 0;
		padding-right: 0;
	}

	.block-main-usps-container {
		flex-flow: column;
		height: inherit;
		padding: 30px 15px 70px 15px;
		margin: 0;
	}

	.block-main-usps .main-usps__content {
		flex: 1;
		margin-right: 0;
		padding: 0 0 20px;
	}

	.block-main-usps .usp-content__title {
		font-size: 1.728rem;
		line-height: 1.2;
	}

	.block-main-usps .usp-content__subtitle {
		width: 90%;
	}

	.block-main-usps .main-usps__items {
		overflow-x: auto;
		margin-bottom: 0;
		padding: 10px 0;
		flex: 1;
		flex-flow: row;
	}

	.block-main-usps .main-usps__items::-webkit-scrollbar,
	.keen-block--disable-mobile .grid-thumbnail-carousel::-webkit-scrollbar,
	.keen-slider--disable-mobile::-webkit-scrollbar,
	.grid-thumbnail-custom::-webkit-scrollbar {
		background: transparent;
	}

	.block-main-usps .main-usps__items::-webkit-scrollbar:horizontal,
	.keen-block--disable-mobile .grid-thumbnail-carousel::-webkit-scrollbar:horizontal,
	.keen-slider--disable-mobile::-webkit-scrollbar:horizontal,
	.grid-thumbnail-custom::-webkit-scrollbar:horizontal {
		height: 2px;
		background-color: #dfdfdf;
		border-radius: 0;
		border: .3125em solid transparent;
	}

	.block-main-usps .main-usps__items::-webkit-scrollbar-thumb,
	.keen-block--disable-mobile .grid-thumbnail-carousel::-webkit-scrollbar-thumb,
	.keen-slider--disable-mobile::-webkit-scrollbar-thumb,
	.grid-thumbnail-custom::-webkit-scrollbar-thumb {
		height: 2px;
		background-color: #000;
		border-radius: 0;
		border: .3125em solid transparent;
	}

	.block-main-usps .usp-item {
		flex: 0 0 50%;
		padding: 0 0 20px;
		margin: 0 25px 0 0;
		justify-content: flex-start;
	}

	.block-main-usps .usp-item:last-child {
		margin: 0;
	}

	.block-main-usps .usp-item__title {
		font-size: .75rem;
		line-height: 1.2;
	}

	.block-main-usps .usp-item__icon-container,
	.block-main-usps .usp-item__icon {
		width: 30px;
		height: 30px;
		margin-bottom: 15px;
	}

	.block-main-usps .usp-item__button {
		margin-top: 20px;
	}

	.block-grid--regions {
		padding-bottom: 20px;
	}

	.block-grid-random {
		padding-bottom: 60px;
	}

	.booking-block-wrapper {
		margin-top: 25px;
	}

	.section-block--bordered {
		border: none;
	}

	.prefooter-wrapper {
		width: 100%;
		border-radius: 0;
	}

	.listing-object-actions:before {
		bottom: 60px;
	}

	.header-navigation:before {
		display: none;
	}

	.fs-inner-container.content {
		width: 100%;
		padding-top: 0 !important;
		position: static;
	}

	.fs-inner-container.content.modal-active {
		z-index: 1000;
	}

	.fs-container .sort-by-select {
		float: left;
		margin-bottom: 20px;
	}

	.fs-container .filtering-button {
		margin-bottom: 20px;
	}

	.listing-container--search-results .sort-by-select {
		float: right;
	}

	.fs-container .showing-results {
		margin-bottom: 15px;
		margin-top: 0;
	}

	.fs-container #search-results-form {
		display: none;
	}

	.fs-container .main-map {
		display: none;
	}

	.fs-container section.search .mobile-filter-buttons {
		display: flex;
		justify-content: space-between;
		text-align: center;
	}

	.search--sticky .mobile-filter-buttons {
		display: block;
	}

	.fs-container section.search .mobile-filter-buttons .show-filters-map {
		display: flex;
		align-items: center;
	}

	.fs-container section.search .show-filters {
		flex: 2;
		margin-right: 10px;
	}

	.fs-container section.search .show-filters i:before {
		content: '';
		filter: invert(100%);
		display: inline-block;
		width: 21px;
		height: 21px;
		background-image: url('/assets/img/svg/controls.svg');
		background-size: 17px 17px;
		background-repeat: no-repeat;
		position: relative;
	}

	.fs-container section.search {
		padding: 20px;
		margin: 0;
		border: none;
		box-shadow: none;
	}

	.fs-inner-container.map-fixed,
	.wrapper--expanded .fs-inner-container.map-fixed {
		height: 100%;
		position: fixed;
		z-index: 1000;
		bottom: 0;
		top: 0;
		padding-top: 0;
		width: 100%;
	}

	.map-container__close-button {
		padding: 2px 12px!important;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;
		background: #fff!important;
	}

	.map-container__close-icon {
		rotate: 180deg;
		margin-bottom: -3px;
	}

	.map-action-header {
		position: absolute;
		left: 10px;
		top: 10px;
		z-index: 1001;
		display: flex;
		gap: 8px;
	}

	.map-action-header .filtering-button {
		background-color: #fff;
		padding: 2px 12px!important;
	}

	.gray-style.main-search-input {
		background-color: transparent;
	}

	.container .row div.padding-right-30 {
		padding-right: 15px !important;
	}

	.container .row div.padding-left-30 {
		padding-left: 15px !important;
	}

	.listing-nav-container {
		margin-top: 20px;
		margin-left: 15px;
		margin-right: 15px;
		display: none;
	}

	.listing-stars--grid-view {
		display: block !important;
	}

	.listing-stars--list-view {
		display: none !important;
	}

	.listing-item-price--grid-view {
		display: block !important;
	}

	.listing-item-price--list-view {
		display: none !important;
	}

	.listing-nav-container.cloned .listing-nav {
		display: none;
	}

	.listing-object-actions {
		background: #fff;
	}

	.listing-nav li {
		margin-right: 20px;
	}

	.listing-lazy-images .keen-prev,
	.listing-lazy-images .keen-next,
	.block-featured .listing-item-container .listing-item-image .keen-prev:not(.keen-disabled),
	.block-featured .listing-item-container .listing-item-image .keen-next:not(.keen-disabled) {
		opacity: 1;
		display: block;
	}

	.keen-prev.keen-disabled,
	.keen-next.keen-disabled {
		opacity: 0;
	}

	.listing-item-container.list-layout .listing-item-inner {
		position: relative;
		top: auto;
		transform: none;
		padding: 10px;
		left: 0;
	}

	.listing-item-container.list-layout .listing-item-inner h3 {
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-item-inner-bottom {
		display: flex;
		flex-direction: column-reverse;
		text-align: center;
		margin-top: 10px;
		position: static;
		justify-content: space-between;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-item-inner-bottom > a {
		width: 100%;
		display: flex;
		justify-content: center;
	}

	.listing-item-container.list-layout .listing-item {
		display: block;
		height: auto;
		padding: 0;
	}

	.listing-item-container.list-layout .listing-item-image {
		min-height: auto;
	}

	.listing-item-container.list-layout .listing-item-image .listing-item-title {
		display: none;
		box-shadow: inset 0 -59px 16px -18px rgba(0,0,0,.26);
		color: #fff;
		position: absolute;
		bottom: 0;
		left: 0;
		font-size: 21px;
		width: 100%;
		z-index: 999;
		padding: 15px;
		margin-bottom: 0;
		text-shadow: 0 1px 1px rgba(0, 0, 0, .5);
	}

	.listing-item-container.list-layout .listing-item-image .listing-item-title > a {
		color: #fff;
	}

	.listing-item-container.list-layout .listing-item-details {
		position: relative;
		bottom: auto;
		right: auto;
		left: 0;
		top: -15px;
		margin: 0 30px 15px 30px;
		padding: 5px 20px;
		background-color: #eee;
		border-radius: 50px;
		font-size: 14px;
		width: auto;
		display: inline-block;
	}

	.show-filters-map-button {
		display: block!important;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li {
		font-size: .875rem;
		margin: 0 0 6px;
		flex: 50%;
	}

	.listing-items-grid .listing-item-container.list-layout .listing-item-inner-middle .main-features li {
		min-width: 0;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-item-inner-top {
		max-width: 100%;
		white-space: normal;
		width: 100%;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-stars {
		top: 5px;
	}

	.listing-item__reviews {
		position: static;
		margin: 12px 0 2px;
	}

	.review-badge-listing {
		position: static;
	}

	.review-badge-listing > .own-reviews-block-section > .own-review-badge {
		display: flex;
		align-items: center;
		gap: 8px;
		margin: 12px 0 2px;
	}

	.review-badge-listing > .own-reviews-block-section > .own-review-badge > .own-review-badge-review-count,
	.review-badge-listing > .own-reviews-block-section > .own-review-badge > .own-review-badge-review-count-link {
		margin-top: 0;
	}

	.listing-items-similar .listing-item-container.list-layout .listing-item-inner .listing-start--so {
		top: -16px;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-item-inner-middle .listing-item-price {
		margin: 0;
		font-weight: 600;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-item-inner-middle .listing-item-price--specific-price {
		font-size: 1.4rem;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-item-inner-middle .listing-item-price .price-from {
		font-weight: 400;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-item-inner-middle .listing-item-price .price-discount {
		font-size: 1rem;
		margin: 0 6px;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-item-inner-top .listing-item-location {
		display: block;
		max-width: 70%;
	}

	.listing-items-grid .listing-item-container.list-layout .listing-item-inner .listing-item-inner-top .listing-item-location {
		max-width: 100%;
		width: 75%;
	}

	.listing-items-grid .listing-item-container.list-layout .listing-item-inner .listing-item-inner-top {
		margin-top: 12px;
	}


	.listing-item-container.list-layout .listing-item-inner-top .listing-item-price {
		margin-bottom: 0;
		top: 8px;
	}

	.search__row {
		flex-direction: column;
	}

	.search__column {
		margin-right: 0;
		margin-bottom: 15px;
	}

	.search__column--location {
		max-width: 100%;
	}

	.search__column--top-spacing {
		margin-top: 0;
	}

	.search__column--filters {
		margin-bottom: 0;
		border-bottom: 1px solid #ddd;
		padding-bottom: 20px;
	}

	.search__column--arrival-departure,
	.search__column--persons {
		flex: auto;
	}

	.modal-mobile-opened {
		overflow: hidden;
		position: fixed;
		width: 100%;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.search-listing-block .more-filters + .panel-dropdown-content,
	.search .more-filters + .panel-dropdown-content {
		left: 0;
		top: 0;
		position: fixed;
		z-index: 1101;
		height: 100%;
		max-height: 100%;
		padding-bottom: 30px;
		box-shadow: none;
		border: none;
		max-width: 100%;
		width: 100%;
	}

	.panel-dropdown-content__column {
		width: 100%;
	}

	.panel-dropdown-content__close {
		display: block;
		position: absolute;
		top: 20px;
		right: 20px;
		z-index: 1000;
	}

	.panel-dropdown .panel-dropdown-content {
		left: 0;
		right: auto;
		width: 400px;
		max-width: 70vw;
	}

	.panel-dropdown-content.checkboxes.filters {
		width: 100% !important;
		max-width: 100vw;
		left: 0;
		padding: 15px;
	}

	.panel-dropdown a {
		background: #f3f3f3;
		padding-top: 6px;
		padding-bottom: 6px;
	}

}

/* Tablet Portrait size to standard 960 (devices and browsers) */
@media (max-width: 991px) {
	.toggle-calendar-flexible-days-wrapper {
		justify-content: flex-start;
		padding-bottom: 12px;
	}

	.header-navigation__sub-items-title--interests {
		border-top: 1px solid #e2e2e2;
	}

	.header-navigation__submenu--destinations-wrapper {
		flex-direction: column;
	}

	.form-subtitle {
		margin-bottom: 24px;
	}

	.evisitor-modal-label {
		font-size: 1.2rem;
	}

	.evisitor-modal-trigger-in-form .evisitor-modal-label {
		font-size: 1rem;
	}

	.header-navigation__submenu-anchor {
		flex-direction: column;
	}

	.header-navigation__submenu-anchors--interests {
		grid-template-columns: repeat(1, 1fr);
		gap: 0;
	}

	.header-navigation__submenu-main-anchor--destinations {
		width: 100%;
	}

	.header-navigation__submenu-country-image {
		width: 100%;
	}

	.flexible-date-label-wrapper.desktop {
		display: none;
	}

	.listing-items-carousel--spo .listing-item-content {
		margin-top: -10px;
	}

	.listing-items-carousel--spo .listing-item-content.new-object {
		margin-top: 0;
	}

	.infoBox-close {
		top: -15px;
		right: -1px;
		line-height: 24px;
	}

	.flexible-date-label-wrapper.mobile {
		display: block;
	}

	.new-object-label--single-villa {
		margin-bottom: -25px;
	}

	.listing-item .own-review-badge-score,
	.listing-item .own-review-badge-label {
		font-size: 1rem;
	}

	.listing-item .own-review-badge-review-count {
		font-size: .75rem;
		margin-top: 0;
	}

	.listing-item .own-review-check-icon {
		display: none;
	}

	.calendar-bottom-action__bottom {
		flex-direction: column;
		gap: 8px;
	}

	.calendar-bottom-action__bottom-number-of-nights-wrapper {
		flex-direction: row;
		width: 100%;
		justify-content: space-between;
	}
	.evisitor-guest-list-item {
		min-width: unset;
	}

	.evisitor-guest-list {
		grid-area: 1 / 1 / 1 / 13;
		flex-direction: row;
		overflow: auto;
		margin-bottom: 16px;
		padding-bottom: 8px;
		margin-right: 0;
	}

	.evisitor-guest-list-item {
		display: none;
	}

	.evisitor-guest-list-item--active {
		display: flex;
	}

	.evisitor-guest-list-item-title-number {
		display: none;
	}

	.evisitor-guest-list-item-title-number--mobile {
		display: inline-block;
	}

	.form-details {
		display: none;
	}

	.evisitor-grid-wrapper {
		margin-top: 30px;
	}

	.form-details.active {
		display: block;
		position: fixed;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 60%;
		height: auto;
		max-height: 500px;
		overflow-y: auto;
		border-radius: 8px;
		background: #fff;
		padding: 16px;
	}

	.evisitor-modal-close-btn {
		display: block;
		position: absolute;
		right: 16px;
		top: 16px;
		background: transparent;
		border: none;
	}

	.forms {
		grid-area: 1 / 1 / 2 / 13;
	}

	.evisitor-modal-trigger-in-form {
		border: 1px solid #252525;
		width: fit-content;
		border-radius: 6px;
		padding: 6px 10px;
		margin-bottom: 32px;
		margin-top: -4px;
		display: flex;
	}

	.evisitor-modal-trigger-in-form .evisitor-modal-label {
		margin-top: -1px;
	}

	.form-headline {
		font-size: 1.2rem;
	}

	.evisitor-modal.active {
		display: block;
		position: fixed;
		top: 0;
		right: 0;
	}

	.evisitor-modal-overlay.active {
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: rgba(0,0,0, .4);
	}

	#header .left-side {
		padding: 0;
	}

	.review-badge-listing.mobile {
		display: block;
		margin: 16px 0 8px 0;
	}

	.listing-item-container.list-layout .listing-item-inner .listing-stars {
		top: 0;
		min-width: 57px;
	}

	.review-badge-listing.desktop {
		display: none;
	}

	.row-flex-mobile-reverted {
		display: flex;
		flex-direction: column-reverse;
	}

	.block-insurance__images {
		margin-bottom: 25px;
	}

	.search-listing-block__filters-container {
		flex-direction: column;
		justify-content: flex-start;
		margin-top: 6px;
	}

	.search-listing-block .button.filter-results {
		margin-top: 12px;
		margin-left: 0;
	}

	.grid-thumbnail-custom__block {
		width: calc(100vw *(1/2) - 14px);
		flex-shrink: 0;
		height: 100%;
		-webkit-overflow-scrolling: touch;
		padding-left: 0;
		padding-right: 10px;
	}

	.grid-thumbnail-custom__block:last-child {
		padding-right: 0;
	}

	.grid-thumbnail-custom__block--hidden {
		display: block;
	}

	.grid-thumbnail-custom {
		position: relative;
		height: auto;
		display: flex;
		overflow-x: auto;
	}

	.grid-thumbnail-view-all {
		display: none;
	}

	.block-grid-random__row {
		position: relative;
	}

	.block-grid-random__item-box,
	.block-grid-random__item-box.img-box.default {
		height: 240px;
	}

	.block-help-top-image {
		display: none;
	}

	.listing-item__spacer {
		width: 100%;
	}

	#header .left-side.navigation {
		margin-left: 0;
	}

	header.fullwidth .container {
		padding: 0 15px;
	}

	.keen-prev, .keen-next {
		opacity: 1;
	}

	.main-search-container .image-container {
		height: 250px;
		width: 100%;
		max-width: 100%;
	}

	.main-search-input-item--persons .main-search-input-item__title {
		display: none;
	}

	.main-search-input button.button {
		height: 55px;
		font-size: 0.938rem;
		text-transform: initial;
	}

	.main-search-input-item.calendar--borderless .flatpickr-calendar {
		width: calc(307.875px + 20px) !important;
		margin-top: 10px;
	}

	.search__column--arrival-departure.calendar--borderless .flatpickr-calendar,
	.search-listing-block .flatpickr-calendar,
	.contact-us__form-block .flatpickr-calendar {
		width: calc(307.875px + 20px) !important;
	}

	.simple-keen-carousel:not(.keen-initialized) .keen-slider__slide {
		min-width: 100%;
	}

	.search-listing-block .row input {
		margin-bottom: 6px;
	}

	.search-listing-block__note {
		display: none;
	}

	.search-listing-block .row .input-with-icon.ui-datepicker-wrapper {
		margin-bottom: 6px;
	}

	.listing-lead-block .listing-lead-block-left {
		margin-right: 20px;
		padding-right: 20px;
		flex: 1;
	}

	.listing-lead-block .listing-lead-block-right {
		flex: 1;
	}

	.panel-dropdown {
		display: block;
		width: 100%;
		float: left;
	}

	.panel-dropdown--location {
		float: none !important;
	}

	.listing-item-container:hover {
		transform: none;
	}

	.button.filter-results {
		width: 100%;
	}

	.more-filters {
		width: 100%;
		text-align: center;
	}

	#googlemaps {
		height: 30vh !important;
		padding-bottom: 0 !important;
	}

	.listing-item-container.listing-item--grid + .listing-item-bottom .actions a.button {
		padding: 5px 15px;
	}

	.prefooter.support-block {
		padding: 15px 0;
	}

	.support-block .content {
		padding: 10px;
	}

	.calendar--single .flatpickr-calendar {
		width: calc(307.875px + 20px) !important;
	}

	.calendar--single .flatpickr-calendar,
	.calendar--single .flatpickr-days {
		width: 100% !important;
	}
}

@media (min-width: 768px) and (max-width: 991px) {
	.calendar--single .inquiry-calendar {
		width: unset!important;
	}
}

@media (max-width: 768px) {
	input[type=text],
	input[type=email],
	input[type=password],
	input[type=date],
	input[type=tel],
	input[type=number],
	input[type=search],
	textarea,
	select {
		font-size: max(.875rem, 1em) !important;
	}

	.showing-results__content {
		margin-top: 25px;
	}

	.search-block__label--dates-box--flexible.flexible-listing-page {
		left: 250px;
		top: 48%;
	}

	.own-review-modal-badge-wrapper {
		flex-direction: column;
	}

	.own-review-modal-filter-block {
		padding: 0;
		background: transparent;
	}

	.own-review-modal {
		width: 100%;
	}

	.calendar-bottom-action__text {
		text-align: left;
		font-size: 12px;
	}

	.grid-thumbnail--reviews {
		margin-left: 0;
	}

	.read-all-reviews-btn {
		width: 100%;
	}

	.own-review-collecting-modal {
		max-height: 90%;
		top: 53%;
		width: 96%;
	}

	.search-result-search-section--sticky {
		position: sticky;
	}

	.fs-container section.search {
		padding: 10px 20px;
	}

	.header-container--search-results {
		position: static;
		margin-top: -107px;
	}

	.header-container--search-results .header-contact-container,
	.header-container--search-results #header {
		z-index: 1;
	}

	.login-anchor--header {
		display: none;
	}

	.listing-item__spo-last {
		height: 370px;
	}

	.listing-top-text__content-shrink {
		max-height: 120px;
	}

	.listing-top-text__content-shrink--collapsed {
		max-height: 100%;
	}

	.listing-bottom-text .heading-2 {
		font-size: 1.5rem;
	}

	.evisitor-complete-info {
		margin-left: auto;
	}

	.show-map {
		width: 50px;
		justify-content: center;
		margin-left: 20px !important;
		border: none !important;
		padding: 0 !important;
		background: transparent !important;
		gap: 8px;
	}

	.show-map:hover {
		background: transparent !important;
	}

	.show-map.list-visible {
		margin-left: 0 !important;
		width: auto;
		padding: 6px 0!important;
	}

	.show-map > span {
		display: none;
	}

	.show-map.list-visible > span {
		display: block;
	}

	.show-map__icon {
		margin-right: 0;
	}

	.button-ml-md {
		margin-left: 0;
	}

	.photo-gallery-modal__img--big {
		height: auto;
		min-height: 375px;
	}

	.photo-gallery-modal__img--small {
		height: auto;
		min-height: 217px;
	}

	.main-search-input input {
		font-size: .875rem !important;
	}

	.main-search-input-item .qtyButtons input {
		font-size: 1.125rem !important;
	}

	.flatpickr-day:hover {
		background: transparent !important;
	}

	.search-listing-notice--visible {
		display: inline-flex;
		position: static;
	}

	.filtering-modal {
		height: 95%;
		max-height: 95%;
	}

	.photo-gallery-modal__grid {
		max-width: 100%;
	}

	.photo-gallery-modal__close-btn {
		position: static;
	}

	.filtering-modal__section-row {
		flex-direction: column;
	}

	.photo-gallery-modal__close-btn::before {
		width: 20px;
		height: 20px;
		background-size: 20px 20px;
	}

	.photo-gallery-modal__filter-container {
		top: 47px;
		width: calc(100% - 20px);
		padding-left: 4px;
		border-bottom: 6px solid white;
	}

	.photo-gallery-modal__title {
		font-size: 1.438rem;
		margin-left: 5px;
	}

	.photo-gallery-modal {
		padding: 0 10px 20px;
	}

	.photo-gallery-modal__grid__wrapper--pt {
		padding-top: 35px;
		max-width: 600px;
		margin: 0 auto;
	}

	.photo-gallery-modal__grid__wrapper {
		scroll-margin-top: 100px;
	}

	.photo-gallery-modal__grid__wrapper--first {
		margin-top: 120px;
	}

	.photo-gallery-modal__header--no-category {
		padding: 10px;
	}

	.photo-gallery-modal__grid__title {
		font-size: 1.438rem;
		margin-left: 5px;
	}

	.photo-gallery-modal__content {
		margin-top: 0;
	}

	.photo-gallery-modal__header {
		padding: 10px;
		padding-bottom: 0;
		left: 0;
		width: 100%;
	}

	.photo-gallery-modal__grid__wrapper--no-category {
		margin-top: 30px;
	}

	.listing-top-content,
	.listing-destinations {
		border-bottom: none;
		margin-bottom: 0;
	}

	.search .panel-dropdown.wide .panel-dropdown-content {
		width: 100%;
		padding: 15px 10px;
		left: 0;
	}

	.testimonial-block:before {
		left: 0;
	}

	.listing-controls.listing-controls--flex {
		justify-content: space-between;
		align-items: center;
	}

	.search-listing-block .button.filter-results {
		height: 51px;
		width: 100%;
	}

	.panel-dropdown .panel-dropdown-content {
		width: 100%;
		max-width: 100%;
	}

	.listing-top-content .listing-top-title,
	.content-block__title {
		font-size: 1.728rem;
		line-height: 1.2;
		margin-top: 10px;
	}

	.listing-top-content .heading-3,
	.content-block .heading-2 {
		font-size: 0.938rem;
		line-height: 1.2;
		margin-top: 10px;
		font-weight: 600;
	}

	.listing-bottom-text .heading-3 {
		font-size: 1.2rem;
	}

	.listing-top-content p,
	.content-block p {
		font-size: 1rem;
		line-height: 1.4;
	}

	.listing-lead-block .listing-lead-block-left {
		border-right: none;
		margin-bottom: 15px;
		display: flex;
		margin-right: 0 !important;
		padding-right: 0 !important;
	}

	.listing-lead-block .listing-lead-block-phone {
		margin-left: 10px;
		padding-left: 45px;
	}

	.listing-lead-block .listing-lead-block-right .listing-lead-block-form button,
	.listing-lead-block .listing-lead-block-right .listing-lead-block-form input {
		width: 100%;
	}

	.listing-lead-block .listing-lead-block-right .listing-lead-block-form input {
		margin-bottom: 10px;
	}

	.main-loader > div {
		width: 90%;
		top: 120px;
		left: 50%;
		transform: translateX(-50%);
	}

	.showing-results--no-margin {
		margin-bottom: 15px;
	}

	.listing-pagination {
		flex-direction: column;
		padding: 15px;
	}

	.listing-pagination-items--show-mobile {
		display: block;
	}

	.listing-pagination-items--hide-mobile {
		display: none;
	}

	.listing-pagination-item {
		display: none;
		float: none;
		margin: 0;
	}

	.listing-pagination-items {
		text-align: center;
	}

	.listing-pagination-item--active,
	.listing-pagination-previous,
	.listing-pagination-next {
		display: inline-block;
		vertical-align: middle;
	}

	.listing-pagination-previous {
		float: left;
	}

	.listing-pagination-next {
		float: right;
		margin-right: 10px;
	}

	.listing-pagination-previous:before,
	.listing-pagination-next:before {
		width: 30px;
		height: 30px;
		font-size: 1rem;
		background-size: 18px 18px;
		color: #fff;
		background-position: center;
	}

	.fs-container .filtering-button {
		margin-bottom: 0;
		margin-top: 25px;
	}

	.fs-container .sort-by-select {
		margin-bottom: 0;
		margin-top: 25px;
	}

	.listing-pagination-item--active {
		border: none;
		width: auto;
		height: auto;
		background: none;
	}

	.listing-pagination-item-content {
		display: inline-block;
		vertical-align: middle;
		margin-top: 1px;
	}

	.listing-pagination-results {
		display: none;
	}

	.listing-pagination-page-mobile {
		display: inline-block;
		vertical-align: middle;
		line-height: 28px;
	}
}
@media (max-width: 767px) {
	.inputs-wrapper {
		grid-template-columns: repeat(1, 1fr);
	}

	.evisitor-button-submit {
		width: 100%;
	}

	.form-details.active {
		width: 95%;
	}

}
@media (max-width: 600px) {
	.grid-thumbnail-custom__block {
		width: calc(100vw - 65px);
	}

	.grid-item-tag {
		width: calc(50% - 15px);
	}

	.img-box-content--bellow h4 {
		font-size: .875rem;
	}

	.photo-gallery-modal__img--big {
		height: auto;
		min-height: 362px;
	}

	.photo-gallery-modal__img--small {
		height: auto;
		min-height: 210px;
	}
}

/* All Mobile Sizes (devices and browser) */
@media (max-width: 767px) {
	body,
	.listing-top-content .listing-top-text {
		font-size: .875rem;
	}

	.tag-cloud {
		display: flex;
		flex-wrap: nowrap;
		overflow-x: auto;
		width: 100%;
		align-items: center;
		margin-bottom: 10px;
	}

	.listing-container--search-results .sort-by-select {
		float: left;
	}

	.fs-container .filtering-button {
		margin: 0;
	}

	.fs-container .sort-by-select {
		margin: 0;
		margin-bottom: 20px;
	}

	.tag-cloud > * {
		margin: 5px 5px 5px 0;
		flex-shrink: 0;
	}

	.header-contact__phone-title--desktop {
		display: none;
	}

	.header-contact__phone-title--mobile {
		display: inline-block;
	}

	.header-contact__phone {
		justify-content: center;
		padding: 2px 12px;
		font-size: .75rem;
	}

	.header-contact__phone-icon--rounded {
		margin-right: 5px;
	}

	.form-column--persons {
		flex-direction: column;
	}

	.qtyButtons--horizontal {
		margin-right: 0;
	}

	.col-flex-center {
		flex-direction: column;
	}

	.button--fullwidth-mobile {
		display: block;
		width: 100%;
		text-align: center;
	}

	button.border.button--tiny,
	.button.border.button--tiny {
		padding: 6px 10px;
	}

	#map-container.map-in-viewport {
		height: 75vh !important;
	}

	.page-breadcrumb__item--hidden-mobile {
		display: none
	}

	.page-breadcrumb__item--visible-mobile {
		display: block;
		color: #347baa;
		text-decoration: underline;
		font-size: .875rem;
	}

	.page-breadcrumb__item:after {
		content: none;
	}

	.page-breadcrumb__item:first-child:before {
		content: none;
	}

	.page-breadcrumb__item:before {
		content: '';
		filter: invert(44%) sepia(47%) saturate(645%) hue-rotate(160deg) brightness(89%) contrast(89%);
		width: 6px;
		height: 8px;
		background-image: url('/assets/img/svg/right-arrow.svg');
		background-size: 6px 6px;
		background-repeat: no-repeat;
		display: inline-block;
		margin: 0 5px;
	}

	.page-breadcrumb__item--single-object:before {
		transform: rotate(-180deg);
		filter: invert(44%) sepia(47%) saturate(645%) hue-rotate(160deg) brightness(89%) contrast(89%);
		margin-left: 0;
	}

	.side-usps {
		margin-top: 30px;
	}

	#footer {
		padding-top: 20px;
		border-top: 1px solid #ddd;
	}

	.footer-area {
		flex-direction: column;
		justify-content: flex-start;
	}

	.row-flex--mobile {
		display: flex;
	}

	.block-insurance__images {
		flex-direction: column;
	}

	.block-insurance__image-container:first-of-type {
		margin-right: 0;
		margin-bottom: 25px;
	}

	.block-insurance__image {
		height: 200px;
	}

	.footer-payments {
		flex-direction: column;
		align-items: center;
	}

	.brands-footer {
		justify-content: flex-start;
		align-items: flex-start;
		flex: 0;
		padding-top: 0;
	}

	.brands-footer__content {
		margin-right: 0;
		justify-content: center;
	}

	.brands-footer__gaveia-group {
		display: flex;
		align-items: center;
		width: 100%;
		justify-content: center;
		padding: 16px 0;
		gap: 8px;
	}

	.brands-footer__gaveia-group-wrapper {
		width: 100%;
		flex-direction: column;
		text-align: center;
	}

	.brands-footer__anchor {
		margin: 8px 15px 0 0;
		display: inline-block;
	}

	.brands-footer__anchor:last-child {
		margin: 8px 0 0 0;
	}

	.main-search-container {
		height: inherit;
	}

	.main-search-container .image-container {
		height: 290px;
		object-position: -35px;
	}

	.main-search-inner {
		padding-bottom: 0;
		padding-top: 0;
		top: 0;
		margin-top: 0;
		height: inherit;
		min-height: 290px;
	}

	.block-main-usps .usp-item {
		flex: 0 0 42%;
	}

	.row-flex--mobile-column-reverse {
		flex-direction: column-reverse;
	}

	h3.headline {
		font-size: 2.074rem;
		line-height: 1.2;
	}

	h3.headline.smaller,
	.section-title {
		font-size: 1.728rem;
		line-height: 1.2;
	}

	h3.headline.centered-mobile {
		text-align: center;
	}

	.wrapper {
		padding-top: 63px;
	}

	.wrapper--expanded {
		padding-top: 107px;
	}

	.wrapper__single-villa {
		padding-top: 0;
	}

	.testimonials-block {
		width: 100%;
		flex-direction: row;
	}

	.testimonials-block.keen-initialized {
		flex-wrap: nowrap;
	}

	.testimonials-title {
		font-size: 1.728rem;
		line-height: 1.2;
	}

	.testimonials-subtitle {
		margin-bottom: 24px;
		font-size: 0.938rem;
	}

	.testimonial-block {
		margin-right: 0 !important;
	}

	.testimonial-block__all {
		display: none;
	}

	.block-main-testimonials .keen-arrows--container {
		position: relative;
		display: inline-block;
		width: 100%;
		height: 40px;
		margin: 10px 0 15px 0;
	}

	.block-main-testimonials .keen-arrow {
		top: 5px;
		opacity: 1;
		box-shadow: none;
		border: none;
		background-color: #efefef;
		border-radius: 50%;
		padding: 10px;
	}

	.block-main-testimonials .keen-disabled {
		display: inline-block;
		opacity: .5;
	}

	.block-main-testimonials .keen-next {
		right: 36%;
		transform: translate3d(0, 0, 0) rotate(0deg);
	}

	.block-main-testimonials .keen-prev {
		left: 36%;
		transform: translate3d(0, 0, 0) rotate(180deg);
	}

	.block-main-testimonials .keen-prev:before,
	.block-main-testimonials .keen-next:before {
		width: 18px;
		height: 18px;
		background-size: 18px 18px;
		filter: invert(0);
	}

	.testimonial-block--hidden {
		display: flex;
	}

	.block-main-testimonials button.button {
		display: none;
	}

	.listing-object-actions {
		background: transparent;
	}

	.listing-object-actions__row {
		display: flex;
		flex-direction: column-reverse;
	}

	.listing-object-actions:before {
		content: none;
	}

	.single-listing__listing-option-item i {
		display: inline-block;
		margin-bottom: 0;
		height: auto;
		margin-right: 5px;
	}

	.single-listing__listing-option-item i.icon-floorplan {
		height: 23px;
	}

	.hide-on-mobile,
	.hide-on-mobile.hide-on-mobile--inline-block {
		display: none;
	}

	.search-result-sort-filter {
		justify-content: flex-start;
	}

	.show-on-mobile {
		display: block;
	}

	.loading-booking > div {
		width: 90%;
	}

	.listing-lead-block {
		flex-direction: column;
	}

	.fs-container .fs-switcher, .fs-container .fs-listings {
		padding: 0
	}

	.main-search-input-item.calendar--borderless .flatpickr-calendar {
		width: 100% !important;
		margin-top: 10px;
	}

	.search__column--arrival-departure.calendar--borderless .flatpickr-calendar {
		width: 100% !important;
	}

	.flatpickr-days {
		width: 100%;
	}

	.block-quiz-flex-block--calendar.calendar--borderless .flatpickr-calendar,
	.block-quiz-flex-block--calendar.calendar--borderless .flatpickr-days {
		width: 100% !important;
	}

	.dayContainer {
		width: 100%;
		min-width: 0;
		max-width: 100%;
	}

	.search .input-with-icon.panel-dropdown .panel-dropdown-content {
		max-width: 100%;
		width: 100%;
	}

	.filtering-button-wrapper {
		justify-content: flex-start;
		gap: 12px;
		margin-top: 10px;
	}

	.search-listing-block {
		flex-direction: column;
	}

	.search-listing-block__item--calendar,
	.search-listing-anchor-filter {
		width: 100%;
	}

	.search-listing-block__item {
		margin-right: 0;
		margin-bottom: 10px;
	}

	.search-listing-block .more-filters {
		margin-top: 6px;
		padding: 11px 5px !important;
	}

	.search-listing-block .filter-results {
		float: none;
		margin-left: 0;
	}

	.video-container .mobile-image-fallback {
		display: block;
	}

	.video-container video {
		display: none;
	}

	.keen-block--disable-mobile .grid-thumbnail-carousel,
	.keen-slider--disable-mobile {
		overflow-x: auto;
		touch-action: initial;
	}

	.keen-block--disable-mobile .grid-thumbnail-block.content-over-image .grid-thumbnail-content {
		margin-bottom: 15px;
	}

	.grid-thumbnail-carousel {
		margin-left: 0;
		margin-right: 0;
	}

	.grid-thumbnail-carousel .grid-thumbnail-block,
	.grid-thumbnail-custom .grid-thumbnail-custom__block,
	.grid-thumbnail--regions .grid-thumbnail-carousel--four-in-a-row .grid-thumbnail-block {
		width: 310px;
		min-width: 310px;
		padding-left: 0;
		padding-right: 10px;
	}

	.grid-thumbnail-carousel .grid-thumbnail-block:last-child,
	.grid-thumbnail-custom .grid-thumbnail-custom__block:last-child,
	.grid-thumbnail--regions .grid-thumbnail-carousel--four-in-a-row .grid-thumbnail-block:last-child {
		padding-right: 0;
	}

	.grid-thumbnail-custom .grid-thumbnail-custom__block:last-child {
		padding-right: 0;
	}

	.grid-thumbnail-custom__anchor {
		margin: 0 0 15px 0;
	}

	.grid-thumbnail-carousel--six-in-a-row,
	.grid-thumbnail-carousel--five-in-a-row,
	.grid-thumbnail--regions .grid-thumbnail-carousel--five-in-a-row {
		margin-left: 0;
		margin-right: 0;
	}

	.grid-thumbnail-carousel--six-in-a-row .grid-thumbnail-block,
	.grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block {
		min-width: 48.0769%;
		padding-left: 0;
		padding-right: 10px;
	}

	.grid-thumbnail-carousel--six-in-a-row .grid-thumbnail-block:last-child,
	.grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block:last-child {
		padding-right: 0;
	}

	.grid-thumbnail-carousel--six-in-a-row .grid-thumbnail-block:nth-child(n+7),
	.grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block:nth-child(n+6),
	.grid-thumbnail-carousel--three-in-a-row .grid-thumbnail-block:nth-child(n+4) {
		display: block;
	}

	.grid-thumbnail--regions .grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block {
		padding-left: 0;
		padding-right: 10px;
		min-width: 280px;
		width: auto;
	}

	.grid-thumbnail-carousel--testimonials {
		padding-bottom: 16px;
	}

	.grid-thumbnail--regions .grid-thumbnail-carousel--five-in-a-row .grid-thumbnail-block:last-child {
		padding-right: 0;
	}

	.grid-thumbnail-image--160h img {
		max-width: 100%;
	}

	.grid-thumbnail-image--300h img {
		max-width: 100%;
	}

	.grid-thumbnail-block.content-over-image .grid-thumbnail-content {
		overflow: visible;
		white-space: nowrap;
	}

	.grid-thumbnail-block.content-over-image .grid-thumbnail-content p {
		line-height: 21px;
	}

	.keen-block--hidden-dots-mobile .keen-dots {
		display: none;
	}

	.keen-block--visible-arrows-mobile .keen-arrow {
		display: block;
		border: 1px solid #999
	}

	.keen-block--visible-arrows-mobile .keen-arrow.keen-prev {
		right: 45px;
		left: auto;
		top: -40px;
	}

	.keen-block--visible-arrows-mobile .keen-arrow.keen-next {
		right: 0;
		left: auto;
		top: -40px;
	}

	.keen-block--visible-arrows-mobile .keen-arrow.keen-prev:before,
	.keen-block--visible-arrows-mobile .keen-arrow.keen-next:before {
		width: 12px;
		height: 12px;
		background-size: 12px 12px;
	}

	.keen-block--visible-arrows-mobile .keen-arrow.keen-prev.keen-disabled,
	.keen-block--visible-arrows-mobile .keen-arrow.keen-next.keen-disabled {
		opacity: .4;
	}

	.listing-items-grid__title {
		font-size: .875rem;
		margin-bottom: 25px;
	}

	.listing-item-container.listing-item--grid + .listing-item-bottom .actions {
		padding: 15px 0;
	}

	.listing-item-container.listing-item--grid + .listing-item-bottom .actions a.button {
		min-width: 130px;
	}

	.listing-items-carousel {
		margin: 0;
	}

	.listing-items-similar.listing-items-grid {
		margin: 0;
	}

	.listing-items-reviews.listing-items-grid {
		padding-bottom: 16px;
		margin-bottom: 16px;
	}

	.listing-items-similar.listing-items-grid .listing-item-container {
		margin: 0;
		padding: 0 10px 0 0;
	}

	.listing-items-similar.listing-items-grid .listing-item-container:last-child {
		padding: 0;
	}

	.listing-items-similar:not(.keen-initialized) .keen-slider__slide {
		min-width: 310px;
	}

	.listing-top-text .listing-top-read-more {
		display: block;
		font-size: 0.938rem;
		font-weight: 400;
		color: #398dc6;
	}

	.listing-top-text > p {
		display: none;
	}

	.listing-top-text .listing-top-read-more:after {
		content: '';
		filter: invert(43%) sepia(99%) saturate(335%) hue-rotate(161deg) brightness(95%) contrast(93%);
		display: inline-block;
		width: 10px;
		height: 10px;
		background-image: url('/assets/img/svg/right-arrow.svg');
		background-size: 10px 10px;
		background-repeat: no-repeat;
		position: relative;
		margin-left: 3px;
		transform: rotate(90deg);
	}

	.listing-top-text .listing-top-read-more--expanded:after {
		transform: rotate(-90deg);
	}

	.showing-results {
		font-size: 0.938rem;
		margin-top: 0;
	}

	.listing-features {
		columns: 1;
	}

	.checkboxes--simple {
		columns: 2;
	}

	.listing-features-block {
		display: block;
	}

	.listing-features-block--hidden {
		display: none;
	}

	.listing-main-content > h3 {
		margin-top: 0;
		font-size: 0.938rem;
		line-height: 1.2;
	}

	.listing-main-features.listing-features-container {
		margin-top: 20px;
		margin-bottom: 0;
	}

	.listing-main-features__icon {
		margin-bottom: 0;
	}

	.listing-content-container .listing-section .listing-features-container.listing-features-container--visible {
		display: block;
	}

	#listing-overview.listing-section > * {
		display: block;
	}

	.listing-features.three-column {
		columns: 2;
	}

	.listing-features-block .listing-features.three-column {
		columns: 1;
	}

	.listing-main-title {
		font-weight: 600;
		font-size: 1.728rem;
		line-height: 1.2;
		margin: 8px 0 4px 0;
		letter-spacing: .33px;
	}

	.listing-main-subtitle {
		font-size: 1rem;
	}

	.listing-items-similar .listing-item-container.list-layout .listing-item-image {
		height: 270px;
	}

	.listing-items-grid .listing-item-container.list-layout .listing-item-inner-middle .main-features + .main-features {
		flex: 0 0 140px;
	}

	.listing-nav {
		display: none;
	}

	.arival-departure-date-container .flatpickr-calendar .flatpickr-content-after {
		margin: 5px 10px;
		flex-direction: column;
		align-items: center;
	}

	.arival-departure-date-container .flatpickr-calendar .flatpickr-content-after__legend {
		width: 100%;
	}

	.flatpickr-content-after__legend {
		display: flex;
		flex-flow: wrap;
		align-items: center;
	}

	.arival-departure-date-container .flatpickr-calendar .flatpickr-content-after__left {
		margin-bottom: 8px;
	}

	.arival-departure-date-container .flatpickr-calendar .flatpickr-day {
		line-height: 42px !important;
		height: 42px !important;
	}

	.flatpickr-calendar .flatpickr-day.nextMonthDay,
	.flatpickr-calendar .flatpickr-day.prevMonthDay {
		text-indent: -99999px;
	}

	.flatpickr-calendar .dayContainer {
		justify-content: flex-start;
	}

	.date-mainpicker + .flatpickr-calendar.static {
		width: 100% !important;
		right: auto;
	}

	#backtotop {
		display: none;
	}

	.small-dialog-header {
		width: calc(100% + 30px);
		left: -15px;
		padding: 15px;
		margin-bottom: 0;
	}

	#small-dialog,
	.inquiry-modal,
	.informational-modal,
	.filtering-modal {
		background: #fff;
		padding: 15px;
		padding-top: 0;
		margin: 15px auto;
	}

	#small-dialog .mfp-close,
	.inquiry-modal .mfp-close,
	.informational-modal .mfp-close,
	.filtering-modal .mfp-close {
		top: 10px;
		right: 10px;
		width: 40px;
		height: 40px;
	}

	.informational-modal {
		height: 100%;
		max-height: 100%;
	}

	#small-dialog input,
	.inquiry-modal input,
	.inquiry-modal .flatpickr-wrapper .flatpickr-input {
		margin-bottom: 10px !important;
		height: 45px;
		line-height: 45px;
	}

	#small-dialog input.qtyInput,
	.inquiry-modal input.qtyInput {
		margin-bottom: 0 !important;
	}

	#small-dialog .inquiry-form--input-departure + .flatpickr-calendar,
	.inquiry-modal .inquiry-form--input-departure + .flatpickr-calendar {
		right: 0 !important;
	}

	#small-dialog label,
	.inquiry-modal label {
		margin-bottom: 2px;
	}

	#small-dialog textarea,
	.inquiry-modal textarea {
		padding: 10px 20px;
		min-height: 85px;
	}

	#small-dialog .checkboxes.checkboxes-pets,
	.inquiry-modal .checkboxes.checkboxes-pets {
		margin: 10px 0 15px 0 !important;
	}

	#small-dialog label.inquiry-message,
	.inquiry-modal label.inquiry-message {
		margin-top: 0 !important;
	}

	.small-dialog-form .hubspot-form .hubspot-thankyou-message {
		padding: 0 15px;
	}


	.inquiry-form.hubspot-form .input-with-icon .input-icon-padding {
		padding-left: 20px !important;
	}

	.inquiry-form.hubspot-form .hubspot-form-submit {
		width: 100%;
	}

	.small-dialog-header h3 {
		padding: 0;
		margin: 0;
		line-height: 1.2;
		width: 90%;
	}

	.mfp-content {
		height: 100%;
		overflow: hidden;
		overflow-y: auto;
		max-height: 100%;
	}

	#small-dialog,
	.inquiry-modal,
	.informational-modal,
	.filtering-modal {
		max-width: 100vw;
	}

	.listing-nav li a {
		border-bottom: 1px solid #e0e0e0;
		padding: 15px;
		width: 100%;
		display: block;
		padding-left: 0;
	}

	.listing-nav li {
		margin-right: 0;
		display: block;
	}

	.listing-nav-container.cloned .listing-nav li:first-child a.active,
	.listing-nav-container.cloned .listing-nav li:first-child a:hover,
	.listing-nav li a.active,
	.listing-nav li a:hover {
		border-color: #e8e8e8;
	}

	.main-search-inner__main-heading {
		font-size: 2.074rem;
		line-height: 1.2;
		margin: 0 0 20px;
		width: 100%;
	}

	.main-search-inner__paragraph {
		display: none;
	}

	.main-search-input__mobile {
		margin-top: 0;
	}

	.main-search-container.dark-overlay:before {
		content: '';
		background: rgba(0, 0, 0, .08);
	}

	.copyrights .col-sm-6 {
		margin: 10px auto;
		text-align: center;
	}

	.footer-links-container {
		width: 100%;
		margin-top: 10px;
	}

	.footer-links {
		margin-bottom: 15px;
	}

	.footer-links-container:first-of-type {
		margin-top: 20px;
	}

	.footer-links-container > div {
		display: none;
	}

	.footer-links-title {
		margin-bottom: 15px;
		position: relative;
		text-transform: uppercase;
	}

	.footer-logo {
		margin-bottom: 20px;
	}

	.copyrights .member {
		float: none;
	}

	#footer .flex {
		flex-direction: column;
	}

	.support-block {
		margin-top: 0 !important;
	}

	.support-block .row {
		border-radius: 0;
	}

	.flatpickr-day {
		max-width: 100% !important;
	}

	.flatpickr-day.hidden {
		text-indent: 0;
	}

	.own-review-bar-wrapper {
		width: 50%;
	}

	.how-reviews-work-block {
		flex-direction: column;
		gap: 0;
		align-items: flex-start;
	}

	.how-reviews-work-block-part {
		margin-top: 10px;
	}

	.own-review-bar__value--spacing {
		margin-right: 16px;
	}

}


@media (max-width: 600px) {
	.panel-buttons {
		flex-direction: column;
		justify-content: space-between;
		padding-top: 0;
		margin-top: 0;
		position: fixed;
		bottom: 15px;
		padding: 0 15px;
		background: #fff;
		z-index: 1;
		width: calc(100% - 1px);
		left: 0;
	}

	.panel-apply {
		width: 100%;
		margin-top: 10px;
	}
}
@media (max-width: 501px) {
	.photo-gallery-modal__img--big {
		height: auto;
		min-height: 299px;
	}

	.photo-gallery-modal__img--small {
		height: auto;
		min-height: 172px;
	}

	.custom-infobox .listing-content {
		width: 63%;
	}
}

@media (max-width: 420px) {
	.page-breadcrumb:not(.page-breadcrumb--single-villa) {
		display: flex;
		flex-wrap: nowrap;
		overflow-x: auto;
		width: 100%;
		padding-bottom: 10px;
	}

	.page-breadcrumb__item {
		flex-shrink: 0;
		float: none;
		-webkit-overflow-scrolling: touch;
	}

	.photo-gallery-modal__img--big {
		height: auto;
		min-height: 248px;
	}

	.photo-gallery-modal__img--small {
		height: auto;
		min-height: 142px;
	}

	.header-contact__phone {
		flex: 1.2;
	}

	.header-contact__phone:first-of-type {
		flex: 1;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features {
		display: block;
		column-count: 1;
		width: 100%;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li:before {
		content: ' · ';
		color: #757e9c;
		display: inline-block;
		position: relative;
		top: 1px;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li {
		float: left;
		margin: 0 6px 0 0;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li i {
		display: none;
	}

	.listing-item-container.list-layout .listing-item-inner-middle .main-features li .icon:before {
		content: none;
	}
}

@media (max-width: 375px) {
	.header-contact__phone {
		padding-left: 5px;
		padding-right: 5px;
		margin-left: 6px;
	}

	.main-search-input__mobile--search-results {
		padding-left: 10px;
	}

	.main-search-input__mobile.main-search-input__mobile--search-results:before {
		content: none;
	}
}

@media (max-width: 320px) {
	header.fullwidth .container {
		padding: 0 10px;
	}

	.help-block .help-image-hero {
		display: none;
	}

	.help-block h3, .help-block p {
		width: 100%;
	}

	.price-available {
		font-size: 1rem;
	}

	.custom-infobox .listing-img-container {
		width: 100px;
	}

	.custom-infobox .listing-img-container img {
		min-width: 100px;
	}

	.flatpickr-calendar.rangeMode,
	.flatpickr-rContainer,
	.flatpickr-days {
		width: 100% !important;
	}

	.flatpickr-days .dayContainer {
		min-width: auto !important;
	}
}


/* ------------------------------------------------------------------- */
/* 06. Colours
---------------------------------------------------------------------- */
.footer-link__anchor:hover,
.main-search-input-item--location a:hover,
.input-with-icon.location a i:hover,
.panel-dropdown a:after,
.button.border.white:hover,
.info-box h4,
.testimonial-carousel .slick-slide.slick-active .testimonial:before,
.headline span i {
	color: #252525;
}

.qtyTotal,
#backtotop a,
input[type="button"],
input[type="submit"],
.button,
table.basic-table th,
mark.color,
input:checked + .slider,
.custom-zoom-in:hover,
.custom-zoom-out:hover,
#scrollEnabling:hover,
#scrollEnabling.enabled,
button.panel-apply {
	background-color: #252525;
}

.testimonial-carousel .slick-slide.slick-active .testimonial-box,
.listing-item-container.list-layout span.tag,
.tip,
.mfp-arrow:hover {
	background: #252525;
}

.trigger.active a,
.ui-accordion .ui-accordion-header-active:hover,
.ui-accordion .ui-accordion-header-active {
	background-color: #252525;
	border-color: #252525;
}

.info-box {
	border-top: 2px solid #252525;
	background: #252525 linear-gradient(to bottom, rgba(255, 255, 255, .98), rgba(255, 255, 255, .95));
	color: #252525;
}

.info-box.no-border {
	background: #252525 linear-gradient(to bottom, rgba(255, 255, 255, .96), rgba(255, 255, 255, .93));
}

.listing-item-container.compact .listing-item-content span.tag {
	background-color: #252525;
}

.map-box h4 a:hover {
	color: #252525;
}

.cluster-visible {
	background-color: #252525;
}

.cluster-visible:before {
	border: 7px solid #252525;
	box-shadow: inset 0 0 0 4px #252525;
}

.marker-arrow {
	border-color: #252525 transparent transparent;
}

#cookie-notice {
	background-color: rgb(0, 37, 60);
	position: static;
	z-index: 9996;
}

#cookie-notice .button.wp-default {
	background: #48a9ea;
	color: #fff;
	position: relative;
	font-size: 14px;
	font-weight: 500;
	display: inline-block;
	transition: all .2s ease-in-out;
	padding: 6px 20px;
	cursor: pointer;
	border: none;
	border-radius: 50px;
	vertical-align: middle;
	text-shadow: none;
}

.block--blink {
	display: none;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	background: #efedaf;
	border-radius: 3px;
}

.no-listing-entries {
	padding: 30px 20px;
	color: #252525;
	border: 1px solid #252525;
	background: #fff;
	border-radius: 2px;
}

.no-listing-entries--with-icon:before {
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	content: '\e926';
	color: #6f6f61;
	margin-right: 4px;
	display: inline-block;
	vertical-align: middle;
	font-size: 14px;
}

.no-listing-entries i:before {
	color: #6f6f61;
	margin-right: 4px;
	display: inline-block;
	vertical-align: middle;
}

.payment-credit-card-state {
	border: 1px solid #c7cc7d;
	background: #eff1d1;
	font-size: 1rem;
	line-height: 21px;
	padding: 8px 10px 8px 34px;
	text-indent: -11px;
	margin-top: 5px;
}

.payment-credit-card-state--error:before {
	content: '';
	filter: invert(38%) sepia(4%) saturate(5233%) hue-rotate(19deg) brightness(90%) contrast(75%);
	display: inline-block;
	width: 14px;
	height: 14px;
	background-image: url("/assets/img/svg/exclamation.svg");
	background-size: 14px 14px;
	background-repeat: no-repeat;
	position: relative;
	margin-right: 3px;
	margin-left: -10px;
	top: 3px;
}

/* Lazy sizes - fade effect on lazyload */
.lazyload,
.lazyloading {
	opacity: .5;
}
.lazyloaded {
	opacity: 1;
	transition: opacity 250ms;
}

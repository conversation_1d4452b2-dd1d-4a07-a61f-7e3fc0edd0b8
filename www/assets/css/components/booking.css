body.booking-layout {
	background: #fff;
}

.sandbox-notification-block {
	background: #ff9800;
	color: #252525;
	text-align: center;
	padding: 10px;
	font-weight: 500;
	width: 100%;
	margin-bottom: 15px;
}


h1, h2, h3, h4 {
	color: #252525;
}

header {
	background: #fff;
	border-bottom: 1px solid #ddd;
}

.header__wrapper {
	display: flex;
	align-items: stretch;
	padding: 8px 0
}

.header__logo {
	display: flex;
	flex: 1 0 auto;
	align-items: center;
}

.header__content {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	color: #fff;
}

.header__content-icon {
	margin-right: 10px;
}

.header__content-phone {
	display: flex;
	align-items: center;
	font-weight: 400;
	font-size: 1rem;
}

.header__content-phone-anchor {
	color: #252525;
	display: flex;
	align-items: center;
}

.header__content-phone-anchor:hover {
	color: #252525;
}

.booking-component {
	margin: 30px 0 50px 0;
}

.booking-component__note {
	display: none;
	background: #faf8ec;
	border: 1px solid #d7cfa3;
	border-radius: 3px;
	padding: 15px 25px;
	line-height: 1.2;
	font-size: 1rem;
	color: #252525;
	margin: 20px 0;
}

.booking-component__note-title,
.booking-component__note-text {
	margin: 0;
	line-height: 21px;
	font-weight: 600;
}

.booking-component__note-text {
	font-size: 1rem;
	font-weight: 400;
}

.booking-component__title {
	font-size: 1.36rem;
	font-weight: 500;
	margin-bottom: 15px;
	margin-top: 0;
	font-family: var(--font-primary);
}

.booking-component__common-list {
	list-style: none;
	color: #252525;
	margin-bottom: 0;
	padding: 0;
	line-height: 1.6;
}

.booking-component__section {
	margin-bottom: 32px;
}

.booking-component__section-divider {
	border-color: #ccc;
}

.booking-component__block {
	border-radius: 8px;
	border: 1px solid #D0D5DD;
	padding: 20px;
	margin-bottom: 32px;
	position: relative;
}

.booking-component__block--ghost {
	border-bottom: 1px solid #D0D5DD;
	padding-bottom: 32px;
	margin-bottom: 32px;
}

.booking-component__switch {
	position: relative;
	display: inline-block;
	width: 48px;
	height: 28px;
	margin-bottom: 0;
}

.booking-component__switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.booking-component__switch-slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 28px;
	background-color: #EAECF0;
	-webkit-transition: .2s;
	transition: .2s;
}

.booking-component__switch-slider:before {
	position: absolute;
	content: "";
	height: 24px;
	width: 24px;
	left: 2px;
	bottom: 2px;
	background-color: white;
	-webkit-transition: .2s;
	transition: .2s;
	border-radius: 24px;
	border: 1px solid #EEF0F3;
	box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.25);
}
  
.booking-component__switch > input:checked + .booking-component__switch-slider {
	background-color: #344054;
}
  
.booking-component__switch > input:checked + .booking-component__switch-slider:before {
	-webkit-transform: translateX(20px);
	-ms-transform: translateX(20px);
	transform: translateX(20px);
}

.booking-component__section-title--with-element {
	display: flex;
	align-items: center;
	gap: 8px;
}

.booking-component__block--no-pb {
	padding-bottom: 0;
}

.booking-component__novasol-inputs {
	display: flex;
}

.booking-component__novasol-input-group {
	width: 100%;
	display: flex;
	gap: 16px;
}

.booking-component__novasol-input-group:first-of-type {
	margin-right: 20px;
}

.booking-component__novasol-input-group--big {
	width: 65%;
	position: relative;
}

.booking-component__novasol-input-group--small {
	width: 35%;
	position: relative;
}

.booking-btn-finish-wrapper {
	display: flex;
	justify-content: flex-end;
}

.checkboxes--black li,
.checkboxes--green li {
	font-size: .875rem;
	color: #1D2939;
	padding-bottom: 16px;
}

.checkboxes--black li:before {
	content: '';
	display: inline-block;
	width: 20px;
	height: 20px;
	background-image: url('/assets/img/svg/check-black.svg');
	background-size: 20px 20px;
	background-repeat: no-repeat;
	margin-right: 8px;
	vertical-align: middle;
	top: -1px;
	position: relative;
}

.checkboxes--green li:before {
	content: '';
	display: inline-block;
	width: 20px;
	height: 20px;
	background-image: url('/assets/img/svg/check-green.svg');
	background-size: 20px 20px;
	background-repeat: no-repeat;
	margin-right: 8px;
	vertical-align: middle;
	top: -1px;
	position: relative;
}

.booking-component__block--borderless {
	border: none;
	padding: 0;
}

.booking-component__label-selection-text {
	display: flex;
	align-items: center;
	background: #FCF7D8;
	border-radius: 20px;
	color: #886D3D;
	padding: 5px 8px;
	font-size: 1rem;
}

.booking-component__label-selection-text--insurance {
	background: #0071e3;
	color: #fff;
}

.booking-component__label-selection-icon {
	margin-right: 6px;
}

.booking-component__label-selection-text {
	display: none;
}

.booking-component__label-selection-text--active {
	display: flex;
}

.booking-component__flex {
	display: flex;
	align-items: center;
}

.booking-component__flex--selection {
	justify-content: space-between;
}

.booking-component__svg-image--allianz {
	margin-left: 5px;
}

.booking-component__anchor-allianz {
	display: block;
	color: #333;
	text-decoration: underline;
	text-align: right;
	font-size: .875rem
}

.booking-component__block-title {
	font-size: 1rem;
	color: #1D2939;
	font-weight: 500;
	margin: 0;
	font-family: var(--font-primary);
	line-height: 1.4;
}

.booking-component__block-title--border-bottom {
	border-bottom: 1px solid #D0D5DD;
	color: #1D2939;
	padding-bottom: 12px;
	margin-bottom: 12px;
}

.booking-policy-list-item {
	color: #475467;
	display: flex;
	align-items: flex-start;
	margin-bottom: 10px;
}

.booking-policy-list-item:last-of-type {
	margin-bottom: 0;
}

.booking-policy-list-item-circle {
	width: 8px;
	height: 8px;
	min-width: 8px;
	background: #D0D5DD;
	border-radius: 50%;
	margin-right: 8px;
	margin-top: 9px;
}

.booking-component__block-subtitle {
	font-size: .875rem;
	color: #667085;
	font-weight: 400;
	margin-top: 6px;
	margin-bottom: 0;
}

.booking-component__block-note {
	margin-top: 6px;
	display: flex;
	align-items: center;
	font-size: 1rem;
}

.booking-component__block-note-icon {
	margin-right: 8px;
}

.booking-component__checkbox-list {
	margin-top: 20px;
	display: flex;
	flex-flow: wrap;
	gap: 12px;
}

.booking-component__section-title-divider {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding-bottom: 10px;
	margin-bottom: 14px;
}

.booking-component__section-title-divider > .booking-component__section-title {
	margin: 0;
}

.booking-component__payment-list {
	display: flex;
	list-style: none;
	margin-bottom: 0;
	padding-left: 0;
}

.booking-component__payment-item {
	margin-left: 5px;
}

.booking-component__form-row {
	display: flex;
	flex: 1;
	margin-bottom: 0;
}

.booking-component__form-row--center-aligned {
	align-items: center;
}

.booking-component__form-row--top-spacing {
	margin-top: 10px;
	margin-bottom: 0;
}

.booking-component__form-col {
	display: flex;
	flex-direction: column;
	flex: 1;
	position: relative;
	margin-right: 20px;
}

#form-personal-details-is-human {
	display: none;
}

.booking-component__form-select,
.booking-component__form-input[type=text] {
	padding: 0 14px;
	border-radius: 6px;
	border: 1px solid #D0D5DD;
	height: 39px;
	line-height: normal;
}

.booking-component__form-input[type=text] {
	margin-bottom: 16px;
}

.booking-component__form-select:focus,
.booking-component__form-input[type=text]:focus {
	border-color: #232d56;
}

.booking-component__form-label {
	font-weight: 400;
	font-size: .938rem;
	color: #1D2939;
	margin-bottom: 6px;
}

.booking-component__error-block {
	color: #e21111;
	font-size: .938rem;
	position: absolute;
	bottom: -5px;
}

.booking-component__error-block-static {
	color: #e21111;
	font-size: .938rem;
	margin-top: -11px;
	margin-bottom: 8px;
	display: block;
}

.booking-component__form-input--novasol {
	margin-bottom: 16px !important;
}

.booking-component__form-input[type=text].booking-component__form-error,
.booking-component__block--error {
	border-color: #e21111!important;
}

.booking-component__form-col--field-title {
	flex-direction: row;
	align-items: center;
	gap: 14px;
	margin-bottom: 12px;
}

.booking-component__form-radio--title {
	margin: 0;
}

.booking-component__form-col:last-of-type {
	margin-right: 0;
}

.booking-component__form-col--field-title > .booking-component-black-radio > input[type="radio"] {
	width: 20px;
	accent-color: #000;
}

.booking-component-black-radio {
	display: flex;
	align-items: center;
	padding: 8px 12px;
	gap: 6px;
	border-radius: 8px;
	border: 1px solid #D0D5DD;
	cursor: pointer;
	backface-visibility: hidden;
	transform: translateZ(0);
	-webkit-font-smoothing: subpixel-antialiased;
	transition: all .1s ease-in-out;
	user-select: none;
}

.booking-component-black-radio > label {
	font-size: .875rem;
}

.booking-payment__options-label > input[type="radio"] {
	width: 15px;
	height: 15px;
	accent-color: #252525;
}

.booking-payment__options-name {
	margin-left: 10px;
	color: #1D2939; 
}

.booking-component__section-title {
	font-size: 1.125rem;
	font-weight: 500;
	font-family: var(--font-primary);
	color: #1D2939;
	margin-top: 0;
}

.insurance-list-wrapper {
	margin-top: 16px;
}

.insurance-list-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 10px;
	gap: 10px;
	line-height: 1.4;
	font-size: .875rem
}

.booking-component__section-title--bigger-spacing {
	margin-bottom: 25px;
}

.booking-component__payment-method {
	border: 1px solid #D0D5DD;
	border-radius: 8px;
	padding: 20px;
	width: 100%;
	height: fit-content;
}

.booking-component__block-sm-mb {
	margin-bottom: 16px;
}

.booking-form--radio-root {
	margin-left: auto;
}

.booking-component__payment-label > input[type="radio"]{
	min-width: 16px!important;
	min-height: 16px!important;
	opacity: .5;
}

.booking-component__payment-label > input[type="radio"]:checked {
	display: none;
}

.booking-component__payment-label > input[type="radio"]:checked + .booking-payment__options-icon--checked {
	display: block;
}

.booking-component__payment-label > input[type="radio"]:not(:checked) + .booking-payment__options-icon--checked {
	display: none;
}

.booking-payment__options-icon--checked {
	min-width: 16px;
	max-width: 16px;
	padding-bottom: 8px;
	margin-left: auto;
}


.booking-component__payment-method--active {
	background: #FCFCFD;
}

.booking-component__payment-label {
	margin-bottom: 0;
	cursor: pointer;
	position: relative;
	display: flex;
	align-items: flex-start;
	gap: 12px;
}

.booking-component__payment-name {
	font-weight: 600;
	font-size: 1rem;
	color: #1D2939;
}

.booking-component__payment-note {
	font-size: .875rem;
	color: #1D2939;
	margin-bottom: 0;
	line-height: 20px;
}

.booking-component__payment-note > span {
	font-size: .875rem;
	color: #1D2939;
	margin-bottom: 0;
	line-height: 1.4;
}

.payment-method-icon-label-wrapper {
	display: flex;
	align-items: center;
	text-align: center;
	border: 1px solid #D0D5DD;
	border-radius: 6px;
	padding: 14px 8px;
	background: #F9FAFB;
}

.payment-method-icon-label-wrapper > span {
	font-weight: 500;
	font-size: .75rem;
	line-height: normal;
	color: #1D2939;
}

.booking-component__payment-method--active > .booking-component__payment-label > .payment-method-icon-label-wrapper {
	background: #1D2939;
}

.booking-component__payment-method--active > .booking-component__payment-label > .payment-method-icon-label-wrapper > span {
	color: #fff;
}

.booking-component__payment-icon {
	margin-right: 12px;
}

.booking-component__payment-info {
	display: none;
}


.booking-form .checkboxes {
	padding: 0 10px;
}

.booking-form label {
	font-size: .875rem;
	font-weight: 600;
	margin-bottom: 4px;
}

.booking-form--radio {
	display: inline-block;
	vertical-align: middle;
	margin-bottom: 0 !important;
}

.payment-two-installments-container, .full-payment-container {
	margin-top: 20px;
}

.booking-form-payment .payment-two-installments-container,
.booking-form-payment .full-payment-container {
	margin-top: 6px;
}

.booking-form-payment > .active > div {
	border-color: #334277;
	background: #f5fbff;
}

.booking-form-payment input[type="radio"]:checked + span {
	font-weight: 600;
}

.booking-form-payment label {
	cursor: pointer;
	position: relative;
	display: flex;
	align-items: center;
	margin-bottom: 6px;
}

.booking-form-payment-icon {
	margin-right: 10px;
	margin-left: 2px;
}

.booking-component__form-col--field-title > .booking-component__form-label--title:first-of-type {
	margin-right: 22px;
}

.booking-component__black-radio-label--title {
	margin-bottom: 0!important;
	color: #1D2939;
	line-height: 1.2;
	cursor: pointer;
}

.booking-form-payment-name {
	font-weight: 600;
	font-size: 1.125rem;
}

.booking-component__block--first-step {
	margin-top: 48px;
}

.booking-form-payment-bank,
.booking-form-payment-credit-card,
.booking-form-payment-cc {
	border: 1px solid #dbdbdb;
	padding: 15px;
	margin-top: 10px;
	cursor: pointer;
	border-radius: 3px;
}

.booking-form-payment .booking-form-payment-description {
	display: inline-block;
	position: absolute;
	right: 10px;
	font-size: .875rem;
	color: #585858;
	margin-bottom: 0;
	line-height: 1.4;
	max-width: 50%;
}

.booking-form-payment .payment-two-installments-container > div:last-of-type,
.booking-form-payment .full-payment-container > div:last-of-type {
	margin-bottom: 0;
}

.booking-form-payment .payment-two-installments-container > div > label,
.booking-form-payment .full-payment-container > div > label {
	margin: 0;
	cursor: pointer;
	padding: 8px 0;
}

.booking-form-payment .payment-two-installments-container > div > label > span,
.booking-form-payment .full-payment-container > div > label > span {
	font-size: .875rem;
}

.booking-payment__options {
	display: none;
}

.booking-component__payment-method--active .booking-payment__options {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-template-rows: repeat(1, 1fr);
	gap: 12px;
}

.payment-option-block-child {
	padding: 20px;
	border-radius: 8px;
	border: 1px solid #D0D5DD;
	background:#FCFCFD;
}

.payment-option-block-child--first {
	grid-area: 1 / 1 / 2 / 2;
}

.payment-option-block-child--second {
	grid-area: 1 / 2 / 2 / 3;
}

.booking-form-radio--black {
	accent-color: #000;
}

.payment-two-installment-option {
	padding: 12px;
	border-radius: 8px;
	border: 1px solid #D0D5DD;
	background: #FFF;
	cursor: pointer;
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}

.payment-option-block-child > .payment-two-installment-option:last-of-type,
.full-payment-container > .full-payment-container:last-of-type {
	margin-bottom: 0;
}

.booking-payment__label,
.checkboxes .booking-payment__label {
	cursor: pointer;
	margin-bottom: 0;
	margin-right: 0;
}

.booking-component__options {
	display: flex;
	margin-top: 24px;
}

.booking-payment__option {
	cursor: pointer;
	border: 1px solid #2F373F;
	border-radius: 6px;
	padding: 10px 20px;
	font-size: 1.125rem;
	font-weight: 600;
	position: relative;
}

.booking-payment__label {
	font-size: 1rem;
	font-weight: 600;
}

.booking-component__block-insurance-list-icon {
	min-width: 20px;
	min-height: 20px;
	margin-top: 2px;
}

.booking-payment__option:hover,
.booking-payment__option--active {
	background: #F9FAFB;
}

.insurance-label  {
	margin-bottom: 0;
}

.insurance-input {
	margin-bottom: 0;
}

.booking-payment__option--active {
	pointer-events: none;
}

.booking-payment__option:first-of-type {
	margin-right: 20px;
}

.booking-payment__options-label {
	display: flex;
	background: #fff;
	margin: 0;
	border: 1px solid #dbdbdb;
	border-radius: 6px;
	padding: 10px;
	cursor: pointer;
	align-items: center;
}

.booking-payment__options-label:last-of-type {
	margin-top: 8px;
}

.booking-payment__options-name {
	font-size: .875rem;
}

.booking-component__pricing-list {
	margin-top: 20px;
	padding-top: 20px;
	color: #252525;
	border-top: 1px solid #D0D5DD;
}

.booking-component__pricing-list--border-bottom {
	border-bottom: 1px solid #D0D5DD;
	border-top: none;
}

.installment-right-side {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}

.checkbox-as-box {
	border: 1px solid #D0D5DD;
	border-radius: 8px;
	padding: 8px;
	display: inline-flex;
	flex-direction: column;
	min-width: calc(25% - 9px);
	cursor: pointer;
	user-select: none;
	backface-visibility: hidden;
	transform: translateZ(0);
	-webkit-font-smoothing: subpixel-antialiased;
	transition: all .1s ease-in-out;
}

.checkbox-as-box:active,
.booking-component-black-radio:active {
	transform: scale(.99);
}

.disabled-checkboxes,
.disabled-checkboxes label {
	cursor: default;
}

.checkbox-as-box--active.disabled-checkboxes:active {
	transform: scale(1.011);
}

.concierge-services-toggle {
	background-color: transparent;
	border: 1px solid #D0D5DD;
	color: #1D2939;
	font-size: 1rem;
	border-radius: 8px;
	padding: 12px;
	line-height: 120%;
	margin-bottom: 32px;
	margin-top: -20px;
	display: none;
	align-items: center;
	justify-content: space-between;
	gap: 8px;
}

.concierge-services-toggle-wrapper {
	display: flex;
	justify-content: flex-end;
}

.booking-component__show-all-concierge-icon {
	fill: green;
}


.ws-pay-logo-booking-payment {
	margin-top: -5px;
}

.booking-component__payment-method--first {
	margin-right: 12px;
}

.payment-due-bold-title {
	color:#1D2939;
	font-size: 1rem;
	font-weight: 500;
	line-height: 1.4;
}

.payment-due-bold-subtitle {
	color: #1D2939;
	font-size: .875rem;
	font-weight: 400;
	line-height: 1.4;
	margin-top: 5px;
}

.mb-20 {
	margin-bottom: 20px;
}

.checkbox-as-box--active,
.booking-component-black-radio--active {
	background: #F9FAFB;
	transform: scale(1.011) translateZ(0);
	box-shadow: 0px 8px 22px -17px rgb(121 121 121 / 50%);
}

.checkbox-bottom-part {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-left: 30px;
}

.textarea-wrapper {
	margin-top: 16px;
}

.textarea-wrapper > textarea {
	margin: 0;
}

.checkbox-bottom-label {
	font-size: .875rem;
	color: #667085;
}

label.booking-component__action-label {
	color: #101828;
	margin-right: 0;
	font-size: .875rem;
	line-height: 1.6;
}

.checkboxes-label-bold {
	font-weight: 600;
	font-size: 1.31rem;
	color: #101828!important;
	line-height: 0;
}

.input-persons-placeholder--main-search--booking {
	top: 13px!important;
	position: static;
	font-size: .875rem;
	color: #1D2939;
	height: 48px;
	margin: 0;
	padding-right: 50px;
	border-radius: 8px;
	border: 1px solid  #D0D5DD;
	box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
	padding: 13px 12px;
}

.booking-component-input-guests {
	display: none!important;
}

.booking-component__dropdown-guests-icon {
	margin-bottom: -4px;
	margin-right: 8px;
}

.booking-component__action-dropdown-icon {
	margin-left: 32px;
	margin-bottom: 4px;
}

.booking-component__concierge-icon {
	filter: invert(31%) sepia(36%) saturate(300%) hue-rotate(175deg) brightness(94%) contrast(93%);
	-webkit-filter: invert(31%) sepia(36%) saturate(300%) hue-rotate(175deg) brightness(94%) contrast(93%);
}

.panel-dropdown--input--booking.active > .input-persons-placeholder--main-search--booking > .booking-component__action-dropdown-icon {
	transform: rotate(180deg);
}

.panel-dropdown--input--booking {
	margin: 0!important;
	margin-top: 20px!important;
	display: block;
	width: fit-content;
}

.checkbox-top-part {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.panel-dropdown--input--booking > .panel-dropdown-content--booking {
	top: 54px;
}

label.booking-component__action-label--normal-height {
	line-height: 1.4;
}

.checkbox-as-box > .checkbox-as-box__label {
	font-size: 1rem;
	padding-top: 2px;
}

.booking-component__action-note {
	margin: 16px 0;
	color: #1D2939;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	font-size: .875rem;
}

.booking-component__action-note-icon {
	margin-right: 10px;
}

button.booking-component__submit {
	padding: 7px 20px
}

.additional-surcharge-for-person-in-guest-list-block {
	margin-top: 16px;
}

.booking-component__action-note--additional-surcharge-for-person {
	justify-content: flex-start;
	margin-bottom: 0;
	font-size: 1rem;
}

.booking-component__sidebar--sticky {
	position: sticky;
	top: 10px;
	left: 0;
	right: 0;
}

.booking-component__sidebar-section {
	border-radius: 8px;
	border: 1px solid #D0D5DD;
	padding: 16px;
	position: relative;
	margin-bottom: 16px;
}

.booking-component__sidebar-section--help {
	margin-top: 20px;
}

.booking-component__sidebar-help-image {
	margin-right: 15px;
}

.booking-page-step__next-btn--wrapper {
	display: flex;
	justify-content: flex-end;
}

.booking-component__side-title {
	margin: 0;
	color: #252525;
	font-size: 1.25rem;
	margin-bottom: 15px;
	font-family: var(--font-primary);
}

.booking-component__content-help {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.booking-component__sidebar-help-text {
	color: #1D2939;
	margin-bottom: 0;
	font-size: 1rem;
	font-weight: 500;
	line-height: 1.4;
}

.booking-component__sidebar-help-number {
	color: #1D2939;
	display: block;
	font-size: 1rem;
	cursor: pointer;
	border: 1px solid #D0D5DD;
	padding: 4px 20px;
	margin-top: 8px;
	border-radius: 6px;
	line-height: normal;
}

.booking-component__sidebar-title {
	color: #252525;
	font-size: 1.44rem;
	font-weight: 600;
	margin-bottom: 10px;
	margin-top: 20px;
}

.booking-component__sidebar-gallery {
	height: 190px;
	overflow: hidden;
	position: relative;
	margin: -16px;
	margin-bottom: 16px;
}

.booking-component-deposit-info {
	color: #1D2939;
	font-size: .75rem;
	font-weight: 400;
	margin: 0;
}

.booking-component-bottom-bar__details {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-top: 5px;
}

.two-installment-badge-sticky-bottom-bar {
	border-radius: 6px;
	border: 1px solid #D0D5DD;
	background: #F5F5F5;
	text-transform: uppercase;
	font-size: 8px;
	font-weight: 600;
	letter-spacing: .08px;
	margin: 0;
	padding: 4px;
	line-height: 1;
	color: #1D2939;
	padding-bottom: 2px;
}

.booking-component-insurance-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.booking-component__gallery-picture-source {
	border-radius: 3px;
	object-fit: cover;
	height: 190px;
	width: 100%;
	background: #f7f7f7;
}

.booking-component__steps-mobile {
	display: none;
}

.booking-component__sidebar-object-title {
	color: #252525;
	font-size: 1.25rem;
	font-weight: 500;
	margin-bottom: 0;
}

.sidebar-modal-close-btn-wrapper {
	display: none;
}

.booking-component__sidebar-object-features {
	font-size: .875rem;
	line-height: 1.4;
	margin-bottom: 0;
	padding-left: 0;
	list-style: none;
	display: flex;
	color: #333;
	flex-wrap: wrap;
}

.booking-component__sidebar-object-feature {
	display: flex;
	align-items: center;
	line-height: 1.6;
}

.booking-component__sidebar-object-dot {
	width: 3px;
	height: 3px;
	margin-left: 5px;
	margin-right: 5px;
	background: #666;
	border-radius: 50%;
	display: inline-flex;
}

.booking-component__sidebar-details {
	color: #252525;
	font-size: 1.125rem;
	line-height: 1.6;
}

.booking-component__sidebar-arrival,
.booking-component__sidebar-departure {
	font-weight: 500;
	font-size: .938rem;
	padding: 2px 6px;
	border: 1px solid #D0D5DD;
	border-radius: 6px;
	background: #F9FAFB;
}

.booking-component__sidebar-dates-middle {
	display: flex;
	width: 100%;
	margin-top: 22px;
	padding: 6px;
}

.sidebar-guests-dot {
	width: 4px;
	height: 4px;
	background: #98A2B3;
	border-radius: 50%;
}

.booking-component__sidebar-arrival-departure-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.booking-component__sidebar-guests-icon {
	margin-right: 4px;
}

.booking-component__sidebar-guests,
.booking-component__sidebar-guests-item {
	display: flex;
	align-items: center;
	gap: 8px;
	color: #1D2939;
	font-size: .875rem;
}

.booking-component__sidebar-installments {
	margin-top: 20px;
	background: #F9FAFB;
	padding: 20px;
	border: 1px solid #EAECF0;
	border-radius: 8px;
}

.booking-component__sidebar-prices--less-margin {
	margin-top: 12px;
}

.booking-component__sidebar-ornament {
	display: flex;
	flex-grow: 1;
	align-items: center;
}

.booking-component__outline-circle {
	min-width: 10px;
	height: 10px;
	border-radius: 50%;
	border: 1px solid #475467;
}

.booking-component__sidebar-dates {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-top: 10px;
}

.booking-component__dash-line {
	width: 100%;
	height: 1px;
	border-bottom: 1px dashed #475467;
	margin: 0 4px;
}

.booking-component__dash-line-minimal {
	border-bottom: 1px dashed #D0D5DD;
	height: 1px;
	display: flex;
	flex-grow: 1;
	margin: 0 4px 6px;
}

.booking-component__sidebar-dates-label {
	font-size: .75rem;
	color: #1D2939;
	text-transform: uppercase;
}

.booking-component__sidebar-nights {
	border: 1px solid #D0D5DD;
	color: #1D2939;
	font-size: .75rem;
	padding: 6px;
	border-radius: 6px;
	text-transform: uppercase;
	font-weight: 500;
}

.booking-component__sidebar-prices-text {
	font-weight: 500;
	font-size: 1rem;
	color: #252525;
	margin: 0;
	line-height: 1.6;
}

.d-none {
	display: none;
}

.booking-component__sidebar-prices-text--smaller {
	line-height: 1.4;
	font-size: 1.125rem;
	font-weight: 400;
}

.booking-component__sidebar-prices-text--bottom-bar {
	display: flex;
	align-items: center;
}

.booking-component__sidebar-prices-additional {
	display: block;
	color: #444;
	font-size: .875rem;
	font-weight: 400;
}

.booking-component__price-discount {
	color: #8a8a8a;
	font-size: .875rem;
}

.payment-two-installments-title {
	color: #1D2939;
	font-size: 1.375rem;
	font-weight: 600;
	margin-bottom: 0;
	line-height: 26px;
}

.two-installments-date {
	display: inline-block;
	color: #1D2939;
	font-size: 1.125rem;
}

.booking-component__sidebar-prices-additional__base_price,
.booking-component__sidebar-prices-additional__cancellation_insurance,
.booking-component__sidebar-prices-additional__property_damage_insurance {
	display: none;
	justify-content: space-between;
	align-items: flex-end;
	margin-bottom: 4px;
}

.booking-component__sidebar-prices-additional__base_price--bottom-bar {
	margin-bottom: 0;
}

.booking-component__sidebar-prices-additional__base_price--sticky-show {
	display: block;
	margin-right: 8px;
}

.booking-component__steps {
	display: flex;
	flex-direction: row;
	margin: 40px 0 60px;
	align-items: center;
	justify-content: space-between;
	text-align: center;
	gap: 16px;
	padding: 0;
}

.booking-component__step {
	position: relative;
	list-style: none;
	width: 100%;
	text-align: left;
}

.booking-component__step--first:before {
	content: none;
}

.booking-component__step--active .booking-component__step-image {
	background: #2E90FA;
}

.booking-component__step-image {
    width: 100%;
    height: 7px;
    border-radius: 20px;
    background-color: #d0d5dd;
}

.booking-compoment__step-content {
	margin-top: 16px;
	color: #98A2B3;
	font-size: .938rem;
	text-align: center;
}

.booking-component__step--active .booking-compoment__step-content {
	color: #1D2939;
}

.booking-component__block--payment-methods {
	display: flex;
	width: 100%;
	margin-bottom: 32px;
}

.booking-component__block--cancellation-insurance,
.booking-component__block--travel-insurance {
	display: flex;
	padding: 0;
	overflow: hidden;
	margin-top: 32px;
}

.cancellation-travel-insurance-image {
	width: 280px;
	min-width: 280px;
	height: 100%;
	object-fit: cover;
}

.insurance-right-side {
	padding: 16px 20px;
	width: 100%;
}

.booking-component__block-title--insurance {
	color: #1D2939;
}

.booking-component__step--arrow {
	width: 100%;
}

.booking-component__sidebar-prices-additional__save-price {
	display: flex;
	justify-content: flex-end;
	font-size: .938rem;
	color: #357b27;
}

.booking-page-step {
	display: none;
}

.booking-payment__options > span:not(.booking-component__error-block-static) {
	display: none;
}

.booking-component__page-step--active {
	display: block;
}

.booking-component__price-discount--mr {
	margin-right: 5px;
}

.booking-component__sidebar-prices-total {
	display: flex;
	justify-content: space-between;
}

.booking-component__sidebar-prices-total--border-top {
	border-top: 1px solid #D0D5DD;
	padding-top: 8px;
	margin-top: 12px;
}

.booking-component__price-discount del {
    color: red;
}

.booking-component__price-discount > del > span {
    color: #8a8a8a;
	font-size: .75rem;
}

.booking-component__sidebar-price {
	font-weight: 600;
	font-size: 1.375rem;
	color: #333;
	margin: 0;
	line-height: 1.2;
	display: flex;
	align-items: flex-end;
}

.booking-component__sidebar-price--smaller {
	font-size: 1.125rem;
	line-height: 1.2;
	font-weight: 400;
}

.booking-component__error {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 9997;
	background: rgba(255,255,255,.7);
}

.booking-component__error-content {
	position: absolute;
	left: 50%;
	top: 30%;
	transform: translate(-50%, -30%);
	background: #fff;
	box-shadow: 0 1px 15px rgb(0, 0, 0, .18);
	border-radius: 5px;
	text-align: center;
	width: 450px;
}

.booking-component__error-image {
	text-align: center;
	padding: 25px 0;
}

.booking-component__error-title {
	font-size: 1.25rem;
	font-weight: 600;
	line-height: 23px;
	margin-bottom: 0;
	color: #252525;
}

.booking-component__error-icon-wrapper {
	background: #fafafa;
	padding: 20px;
	border-radius: 50%;
	display: inline-block;
	vertical-align: middle;
}

.booking-component__error-subtitle {
	font-size: 1rem;
	font-weight: 400;
	color: #444;
	margin: 10px 30px 0 30px;
	line-height: 21px;
}

.booking-component__error-actions {
	margin: 15px 0;
}

.booking-component__loader,
.booking-component__loader--small {
	background: rgba(255, 255, 255, .8);
	height: 100%;
	left: 0;
	margin: auto;
	position: absolute;
	right: 0;
	top: 0;
	width: 100%;
	z-index: 101;
	border-radius: 5px;
}

.booking-component__loader--without-spinner {
	position: fixed;
	animation: dull .3s forwards;
}

@keyframes dull {
	0% {
		opacity: .2;
	}
	50% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}



.booking-component__sidebar-details--sticky {
	display: none;
	flex-direction: row;
	justify-content: space-between;
	position: fixed;
	bottom: 0;
	z-index: 2;
	width: 100vw;
	background: #fff;
	left: 0;
	right: 0;
	padding: 12px 20px;
	border-top: 1px solid #DADADA;
}

.booking-component__sidebar-details--sticky-content {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: space-between;
}

.sidebar-sticky-cta-wrapper {
	display: flex;
	align-items: center;
}

.button-sticky-next-step {
	font-size: 1rem!important;
	padding-bottom: 10px!important;
	border-radius: 6px!important;
	max-height: 41px;
}

.submit-booking-btn {
	padding-top: 9px!important;
}

.button-show-sidebar {
	padding: 0!important;
	line-height: 1.2!important;
	font-size: .875rem!important;
	color: #1D2939!important;
	border: none!important;
	text-decoration: underline;
}

.sidebar-details-dates-sticky__circle {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: #999;
}

.booking-component__sidebar-price--bottom-bar {
	color: #297B29;
}

.d-none {
	display: none!important;
}

.sidebar-details-dates-sticky {
	display: flex;
	font-size: .875rem;
	align-items: center;
	gap: 6px;
}

.booking-component__sidebar-prices-additional__base_price--sticky {
	justify-content: flex-end;
}

.booking-component__sidebar-prices-additional__base_price--sticky  > .booking-component__sidebar-prices-amount > .booking-component__price-discount > del > span {
	font-size: .875rem;
}

.booking-component__sidebar-prices-additional__base_price--sticky > .booking-component__dash-line-minimal {
	display: none;
}

.insurance-left-side {
	position: relative;
}

.booking-component__block-insurance-logo {
	position: absolute;
	right: 20px;
	bottom: 20px;
}

.sidebar-details-dates-sticky__nights {
	display: flex;
	align-items: center;
	gap: 6px;
	font-size: .875rem;
}

.booking-component__sidebar-price--sticky {
	justify-content: flex-end;
	font-size: 1.25rem;
}

.you-save--sticky {
	font-size: .875rem;
}

.sidebar-details-dates-sticky__departure::before {
	content: '→';
	margin-right: 6px;

}

.booking-component__sidebar-dates--sticky {
	flex-direction: column;
	align-items: flex-start;
	margin: 0;
}

.booking-component__sidebar-prices--total-price {
	position: relative;
}

.booking-component__loader:after,
.booking-component__loader--small:after {
	content: '';
	background: url('../../img/svg/spinner.svg') no-repeat center center;
	width: 100px;
	height: 53px;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 101;
}

.booking-component__loader--small:after {
	transform: scale(.5) translate(-100%, -100%);
}

.booking-component__loader--without-spinner:after {
	content: none;
}

.booking-component__review {
	position: absolute;
	top: 20px;
	left: 20px;
	z-index: 101;
}

.booking-component__content-customer-care {
	margin-top: 20px;
	display: flex;
	align-items: center;
	flex-direction: row;
	min-height: 50px;
}

.trustpilot-widget--booking-sidebar {
	display: none;
}

.booking-component__trustpilot {
	margin-top: 25px;
	margin-bottom: 25px;
}

@media (max-width: 1440px) {
	.booking-component__block--cancellation-insurance,
	.booking-component__block--travel-insurance {
		flex-direction: column;
	}

	.cancellation-travel-insurance-image {
		width: 100%;
		height: 190px;
	}

	.checkbox-as-box {
		min-width: calc(33% - 6px);
	}
}

@media (max-width: 1024px) {
	.panel-dropdown-content--booking {
		position: absolute!important;
		border: 1px solid #D0D5DD!important;
		top: 100px!important;
		opacity: 0!important;
	}

	.panel-dropdown--input--booking {
		float: unset!important;
	}

	.panel-dropdown--input--booking > .input-persons-placeholder--main-search--booking {
		display: block!important;
		left: 16px!important;
	}

	.booking-component__action-dropdown-icon {
		margin-left: 27px;
	}

	.panel-dropdown--input--booking.active > .panel-dropdown-content--booking {
		opacity: 1!important;
	}
}

@media (min-width: 991px) and (max-width: 1240px) {
	.booking-component__sidebar-prices-amount--flex {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}

	.checkbox-as-box {
		min-width: calc(50% - 6px);
	}

	.booking-component__sidebar-prices-additional--flex {
		align-items: flex-end;
	}

	.booking-component__sidebar-prices--property-damage-insurance-label {
		max-width: 180px;
	}
}

@media (max-width: 767px) {
	.booking-layout .header__content-icon {
		margin-right: 0;
	}

	.first-payment-error {
		grid-area: 2 / 1 / 3 / 3;
	}

	.booking-component__payment-method--active .booking-payment__options {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-template-rows: repeat(2);
		gap: 12px;
	}

	.payment-option-block-child--first {
		grid-area: 1 / 1 / 2 / 3;
	}

	.payment-option-block-child--second {
		grid-area: 3 / 1 / 3 / 3;
	}

	.booking-component__payment-method--active > .full-payment-container {
		display: flex;
		flex-direction: column;
		gap: 0;
	}

	.concierge-services-toggle {
		display: flex;
	}

	.booking-component__block--payment-methods {
		flex-direction: column;
		margin-bottom: 20px;
	}

	.booking-component__payment-list {
		display: none;
	}

	.booking-component__pricing-list {
		margin-top: 12px;
		padding-top: 12px;
	}

	.checkboxes--black li:before,
	.checkboxes--green li:before {
		margin-right: 8px;
	}

	.booking-component__block-subtitle--no-mobile {
		display: none;
	}

	.checkbox-as-box--concierge {
		animation: show .3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
	}

	.concierge-services-toggle--shut-down {
		animation: shut-down .4s ease forwards;
	}

	.checkbox-as-box--concierge:nth-child(6) {
		animation-delay: .1s;
	}
	.checkbox-as-box--concierge:nth-child(7) {
		animation-delay: .2s;
	}
	.checkbox-as-box--concierge:nth-child(8) {
		animation-delay: .3s;
	}
	.checkbox-as-box--concierge:nth-child(9) {
		animation-delay: .4s;
	}



	@-webkit-keyframes show {
		0% {
			opacity: 0;

		}
		100% {
			opacity: 1;
		}
	}
	@keyframes show {
		0% {
			opacity: 0;
		}
		100% {
			opacity: 1;
		}
	}



	@-webkit-keyframes shut-down {
		0% {
			opacity: 1;

		}
		99% {
			opacity: 0;
		}
		100% {
			display: none;
		}
	}
	@keyframes shut-down {
		0% {
			opacity: 1;

		}
		99% {
			opacity: 0;
		}
		100% {
			display: none;
		}
	}

	.booking-component__block-title {
		font-size: .938rem;
		font-weight: 600;
	}

	.booking-component__novasol-inputs {
		flex-direction: column;
	}

	.booking-layout .header__content-phone {
		padding: 6px 10px;
		border-radius: 15px;
		border: 1px solid #333;
	}

	.checkbox-as-box {
		min-width: 100%;
	}

	.booking-form-payment .booking-form-payment-description {
		display: none;
	}

	.booking-component__error-block {
		position: static;
		bottom: 0;
	}

	.booking-component__error-block-static {
		margin-top: -11px;
		margin-bottom: 15px;
	}

	.booking-component__block--error input {
		border-color: #e21111;
	}

	.booking-component__error-content {
		width: 90%;
	}

	.header__content-phone-text {
		display: none;
	}

	.booking-component {
		margin: 5px 0 40px 0;
	}

	.booking-component__section {
		margin-bottom: 20px;
		margin-left: -15px;
		padding-left: 15px;
		margin-right: -15px;
		padding-right: 15px;
	}

	.booking-component__block--first-step {
		margin-top: 0;
	}

	.booking-component__title {
		font-size: 1.125rem;
		margin-bottom: 32px;
		margin-top: 0;
		line-height: 1.4;
	}

	.booking-component__section-divider {
		display: none;
	}

	.booking-component__form-select,
	.booking-component__form-input[type=text] {
		padding: 5px 10px;
		height: 40px;
	}

	.booking-component__block--ghost {
		border: none;
		padding-bottom: 0;
	}

	.insurance-right-side {
		padding: 16px;
	}

	.booking-component__block {
		margin-top: 0;
		padding: 16px;
		margin-bottom: 20px;
	}

	.booking-component__block--no-pb {
		padding-bottom: 0;
	}

	.booking-component__block--cancellation-insurance,
	.booking-component__block--travel-insurance {
		padding: 0;
	}

	.booking-component__checkbox-list {
		margin-top: 12px;
	}

	.booking-component__section-title-divider {
		flex-direction: column;
		align-items: flex-start;
	}

	.booking-component__form-row {
		flex-direction: column;
	}

	.booking-component__form-row--row-mobile {
		flex-direction: row;
		flex-wrap: wrap;
	}

	.booking-component__form-row--row-mobile .booking-component__form-col:last-of-type {
		flex-basis: 100%;
	}

	.booking-component__form-col {
		margin-right: 0;
	}

	.booking-component__form-col--field-title {
		flex: 0 0 80px;
	}

	.booking-component__form-select--title {
		background: #ddd;
		border-right: 0;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}

	input.booking-component__form-input--first-name {
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
		border-left: 0;
	}

	.booking-component__payment-info {
		display: block;
	}

	.booking-component__payment-info-icon {
		margin-left: 6px;
	}

	.booking-payment__options-name {
		margin-left: 5px;
	}

	.booking-payment__options-label {
		display: flex;
		line-height: 18px;
		align-items: center;
		padding: 10px;
	}

	.booking-component__sidebar-content {
		border-bottom: 0;
		padding-bottom: 0;
	}

	.booking-component__sidebar-object-features {
		font-size: .938rem;
	}

	.booking-component__sidebar-dates {
		margin-top: 5px;
	}

	.booking-component__sidebar-title {
		display: none;
	}

	.booking-component__sidebar-details-title {
		font-size: 1.125rem;
		font-weight: 600;
		margin-bottom: 0;
		line-height: 1.2;
		margin-top: 10px;
	}

	.booking-component__sidebar-arrival,
	.booking-component__sidebar-departure,
	.booking-component__sidebar-guest {
		font-size: .875rem;
	}

	.booking-component__sidebar-prices--less-margin {
		margin-top: 5px;
	}

	.booking-component__action-note {
		line-height: 1.3;
	}

	.booking-component__common-item {
		margin-bottom: 10px;
	}

	button.booking-component__submit {
		width: 100%;
	}

	.booking-component__flex--selection {
		flex-direction: column-reverse;
		align-items: flex-start;
	}

	.booking-component__label-selection {
		display: flex;
		justify-content: flex-end;
		width: 100%;
		margin-bottom: 15px;
	}

	.insurance-list-item {
		margin-bottom: 13px;
	}

	.checkboxes-label-bold {
		font-size: 1rem;
	}

	.booking-component__block-insurance-list-icon {
		margin-top: 2px;
		min-width: 20px;
		min-height: 20px;
	}

	.booking-component-black-radio--insurance {
		width: 100%;
	}

	.booking-component__options {
		flex-direction: column;
		align-items: flex-start;
		margin-bottom: 0;
	}

	.booking-component__section-title {
		font-size: 1rem;
		margin-bottom: 24px;
		margin-top: 0;
	}

	.booking-policy-list-item {
		font-size: 1rem;
	}

	.booking-component__payment-method--first {
		margin-bottom: 12px;
	}

	.booking-payment__option {
		margin-top: 10px;
	}

	.booking-payment__option:first-of-type {
		margin-right: 0;
	}

	.booking-component__anchor-allianz {
		justify-content: center;
	}

	.booking-component__review {
		left: 6px;
		top: 6px;
	}
}

@media (max-width: 991px) {
	.booking-component__steps-mobile {
		display: block;
		margin: 16px 0 0;
		font-weight: 500;
		padding-bottom: 10px;
		font-size: 1rem;
		color: #333;
	}

	.checkboxes--black li,
	.checkboxes--green li {
		padding-bottom: 10px;
	}

	.mobile-d-none {
		display: none;
	}

	.booking-component__sidebar--as-modal {
		position: fixed;
		top: 10%;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1;
		background: #fff;
		padding: 0 16px 16px;
		display: block;
		transform: translateY(100%);
		overflow: scroll;
		border-radius: 8px;
	}

	.booking-component-modal-bottom-bar-wrapper--active { 
		position: fixed;
		width: 100%;
		height: 100%;
		background: #000;
		top: 0;
		left: 0;
		bottom: 0;
		/* animation duration should match the css animation time - 1ms in handleSidebarModal */
		animation: showModalBg .4s cubic-bezier(0.21, 0.62, 0.38, 0.92) forwards; 
	}
	
	.booking-component-modal-bottom-bar-wrapper--closing {
		/* animation duration should match the css animation time - 1ms in handleSidebarModal */
		animation: hideModalBg .4s cubic-bezier(0.41, 0.62, 0.38, 0.92) forwards;
	}

	.booking-component__sidebar--as-modal--active {
		transform: translateY(0);
		display: block;
		z-index: 3;
		/* animation duration should match the css animation time - 1ms in handleSidebarModal */
		animation: showFromBottom .4s cubic-bezier(0.21, 0.62, 0.38, 0.92) forwards; 
	}

	.booking-component__sidebar--as-modal--closing {
		z-index: 3;
		/* animation duration should match the css animation time - 1ms in handleSidebarModal */
		animation: closeSidebarModal .4s cubic-bezier(0.41, 0.62, 0.38, 0.92) forwards; 
	}

	@keyframes showModalBg {
		0% {
			opacity: 0;
		}
		100% {
			opacity: .7;
		}
	}

	@keyframes hideModalBg {
		0% {
			opacity: .7;
		}
		100% {
			opacity: 0;
		}
	}

	@keyframes showFromBottom {
		0% {
			transform: translateY(100%);
		}

		100% {
			transform: translateY(0);
		}
	}

	@keyframes closeSidebarModal {
		0% {
			transform: translateY(0);
		}

		100% {
			transform: translateY(100%);
		}
	}

	.sidebar-modal-close-btn-wrapper {
		display: flex;
		justify-content: flex-end;
		position: sticky;
		z-index: 200;
		right: 20px;
		top: 0px;
		background: #fff;
		padding: 8px 0;
		outline: 1px solid #fff;
	}

	.sidebar-modal-close-btn {
		background: #fff!important;
		border: 1px solid #000!important;
		border-radius: 50%!important;
		padding: 1px!important;
		width: 28px;
		height: 28px;
	}

	.booking-compoment__step-content {
		display: none;
	}

	.booking-page-step {
		margin-bottom: 150px;
	}

	.booking-component__steps {
		gap: 8px;
		margin: 0;
		margin-bottom: 32px;
	}

	.booking-component__sidebar-details--sticky {
		display: flex;
	}
}


@media (min-width: 768px) and (max-width: 991px) {
	.booking-component {
		margin: 5px 0 40px 0;
	}

	.booking-component__sidebar-content {
		border-bottom: 0;
		margin-bottom: 0;
		padding-bottom: 0;
	}

	.booking-component__gallery-picture-source {
		height: 250px;
	}

	.booking-component__sidebar-gallery {
		height: 250px;
	}
}

@media (max-width: 1439px) {
	.booking-component__anchor-allianz {
		position: static;
		margin-top: 0;
	}

	.booking-component__anchor-allianz--cancellation-insurance {
		margin-top: 24px;
	}
}

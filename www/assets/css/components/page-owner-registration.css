.page-owner-registration {
	margin-top: 20px;
	margin-bottom: 80px;
}

.page-owner-registration__section--welcome {
	padding: 60px 0;
	background: #fcfcfc;
	margin-top: 30px;
}

.page-owner-registration__section--about-us {
	margin: 100px 0;
}

.page-owner-registration__section--other-method {
	text-align: center;
	margin: 60px 0;
}

.page-owner-registration__section--company {
	text-align: center;
	margin: 50px 0;
}

.page-owner-registration__section--testimonials {
	margin: 80px 0 200px 0;
}

.page-owner-registration__section--form-area {
	background: #fcfcfc;
	padding: 80px 0;
}

.page-owner-registration__title {
	font-size: 3.583rem;
	font-weight: 600;
	margin: 15px 0 10px 0;
	line-height: 1.2;
}

.page-owner-registration__section-title {
	font-size: 2.986rem;
	font-weight: 600;
	margin: 5px 0 10px 0;
	line-height: 1.2;
	font-family: '<PERSON>', sans-serif;
}

.page-owner-registration__section-title--smaller {
	font-size: 2.074rem;
}

.page-owner-registration__section-title--testimonial {
	font-size: .875rem;
	line-height: 1.6;
	font-weight: 400;
	font-family: var(--font-primary);
	margin-bottom: 15px;
}

.page-owner-registration__section-subtitle {
	text-transform: uppercase;
	font-weight: 600;
	font-size: 1rem;
	letter-spacing: 1px;
}

.page-owner-registration__section-paragraph {
	margin: 20px 0;
}

.page-owner-registration__image--rounded {
	border-radius: 4px;
}

.page-owner-registration__image--about-us {
	object-fit: cover;

}

.page-owner-registration__trustpilot {
	margin: 30px 0;
}

.page-owner-registration__trustpilot-widget {
	background: #fcfcfc;
	padding: 15px;
	border-radius: 4px;
}

.page-owner-registration__section--usps {
	background: #fcfcfc;
	margin: 40px 0;
}

.page-owner-registration__section--usps .main-usps__content {
	margin-right: 0;
}

.page-owner-registration__section--usps .usp-content__subtitle {
	max-width: 50%;
}

.page-owner-registration__section--usps .usp-content__title {
	text-transform: inherit;
}

.page-owner-registration__section--usps .block-main-usps-container {
	height: auto;
	padding: 80px 15px 80px 15px;
}

.page-owner-registration__section--usps .usp-item {
	flex: 0 0 33.3%;
}

.page-owner-registration__subtitle {
	font-size: 1.44rem;
	margin-bottom: 20px;
	margin-top: 10px;
	font-family: var(--font-primary);
}

.page-owner-registration__subtitle--top-spacing {
	margin-top: 20px;
}

.page-owner-registration__title-text {
	font-size: 1.125rem;
	font-weight: 400;
	margin-bottom: 5px;
}

.page-owner-registration__company-details {
	display: flex;
	justify-content: center;
	background: #fcfcfc;
	border-radius: 4px;
	padding: 50px 0;
	margin-top: 30px;
}

.page-owner-registration__company-detail {
	width: 33.3%;
}

.page-owner-registration__company-detail-title {
	font-size: 3.583rem;
	font-weight: 600;
	margin-bottom: 5px;
	font-family: 'Butler', sans-serif;
}

.page-owner-registration__company-detail-subtitle {
	font-size: 1.25rem;
}

.page-owner-registration__form {
	background: #fff;
	border-radius: 12px;
	border: 1px solid #666;
	padding: 40px 30px;
	box-shadow: 0 3px 24px rgba(0, 0, 0, .15);
	width: 100%;
}

.page-owner-registration__form input.has-error {
	border-color: red;
}

.page-owner-registration__form .help-block.text-danger {
	color: red;
}

.page-owner-registration__form .alert.alert-danger {
	background-color: #fef7ea;
	border-radius: 4px;
	color: #333;
	display: block;
	padding: 4px 10px;
	border: 1px solid #fadeaf;
	margin-bottom: 15px;
	text-align: center;
}

.page-owner-registration__form-title {
	font-size: 2.074rem;
	margin-bottom: 20px;
}

.page-owner-registration__form--hidden {
	display: none;
}

.page-owner-registration__form-section--centered {
	text-align: center;
}

.page-owner-registration__section--testimonials .keen-next,
.page-owner-registration__section--testimonials .keen-prev {
	right: auto;
	left: 50%;
	bottom: -80px;
	top: auto;
	background: #fff;
}

.page-owner-registration__section--testimonials .keen-disabled:before {
	filter: invert(99%) sepia(2%) saturate(115%) hue-rotate(143deg) brightness(104%) contrast(87%);
}

.page-owner-registration__section--testimonials .keen-disabled {
	display: block;
	cursor: default;
}

.page-owner-registration__section--testimonials .keen-prev {
	left: calc(50% - 50px);
}

.page-owner-registration__section--testimonials .keen-slider__slide {
	min-width: 100%;
}

.page-owner-registration__testimonials {
	position: static;
}

.page-owner-registration__testimonial {
	display: flex;
}

.page-owner-registration__testimonial-image {
	flex: 0 0 40%;
}

.page-owner-registration__testimonial-content {
	flex: 0 0 60%;
	padding-left: 60px;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.page-owner-registration__testimonial-name {
	font-size: 1.125rem;
	font-weight: 600;
}

.page-owner-registration__action-block {
	width: 420px;
	display: flex;
	justify-content: center;
	margin: 0 auto;
}

.block-main-usps-container {
	margin: 0;
}

.block-main-usps .usp-content__title {
	line-height: 1.2;
}

a.button.page-owner-registration__additional-contact {
	margin-right: 15px;
	font-size: 1rem;
}

.page-owner-registration__additional-contact-icon {
	margin-right: 10px;
}

.page-owner-registration__additional-form-text {
	font-size: 1rem;
	line-height: 1.4;
}

.page-owner-registration__main-list {
	margin-top: 40px;
	list-style: none;
	padding-left: 0;
}

.page-owner-registration__main-list-item {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.page-owner-registration__main-list-icon {
	margin-right: 10px;
	flex: 0 0 auto;
}

.page-owner-registration__form-go-to-login,
.page-owner-registration__form-go-to-register {
	font-weight: 600;
	cursor: pointer;
	color: #333!important;
}

.page-owner-registration__form-go-to-login:hover,
.page-owner-registration__form-go-to-register:hover {
	text-decoration: underline;
}

button.page-owner-registration__form-submit {
	padding-left: 50px;
	padding-right: 50px;
	margin-bottom: 0;
	line-height: 1.4 !important;
	height: auto;
	width: 100%;
}

button.page-owner-registration__form-submit + span {
	display: none;
}

.page-owner-registration__section--back {
	border-top: 1px solid #eee;
	margin-top: 70px;
	padding-top: 70px;
}

.page-owner-registration__back-block {
	background: #fcfcfc;
	border: 1px solid #666;
	padding: 40px;
	text-align: center;
	box-shadow: 0 3px 24px rgba(0, 0, 0, .1);
	border-radius: 12px;
}

.form-column--login-languages > fieldset {
	display: flex;
	margin-top: -2px;
}

.form-column--login-languages .custom-control {
	display: flex;
}

.form-column--login-languages .custom-control input[type="radio"] {
	position: absolute;
	visibility: hidden;
	opacity: 0;
}

.form-column--login-languages .custom-control label {
	font-size: 0;
	display: block;
	border-radius: 5px;
	margin-right: 8px;
	margin-bottom: 0;
	cursor: pointer;
	filter: grayscale(1);
	width: 25px;
	height: 25px;
}

.form-column--login-languages .custom-control input[type="radio"]:checked + label,
.form-column--login-languages .custom-control label:hover {
	filter: none;
}

.form-column--login-languages .custom-control:nth-child(1) label {
	background: url('../../img/svg/lang-hr.svg');
}

.form-column--login-languages .custom-control:nth-child(2) label {
	background: url('../../img/svg/lang-de.svg');
}

.form-column--login-languages .custom-control:nth-child(3) label {
	background: url('../../img/svg/lang-it.svg');
}

.form-column--login-languages .custom-control:nth-child(4) label {
	background: url('../../img/svg/lang-en.svg');
}

@media (max-width: 1024px) {
	.page-owner-registration__section--usps .usp-content__subtitle {
		max-width: 100%;
	}

	.keen-prev.keen-disabled,
	.keen-next.keen-disabled {
		opacity: 1;
	}
}

@media (max-width: 767px) {
	.page-owner-registration__section--welcome {
		padding: 50px 0;
	}

	.page-owner-registration__section--about-us {
		margin: 60px 0;
	}

	.page-owner-registration__section--testimonials {
		margin: 60px 0 100px 0;
	}

	.page-owner-registration__image {
		max-height: 240px;
		object-fit: cover;
	}

	.page-owner-registration__section--usps .block-main-usps-container {
		padding: 40px 15px;
	}

	.page-owner-registration__section--usps.block-main-usps .main-usps__items {
		display: block;
		overflow-x: initial;
	}

	.page-owner-registration__section--usps.block-main-usps .usp-item {
		padding: 15px;
		margin: 0 0 20px 0;
		background: #fff;
		border-radius: 6px;
	}

	.page-owner-registration__testimonials {
		padding-bottom: 10px;
	}

	.page-owner-registration__section--testimonials .keen-slider__slide {
		width: auto;
		flex-direction: column;
		padding-right: 0;
		padding-left: 0;
	}

	.page-owner-registration__testimonial-image img {
		min-height: 200px;
		max-height: 300px;
	}

	.page-owner-registration__testimonial-content {
		padding-left: 0;
	}

	.page-owner-registration__section--testimonials .col-flex {
		display: block;
	}

	.page-owner-registration__company-detail-title {
		font-size: 2.986rem;
	}

	.page-owner-registration__company-details {
		flex-direction: column;
		align-items: center;
	}

	.page-owner-registration__title {
		font-size: 2.488rem;
		line-height: 1.2;
		margin-top: 0;
	}

	.page-owner-registration__main-list {
		margin-bottom: 30px;
	}

	.page-owner-registration__section-title,
	.block-main-usps .usp-content__title {
		font-size: 1.728rem;
	}

	.page-owner-registration__section-title--testimonial {
		font-size: 1.125rem;
		line-height: 1.5;
	}

	.page-owner-registration__testimonial-content {
		flex: 0 0 auto;
		margin-top: 20px;
	}

	.page-owner-registration__section--testimonials .keen-next,
	.page-owner-registration__section--testimonials .keen-prev {
		bottom: -70px;
	}

	.page-owner-registration__action-block {
		width: 100%;
	}

	.page-owner-registration__usps {
		margin: 30px 0 50px 0;
	}

	.page-owner-registration__form {
		padding: 20px;
	}

	.page-owner-registration__additional-form {
		margin-top: 20px;
	}

	a.button.page-owner-registration__additional-contact {
		margin-right: 0;
		margin-bottom: 15px;
	}
}

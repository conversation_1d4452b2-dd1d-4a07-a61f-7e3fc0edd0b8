.contact-us__head {
	margin: 80px 40px;
	text-align: center;
	color: #252525;
}

.contact-us__title {
	font-size: 2.986rem;
	line-height: 1.4;
	font-weight: 600;

}

.contact-us__subtitle {
	font-family: var(--font-primary);
	font-size: 1.12rem;
	line-height: 1.6;
	font-weight: 400;
	margin-top: 15px;
}

.contact-us__options {
	display: flex;
	margin-bottom: 80px;
}

.contact-us__option {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-direction: column;
	flex: 1 1 50%;
	padding: 0 35px;
	border-right: 1px solid #ccc;
	text-align: center;
}

.contact-us__option:last-of-type {
	border-right: none;
}

.contact-us__option-title {
	margin-bottom: 15px;
	font-size: 1.25rem;
	font-weight: 600;
}

.contact-us__icon {
	width: 36px;
	height: 36px;
	margin-bottom: 15px;
}

.contact-us__option-number {
	font-size: 1rem;
	font-weight: 600;
	margin-bottom: 10px;
}

.contact-us__option-number-phone {
	margin-right: 15px;
	color: #333;
	background: #efefef;
	padding: 4px 9px;
	font-size: .875rem;
	border-radius: 6px;
	display: inline-flex;
	align-items: center;
	font-weight: 500;
}

.contact-us__option-number-phone:hover {
	background: #f5f5f5;
}

.contact-us__option-number-title {
	display: inline-flex;
	margin: 0 5px;
}

.contact-us__option-anchor {
	margin-top: 15px;
	padding: 10px 25px;
}

.contact-us__option-anchor--callback {
	padding: 5px 15px;
	margin-top: 10px;
}

.contact-us__section-title {
	font-size: 1.728rem;
	line-height: 1.2;
	margin-bottom: 5px;
}

.contact-us__section-subtitle {
	font-family: var(--font-primary);
	font-size: 1.125rem;
	color: #252525;
	font-weight: 400;
}

.contact-us__form {
	background: #f9f9f9;
	padding-top: 60px;
	padding-bottom: 60px;
}

.form-container--contact-us {
	max-width: 840px;
	margin-top: 40px;
}

.form-container--contact-us .hubspot-form-inline {
	background: transparent;
}

.form-container--contact-us .hubspot-form-inline label {
	font-size: .875rem;
}

.form-container--contact-us .hubspot-form-inline input[type=text],
.form-container--contact-us .hubspot-form-inline input[type=date],
.form-container--contact-us .hubspot-form-inline input[type=email],
.form-container--contact-us .hubspot-form-inline input[type=number] {
	border-color: #ddd;
	border-radius: 5px;
	padding: 10px;
	height: 45px;
	margin-bottom: 20px;
}

.form-container--contact-us .hubspot-form-inline input[type=checkbox] + label {
	margin-bottom: 12px;
}

.form-container--contact-us .hubspot-form-inline .input-block--has-icon input[type=text] {
	padding-left: 40px;
}

.contact-us__informations {
	margin-top: 30px;
	margin-bottom: 30px;
}

.contact-us__information-blocks,
.contact-us__information-block {
	display: flex;
	width: 100%;
}

.contact-us__information-block {
	flex: 1;
	margin-right: 15px;
}

.contact-us__information-block-icon {
	width: 30px;
	height: 30px;
}

.contact-us__information-blocks {
	margin-top: 50px;
}

.contact-us__information-section {
	margin-left: 15px;
}

.contact-us__information-section-title {
	font-size: 1.125rem;
	margin-bottom: 8px;
}

.contact-us__information-section-content {
	font-size: .875rem;
	line-height: 1.6;
}

.form-area__inquiry {
	display: none;
}

.form-area__inquiry--visible {
	display: block;
}

.contact-us__social-anchor {
	display: inline-block;
	margin-right: 5px;
}

.contact-us__social-icon {
	width: 30px;
	height: 30px;
}

@media (max-width: 991px) {
	.contact-us__option-number-phone {
		margin-right: 0;
		margin-bottom: 15px;
	}
}

@media (max-width: 991px) and (min-width: 768px) {
	.contact-us__option {
		padding: 0 25px;
	}

	.contact-us__information-blocks {
		flex-wrap: wrap;
	}

	.contact-us__information-block {
		flex: 1 0 33.33%;
		margin-bottom: 25px;
	}
}

@media (max-width: 767px) {
	.contact-us__head {
		margin: 80px 20px;
	}

	.contact-us__options {
		flex-direction: column;
		margin-bottom: 20px;
	}

	.contact-us__option {
		border-right: none;
		margin-bottom: 30px;
		padding-bottom: 30px;
		border-bottom: 1px solid #ccc;
	}

	.contact-us__option:last-of-type {
		border-bottom: 0;
	}

	.contact-us__information-blocks {
		flex-direction: column;
	}

	.contact-us__information-block {
		margin-bottom: 25px;
	}
}


.booking-widget {
	z-index: 100;
}

.booking-widget .panel-dropdown .panel-dropdown-content {
	overflow: visible;
	padding: 20px;
	box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
}

.booking-widget .panel-dropdown {
	width: 100%;
}

.booking-widget .panel-dropdown.active .panel-dropdown-content:before {
	opacity: 1;
	visibility: visible;
}

.booking-widget .panel-dropdown a {
	border: none;
	cursor: pointer;
	box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
	font-size: 1rem;
	height: auto;
	padding: 10px 16px;
	line-height: 30px;
	margin: 0 0 15px 0;
	position: relative;
	background-color: #fff;
	text-align: left;
	color: #888;
	display: block;
	width: 100%;
	transition: color .3s;
}

.booking-widget .panel-dropdown a:hover {
	color: #252525;
}

.booking-widget .panel-dropdown a:after {
	font-size: 1.25rem;
	color: #c0c0c0;
	margin-left: 0;
	position: absolute;
	right: 20px;
}

.booking-widget .panel-dropdown--input-open {
	margin-bottom: 15px !important;
}

.booking-widget .qtyButtons {
	margin: 0 0 7px 0;
}

.booking-widget .panel-dropdown--input-open .qtyTotal {
	position: absolute;
	left: auto;
	right: 40px;
	top: 50%;
	transform: translateY(-50%);
}

.booking-widget .panel-dropdown--input-open > input {
	margin-bottom: 0 !important;
}

.booking-widget .panel-dropdown--input-open .panel-dropdown-content {
	width: 100%;
	top: 55px;
	left: 0;
}


.booking-widget .booking-item-name {
	line-height: 1.2;
	font-weight: 600;
	font-size: 1rem;
}

.booking-widget .booking-item-name span {
	display: block;
	font-size: .875rem;
	color: #666;
	font-weight: 500;
}

.booking-widget .booking-select-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 5px;
	font-size: .875rem;
	line-height: 20px;
}

.booking-widget .booking-select-item p {
	margin-bottom: 0;
	font-size: .875rem;
	line-height: 20px;
}

.booking-widget .booking-select-item .booking-select-item-fields {
	font-weight: 600;
	color: #000;
	display: flex;
}

.booking-widget .booking-selected-items-line {
	margin: 15px 0 10px 0;
	border-top: 2px solid #ddd;
}

.booking-widget .booking-final-price {
	align-items: flex-end;
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}
.booking-widget .booking-final-price > * {
	font-size: .875rem;
	line-height: 20px;
	font-weight: 600;
	color: #000;
	margin-bottom: 0;
}

.booking-widget .booking-final-price span {
	font-size: 1rem;
	font-weight: 600;
}

.booking-widget .booking-final-price span.not-available-message span {
	font-size: 1rem;
}


.booking-widget .booking-final-price span.price-discount {
	display: block;
	text-align: right;
}

.booking-widget .booking-final-price span.price-discount > del {
	color: red;
}

.booking-widget .booking-final-price span.price-discount > del > span {
	color: #8a8a8a;
	font-size: .875rem;
}

.booking-widget .booking-no-extra-costs {
	margin-top: -10px;
	color: #232d54;
	display: flex;
	justify-content: flex-end;
	margin-bottom: 20px;
}

.booking-widget .booking-no-extra-costs p {
	font-size: .875rem;
	margin: 0;
	line-height: 1.2;
}

.booking-widget .booking-no-extra-costs__icon--gift {
	vertical-align: middle;
	margin-right: 3px;
}

.booking-widget .booking-no-extra-costs__icon--gift:before {
	content: '';
	filter: invert(41%) sepia(73%) saturate(1849%) hue-rotate(335deg) brightness(94%) contrast(77%);
	display: inline-block;
	width: 12px;
	height: 12px;
	background-image: url("/assets/img/svg/gift.svg");
	background-size: 12px 12px;
	background-repeat: no-repeat;
	position: relative;
}

.booking-widget .checkboxes #confirm-book + label {
	font-size: .875rem;
	line-height: 1.2;
}

.booking-widget .checkboxes #confirm-book + label:before {
	top: 0;
}

.booking-widget .booking-book-process-form .checkboxes label {
	font-size: .875rem;
	line-height: 1.2;
	color: #868686;
}

.booking-widget .booking-book-secure p {
	font-size: .875rem;
	line-height: 1.2;
	margin: 0;
}

.booking-widget .booking-book-secure__icon--lock {
	float: left;
}

.booking-widget .booking-book-secure__icon--lock:before {
	content: '';
	filter: invert(53%) sepia(52%) saturate(3972%) hue-rotate(109deg) brightness(99%) contrast(80%);
	display: inline-block;
	margin-right: 8px;
	vertical-align: middle;
	width: 18px;
	height: 18px;
	background-image: url("/assets/img/svg/locked-padlock.svg");
	background-size: 18px 18px;
	background-repeat: no-repeat;
	position: relative;
}

.button.button--view-all-general-info {
	display: none;
}

.single-listing-map #map-container {
	height: 480px;
	margin-bottom: 0;
	z-index: 1;
	border-radius: 3px;
}

.listing-nav-wrapper {
	position: sticky;
	top: 0;
	z-index: 4;
}

.listing-nav-container {
	background-color: #fff;
	border-bottom: 1px solid #ddd;
}

.listing-nav {
	width: 100%;
	padding: 0;
	list-style: none;
	margin: 0;
}

.listing-ref {
	position: absolute;
	right: 15px;
	padding: 15px 0;
	top: 0;
	color: #252525;
}

.listing-ref--top-area,
.listing-ref--mobile {
	display: none;
}

.listing-nav li {
	display: inline-block;
	margin-right: 20px;
}

.listing-nav li a {
	display: inline-block;
	border-bottom: 3px solid transparent;
	transition: .3s;
	font-size: .938rem;
	color: #333;
	padding: 15px 12px 12px 12px;
}

.listing-nav-container.cloned .listing-nav li:first-child a.active,
.listing-nav-container.cloned .listing-nav li:first-child a:hover,
.listing-nav li a.active,
.listing-nav li a:hover {
	border-color: #444;
	color: #444;
}

.listing-main-content__text--secondary {
	height: 124px;
	overflow: hidden;
}

.listing-main-content__text > .heading-3 {
	margin-bottom: 10px;
	margin-top: 0;
	color: #252525;
}


.listing-main-content__text > p:nth-child(n+2) {
	display: none;
}

.listing-main-content__text--secondary > p:nth-child(n+2) {
	display: block;
}

.listing-content-container__left-area--second-variant .listing-main-content__text > *:nth-child(n + 3) {
	display: none;
}

.listing-main-content__show-more,
.listing-main-content__show-more-secondary {
	cursor: pointer;
	font-weight: 500;
	text-decoration: underline;
	display: inline-block;
}

.listing-content-container__left-area--second-variant .listing-main-content__show-more {
	cursor: pointer;
	display: inline-block;
}

.listing-desc-headline {
	font-family: var(--font-primary);
	font-size: 1.25rem;
	margin-top: 5px;
	margin-bottom: 15px;
	display: flex;
	align-items: center;
	color: #252525;
	font-weight: 500;
}

.listing-desc-headline--spacing-top {
	margin-top: 20px;
}

.listing-desc-headline--sidebar {
	display: none;
}

.listing-desc-headline--smaller {
	font-size: 1.125rem;
}

.listing-desc-headline--top-spacing {
	margin-top: 30px;
}

.listing-desc-headline--bottom-spacing {
	margin-bottom: 30px;
}

.listing-desc-icon {
	margin-right: 10px;
}

.listing-section,
.listing-features-outer-container {
	width: 100%;
	margin-top: 30px;
	padding-top: 30px;
	border-top: 1px solid #ddd;
}

.listing-features-outer-container--borderless {
	border-top: 0;
	margin: 0;
	padding: 0;
}

.listing-section--no-line {
	border-top: none;
}

.listing-section--less-spacing {
	margin-top: 20px;
	padding-top: 0
}

.listing-section--more-spacing {
	margin-top: 40px;
	padding-top: 30px;
}

.listing-section--main {
	padding-top: 0;
	margin-top: 0;
	border-top: none;
}

.listing-section--tablet {
	display: none;
}

.listing-section--desktop {
	display: block;
}

.listing-content-container,
.listing-dynamic-container {
	position: relative;
}

.listing-content-container {
	padding-top: 40px;
}

.listing-content-container__left-area {
	padding-right: 40px;
}

/* Listing features */
.listing-features-container {
	background: #fff;
	margin-top: 20px;
}

.listing-main-features {
	margin: 25px 0 20px 0;
}

.listing-main-content--second-variant .listing-main-features {
	margin: 10px 0 30px 0;
}

.listing-features__title {
	margin-top: 0;
	font-family: var(--font-primary);
	font-size: 1.125rem;
	color: #333;
	margin-bottom: 8px;
	font-weight: 500;
}

.listing-features__title--no-margin {
	margin-bottom: 0;
}

.listing-features__title--small {
	font-size: 1.125rem;
}

.listing-main-features__list {
	display: flex;
	flex-direction: row;
	list-style-type: none;
	margin: 0;
	padding: 0;
}

.listing-main-features__item {
	flex: 0 0 auto;
	text-align: center;
	font-size: 1.125rem;
	line-height: 1.4;
	margin-right: 45px;
}

.listing-main-features__item:last-of-type {
	margin-right: 0;
}

.listing-features__icon--block {
	display: block;
	width: 100%;
	margin-bottom: 5px;
}

.listing-main-features__icon:before {
	content: '';
	display: inline-block;
	width: 37px;
	height: 37px;
	background-size: 37px 37px;
	background-repeat: no-repeat;
}

.listing-features-section {
	width: 100%;
}

.listing-features-block {
	box-shadow: 1px 1px 14px rgba(0, 0, 0, .06);
	border-radius: 4px;
	padding: 20px;
	display: flex;
	flex-direction: column;
	flex: 0 0 calc(33.33% - 20px);
	margin-right: 20px;
	margin-bottom: 30px;
}

.listing-features-block--hidden {
	display: none;
}

.listing-features-block__title {
	font-size: 1.125rem;
	font-weight: 500;
	margin-bottom: 10px;
	width: 100%;
}

.listing-features-block__more-less {
	display: inline-block;
	margin-top: 20px;
	margin-bottom: 20px;
	font-weight: 500;
	cursor: pointer;
	border: 1px solid #000;
	padding: 8px 15px;
	border-radius: 4px;
	transition: all .2s ease-in-out;
}

.listing-features-block__more-less:hover {
	background: #f1f1f1;
}

.listing-features--shorten li:nth-child(n+4) {
	display: none
}

.listing-features--four-items li:nth-child(n+4) {
	display: block;
}

.listing-features--four-items li:nth-child(n+5) {
	display: none
}

.listing-features--block {
	columns: initial;
	flex: 1;
}

.listing-features--block:first-of-type {
	flex: 1.5;
}

.listing-features--smaller {
	font-size: 1rem;
}

.listing-features--price-included {
	columns: 1;
}

.listing-features--3-columns {
	columns: 3;
}

.listing-features.list > li {
	text-indent: -15px;
	padding-left: 15px;
	line-height: 1.8;
}

.listing-features.list > li.title:before {
	content: none;
}

.listing-features.list > li.title {
	font-weight: 600;
}

.listing-features.three-column {
	columns: 3;
}

.listing-features.one-column {
	columns: 1;
}

.listing-features-split {
	column-count: 2;
}

.listing-features-split__item,
.listing-features-split > div {
	margin-bottom: 20px;
	break-inside: avoid;
}

.listing-features-split > div > ul {
	columns: 1;
}

.listing-features-carousel {
	display: flex;
	flex-wrap: wrap;
	width: calc(100% + 20px);
}

.listing-features-carousel__item {
	box-shadow: 1px 1px 14px rgba(0, 0, 0, .06);
	border-radius: 4px;
	padding: 20px;
	display: flex;
	flex-direction: column;
	flex: 0 0 calc(33.33% - 20px);
	margin-right: 20px;
	margin-bottom: 30px;
}

.listing-features-carousel__icon {
	margin-bottom: 15px;
}

.listing-features__modal-content {
	overflow: visible;
	overflow-y: auto;
	height: calc(100% - 80px);
}

.listing-features__modal-content .listing-features-outer-container {
	padding-top: 0;
	border-top: 0;
	margin-top: 20px;
}

.listing-features__modal-content .listing-features-outer-container--borderless {
	margin-top: 0;
}

.listing-features__modal-content .listing-features-carousel {
	width: 100%;
	flex-direction: column;
}

.listing-features__modal-content .listing-features-carousel__item,
.listing-features__modal-content .listing-features-block {
	box-shadow: none;
	border-radius: 0;
	flex: 1;
	padding: 0;
	flex-direction: row;
	flex-wrap: wrap;
	margin-bottom: 0;
	margin-right: 0;
}

.listing-features__modal-content .listing-features--shorten li:nth-child(n+4),
.listing-features__modal-content .listing-features--four-items li:nth-child(n+5),
.listing-features__modal-content .listing-features__item--distances:nth-child(n+7) {
	display: block;
}

.listing-features__modal-content .listing-features-carousel__icon {
	margin-bottom: 0;
	margin-right: 10px;
}

.listing-features__modal-content .listing-features {
	flex-basis: 100%;
	margin-top: 10px;
	margin-bottom: 40px;
}

.listing-features__modal-content .listing-features-carousel__item:last-of-type .listing-features {
	margin-bottom: 0;
}

.listing-features__modal-content .listing-desc-headline {
	font-size: 1.44rem;
}

.listing-features__modal-content .listing-features-block__title,
.listing-features__modal-content .listing-features__title {
	margin-bottom: 0;
}

.listing-features__modal-content .listing-features > li {
	border-bottom: 1px solid #c9c9c9;
	padding: 15px 0;
}

.listing-features__modal-content .listing-features--distances > li {
	padding: 5px 0;
}

.listing-features__modal-content .listing-features__distances {
	margin-bottom: 10px;
}

.listing-features__modal-content .listing-features__subitem:before {
	content: ' · ';
	font-size: 28px;
	margin: 0 4px 0 0;
	color: #252525;
	height: 0;
	display: inline-flex;
	align-items: center;
	position: relative;
	top: 6px;
}

.listing-features__modal-content .listing-features__subitem:first-child:before {
	margin-right: 0;
}

.listing-features__modal-content .listing-features__subitems {
	display: block;
}

.listing-features__modal-content .listing-features__item--distances {
	min-width: 100%;
	width: 100%;
	margin-right: 0;
}

.listing-features li.not-available {
	text-decoration: line-through;
	color: #aaa;
}

.listing-features.simplified {
	columns: auto;
	border: 1px solid #f1f1f1;
	border-radius: 3px;
	overflow: hidden;
}

.listing-features.simplified li {
	padding: 6px 10px;
	background: #fff;
}

.listing-features.simplified li:nth-child(even) {
	background-color: #eee;
}

.listing-features.checkboxes li {
	color: #333;
	display: block;
	position: relative;
	margin: 0 0 6px 0;
	padding: 0 0 0 30px;
}

.listing-features.list > li:before,
.listing-features__item--as-list:before {
	content: '';
	display: inline-block;
	width: 7px;
	height: 7px;
	line-height: 16px;
	background-color: #cdd0dc;
	border-radius: 2px;
	vertical-align: middle;
	margin-right: 8px;
	position: relative;
	top: -1px;
}

.listing-features.list .listing-subfeatures {
	list-style: none;
}

.listing-features.list .listing-subfeatures li:before {
	content: ' - ';
}

.listing-features.list li span.nogo,
.listing-features.list li.nogo {
	text-decoration: line-through;
	color: #aaa;
}

.listing-features li:last-child {
	margin: 0;
}

.listing-features.currencysign li:before {
	display: inline-block;
	content: none;
	font-size: 1rem;
	color: #666;
	margin-right: 8px;
	font-weight: 600;
}

.listing-features__item {
	display: flex;
	align-items: center;
	line-height: 2.4;
	flex-wrap: wrap;
}

.listing-features__item--as-column {
	flex-direction: column;
	align-items: flex-start;
}

.listing-features--distances {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}

.listing-features__item--distances {
	width: calc(100% * (1/3) - 30px);
	margin-right: 45px;
}

.listing-features__item--wrapped {
	flex-wrap: wrap;
}

.listing-features__item--distances:nth-child(n+7) {
	display: none;
}

.listing-features__subitems--left-spacing {
	margin-left: 4px;
}

.listing-features__subitems--newline {
	flex-basis: 100%;
	margin-left: 36px;
	line-height: 1.6;
}

.listing-features__subitem:first-child:before {
	content: '';
	margin-left: 0;
}

.listing-features__subitem:before {
	content: ', ';
	margin-left: -4px;
}

.listing-features__icon {
	margin-right: 10px;
	min-width: 25px;
}

.listing-features-modal__list {
	list-style: none;
	margin-bottom: 0;
	padding-left: 0;
}

.listing-features-modal__item {
	padding: 15px;
	border-bottom: 1px solid #c9c9c9;
}

.listing-features-modal__title {
	display: flex;
	align-items: center;
	font-size: 1.44rem;
}

.listing-features-modal__icon {
	margin-right: 10px;
}

.listing-features__distances {
	width: 100%;
	margin-bottom: 20px;
}

.listing-features__distance-value {
	color: #666;
}

.listing-feature__distance {
	display: flex;
	justify-content: space-between;
	line-height: 1.4;
}

.listing-features__distance-name {
	line-height: 1.4;
}

.listing-features__distance-beach-type {
	display: block;
}

.listing-features__scrollto-action {
	color: #333;
	font-weight: 600;
	text-decoration: underline;
	line-height: 2.2;
	display: inline-block;
}

.main-features__strong {
	font-weight: 600;
}

#listing-season-overview {
	margin-top: 25px;
	margin-bottom: 25px;
}

.season-overview__container {
	margin-top: 20px;
	margin-bottom: 20px;
	display: none;
}

.seasons-overview__year-container {
	border-bottom: 1px solid #cdcdcd;
	background: #fbfbfb;
	padding: 10px;
	display: flex;
	align-items: center;
	padding-left: 6px;
}

.season-overview__year-label {
	margin-bottom: 0;
	padding: 0 10px;
}

.season-overview__table {
	width: 100%;
	margin-top: 0;
	font-size: .875rem;
}

.single-villa-bottom-sticky-bar__item {
	display: flex;
	align-items: center;
	flex-direction: column;
	font-size: 1rem;
	line-height: 22px;
	color: black;
	position: relative;
	padding-top: 10px;
	padding-bottom: 7px;
	border-top: 3px solid transparent;
}

.single-villa-bottom-sticky-bar__item--active {
	border-top: 3px solid #333;
}

.season-overview__years-selection {
	width: auto;
	height: 34px;
	line-height: 34px;
	margin-bottom: 0;
	padding: 0 10px;
}

.season-overview__data {
	border-bottom: 1px solid #cdcdcd;
	padding: 10px 15px;
}

.season-overview__row:nth-child(even) {
	background: #fdfdfd;
}

.season-overview__row:hover {
	background: #fffffb;
}

.season-overview__data--price {
	font-weight: 600;
}

.season-overview__data-discount {
	font-weight: normal;
	color: #666;
	text-decoration: line-through;
	display: inline-flex;
	margin-right: 4px;
}

.season-overview__disclaimer {
	margin: 10px 0;
	font-size: 1rem;
	padding: 0 10px;
	font-weight: 600;
}

.single-listing {
	padding-top: 30px;
}

.single-listing__overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, .2);
	width: 100%;
	height: 100%;
	z-index: 110;
	display: none;
}

.single-listing__top-container {
	position: relative;
}

.single-listing__gallery {
	display: flex;
	height: 560px;
	justify-content: center;
	position: relative;
}

.single-listing__gallery-item-anchor {
	display: flex;
	cursor: pointer;
}

.single-listing__gallery-image-bigger {
	flex: 0 0 60%;
	position: relative;
}

.single-listing__gallery-image-normal {
	margin-left: 8px;
	display: flex;
	flex-direction: column;
	width: calc(20% - 8px);
}

.single-listing__listing-top-content {
	margin-bottom: 25px;
	display: flex;
	justify-content: space-between;
}

.single-listing__listing-top-content-right {
	height: 120px;
	width: 120px;
	display: flex;
	align-self: flex-end;
	position: relative;
	top: 15px;
	right: 15px;
}

.single-listing__gallery-item {
	height: calc(552px / 2);
	width: 100%;
	object-fit: cover;
	margin-bottom: 8px;
}

.single-listing__gallery-image-bigger .single-listing__gallery-item {
	height: 560px;
	border-bottom-left-radius: 3px;
	border-top-left-radius: 3px;
}

.single-listing__gallery-image-normal:last-of-type .single-listing__gallery-item-anchor:first-of-type .single-listing__gallery-item {
	border-top-right-radius: 3px;
}

.single-listing__gallery-image-normal:last-of-type .single-listing__gallery-item-anchor:last-of-type .single-listing__gallery-item {
	border-bottom-right-radius: 3px;
}

.single-listing__listing-options {
	position: absolute;
	left: 25px;
	bottom: 15px;
}

.single-listing__listing-option-list {
	margin: 0;
	padding: 0;
	list-style: none;
	display: flex;
}

.single-listing__listing-option-item {
	margin-left: 10px;
}

.single-listing__listing-option-item i {
	display: block;
	text-align: center;
	margin-bottom: 3px;
	height: 32px;
}

.single-listing__listing-option-anchor {
	color: #252525;
	font-size: .875rem;
	background: #fff;
	padding: 4px 12px;
	display: flex;
	line-height: 1.4;
	text-align: center;
	border-radius: 4px;
	border: 1px solid #252525;
	align-items: center;
	position: relative;
	cursor: pointer;
}

.single-listing__listing-option-anchor--with-icon {
	padding-left: 38px;
}

.single-listing__listing-option-icon {
	margin-right: 5px;
	position: absolute;
	left: 12px;
}

.floorplan-gallery .lg-sub-html {
	bottom: 10px;
	width: 100%;
	left: auto;
	right: auto;
	margin: 0 auto;
}

.floorplan-legend,
.show-floorplan-legend {
	display: none;
}

.floorplan-legend-outer .floorplan-legend {
	display: none !important;
}

.floorplan-gallery .floorplan-legend {
	background: #f7f7f7;
	padding: 10px;
	margin: 0 auto;
	display: block;
	color: #252525;
}

.floorplan-gallery .floorplan-legend-close {
	display: none;
}

.floorplan-gallery .floorplan-legend .floorplan-legend-title {
	font-size: 1.125rem;
	font-weight: 600;
	margin-bottom: 15px;
	margin-top: 0;
}

.floorplan-gallery .floorplan-legend-list {
	padding: 0;
	margin: 0;
	list-style: none;
	display: flex;
	flex-wrap: wrap;
}

.floorplan-gallery .floorplan-legend-list li {
	margin: 8px 5px;
	width: 30%;
	display: inline-block;
	vertical-align: top;
}

.floorplan-gallery .floorplan-legend-list li p {
	font-size: 1rem;
	line-height: 1.4;
}

.listing-similar {
	padding: 20px 0 50px 0;
	margin-top: 40px;
	border-top: 1px solid #eee;
}

.single-listing__section-gallery {
	display: flex;
	flex-wrap: wrap;
}

.single-listing__section-gallery-item--with-image {
	margin-right: 4px;
	margin-bottom: 4px;
	display: block;
	max-width: calc(100% * (1 / 4) - 4px);
	width: calc(100% * (1 / 4) - 4px);
	position: relative;
	cursor: pointer;
}

.single-listing__section-gallery-item:nth-child(4n) {
	margin-right: 0;
}

.single-listing__section-gallery-source {
	object-fit: cover;
	width: 100%;
	height: 200px;
	border-radius: 3px;
}

.single-listing__section-gallery-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .6);
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.25rem;
	z-index: 1;
	border-radius: 3px;
}

.listing-main-stars-container,
.listing-main-stars-container-review {
	display: flex;
	cursor: pointer;
}

.listing-main-stars {
	display: flex;
	position: relative;
	height: 17px;
	margin-top: 25px;
}

.listing-main-stars__icon--info {
	display: flex;
	margin-left: 3px;
	position: relative;
	top: -4px;
}

.listing-main-stars-explanation {
	position: absolute;
	top: 30px;
	background: #fff;
	max-width: 410px;
	padding: 20px;
	font-size: .875rem;
	box-shadow: 0 2px 10px rgba(0, 0, 0, .2);
	cursor: auto;
	border-radius: 3px;
	display: none;
	z-index: 1000;
}

.listing-main-stars-explanation__title {
	font-size: 1rem;
	color: #252525;
	margin-top: 0;
	font-family: var(--font-primary);
}

.listing-main-stars-explanation__icon--close {
	cursor: pointer;
	position: absolute;
	top: 15px;
	right: 18px;
}

.listing-main-stars-explanation__icon--close:before {
	content: '';
	display: inline-block;
	width: 15px;
	height: 15px;
	background-image: url('/assets/img/svg/close.svg');
	background-size: 15px 15px;
	background-repeat: no-repeat;
}

.listing-main-stars-explanation .star-explanation {
	margin-bottom: 0;
	margin-top: 10px;
}

.star-explanation__icon--star {
	margin-left: -3px;
}

.star-explanation__icon--star:before {
	text-shadow: none;
	content: '';
	display: inline-block;
	width: 15px;
	height: 15px;
	background-image: url('/assets/img/svg/star.svg');
	background-size: 15px 15px;
	background-repeat: no-repeat;
}

.listing-main-stars-explanation .paragraph-explanation {
	display: block;
	line-height: 1.6;
}

.listing-main-stars-explanation p {
	font-size: .875rem;
	line-height: 1.6;
	margin-bottom: 5px;
}

.booking-process__inner {
	display: flex;
	flex-direction: row;
	align-items: flex-end;
	justify-content: space-between;
	width: 100%;
	position: relative;
	margin-bottom: 12px;
}

.booking-process__with-price .booking-process__inner {
	flex-direction: column;
	align-items: flex-start;
}

.booking-process__with-price .booking-process__included-in-price {
	margin-top: 12px;
}

.booking-process__inner--no-price {
	flex-direction: column;
}

.booking-process__inner:after {
	content: '';
	position: absolute;
	width: calc(100% + 40px);
	bottom: 0;
	left: -20px;
	right: -20px;
}

.booking-process__prices-wrapper {
	display: flex;
	align-items: center;
	gap: 8px;
}

.booking-process__prices-wrapper--no-gap {
	gap: 0;
}

.booking-process__your-price-label {
	font-size: .938rem;
	color: #252525;
}

.booking-process__save {
	color: #357B27;
	font-size: 1rem;
	line-height: 1;
	margin-top: 2px;
	font-weight: 500;
}

.booking-process__price--price-not-available {
	margin: 0;
	font-weight: 500;
	font-size: 1.25rem;
}

.booking-process__included-in-price {
	color: #252525;
	text-decoration: underline;
	font-size: .875rem;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	line-height: 1.4;
}

.booking-process__included-in-price--without-price {
	width: 100%;
	justify-content: flex-start;
}

.input-sm-height {
	height: 45px!important;
}

.booking-widget .input-mb-sm {
	margin-bottom: 8px!important;
}

.your-price__value {
	line-height: 1;
}

.booking-process__icon--information {
	margin-right: 5px;
}

.booking-process__price-display {
	display: flex;
}

.booking-process__price-display-item {
	display: inline-block;
	cursor: pointer;
	color: #252525;
	border: 1px solid #252525;
	padding: 1px 10px;
	border-radius: 6px;
	margin-left: 10px;
	font-size: .875rem;
}

.booking-process__price-display-item:hover,
.booking-process__price-display-item--active {
	color: #fff;
	background: #252525;
}

.booking-process__price-display-item:first-child {
	margin-left: 0;
}

.booking-block__safety-package {
	display: flex;
	position: relative;
	padding-left: 32px;
	margin: -5px 0 12px 0;
	flex-direction: column;
}

.booking-block__safety-package-icon {
	position: absolute;
	left: 0;
	top: 5px;
}

.booking-block__safety-package-title {
	color: #252525;
	font-size: 1rem;
	font-weight: 600;
	line-height: 1.2;
	margin-bottom: 0;
}

.booking-block__safety-package-content {
	font-size: .875rem;
	margin-bottom: 0;
	line-height: 1.2;
}

.booking-block__safety-package-show-more {
	font-size: .875rem;
	text-decoration: underline;
	display: inline-block;
}

.booking-block__safety-package-show-more:hover {
	color: #398dc6;
}

.listing-location__name {
	margin-top: 5px;
	text-transform: capitalize;
	color: #252525;
	background: #fff;
}

#singleListingMap {
	height: 480px;
}

#singleListingMap-container {
	height: auto;
	position: relative;
	display: block;
}

#tiptip_holder {
	display: none;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 99999;
}

#tiptip_holder.tip_top {
	padding-bottom: 5px;
	transform: translateX(12px)
}

#tiptip_holder.tip_bottom { padding-top: 5px; }
#tiptip_holder.tip_right { padding-left: 5px; }
#tiptip_holder.tip_left { padding-right: 5px; }

#tiptip_content {
	color: #fff;
	font-size: 1rem;
	line-height: 17px;
	padding: 6px 10px;
	background-color: #232d54;
	border-radius: 3px;
}

#tiptip_content {
	background-color: #d8c66c;
	border-radius: 0;
	color: #fff;
}

#tiptip_arrow, #tiptip_arrow_inner {
	position: absolute;
	border-color: transparent;
	border-style: solid;
	border-width: 6px;
	height: 0;
	width: 0;
}

#tiptip_holder.tip_top #tiptip_arrow { border-top-color: #000; }
#tiptip_holder.tip_bottom #tiptip_arrow { border-bottom-color: #000; }
#tiptip_holder.tip_right #tiptip_arrow { border-right-color: #000; }
#tiptip_holder.tip_left #tiptip_arrow { border-left-color: #000; }

#tiptip_holder.tip_top #tiptip_arrow_inner {
	margin-top: -7px;
	margin-left: -6px;
	border-top-color: #000;
}

#tiptip_holder.tip_bottom #tiptip_arrow_inner {
	margin-top: -5px;
	margin-left: -6px;
	border-bottom-color: #000;
}

#tiptip_holder.tip_right #tiptip_arrow_inner {
	margin-top: -6px;
	margin-left: -5px;
	border-right-color: #000;
}

#tiptip_holder.tip_left #tiptip_arrow_inner {
	margin-top: -6px;
	margin-left: -7px;
	border-left-color: #000;
}

#tiptip_holder.tip_top #tiptip_arrow {
	border-top-color: transparent;
}

#tiptip_holder.tip_top #tiptip_arrow_inner {
	border-top-color: transparent;
}

.booking-process__info-price {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 6px;
	width: 100%;
	margin-bottom: 10px;
}

.booking-process__with-price {
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	margin-bottom: 12px;
}

.booking-process__with-price > .booking-process__info-price {
	width: auto;
}

.price-discount-single-villa {
	color: #FF0000;
	text-decoration: line-through;

}

.booking-process__old-price {
	font-size: 1rem;
	color: #585858;
	vertical-align: middle;
}

@media screen and (min-width: 1024px) and (max-height: 800px) {
	.booking-widget--800h-z-index-1 {
		z-index: 1;
	}
	
	.static-on-800h {
		position: static;
	}

	.input-with-item__icon--800h-z-index-1 {
		z-index: 1;
	}
}

@media screen and (min-width: 1260px) and (max-width: 1440px) {
	.booking-process__prices-wrapper {
		display: flex;
		align-items: flex-start;
		flex-direction: column;
		gap: 0;
	}

	.price-available {
		font-size: 1.33rem;
	}

	.booking-process__info-price {
		flex-direction: column;
		align-items: flex-start;
	}

}

@media only screen and (min-width: 1240px) and (max-width: 1366px)  {
	.booking-widget .button--to-booking {
		padding: 8px;
	}

	.booking-widget a.button.border {
		padding: 6px 20px;
	}

	.booking-widget .panel-dropdown--input-open {
		margin-bottom: 10px !important;
	}

	.listing-nav li a {
		padding: 15px 3px;
	}

	.flatpickr-day {
		height: 40px !important;
		line-height: 40px !important;
	}
}

@media (max-width: 1239px) {
	.listing-desc-headline--sidebar {
		display: block;
	}

	.listing-ref {
		display: none;
	}

	.listing-ref--top-area {
		display: block;
		top: 25px;
	}

	.listing-features-carousel {
		width: 100%;
	}

}

@media (max-width: 1024px) {
	.listing-content-container__left-area {
		padding-right: 15px;
	}
}

@media (max-width: 768px) {
	.booking-process__info-price {
		flex-direction: column;
		align-items: flex-start;
	}

	.listing-main-stars {
		margin-top: 37px;
	}
}

@media (min-width: 768px) and (max-width: 1024px) {
	.listing-main-title {
		font-size: 2.074rem;
		margin-top: 10px;
	}
}

@media (min-width: 992px) {
	.listing-features__item--distances:nth-child(3n) {
		margin-right: 0;
	}
}

@media (max-width: 991px) {
	.lg-outer.floorplan-gallery .floorplan-legend {
		width: 100%;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		height: calc(100vh - 20px);
		overflow-y: auto;
	}

	.floorplan-gallery .floorplan-legend-list li {
		margin: 15px 0 0 0;
		flex: 0 0 33.3%;
		width: 100%;
	}

	.floorplan-gallery .floorplan-legend-close {
		position: absolute;
		right: 25px;
		top: 10px;
		cursor: pointer;
		display: block;
	}

	.floorplan-gallery .floorplan-legend-close:before {
		content: '';
		filter: invert(10%) sepia(0%) saturate(0%) hue-rotate(359deg) brightness(98%) contrast(89%);
		display: inline-block;
		width: 16px;
		height: 16px;
		background-image: url('/assets/img/svg/close.svg');
		background-size: 16px 16px;
		background-repeat: no-repeat;
	}

	.floorplan-gallery .floorplan-legend {
		display: none;
	}

	.floorplan-gallery .show-floorplan-legend {
		display: inline-block;
		background: #f6f6f6;
		border-radius: 3px;
		font-size: 1rem;
		cursor: pointer;
		font-weight: 600;
		padding: 6px 15px;
		color: #252525;
	}

	.floorplan-gallery .lg-sub-html.visible {
		padding: 0 10px;
		top: 10px;
		z-index: 9999;
		width: 100vw;
	}

	.single-listing__listing-top-content--has-feefo .listing-main-stars {
		margin-top: 35px;
	}

	.listing-main-stars-explanation {
		width: calc(100vw - 30px);
		min-width: 0;
		opacity: 1;
		position: absolute;
	}

	.listing-main-stars-explanation:after,
	.listing-main-stars-explanation:before {
		content: none;
	}
}

@media (min-width: 768px) and (max-width: 991px) {
	.single-listing__listing-top-content-right {
		margin-left: 15px;
	}

	.listing-features__item--distances {
		min-width: calc(100% * (1/2) - 30px)
	}

	.listing-features__item--distances:nth-child(2n) {
		margin-right: 0;
	}

	.single-listing__gallery {
		height: 430px;
	}

	.single-listing__gallery-image-bigger .single-listing__gallery-item {
		height: 430px;
	}

	.single-listing__gallery-item {
		height: calc(422px / 2);
	}

	:not(.date-rangepicker) + .flatpickr-calendar {
		width: 50%;
	}
}

@media (max-width: 767px) {
	.single-listing {
		padding-top: 20px;
	}

	.listing-features--3-columns {
		columns: 2;
	}

	.single-listing__listing-top-content {
		margin-bottom: 15px;
		position: static;
	}

	.booking-widget .panel-dropdown--input-open .panel-dropdown-content {
		max-width: 100%;
	}

	.price-calculate-note {
		font-size: 1rem;
	}

	.listing-ref {
		display: none;
		padding: 0;
		position: absolute;
		right: 15px;
		top: 3px;
		font-size: .875rem;
	}

	.listing-ref--top-area {
		display: block;
		top: 0;
	}

	.listing-ref--mobile {
		display: block;
		position: static;
		margin-bottom: 5px;
	}

	.reevoo-widget--single-object-top {
		position: absolute;
		right: 15px;
		top: 25px;
	}

	.listing-main-features__list {
		flex-wrap: wrap;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.listing-features__icon--block {
		margin-bottom: 0;
		width: 25px;
	}

	.listing-main-features__item {
		display: flex;
		align-items: center;
		margin-right: 0;
		line-height: 2;
	}

	.listing-content-container {
		padding-top: 8px;
	}

	.listing-features-split {
		column-count: auto;
	}

	.listing-features-carousel {
		flex-wrap: nowrap;
		overflow-x: auto;
		flex-flow: row;
		padding: 10px 0 0 10px;
		margin-left: -10px;
		width: calc(100% + 20px);
	}

	.listing-features-carousel__item:last-of-type {
		margin-right: 10px;
	}

	.listing-features-carousel__item,
	.listing-features-block {
		flex: 0 0 300px;
	}

	.button--all-features {
		width: 100%;
		text-align: center;
		margin-top: 10px;
		justify-content: center;
	}

	.button.button--view-all-general-info {
		display: block;
		margin-top: 20px;
	}

	.listing-features__item--hidden-mobile,
	.listing-features-list__item--hidden-mobile {
		display: none;
	}

	.listing-features__item--distances {
		min-width: 100%;
		width: 100%;
		margin-right: 0;
	}

	.listing-features__item--distances:nth-child(n+6) {
		display: none;
	}

	.listing-features__distances {
		margin-bottom: 10px;
	}

	.listing-features__modal-content .listing-features > li {
		display: block;
	}

	.listing-features__modal-content .listing-features-carousel {
		margin-left: 0;
		padding: 0;
	}

	.listing-features__subitem:first-child:before {
		margin-right: 0;
	}

	.listing-main-content__text > .heading-3 {
		margin-top: 5px;
	}

	.listing-desc-headline--trigger-mobile {
		margin-bottom: 0;
		position: relative;
	}

	.listing-desc-headline--trigger-mobile.opened {
		margin-bottom: 15px;
	}

	.listing-desc-headline:after {
		margin-left: auto;
		margin-right: auto;
	}

	.listing-section--no-line {
		border-top: 1px solid #ddd;
	}

	.listing-section--main {
		padding-top: 0;
		margin-top: 0;
	}

	#listing-season-overview {
		border-top: 0;
		padding-top: 0;
	}

	.button--show-season-overview {
		width: 100%;
	}

	table.season-overview__table td {
		text-align: left;
		border-bottom: 0;
	}

	.season-overview__data {
		padding: 0 10px;
	}

	table.season-overview__table td:first-child {
		padding-top: 10px;
	}

	table.season-overview__table td:last-child {
		border-bottom: 1px solid #cdcdcd;
		padding-bottom: 10px;
	}

	.single-listing__section-gallery-item--with-image {
		max-width: calc(100% * (1 / 2) - 2px);
		width: calc(100% * (1 / 2) - 2px);
	}

	.single-listing__section-gallery-item--with-image:nth-child(1) {
		width: 100%;
		max-width: 100%;
	}

	.single-listing__section-gallery-item--with-image:nth-child(1) .single-listing__section-gallery-source {
		height: 220px;
	}

	.single-listing__section-gallery-source {
		height: 140px;
	}

	.single-listing__listing-option-anchor {
		font-size: .875rem;
		margin-right: 10px;
	}

	.single-listing__listing-option-anchor--show-all {
		width: 100%;
		margin: 5px 0 15px 0;
		justify-content: center;
		padding-top: 12px;
		padding-bottom: 12px;
		font-size: .938rem;
	}

	.single-listing__section-gallery-item:nth-child(1),
	.single-listing__section-gallery-item:nth-child(3),
	.single-listing__section-gallery-item:nth-child(5) {
		margin-right: 0;
	}

	.single-listing__section-gallery-item:nth-child(4n) {
		margin-right: 4px;
	}

	.single-listing__gallery {
		height: auto;
	}

	.single-listing__gallery-image-bigger {
		flex: 0 0 100%;
	}

	.single-listing__gallery-item-anchor {
		margin: 0;
	}

	.single-listing__gallery-image-bigger .single-listing__gallery-item {
		height: 300px;
		border-radius: 3px;
	}

	.single-listing__gallery-item {
		margin-bottom: 0;
	}

	.single-listing__listing-option-item {
		margin-left: 0;
	}
}

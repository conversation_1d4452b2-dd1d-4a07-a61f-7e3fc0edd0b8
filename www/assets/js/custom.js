var $ = jQuery.noConflict(),
  wi = $(window).width(),
  defaultDatePicker,
  defaultDatePickerRange,
  inquiryDatePickerRange,
  trigger_dropdown = false,
  body = 'body',
  scrollDistance = 0,
  body_class_opened = 'modal-mobile-opened',
  body_class_general_opened = 'modal-general-opened',
  $window = $(window),
  searchResultsContent = '.fs-inner-container.content',
  modalActive = 'modal-active',
  panelFilter = '.panel-dropdown--filters',
  scrollLockExecuted = false;
  calendarLang = $language in flatpickr.l10ns ? flatpickr.l10ns[$language] : flatpickr.l10ns.en;

function isIOS() {
  return /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent) && !window.MSStream;
}

/**
 * Searchbar actions
 *
 * @returns {void}
 */
function searchBarActions () {
  let mainSearchInputOverlayed = 'main-search-input--overlayed';

  $('.main-search-input__mobile').on('click', function (e) {
    e.preventDefault();
    $(this).next('.main-search-input').addClass(mainSearchInputOverlayed);
  });

  $('.main-search-input__close-button').on('click', function (e) {
    e.preventDefault();
    $(this).parent().removeClass(mainSearchInputOverlayed);
  });

  $('.main-search-input__mobile-section--search').on('click', function (e) {
    e.preventDefault();
    $('.main-search-input--search-results').addClass(mainSearchInputOverlayed)
  });
}

/**
 * Action buttons for filtering
 *
 * @returns {void}
 */
function actionButtonsFilters () {
  let filteringModal = $('.filtering-modal');

  // Apply / Close buttons
  $('.panel-apply').on('click', function () {
    disableScrollLock();

    activeModalSearchResults(false);
    iconFilteredItems();

    $('.mfp-close').trigger('click');

    // call click event after popup is removed from the DOM (option 'removalDelay')
    setTimeout(function() {
      $('#filter_submit').click();
    }, 301)
  });

  $('.panel-clear').on('click', function (e) {
    e.preventDefault();

    filteringModal
      .find('input:checkbox')
      .removeAttr('checked');

    filteringModal
      .find('select')
      .prop('selectedIndex', 0);

    $('.option-tags').each(function() {
      $(this)
        .find('.custom-radio:first')
        .children()
        .click();
    });

    filteringModal.find('input:checkbox').prop('checked', false);
    filteringModal.find('select').prop('selectedIndex', 0);

    $('.option-tags').each(function () {
      $(this).find('.custom-radio:first').children().each(function () {
        $(this).prop('checked', true);
      });
    });

    $('.nouislider').each(function () {
      var valueMin = $(this).data('min') || 0,
          valueMax = $(this).data('max');

      $(this)[0].noUiSlider.set([valueMin, valueMax]);

      const name = $(this).data('name'); // e.g., "price"

      if (!name) return; // skip if no data-name

      const filterName = name + '_filter';

      // Find the input with the name ending in _filter
      const input = $(`[name="${filterName}"]`);
      input.removeAttr('value');
    });

    triggerFilterChange();
  });
}

function faqAccordion () {
  let accordionParentClass = '.faq-item',
    accordionQuestionClass = '.faq-item-question',
    accordionContentClass = '.faq-item-answer';

  $(accordionParentClass).each(function(index) {
    let _this = $(this);

    if (index !== 0) {
      _this.addClass('close');
      _this.find(accordionContentClass).hide();
    }

  });

  $(accordionQuestionClass).click(function() {
    let _this = $(this),
      faqClass = _this.closest(accordionParentClass).attr('class');

    if(faqClass.indexOf('close') != -1){
      $(accordionParentClass).find(accordionContentClass).slideUp('200');
      $(accordionParentClass).addClass('close').removeClass('open');

      _this.closest(accordionParentClass).removeClass('close');
      _this.closest(accordionParentClass).addClass('open');
      _this.closest(accordionParentClass).find(accordionContentClass).slideDown('200');
    } else {
      _this.closest(accordionParentClass).addClass('close');
      _this.closest(accordionParentClass).removeClass('open');
      _this.closest(accordionParentClass).find(accordionContentClass).slideUp('200');
    }

  });

  $(accordionContentClass).each(function() {
    const content = $(this);

    // Replace URLs with clickable links
    content.html(content.html().replace(/(https?:\/\/[^\s]+)([.,!?]?)\b/g, function(match, url, punctuation) {
      // Remove trailing punctuation from the URL
      if (['.', ',', '!', '?'].includes(punctuation)) {
          url = url.slice(0, -1);
      }
      return '<a href="' + url + '" target="_blank">' + url + '</a>' + punctuation;
    }));
  });
}

/**
 * Number of filtered items icon
 *
 * @returns {void}
 */
function iconFilteredItems () {
  let filteredIcon = '.search-notice',
    filteredIconVisible = 'search-notice--visible',
    checkboxesSection = '.filtering-modal__checkboxes',
    optionTagsSection = '.option-tags',
    slidersSection = '.nouislider',
    activeFilterNumber = sliderMinValue = sliderMaxValue = sliderCurrentMinValue = sliderCurrentMaxValue = 0;

  setTimeout(function () {
    // Checked checkbox
    $(checkboxesSection + ' input:checked').each(function () {
      activeFilterNumber = activeFilterNumber + 1;
    });

    $(optionTagsSection + ' .custom-radio input:checked').each(function (key, element) {
      if ($(element).val() !== '') {
        activeFilterNumber = activeFilterNumber + 1;
      }
    });

    $(slidersSection).each(function (index, item) {
      let sliderMinValue = ($(item).data('min') === '') ? 0 : $(item).data('min'),
        sliderMaxValue = ($(item).data('max') === '') ? 0 : $(item).data('max'),
        sliderCurrentMinValue = parseInt($('#' + $(item).attr('name') + '_min').val()),
        sliderCurrentMaxValue = parseInt($('#' + $(item).attr('name') + '_max').val());

      if ((sliderMinValue !== sliderCurrentMinValue) || (sliderMaxValue !== sliderCurrentMaxValue)) {
        activeFilterNumber = activeFilterNumber + 1;
      }
    });

    if (activeFilterNumber > 0) {
      $(filteredIcon).addClass(filteredIconVisible);
    } else {
      $(filteredIcon).removeClass(filteredIconVisible);
    }

    // Write total number of active filters to notice
    $(filteredIcon).html(activeFilterNumber);

  }, 100);
}

/**
 * Filtering in modal functionality
 *
 * @returns {void}
 */
function modalFiltering () {
  let showAllFilters = '.filtering-modal__show-more',
    filteringModalBlockHidden = 'filtering-modal__block--hidden-content';

  $(body).on('click', showAllFilters, function() {
    var _this = $(this);

    if (_this.parent().hasClass(filteringModalBlockHidden)) {

      _this
        .html(_this.data('show-less'))
        .parent()
        .removeClass(filteringModalBlockHidden);

      return;
    }

    _this
      .html(_this.data('show-all'))
      .parent()
      .addClass(filteringModalBlockHidden)
  });

  $("#filtering input[type='checkbox'], #filtering input[type='radio'], #filtering input[type='hidden']").on("change", function () {
    // Call your function to handle the change
    triggerFilterChange();
  });

  setInterval(function () {
    // Attach event listener for slider changes
    $(".nouislider").each(function () {
      let sliderInstance = this.noUiSlider;

      if (sliderInstance) {
        // Detach previous event listener if any, to avoid duplicates
        sliderInstance.off('change');

        // Attach the 'change' event only once
        sliderInstance.on('change', function () {
          triggerFilterChange();
        });
      }
    });
  }, 2000);
}

function getFilterData() {
  let formData = $("#filtering input, #filtering select").serializeArray(); // Get form elements inside modal

  let params = {};
  $.each(formData, function (i, field) {
    params[field.name] = field.value; // Convert to object
  });

  // Handle noUiSlider values
  $(".nouislider").each(function () {
    let slider = this.noUiSlider;
    if (slider) {
      let values = slider.get(); // Get slider values
      let name = $(this).attr("data-name"); // Slider name
      params[name + "_min"] = values[0]; // Min value
      params[name + "_max"] = values[1]; // Max value
    }
  });

  return params;
}

function eVisitor () {
  if(evisitorData === null && errorMessage) {
    $('.evisitor-content').fadeOut(200)
    $('.evisitor-thankyou').fadeIn(200).addClass('container')
    $('.evisitor-spinner-wrapper').hide();
    $('.evisitor-guest-list').css('margin', 0)
    return;
  }

  // add adults list item to sidebar
  if(evisitorData && evisitorData.number_of_adults > 0) {
    const guestListContainer = document.querySelector('.evisitor-guest-list');
    const templateItem = document.querySelector('.evisitor-guest-list-item[data-initialized="false"]');
    
    for (let index = 0; index < evisitorData.number_of_adults; index++) {
      if (templateItem) {
        const clonedItem = templateItem.cloneNode(true);
        clonedItem.setAttribute('data-initialized', 'true');
        clonedItem.setAttribute('data-order', index);
        const title = clonedItem.querySelector('.evisitor-guest-list-item-title-number')
        $(clonedItem).find('.kids-svg').hide();
        title.innerHTML = `${index + 1}.`
        const mobileTitle = clonedItem.querySelector('.evisitor-guest-list-item-title-number--mobile')
        mobileTitle.innerHTML = `${index + 1} / ${(evisitorData.number_of_adults + evisitorData.number_of_children)}` 
        guestListContainer.appendChild(clonedItem);
        if(index === 0) {
          clonedItem.classList.add('evisitor-guest-list-item--active');
        }
      }
    }
  }

  // add children list item to sidebar
  if(evisitorData && evisitorData.number_of_children > 0) {
    const guestListContainer = document.querySelector('.evisitor-guest-list');
    const templateItem = document.querySelector('.evisitor-guest-list-item[data-initialized="false"]');
    
    for (let index = 0; index < evisitorData.number_of_children; index++) {
      if (templateItem) {
        const clonedItem = templateItem.cloneNode(true);
        clonedItem.setAttribute('data-initialized', 'true');
        clonedItem.setAttribute('data-order', index + evisitorData.number_of_adults);
        const subtitle = clonedItem.querySelector('.evisitor-guest-list-item-subtitle');
        const dataChild = subtitle.getAttribute('data-child');
        $(clonedItem).find('.adult-svg').hide();
        $(clonedItem).find('.evisitor-checkmark-svg').addClass('evisitor-checkmark-svg--child')
        const title = clonedItem.querySelector('.evisitor-guest-list-item-title-number')
        title.innerHTML = `${index + 1 + evisitorData.number_of_adults}.`
        const mobileTitle = clonedItem.querySelector('.evisitor-guest-list-item-title-number--mobile')
        mobileTitle.innerHTML = `${index + 1 + evisitorData.number_of_adults} / ${(evisitorData.number_of_adults + evisitorData.number_of_children)}` 
        subtitle.innerHTML = dataChild;
        guestListContainer.appendChild(clonedItem);
      }
    }
  }

  // add forms for adults
  if(evisitorData && evisitorData.number_of_adults > 0) {
    const templateItem = document.querySelector('.form-wrapper[data-initialized="false"]');
    
    for (let index = 0; index < evisitorData.number_of_adults; index++) {
      if (templateItem) {
        const clonedItem = templateItem.cloneNode(true);
        $('.forms').append(clonedItem)
        clonedItem.setAttribute('data-initialized', 'true');
        clonedItem.setAttribute('data-order', index);
        clonedItem.setAttribute('data-age', 'adult')
        const headlineNum = clonedItem.querySelector('.form-headline-num');
        headlineNum.textContent = `${index + 1}.`;
        if(index === 0) {
          clonedItem.classList.add('active')
        }
        changeIDEvisitorInputs(index)
        initializeSelectsOfEvisitorForm(index, evisitorData.prepopulated_data)
        const submitButton = clonedItem.querySelector('.evisitor-button-submit')
        submitButton.setAttribute('data-order', index)
        addSettlementSelectToForm(index)
        flatpickr(`#date_of_birth_${index}`, {
          altInputClass: 'date-rangepicker',
          dateFormat: "Y-m-d",
          maxDate: "today",
          altInput: true,
          altFormat: "d.m.Y",
          disableMobile: true,
          locale: {
            ...calendarLang,
            rangeSeparator: ' - ',
          },
          onReady: function(selectedDates, dateStr, instance) {
            instance.calendarContainer.classList.add('with-year-selection');
          },
          onChange: function(selectedDates, dateStr, instance) {
            $('.flatpickr-input').removeClass('form-visitor-error')
            // if date is younger then 18 years, add error class 
            if (selectedDates.length) {
              const selectedDate = selectedDates[0];
              const today = new Date();

              // Calculate the 18-year threshold date
              const cutoffDate = new Date(
                today.getFullYear() - 18,
                today.getMonth(),
                today.getDate()
              );

              if (selectedDate > cutoffDate) {
                // User is younger than 18
                $('.flatpickr-input').addClass('form-visitor-error');
                $('.date-error-msg').show();
              }else {
                $('.date-error-msg').hide();
              }
            }
          },
        });
      }
    }
  }

  // add forms for children
  if(evisitorData && evisitorData.number_of_children > 0) {
    const templateItem = document.querySelector('.form-wrapper[data-initialized="false"]');
    
    for (let index = 0; index < evisitorData.number_of_children; index++) {
      if (templateItem) {
        const clonedItem = templateItem.cloneNode(true);
        $('.forms').append(clonedItem)
        clonedItem.setAttribute('data-initialized', 'true');
        clonedItem.setAttribute('data-age', 'child')
        clonedItem.setAttribute('data-order', index + evisitorData.number_of_adults);
        const headlineNum = clonedItem.querySelector('.form-headline-num');
        headlineNum.textContent = `${index + 1 + evisitorData.number_of_adults}.`;
        const headlineLabel = clonedItem.querySelector('.form-headline-label');
        const dataChild = document.querySelector('.form-headline').getAttribute('data-child');
        headlineLabel.innerHTML = dataChild;
        changeIDEvisitorInputs(index + evisitorData.number_of_adults)
        initializeSelectsOfEvisitorForm(index + evisitorData.number_of_adults, evisitorData.prepopulated_data)
        const submitButton = clonedItem.querySelector('.evisitor-button-submit')
        submitButton.setAttribute('data-order', index + evisitorData.number_of_adults)
        addSettlementSelectToForm(index + evisitorData.number_of_adults)
        flatpickr(`#date_of_birth_${index + evisitorData.number_of_adults}`, {
          dateFormat: "Y-m-d",
          maxDate: "today",
          altInput: true,
          altFormat: "d.m.Y",
          disableMobile: true,
          locale: {
            ...calendarLang,
            rangeSeparator: ' - ',
          },
          onReady: function(selectedDates, dateStr, instance) {
            instance.calendarContainer.classList.add('with-year-selection');
          },
          onChange: function(selectedDates, dateStr, instance) {
            $(instance.input).removeClass('form-visitor-error');

            if (selectedDates.length) {
              const selectedDate = selectedDates[0];
              const today = new Date();

              const selected = new Date(
                selectedDate.getFullYear(),
                selectedDate.getMonth(),
                selectedDate.getDate()
              );

              const cutoff = new Date(
                today.getFullYear() - 18,
                today.getMonth(),
                today.getDate()
              );

              if (selected < cutoff) {
                $(instance.input).addClass('form-visitor-error');
                const label = $('.date-error-msg').data('child');
                $('.date-error-msg').html(label).show();
              } else {
                $('.date-error-msg').hide();
              }
            }
          }
        });
      }
    }
  }

  // prepopulate data
  if (evisitorData && evisitorData.data) {
    if (evisitorData.data.adults && evisitorData.data.adults.length) {
      populateForms(evisitorData.data.adults);
    }
    if (evisitorData.data.children && evisitorData.data.children.length) {
      populateForms(evisitorData.data.children, evisitorData.data.adults ? evisitorData.data.adults.length : 0);
    }

    var adultsLength = (evisitorData.data.adults && evisitorData.data.adults.length) || 0;
    var childrenLength = (evisitorData.data.children && evisitorData.data.children.length) || 0;
    var orderNum = adultsLength + childrenLength;

    for (var index = 0; index < orderNum; index++) {
      changeFormVisibility(index, 0);
    }
  }
  $('.evisitor-spinner-wrapper').hide();
  $('.evisitor-complete-info').appendTo($('.evisitor-complete-info').parent());

  if(wi <= 768) {
    $('.evisitor-complete-info').appendTo('.evisitor-guest-list-item--active');
  }

  var buttons = document.querySelectorAll('.evisitor-button-submit');
  for (var i = 0; i < buttons.length; i++) {
    buttons[i].addEventListener('click', submitEvisitorBtnHandler);
  }

  $('.form-wrapper').on('input change', 'input, select, textarea', function() {
    $(this).removeClass('form-visitor-error');
  });

  // generate whatsapp href
  var helpBtn = document.querySelector('.evisitor-modal-help-cta');
  var label = helpBtn && helpBtn.getAttribute('data-label'); // gets data-label
  var reservationCode = evisitorData.reservation_code; // gets reservation code from object

  if (helpBtn && label && reservationCode) {
    var message = label + ' ' + reservationCode;
    var encodedMessage = encodeURIComponent(message);
    var phoneNumber = '385998039540';

    helpBtn.href = 'https://wa.me/' + phoneNumber + '?text=' + encodedMessage;
  }

  $('.evisitor-modal-trigger-in-form').on('click', function() {
    $('.form-details').addClass('active');
    $('.evisitor-modal-overlay').addClass('active');
  });

  $('.evisitor-modal-close-js').on('click', function() {
    $('.form-details').removeClass('active');
    $('.evisitor-modal-overlay').removeClass('active');
  });

  $('select[name="country_of_residence"]').on('change', function () {
    const id = $(this).attr('id');
    const index = id.split('_').pop();
    const value = $(this).val();

    const $city = $(`#city_of_residence_${index}`);
    const $select2Wrapper = $(`#select2-wrapper-evisitor_${index}`);
    $('.city-error-msg').hide();
    if (value === 'HRV') {
      $city.hide();
      $select2Wrapper.show();
    } else {
      $city.show().prop('readonly', false)
          .attr('placeholder', $city.data('placeholder'));
      $city.removeAttr('title')
      $select2Wrapper.hide();
    }
  });

  $('select').on('select2:select', function (e) {
    const selected = e.params.data;
    const select2Container = $(this).next('.select2');
    select2Container.find('.select2-selection').removeClass('form-visitor-error');
  });

  $('input[name="city_of_residence"]').on('click', function(e) {
    const id = $(this).attr('id');
    const index = id.split('_').pop();

    if($(`#country_of_residence_${index}`).val()) {
      return
    }

    $(`#country_of_residence_${index}`).addClass('form-visitor-error shake');
    $(`#country_of_residence_${index}`).one('animationend', function() {
      $(this).removeClass('shake');
    });
    $('.city-error-msg').show()
  })
}

function populateForms(group, startIndex) {
  if (typeof startIndex === 'undefined') {
    startIndex = 0;
  }

  group.forEach(function(person, index) {
    var formIndex = index + startIndex;
    var form = $('.form-wrapper[data-order=' + formIndex + ']');

    form.find('input[name="name"]').val(person.name);
    form.find('input[name="surname"]').val(person.surname);
    form.find('input[name="city_of_residence"]').val(person.city_of_residence);
    form.find('select[name="country_of_residence"]').val(person.country_of_residence);
    form.find('select[name="citizenship"]').val(person.citizenship);
    form.find('select[name="country_of_birth"]').val(person.country_of_birth);

    var dob = '';
    if (person.date_of_birth) {
      dob = person.date_of_birth.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
    }
    form.find('#date_of_birth_' + formIndex).val(dob);

    form.find('select[name="identity_document"]').val(person.document_type);
    form.find('input[name="document_number"]').val(person.document_number);
    form.find('input[name="gender_' + formIndex + '"][value="' + person.gender + '"]').prop('checked', true);
    form.find('input[name="tax_type_' + formIndex + '"][value="' + person.payment_category + '"]').prop('checked', true);
  });
}


function submitEvisitorBtnHandler(event) {
  var clickedBtn = event.currentTarget;
  var dataOrder = clickedBtn.getAttribute('data-order');

  var form = $('.form-wrapper[data-order=' + dataOrder + ']');
  var inputs = form.find('input, select, textarea');
  var isAdult = form.data('age') === 'adult';
  var isValid = true;

  var radioGroupsChecked = {};
  inputs.each(function() {
    var $el = $(this);
    var value = $el.val();

    // Grouped radio buttons
    if ($el.is(':radio')) {
      var name = $el.attr('name');

      if (!radioGroupsChecked[name]) {
        radioGroupsChecked[name] = true;

        if (form.find('input[type="radio"][name="' + name + '"]:checked').length === 0) {
          form.find('input[type="radio"][name="' + name + '"]').each(function () {
            addErrorWithShake($(this));
          });
          isValid = false;
        }
      }
    }
    // Checkboxes
    else if ($el.is(':checkbox')) {
      if (!$el.is(':checked')) {
        addErrorWithShake($el);
        isValid = false;
      }
    }
    // Other inputs/selects/textareas
    else {
      if (!value || value.trim() === '') {
        let targetEl = $el;

        // Check if it's a Select2 element (usually <select> with select2 applied)
        if ($el.hasClass('select2-hidden-accessible')) {
          const select2Container = $el.next('.select2'); // this is the main Select2 container

          if (select2Container.length) {
            const selectionEl = select2Container.find('.select2-selection');
            if (selectionEl.length) {
              targetEl = selectionEl;
            }
          }
        }

        const id = $el.attr('id') || '';
        const countryValue = form.find('select[name="country_of_residence"]').val();

        if (id.includes('settlement') && countryValue !== 'HRV') {
          return; // skip validation
        }

        addErrorWithShake(targetEl);
        isValid = false;
      }
    }

    // Validate Date of Birth
    if ($el.is('#date_of_birth_' + dataOrder)) {
      var dateOfBirth = new Date(value);
      var today = new Date();
      var age = today.getFullYear() - dateOfBirth.getFullYear();
      var m = today.getMonth() - dateOfBirth.getMonth();

      if (m < 0 || (m === 0 && today.getDate() < dateOfBirth.getDate())) {
        age--;
      }

      if (isAdult && age < 18) {
        $('.date-error-msg').show();
        addErrorWithShake($('.form-control-visitor.flatpickr-input'));
        isValid = false;
      } else if (!isAdult && age >= 18) {
        $('.date-error-msg').show();
        addErrorWithShake($('.form-control-visitor.flatpickr-input'));
        isValid = false;
      }
    }
  });

  if (!isValid) {
    return;
  }

  var numberOfForms = $('.form-wrapper[data-initialized="true"]').length;

  // Helper to get URL parameters (URLSearchParams polyfill)
  function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    var results = regex.exec(window.location.search);
    return results === null ? null : decodeURIComponent(results[1].replace(/\+/g, ' '));
  }

  function getEvisitorHashValue() {
    let hash = getUrlParameter('hash');

    if (!hash) {
      const pathParts = window.location.pathname.split('/');
      const hashIndex = pathParts.indexOf('evisitor') + 1;
      hash = pathParts[hashIndex] || null;
    }

    return hash;
  }

  var hashValue = getEvisitorHashValue();
  var baseUrl = window.location.origin;
  var formData = collectEvisitorData();

  var dataToSend = {
    hash: hashValue,
    data: formData
  };

  var settings = {
    url: baseUrl + '/confirm-evisitor-data',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: JSON.stringify(dataToSend)
  };

  if ((numberOfForms - 1) > dataOrder) {
    // send data and then change form and listing
    $.ajax(settings).done(function(response) {
      changeFormVisibility(dataOrder);
    });
  } else {
    // last step
    $.ajax(settings).done(function(response) {
      $('.evisitor-content').fadeOut(200);
      $('.evisitor-thankyou').fadeIn(200);
      $('.evisitor-guest-list-item--active').addClass('visitor-guest-list-item--done-last')
      if (wi < 768) {
        $('.evisitor-complete-info').appendTo('.evisitor-guest-list-item--active');
      }
      $('.evisitor-complete-info').fadeIn(200);
    });
  }
}

function collectEvisitorData() {
  var data = {
    adults: [],
    children: []
  };

  var forms = $('.form-wrapper[data-initialized="true"]');

  forms.each(function (index, element) {
    var form = $(element);
    var isChild = form.data('age') === 'child';
    var gender = form.find('input[name="gender_' + index + '"]:checked').val();
    var firstName = form.find('input[name="name"]').val();
    var lastName = form.find('input[name="surname"]').val();
    var cityOfResidence = form.find('input[name="city_of_residence"]').val();
    var countryOfResidence = form.find('select[name="country_of_residence"]').val();
    var citizenship = form.find('select[name="citizenship"]').val();
    var countryOfBirth = form.find('select[name="country_of_birth"]').val();
    var birthDate = $('#date_of_birth_' + index).val();
    var identityDocument = form.find('select[name="identity_document"]').val();
    var documentNumber = form.find('input[name="document_number"]').val();
    var disability = form.find('input[name="tax_type_' + index + '"]:checked').val();
    var taxType;
    var birthDateForSend = birthDate && birthDate.replace(/-/g, '');

    if (disability === 'xx' && birthDate) {
      var birth = new Date(birthDate);
      var today = new Date();

      var age = today.getFullYear() - birth.getFullYear();
      var m = today.getMonth() - birth.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birth.getDate())) {
        age--;
      }

      if (age < 12) {
        taxType = 1;
      } else if (age >= 12 && age < 18) {
        taxType = 2;
      } else {
        taxType = 14;
      }
    } else {
      taxType = disability;
    }

    if(countryOfResidence === "HRV") {
      cityOfResidence = $(`#settlement_${index}`).val()
    }

    var person = {
      name: firstName,
      surname: lastName,
      gender: gender,
      city_of_residence: cityOfResidence,
      citizenship: citizenship,
      country_of_residence: countryOfResidence,
      country_of_birth: countryOfBirth,
      date_of_birth: birthDateForSend,
      document_type: identityDocument,
      document_number: documentNumber,
      payment_category: taxType.toString()
    };

    if (firstName) {
      if (isChild) {
        data.children.push(person);
      } else {
        data.adults.push(person);
      }
    }
  });

  return data;
}

function changeFormVisibility(order, animationDuration) {
  if (typeof animationDuration === 'undefined') {
    animationDuration = 200;
  }

  var currentForm = $('.form-wrapper[data-order=' + order + ']');
  var nextForm = $('.form-wrapper[data-order=' + (Number(order) + 1) + ']');
  var currentListItem = $('.evisitor-guest-list-item[data-order=' + order + ']');
  var nextListItem = $('.evisitor-guest-list-item[data-order=' + (Number(order) + 1) + ']');

  currentForm.fadeOut(animationDuration, function () {
    currentForm.removeClass('active');
    nextForm.fadeIn(animationDuration).addClass('active');

    // Update list item classes
    currentListItem.addClass('visitor-guest-list-item--done').removeClass('evisitor-guest-list-item--active');
    nextListItem.addClass('evisitor-guest-list-item--active');
  });
}


function addErrorWithShake($el) {
  $el.addClass('form-visitor-error shake');
  $el.one('animationend', function() {
    $(this).removeClass('shake');
  });
}

function changeIDEvisitorInputs(index) {
  var form = $('.form-wrapper[data-order=' + index + ']');

  var radioGroups = {};

  form.find('input, select, textarea').each(function () {
    var $input = $(this);

    // Update ID and label
    var originalId = $input.attr('id');
    if (originalId) {
      var newId = originalId + '_' + index;
      $input.attr('id', newId);
      form.find('label[for="' + originalId + '"]').attr('for', newId);
    }

    var originalName = $input.attr('name');
    if ($input.is(':radio')) {
      if (originalName) {
        var newName = originalName + '_' + index;
        $input.attr('name', newName);
        $input.prop('checked', false);

        if (originalName === 'tax_type') {
          if (!radioGroups[newName]) {
            radioGroups[newName] = [];
          }
          radioGroups[newName].push($input);
        } else {
          radioGroups[newName] = $input;
        }
      }

    } else if ($input.is(':checkbox')) {
      if (originalName) {
        var newName = originalName + '_' + index;
        $input.attr('name', newName);
      }
    }

    $('input[type="radio"]').each(function () {
      if ($(this).val().toLowerCase() === 'muški') {
        $(this).prop('checked', true);
      }
    });

  });

  // Set radio selections
  for (var groupName in radioGroups) {
    if (radioGroups.hasOwnProperty(groupName)) {
      var radios = radioGroups[groupName];

      if (Array.isArray(radios)) {
        // tax_type group
        var selected = radios.find(function (r) {
          return r.val() === 'xx';
        });
        if (selected) {
          selected.prop('checked', true);
        } else {
          radios[radios.length - 1].prop('checked', true); // fallback
        }

      } else if (radios && typeof radios.prop === 'function') {
        radios.prop('checked', true);
      }
    }
  }

  form.find('.select2-wrapper-evisitor').attr('id', 'select2-wrapper-evisitor_' + index);
}

function initializeSelectsOfEvisitorForm(index, data) {
  var countryOfResidenceSelect = $('#country_of_residence_' + index);
  var countryOfBirthSelect = $('#country_of_birth_' + index);
  var citizenshipSelect = $('#citizenship_' + index);
  var identityDocumentSelect = $('#identity_document_' + index);
  

  // Populate country selects
  var relevantGroup = $('<optgroup label="' + countryOfBirthSelect.data('common') + '"></optgroup>');
  var otherGroup = $('<optgroup label="' + countryOfBirthSelect.data('other') + '"></optgroup>');

  // Append relevant countries
  data.countries.relevant_countries.forEach(function(country) {
    var option = '<option value="' + country.code + '">' + country.name + '</option>';
    relevantGroup.append(option);
  });

  // Append other countries
  data.countries.other_countries.forEach(function(country) {
    var option = '<option value="' + country.code + '">' + country.name + '</option>';
    otherGroup.append(option);
  });

  // Clear any existing options and append groups
  countryOfResidenceSelect.append(relevantGroup.clone());
  countryOfBirthSelect.append(relevantGroup.clone());
  countryOfResidenceSelect.append(otherGroup.clone());
  countryOfBirthSelect.append(otherGroup.clone());
  citizenshipSelect.append(relevantGroup.clone());
  citizenshipSelect.append(otherGroup.clone());

  data.document_types.forEach(function(doc) {
    var option = '<option value="' + doc.code + '">' + doc.name + '</option>';
    identityDocumentSelect.append(option);
  });
}


function addSettlementSelectToForm(index) {
  const newId = `settlement_${index}`;

  // Clone the template
  const cloned = $('#settlement_template').clone();

  // Update ID and name
  cloned
    .attr('id', newId)
    .attr('name', 'settlement-' + index)
    .attr('data-initialized', 'true')
    .css('display', '')
    .removeAttr('data-select2-id');

  $('#select2-wrapper-evisitor_' + index).append(cloned).hide()

  // initialize Select2 on the cloned element
  if ($.fn.select2) {
    cloned.select2({
      placeholder: $(cloned).data('placeholder'),
        minimumInputLength: 2,
    ajax: {
      transport: function (params, success, failure) {
        const term = params.data.q.toLowerCase();
        const filtered = evisitorData.prepopulated_data.settlements.filter(c =>
          c.text.toLowerCase().includes(term)
        );
 
        // Simulate async (optional)
        setTimeout(() => {
          success({ results: filtered.slice(0, 50) }); // limit to 50 results
        }, 100);
      }
    }
  });
  }

}

function triggerFilterChange() {
  let formData = $("#frm-listingResultControl-filterFormControl-filterForm, #frm-searchResultsControl-filterFormControl-filterForm").serializeArray(); // Get form data as an array

  let params = {};
  $.each(formData, function (i, field) {
    params[field.name] = field.value; // Convert array to object
  });

  let filterParams = getFilterData();
  let mergedParams = Object.assign({}, params, filterParams);
  appendButtonLoaderOnFilterButton();

  $.ajax({
    url: actionCounterUrl,
    method: "POST",
    data: JSON.stringify(mergedParams),
    success: function (response) {
      removeButtonLoaderOnFilterButton();
      $('.panel-apply').removeClass('button-disabled');
      if(response.counter > 0) {
        let label = $('.panel-apply').data('available-label');
        // find numbers inside label and replace with response.counter
        label = label.replace(/(\d+)/, response.counter);
        $('.panel-apply').text(label);
      } else {
        $('.panel-apply').text($('.panel-apply').data('unavailable-label'));
      }
    },
    error: function (xhr, status, error) {
      removeButtonLoaderOnFilterButton();
      $('.panel-apply').removeClass('button-disabled');
      console.error("Error applying filters:", error);
    }
  });
}

/**
 * Reevoo inject custom styling
 *
 * @returns {void}
 */
function reevooInjectCSS () {
  // Observe if element is added to the DOM with MutationObserver
  let observerConfig = {
    subtree: false,
    childList: true
  };

  $('.reevoo-badge--inject-css').each(function () {
    var target = this;

    const observer = new MutationObserver(function (mutations_list) {
      mutations_list.forEach(function (mutation) {
        mutation.addedNodes.forEach(function (added_node) {
          if (added_node.hasChildNodes()) {
            var iframe = $(target).children('iframe');

            iframe
              .contents()
              .find("body")
              .append("<style>" +
                ".reevoo__product-badge--mobile reevoo-logo { height: 17px; }" +
                ".reevoo__product-badge--mobile reevoo-score { height: 23px; }" +
                ".reevoo__product-badge--mobile reevoo-score > svg { height: 20px; }" +
                ".reevoo__product-badge--mobile .reevoo__position--right {margin-left: 10px;} " +
                ".reevoo__product-badge--mobile { padding: 5px; }" +
                "</style>");

            observer.disconnect();
          }
        });
      });
    });

    $(target)
      .fadeToggle()
      .css('display', 'flex');

    observer.observe(target, observerConfig);
  });
}

/**
 * Headerbar slideInOut
 *
 * @returns {void}
 */
function headerBarSlide() {
  $('#header')
    .not('#header.not-sticky')
    .clone(true)
    .addClass('cloned unsticky')
    .insertAfter('#header');

  $(window).scroll(function () {
    if ($(window).scrollTop() >= $('.header-container').height() * 2) {
      $('#header.cloned').addClass('sticky').removeClass('unsticky');
    } else {
      $('#header.cloned').addClass('unsticky').removeClass('sticky');
    }
  });
}


/**
 * BackToTop
 *
 * @returns {void}
 */
function backToTop() {
  var pxShow = 600,
    scrollSpeed = 500,
    id = $('#backtotop');

  $(window).scroll(function () {
    if ($(window).scrollTop() >= pxShow) {
      $(id).addClass('visible');
    } else {
      $(id).removeClass('visible');
    }
  });

  $('.backtotop__anchor').on('click', function () {
    $('html, body').animate({scrollTop: 0}, scrollSpeed);
    return false;
  });
}

/**
 * Close panel dropdown
 *
 * @returns {void}
 */
function close_panel_dropdown () {
  $('.panel-dropdown').removeClass('active');

  disableScrollLock();
  activeModalSearchResults(false);
}

/**
 * Panel dropdown
 *
 * @returns {void}
 */
function panelDropdown() {
  var mouse_is_inside = false;


  $('.panel-dropdown-content__close').on('click', function () {
    enableScrollLock();
    close_panel_dropdown();
    activeModalSearchResults(false);
  });

  $('.panel-dropdown:not(.panel-dropdown-content--location) a:not(.more-filters), .panel-dropdown--input-open input, .panel-dropdown--input-open .input-persons-placeholder').on('click', function (e) {
    if ($(this).parent().is('.active')) {
      close_panel_dropdown();
    } else {
      $(this).parent().addClass('active');
    }

    e.preventDefault();
  });

  $('.panel-dropdown').hover(function () {
    mouse_is_inside = true;
  }, function () {
    mouse_is_inside = false;
  });

  $('body').mouseup(function (e) {
    if (!mouse_is_inside && !trigger_dropdown && $('.persons.panel-dropdown--input-open').hasClass('active')) {
      close_panel_dropdown();
    }
    trigger_dropdown = false;

    if ($(panelFilter).hasClass('active') && !$(panelFilter).is(e.target) && $(panelFilter).has(e.target).length === 0) {
      $('.panel-cancel').click();
    }
  });
}

/**
 * Change z-index of modal while active on search results
 * needs to be refactored
 *
 * @param state
 */
function activeModalSearchResults(state) {
  if (state === true) {
    if ($(searchResultsContent).length > 0) {
      $(searchResultsContent).addClass(modalActive);
    }
  }

  if (state === false) {
    if ($(searchResultsContent).length > 0) {
      $(searchResultsContent).removeClass(modalActive);
    }
  }
}

/**
 * Calculate current scrollTop position, set the top selector to body tag
 *
 */
function enableScrollLock (element) {
  if (scrollLockExecuted) {
    return false;
  }

  if (element === 'modal') {
    body_class_opened = 'modal-general-opened';
  }

  scrollDistance = $window.scrollTop();
  $(body)
    .css('top', scrollDistance * -1)
    .addClass(body_class_opened);

  scrollLockExecuted = true;
}

/**
 * Remove the top selector on body tag
 *
 */
function disableScrollLock (element) {
  if (element === 'modal') {
    body_class_opened = 'modal-general-opened';
  }

  if ($(body).hasClass(body_class_opened)) {
    $window.scrollTop(scrollDistance);
    $(body)
      .css('top', '')
      .removeClass(body_class_opened);
    $window.scrollTop(scrollDistance);

    scrollLockExecuted = false;
  }
}


function getQueryString () {
  var key = false,
    res = {},
    itm = null,
    // get the query string without the ?
    qs = location.search.substring(1);
  // check for the key as an argument

  if (arguments.length > 0 && arguments[0].length > 1) {key = arguments[0];}
  // make a regex pattern to grab key/value
  var pattern = /([^&=]+)=([^&]*)/g;
  // loop the items in the query string, either
  // find a match to the argument, or build an object
  // with key/value pairs

  while (itm = pattern.exec(qs)) {
    if (key !== false && decodeURIComponent(itm[1]) === key) {
      return decodeURIComponent(itm[2]);
    } else if (key === false) {
      res[decodeURIComponent(itm[1])] = decodeURIComponent(itm[2]);
    }
  }

  return key === false ? res : null;
}


/**
 * Email address validation function
 *
 * @param {String} element_id ID selector for the inputfield
 * @returns {boolean} True is a valid emailaddress
 */
function validateEmail (element_id) {
  var value = $(element_id).val(),
    filter = /^[a-z\p{L}0-9!#$%&\'*+\/=?^`{}|~_-]+[.a-z\p{L}0-9!#$%&\'*+\/=?^`{}|~_-]*@[a-z\p{L}0-9]+[._a-z\p{L}0-9-]*\.[a-z0-9]+$/i;

  return filter.test(String(value).toLowerCase());
}

/**
 * Header main menu functionality
 *
 * @returns {void}
 */
function headerMainMenu () {
  var fullscreen_overlay = '.fullscreen-overlay',
    header_navigation = '.header-navigation__list--overlayed',
    header_menu_item_no_action = '.header-navigation__item-anchor--no-action';

  if (wi > 1024) {
    $(header_navigation).hoverIntent(function() {
      $(fullscreen_overlay).fadeIn(100);

    }, function () {
      $(fullscreen_overlay).fadeOut(100);
    });

    $(header_menu_item_no_action).on('click', function (e) {
      e.preventDefault();
    });
  }
}

/**
 * Show sticky bar for mobile devices
 * templates: page-listing, single-villa, page-sidebar
 *
 * return {void}
 */
function toggleStickybarMobile () {
  var wi = $(window).width(),
    stickybar = '.single-stickybar--only-mobile',
    stickybar_visible = 'single-stickybar--visible',
    stickybar_button_scrollto = '.button--scrollto',
    stickybar_button_trigger = '.button--trigger',
    stickybar_item = '.single-villa-bottom-sticky-bar__item',
    stickybar_item_active = 'single-villa-bottom-sticky-bar__item--active',
    stickybar_scrollspy = '.single-villa-bottom-sticky-bar__item--scrollspy',
    $document = $(document),
    is_visible = false,
    triggered = false,
    listing_amenities = '#listing-amenities';

  if (wi < 1025 && !is_visible) {

    if ($(stickybar).data('holder')) {

      $document.scroll(function () {
        if ($($(stickybar).data('holder')).length > 0 && $document.scrollTop() >= $($(stickybar).data('holder')).position().top) {
          if ($(stickybar_button_scrollto).data('scrollto')) {
            if ($document.scrollTop() > ($($(stickybar_button_scrollto).data('scrollto')).offset().top - $($(stickybar_button_scrollto).data('scrollto')).height())) {
              $(stickybar).removeClass(stickybar_visible);
              is_visible = false;
              return;
            }
          }

          $(stickybar).addClass(stickybar_visible);
          is_visible = true;
        } else {
          $(stickybar).removeClass(stickybar_visible);
          is_visible = false;
        }

        if ($(stickybar_scrollspy).length > 0) {
          $(stickybar_scrollspy).each(function(){

            var container = $(this).data('destination'),
              containerOffset = $(container).offset().top,
              containerHeight = $(container).outerHeight(),
              containerBottom = containerOffset + containerHeight,
              scrollPosition = $(document).scrollTop();

            if(scrollPosition < containerBottom - 30 && scrollPosition >= containerOffset - 30) {
              $(this).addClass(stickybar_item_active);
            } else{
              $(this).removeClass(stickybar_item_active);
            }
          });
        }
      });

    } else {
      if ($(listing_amenities).length > 0) {

        $document.scroll(function () {
          if ($document.scrollTop() >= $(listing_amenities).position().top) {
            $(stickybar).addClass(stickybar_visible);
            is_visible = true;
          }
        });
      }
    }
  }

  $(stickybar_button_scrollto).on('click', function (e) {
    e.preventDefault();

    var scrollTo;

    if ($(stickybar_button_scrollto).data('scrollto')) {
      scrollTo = $(stickybar_button_scrollto).data('scrollto');
    }

    if ($(stickybar_button_scrollto).data('scroll')) {
      scrollTo = $(stickybar_button_scrollto).data('scroll');
    }

    $('html, body').animate({
      scrollTop: $(scrollTo).offset().top - $('.header-container').outerHeight() - 15
    }, 600);
  });

  $(stickybar_button_trigger).on('click', function (e) {
    e.preventDefault();

    if (triggered === false) {
      $($(this).data('trigger')).trigger('click');
    }

    triggered = true;
  });

  $(stickybar_item).on('click', function (e) {
    e.preventDefault();

    let $this = $(this);

    if($this.hasClass('modal-gallery-opener')) {
      $('.photo-gallery-modal__opener').trigger('touchstart');
      $('.photo-gallery-modal__opener').trigger('click');

      $('.photo-gallery-modal').animate({scrollTop: 0}, 0);

    } else if ($this.hasClass('popup-with-zoom-anim')) {

      setTimeout(() => {
        $('.inquiry-form--input-family').trigger('touchstart');
      }, 500);

    } else {

      $('html, body').animate({
        scrollTop: $($this.data('destination')).offset().top - 20
      }, 1000);

    }
  });
}

/**
 * Mobile menu
 * template: header
 * visible: mobile or tablet
 *
 * return {void}
 */
function mobileMenu () {
  var mobile_menu = '.header-navigation',
    mobile_submenu = '.header-navigation__submenu',
    mobile_menu_visible = 'header-navigation--visible',
    mobile_submenu_visible = 'header-navigation__submenu--mobile-visible',
    mobile_menu_trigger = '.mobile-menu-button',
    mobile_menu_overlay = '.mobile-menu__overlay',
    mobile_menu_close = '.header-navigation__mobile-close',
    mobile_menu_anchor = '.header-navigation__item-anchor--no-action',
    mobile_menu_back = '.header-navigation__sub-items-top-anchor',
    mobile_menu_container = '.header-navigation__container',
    mobile_menu_container_visible = 'header-navigation__container--visible';

  $(mobile_menu_container).addClass(mobile_menu_container_visible);

  $(document).on('click', function (e) {
    if ($(e.target).hasClass('mobile-menu__overlay')) {
      $(mobile_menu_close).click();
    }
  });

  $('.mobile-menu__overlay').on('click', function () {
    $(mobile_menu_close).trigger('click');
  });

  $(mobile_menu_trigger).on('click', function () {
    enableScrollLock();
    $(mobile_menu).addClass(mobile_menu_visible);
    $(mobile_menu_overlay).fadeIn(300);
    $('.search--sticky').css('z-index', '0');
  });

  $(mobile_menu_close).on('click', function () {
    disableScrollLock();
    $(mobile_menu).removeClass(mobile_menu_visible);
    $(mobile_menu_overlay).fadeOut(250);
    setTimeout(() => {
      $('.search--sticky').css('z-index', '1');
    }, 300);
  });

  $(mobile_menu_anchor).on('click touchstart', function (e) {
    e.preventDefault();

    var $this = $(this),
      $menu = $this.data('menu');

    $(mobile_submenu + '[data-menu="' + $menu + '"]').addClass(mobile_submenu_visible);

    $(mobile_menu_container).removeClass(mobile_menu_container_visible);
  });

  $(mobile_menu_back).on('click', function () {
    var $this = $(this),
      $menu = $this.data('menu');

    $(mobile_menu).addClass(mobile_menu_visible);
    $(mobile_submenu + '[data-menu="' + $menu + '"]').removeClass(mobile_submenu_visible);

    $(mobile_menu_container).addClass(mobile_menu_container_visible);
  });
}

/**
 * keenSlider carousel
 *
 * @returns {void}
 */
function keenCarousel () {
  var galleryCarousel = '.listing-lazy-images',
    gallerySliders = [];

  $(galleryCarousel).each(function () {
    $(this).addClass('keen-initialized');

    var singleSlider = $(this),
      elements = singleSlider.find('.keen-slider__slide--fade'),
      arrow_left = singleSlider.find('.keen-prev')[0],
      arrow_right = singleSlider.find('.keen-next')[0];

    gallerySliders.push(new KeenSlider(this, {
      slides: elements.length,
      duration: 0,
      created: (instance) => {
        arrow_left.addEventListener('click', function () {
          instance.prev();
        });

        arrow_right.addEventListener('click', function () {
          instance.next();
        });

        0 == instance.details().absoluteSlide
          ? arrow_left.classList.add('keen-disabled')
          : arrow_left.classList.remove('keen-disabled');

        instance.details().size - 1 == instance.details().absoluteSlide
          ? arrow_right.classList.add('keen-disabled')
          : arrow_right.classList.remove('keen-disabled');
      },
      slideChanged (instance) {
        0 == instance.details().absoluteSlide
          ? arrow_left.classList.add('keen-disabled')
          : arrow_left.classList.remove('keen-disabled');

        instance.details().size - 1 == instance.details().absoluteSlide
          ? arrow_right.classList.add('keen-disabled')
          : arrow_right.classList.remove('keen-disabled');
      },
      move: (s) => {
        var opacities = s.details().positions.map((slide) => slide.portion);

        elements.each(function (idx, element) {
          element.dataset.slide = Math.round(opacities[idx]);
        })
      },
    }));
  });
}

/**
 * keenSlider updateClasses
 *
 * returns {void}
 */
function updateKeenDots (instance, dots_wrapper) {
  var slide = instance.details().relativeSlide;

  if (dots_wrapper != undefined) {
    var dots = dots_wrapper[0].getElementsByClassName('keen-dot'),
      id = 0;

    for (var dot of dots) {
      slide === id
        ? dot.classList.add('keen-dot--active')
        : dot.classList.remove('keen-dot--active');
      id++;
    }
  }
}

/**
 * keen carousel on grid thumbnail
 *
 * @returns {void}
 */
function keenGrid () {
  var campGridCarousel = '.listing-items-similar',
    gridThumbnailCarousel = '.grid-thumbnail-carousel',
    gallerySliders = [];

  $(campGridCarousel).each(function () {
    var loadKeenSimilar = (wi < 768 || $(this).children().find('> div').length > 3);

    if ($(this).hasClass('keen-slider--disable-mobile') && wi < 768) {
      loadKeenSimilar = false;
    }

    if (loadKeenSimilar) {
      $(this).addClass('keen-initialized');
      $(this).addClass('keen-slider');
      $(this).parent().append("<div class='keen-prev keen-prev-outer keen-arrow'></div>");
      $(this).parent().append("<div class='keen-next keen-next-outer keen-arrow'></div>");
      $(this).parent().append("<div class='keen-dots keen-dots--line' role='presentation'></div>");

      var singleSlider = $(this),
        elements = singleSlider.find('.keen-slider__slide'),
        dots_wrapper = singleSlider.parent().find(".keen-dots"),
        arrow_left = singleSlider.parent().find(".keen-prev-outer")[0],
        arrow_right = singleSlider.parent().find(".keen-next-outer")[0],
        j = 0;

      var slides_per_view  = ($(window).width() < 600) ? 1.08 : ($(window).width() < 900) ? 2 : 3;

      if ($(this).hasClass('listing-items-similar--single-villa')) {
        slides_per_view = $(window).width() <= 767 ? 1.3 : $(window).width() >= 768 && $(window).width() <= 991 ? 3 : 4;
      }
      if ($(this).hasClass('listing-items-carousel--spo')) {
        if($(window).width() <= 767) {
          slides_per_view = 1.3;
        } else if ($(window).width() >= 768 && $(window).width() <= 1440) {
          slides_per_view = 3;
        } else {
          slides_per_view = 4;
        }
      }

      var slides_moves = 1,
        slides_number = Math.ceil(elements.length / slides_moves) - (slides_per_view - slides_moves);

      gallerySliders.push(new KeenSlider(this, {
        slidesPerView: slides_per_view,
        spacing: 0,
        rubberband: false,
        duration: 500,
        created: function (instance) {
          arrow_left.addEventListener("click", function () {
            instance.moveToSlideRelative(instance.details().relativeSlide - slides_moves)

            0 == instance.details().absoluteSlide
              ? arrow_left.classList.add("keen-disabled")
              : arrow_left.classList.remove("keen-disabled");

          });

          arrow_right.addEventListener("click", function () {
            instance.moveToSlideRelative(instance.details().relativeSlide + slides_moves);

            Math.round(instance.details().size - slides_per_view) == instance.details().absoluteSlide
              ? arrow_right.classList.add("keen-disabled")
              : arrow_right.classList.remove("keen-disabled");
          });

          for (let i = 0; i < slides_number; i++) {
            var dot = document.createElement("button");
            dot.setAttribute("aria-label", keenSliderAriaLabel);
            dot.classList.add("keen-dot");
            dot.setAttribute("title", "presentation");
            dot.dataset.move = j;
            dots_wrapper[0].appendChild(dot);
            dot.addEventListener("click", function () {
              instance.moveToSlide(this.dataset.move);
            });
            if (j + slides_moves < elements.length) {
              j = j + slides_moves;
            }
          }

          updateKeenDots(instance, dots_wrapper);
        },
        slideChanged(instance) {
          0 == instance.details().absoluteSlide
            ? arrow_left.classList.add("keen-disabled")
            : arrow_left.classList.remove("keen-disabled");

          Math.round(instance.details().size - slides_per_view) == instance.details().absoluteSlide
            ? arrow_right.classList.add("keen-disabled")
            : arrow_right.classList.remove("keen-disabled");

          updateKeenDots(instance, dots_wrapper);
        },
      }));
    }
  });

  $(gridThumbnailCarousel).each(function () {
    var loadKeenGrid = (wi < 768 || $(this).children().length > 3);
    
    if($(this).parent().hasClass('keen-no-init')) {
      loadKeenGrid = false;
    }
    if ($(this).parent().hasClass('grid-thumbnail--destinations') && $(this).children().length <= 6 && wi >= 768) {
      loadKeenGrid = false;
    }

    if ($(this).parent().hasClass('keen-block--disable-mobile') && wi < 768) {
      loadKeenGrid = false;
    }

    if (loadKeenGrid) {
      $(this).addClass('keen-initialized');
      $(this).parent().removeClass('keen-block');

      var singleSlider = $(this),
        elements = singleSlider.find('.keen-slider__slide'),
        dots_wrapper = singleSlider.parent().find(".keen-dots"),
        arrow_left = singleSlider.parent().find(".keen-prev")[0],
        arrow_right = singleSlider.parent().find(".keen-next")[0],
        j = 0,
        slides_per_view  = ($(window).width() < 600) ? 1.08 : ($(window).width() < 900) ? 2 : 3;

      if ($(this).parent().hasClass('grid-thumbnail--destinations')) {
        slides_per_view = $(window).width() <= 767 ? 2.08 : $(window).width() > 768 && $(window).width() <= 991 ? 3 : 6;
      }

      if ($(this).parent().hasClass('grid-thumbnail--regions') || $(this).parent().hasClass('grid-thumbnail--reviews')) {
        slides_per_view = $(window).width() <= 767 ? 1.3 : $(window).width() >= 768 && $(window).width() <= 991 ? 3 : 5;
      }

      if ($(this).parent().hasClass('grid-thumbnail--fullwidth')) {
        slides_per_view = 1;
      }

      var slides_moves = 1,
        slides_number = Math.ceil(elements.length / slides_moves) - (slides_per_view - slides_moves);

      gallerySliders.push(new KeenSlider(this, {
        slidesPerView: slides_per_view,
        spacing: 0,
        rubberband: false,
        created: function (instance) {
          if ($(arrow_left).length) {
            arrow_left.addEventListener("click", function () {
              instance.prev()

              0 == instance.details().absoluteSlide
                ? arrow_left.classList.add("keen-disabled")
                : arrow_left.classList.remove("keen-disabled");
            });
          }

          if ($(arrow_right).length) {
            arrow_right.addEventListener("click", function () {
              instance.next();

              Math.round(instance.details().size - slides_per_view) == instance.details().absoluteSlide
                ? arrow_right.classList.add("keen-disabled")
                : arrow_right.classList.remove("keen-disabled");
            });
          }

          if ($(arrow_left).length && $(arrow_right).length) {
            for (let i = 0; i < slides_number; i++) {
              var dot = document.createElement("button");
              dot.setAttribute("aria-label", keenSliderAriaLabel);
              dot.classList.add("keen-dot");
              dot.dataset.move = j;
              dots_wrapper[0].appendChild(dot);
              dot.addEventListener("click", function () {
                instance.moveToSlide(this.dataset.move);
              });
              if (j + slides_moves < elements.length) {
                j = j + slides_moves;
              }
            }


            updateKeenDots(instance, dots_wrapper);
          }
        },

        slideChanged(instance) {
          if ($(arrow_left).length) {
            0 == instance.details().absoluteSlide
              ? arrow_left.classList.add("keen-disabled")
              : arrow_left.classList.remove("keen-disabled");

            Math.round(instance.details().size - slides_per_view) == instance.details().absoluteSlide
              ? arrow_right.classList.add("keen-disabled")
              : arrow_right.classList.remove("keen-disabled");

            updateKeenDots(instance, dots_wrapper);
          }
        },
      }));
    }
  });
}


/**
 * Custom thumbnail grid - show all blocks
 *
 * @returns {void}
 */
function gridThumbnailCustomShowAll () {
  var gridThumbnailShowAllButton = '.grid-thumbnail-view-all__button',
    gridThumbnailShowAll = '.grid-thumbnail-view-all',
    gridThumbnailCustomBlock = '.grid-thumbnail-custom__block',
    gridThumbnailCustomBlockHidden = 'grid-thumbnail-custom__block--hidden';


  $(gridThumbnailShowAllButton).click(function (e) {
    e.preventDefault();

    $(this)
      .closest(gridThumbnailShowAll)
      .hide();

    $(gridThumbnailCustomBlock).removeClass(gridThumbnailCustomBlockHidden);
  });
}

/**
 * Show the total number of guests and pets in input placeholder
 *
 * @return {void}
 */
function updateGuestsPetsPlaceholder () {
  var quantity_button = '.qtyButtons',
    placeholder_guests = '.input-placeholder-guests',
    placeholder_pets = '.input-placeholder-pets';

  $(placeholder_guests).text(parseInt($(quantity_button).find('input#adults_num').val()) + parseInt($(quantity_button).find('input#children_num').val()));
  $(placeholder_pets).text($('input#pets_num').val());
}

function quantityButtonLoadCheck () {
  var $quantityButtons = $('.qtyButtons'),
    quantityValue = '.qtyInput',
    quantityButtonLower = '.qtyDec',
    quantityButtonHigher = '.qtyInc',
    quantityButtonDisabled = 'qtyButton--disabled';

  setTimeout(function () {
    $quantityButtons.each(function () {
      var $this = $(this),
        $quantityValue = Number($this.children(quantityValue).val()),
        $quantityLower = $this.children(quantityButtonLower),
        $quantityHigher = $this.children(quantityButtonHigher),
        $quantityMin = $this.children(quantityButtonLower).data('min'),
        $quantityMax = $this.children(quantityButtonHigher).data('max');

      if ($quantityValue === $quantityMin) {
        $quantityLower.addClass(quantityButtonDisabled);
      }

      if ($quantityValue === $quantityMax) {
        $quantityHigher.addClass(quantityButtonDisabled);
      }
    });
  }, 100)
}

/**
 * Update quantity buttons
 *
 * @returns {void}
 */
function quantityButtonUpdate () {
  $('body').on('click', '.qtyDec, .qtyInc', function () {
    var $button = $(this),
      oldValue = $button.parent().find('input').val(),
      adults_field_value = $('#adults_num').val(),
      adults_qty_persons_inc = 'qtyInc--persons',
      adults_qty_persons_dec = 'qtyDec--persons',
      childrens_field_value = $('#children_num').val(),
      max_house_unit = $('.input-persons-placeholder').data('max-persons'),
      $quantityMax = $button.data('max'),
      $quantityMin = $button.data('min'),
      quantityButtonDisabled = 'qtyButton--disabled',
      newVal;

    if ($button.hasClass('qtyInc')) {

      newVal = parseFloat(oldValue) + 1;
      $button.parent().children('.qtyDec').removeClass(quantityButtonDisabled);

      if (newVal == $quantityMax) {
        $button.addClass(quantityButtonDisabled);
      }

      if ((parseInt(adults_field_value) + parseInt(childrens_field_value)) === max_house_unit && $(this).hasClass(adults_qty_persons_inc)) {
        $('.' + adults_qty_persons_inc).addClass(quantityButtonDisabled);
        return;
      }

    }
    if ($button.hasClass('qtyDec')) {

      if (oldValue > 1) {
        newVal = parseFloat(oldValue) - 1;
      } else {
        $button.addClass(quantityButtonDisabled);
        newVal = 0;
      }

      if (newVal == $quantityMin) {
        $button.addClass(quantityButtonDisabled);
      }

      if($button.hasClass(adults_qty_persons_dec)) {
        $('.' + adults_qty_persons_inc).removeClass(quantityButtonDisabled);
      }

      if (newVal != $quantityMax) {
        $button.parent().children('.qtyInc').removeClass(quantityButtonDisabled);
      }
    }

    $button
      .parent()
      .find('input')
      .val(newVal);

    if (!$button.hasClass('qtyInc--inquiry') && !$button.hasClass('qtyDec--inquiry')) {
      updateGuestsPetsPlaceholder();
    }
  });
}

/**
 * Loader component for button
 *
 * return {@void}
 */
function appendButtonLoaderOnFilterButton () {
  var buttonAppendLoaderClass = '.panel-apply',
    buttonLoaderContent = $('<span />', {
      class: 'button-loader'
    }),
    buttonLoader = '.button-loader',
    buttonLoaderClassDisabled = 'button-disabled';

  if ($(buttonAppendLoaderClass).length > 0) {
    $(buttonAppendLoaderClass)
      .wrapInner('<span class="button-hidden-content" />')
      .prepend(buttonLoaderContent)
      .addClass(buttonLoaderClassDisabled)
      .find(buttonLoader).fadeIn(350);

  }
}

/**
 * Loader component for button
 * remove the loader state from button
 *
 * @return {void}
 */
function removeButtonLoaderOnFilterButton () {
  var buttonLoaderClass = 'panel-apply',
    buttonAppendLoaderClass = 'button-hidden-content',
    buttonLoaderClassDisabled = 'button-disabled';

  $('.' + buttonLoaderClass)
    .removeClass(buttonLoaderClass + ', ' + buttonLoaderClassDisabled)
    .children('.button-loader')
    .remove();

  $('.' + buttonAppendLoaderClass)
    .contents()
    .unwrap();
}

/**
 * Loader component for button
 *
 * return {@void}
 */
function appendButtonLoader () {
  var buttonAppendLoaderClass = '.button--append-loader',
    buttonLoaderContent = $('<span />', {
      class: 'button-loader'
    }),
    buttonLoader = '.button-loader',
    buttonLoaderClassDisabled = 'button-disabled';

  if ($(buttonAppendLoaderClass).length > 0) {
    $(buttonAppendLoaderClass)
      .wrapInner('<span class="button-hidden-content" />')
      .prepend(buttonLoaderContent)
      .addClass(buttonLoaderClassDisabled)
      .find(buttonLoader).fadeIn(350);

  }
}

/**
 * Loader component for button
 *
 * return {@void}
 */
function addButtonLoader () {
  var buttonLoaderClass = '.button--loader',
    buttonLoader = '.button-loader',
    buttonLoaderClassDisabled = 'button-disabled',
    buttonLoaderContent = $('<span />', {
      class: 'button-loader'
    });

  if ($(buttonLoaderClass).length > 0) {
    $(buttonLoaderClass).on('click', function (e) {
      $(e.currentTarget)
        .wrapInner('<span class="button-hidden-content" />')
        .prepend(buttonLoaderContent)
        .addClass(buttonLoaderClassDisabled)
        .find(buttonLoader).fadeIn(350);
    });
  }
}

/**
 * Loader component for button
 * remove the loader state from button
 *
 * @return {void}
 */
function removeButtonLoader () {
  var buttonLoaderClass = 'button--loader',
    buttonAppendLoaderClass = 'button-hidden-content',
    buttonLoaderClassDisabled = 'button-disabled';

  $('.' + buttonLoaderClass)
    .removeClass(buttonLoaderClass + ', ' + buttonLoaderClassDisabled)
    .children('.button-loader')
    .remove();

  $('.' + buttonAppendLoaderClass)
    .contents()
    .unwrap();
}

/**
 * Show loader on hubspot form submit
 *
 * @return {void}
 */
function formSubmitLoader () {
  var inquiry_form = '.inquiry-modal .inquiry-form .hubspot-form-submit',
    inline_form = '.hubspot-inline-form .hubspot-form-submit',
    loader_form = '<div class="form-loader"></div>';

  $(inquiry_form).on('click', function () {
    var loader_container = $(this).closest('.inquiry-form');

    setTimeout(function () {
      if (loader_container.find('input').hasClass('has-error')) {
        return;
      }

      $(loader_container).append(loader_form);
      $('.form-loader').fadeIn(250);
    }, 150);
  });

  $(inline_form).on('click', function () {
    var loader_container = $(this).closest('.hubspot-inline-form');

    setTimeout(function () {
      if (loader_container.find('input').hasClass('has-error')) {
        return;
      }

      $(loader_container).append(loader_form);
      $('.form-loader').fadeIn(250);
    }, 150);
  });
}

/**
 * Scroll to thank you message on mobile
 *
 * @return {void}
 */
function scrollToThankYou () {
  var modal_wrapper = '.mfp-wrap';

  if ($(modal_wrapper).length > 0 && $(window).width() < 768) {
    $('html, body').animate({
      scrollTop: parseInt($(modal_wrapper).css('top'), 10)
    }, 850);
  }
}

/**
 * Open cookie settings modal
 *
 * @returns {void}
 */
function showCookieSettings() {
  var cookieSettingsAnchor = '.cookie-settings';

  $(cookieSettingsAnchor).click(function (e) {
    e.preventDefault();
    UC_UI.showSecondLayer();
  });
}

function languageSwitcherSelector () {
  const openedClass = 'header-navigation__language--opened';

  $('.header-navigation__item-anchor--language').on('click', function () {
    if ($(window).scrollTop() >= $('.header-container').height() * 2) {
      $('.header-navigation__language').eq(1).toggleClass(openedClass);
    }else {
      $('.header-navigation__language').eq(0).toggleClass(openedClass);
    }
  });

  $('body').on('click', handleClickOutsideLanguageSwitcher);
  $(window).on('scroll', function () {
    $('.header-navigation__language').eq(0).removeClass(openedClass);
    $('.header-navigation__language').eq(1).removeClass(openedClass);
  })
}

/**
 * No action on disabled button
 *
 * @return {void}
 */
function disabledButton () {
  $('body').on('click', '.button--disabled', function (e) {
    e.preventDefault();
    return false;
  });
}

function handleClickOutsideLanguageSwitcher(e) {
  const target = $(e.target);
  // Check if the clicked element or any of its parents is not within .header-navigation__item-anchor--language
  if (!target.closest('.header-navigation__item-anchor--language').length) {
    // If clicked outside, remove the opened class
    $('.header-navigation__language').removeClass('header-navigation__language--opened');
  }
}

/**
 * Init select2
 *
 * @return {void}
 */
function initializeSelect2 () {
  var tagsSelector = '.tagsSelectpicker';

  if ($(tagsSelector).length > 0) {
    $(tagsSelector).select2({
      width: '100%',
      tags: false
    });
  }
}


/**
 * Select2 on select trigger
 *
 * @return {void}
 */
function select2OnSelect () {
  var selectpicker = '.selectpicker',
    select_active = 'select2-container-active';

  $(selectpicker).on('select2:select', function () {
    if($('#max_latitude').length) {
      $('#max_latitude').val('');
    }

    if($('#min_latitude').length) {
      $('#min_latitude').val('');
    }

    if($('#max_longitude').length) {
      $('#max_longitude').val('');
    }

    if($('#min_longitude').length) {
      $('#min_longitude').val('');
    }

    if (defaultDatePickerRange) {
      setTimeout(function () {
        defaultDatePickerRange.open();
      }, 50);
    }

    setTimeout(function () {
      $('.' + select_active).removeClass(select_active);
      $(':focus').blur();
    }, 1);
  });


  $('.select2-selection__rendered').on('click', function() {
    if ($('.select2-selection__choice').length) {
      $('.selectpicker').val(null).trigger('change');
    }
  });
}

/**
 * Initialize Light Gallery
 *
 * return {void}
 */
function lightGalleryInit () {
  var gallery_container = '.mfp-gallery-container';

  $(gallery_container).each(function () {
    $(this).lightGallery({
      selector: '.mfp-gallery',
      thumbnail: true,
      exThumbImage: 'data-exthumbimage',
      download: false,
      mode: 'lg-fade',
      speed: 100,
      thumbWidth: 120,
      cssEasing: 'linear',
      getCaptionFromTitleOrAlt: false
    });
  });

  $('.listing-floorplan').lightGallery({
    selector: 'div.mfp-image',
    addClass: 'floorplan-gallery',
    thumbnail: false,
    download: false,
    mode: 'lg-fade',
    speed: 100,
    cssEasing: 'linear',
    getCaptionFromTitleOrAlt: false,
    enableDrag: false,
    enableSwipe: false
  });
}

/**
 * Initialize Magnific Popup
 *
 * return {void}
 */
function magnificPopupInit () {
  var popup_zoom = '.popup-with-zoom-anim',
    popup_close = '.mfp-close',
    popup_container = '.zoom-anim-dialog';

  if ($(popup_zoom).length) {
    $(popup_zoom).on('click', function() {
      setTimeout(function () {
        enableScrollLock('modal');
      }, 150);
    });

    $('body').on('click', popup_close, function() {
      if ($('body').hasClass(body_class_general_opened)) {
        disableScrollLock('modal');
      }
    });

    $(document).click(function (e) {
      if ($('body').hasClass(body_class_general_opened) &&
        !$(popup_container).is(e.target) &&
        $(popup_container).has(e.target).length === 0) {
        disableScrollLock('modal');
      }
    });

    $(popup_zoom).magnificPopup({
      type: 'inline',
      fixedContentPos: false,
      fixedBgPos: true,
      overflowY: 'auto',
      closeBtnInside: true,
      preloader: false,
      midClick: true,
      removalDelay: 300,
      mainClass: 'my-mfp-zoom-in',
      callbacks: {
        close: function() {
          disableScrollLock('modal');
        }}
    });
  }
}


/**
 * Initialize flatpickr inquiry calendar
 *
 * return {void}
 */
function flatpickrCalendarsInquiry () {
  var monthToShow = 2,
    calendarInlined = false,
    arrive = getQueryString('arrive') !== 'undefined' && getQueryString('arrive') !== '' ? getQueryString('arrive') : '',
    departure = getQueryString('departure') !== 'undefined' && getQueryString('departure') !== '' ? getQueryString('departure') : '';

  if ($('#booking-date-arrival').val() !== '') {
    arrive = $('#booking-date-arrival').val();
  }

  if ($('#booking-date-departure').val() !== '') {
    departure = $('#booking-date-departure').val();
  }

  if (wi < 992) {
    monthToShow = 1;
    calendarInlined = false;
  }

  inquiryDatePickerRange = flatpickr('.flatpickr-range-inquiry', {
    altInputClass: 'booking-date-range',
    showMonths: monthToShow,
    mode: 'range',
    animate: false,
    minDate: 'today',
    altInput: true,
    altFormat: 'd.m.Y',
    dateFormat: 'd.m.Y',
    defaultDate: [arrive, departure],
    disableMobile: true,
    static: true,
    inline: calendarInlined,
    allowInput: false,
    locale: {
      ...calendarLang,
      rangeSeparator: ' - ',
    },
    
    onReady: function(selectedDates, dateStr, instance) {
      instance.calendarContainer.classList.add('inquiry-calendar');
    },

    onChange: function (selectedDates) {
      // Choose departure date
      if (selectedDates.length === 2) {
        var booking_arrival_date = flatpickr.formatDate(selectedDates[0], 'd.m.Y'),
          booking_departure_date = flatpickr.formatDate(selectedDates[1], 'd.m.Y');

        $('#booking-date-arrival').val(booking_arrival_date);
        $('#booking-date-departure').val(booking_departure_date);
      }
    },
  });
}


let IS_DEFAULT_DATEPICKER_OPENED = false;

/**
 * Initialize flatpickr Calendars
 *
 * return {void}
 */
function flatpickrCalendars () {
  var monthToShow = 2,
    calendarInlined = false,
    panel_persons_active = '.persons.panel-dropdown--input-open.active',
    panel_persons = '.persons.panel-dropdown--input-open .input-persons-placeholder',
    arrive = getQueryString('arrive') !== 'undefined' && getQueryString('arrive') !== '' ? getQueryString('arrive') : '',
    departure = getQueryString('departure') !== 'undefined' && getQueryString('departure') !== '' ? getQueryString('departure') : '',
    arrival_input_value = $('#booking-date-arrival').val(),
    departure_input_value = $('#booking-date-departure').val();

  if (arrive === '' || arrive === null) {
    if (arrival_input_value !== 'undefined') {
      arrive = arrival_input_value;
    }
  }

  if (departure === '' || departure === null) {
    if (departure_input_value !== 'undefined') {
      departure = departure_input_value;
    }
  }

  if (wi < 992) {
    monthToShow = 1;
    calendarInlined = false;
  }

  /**
   * Default flatpickr calendar
   *
   * @return {void}
   */
  defaultDatePicker = flatpickr('.flatpickr', {
    altInputClass: 'default-mainpicker',
    showMonths: 1,
    animate: false,
    minDate: 'today',
    altInput: true,
    altFormat: 'd.m.Y',
    dateFormat: 'd.m.Y',
    disableMobile: true,
    static: true,
    locale: {
      ...calendarLang,
      rangeSeparator: ' - ',
    },
    onChange: function (selectedDates) {

      // Used on callUsBackForm
      if ($('#callusback-date-time').length > 0) {
        $('#callusback-date-time').val(flatpickr.formatDate(selectedDates[0], 'd.m.Y'));
      }
    },
  });

  /**
   * Default flatpickr range calendar
   *
   * @return {void}
   */
  defaultDatePickerRange = flatpickr('.flatpickr-range', {
    altInputClass: 'booking-date-ranger',
    showMonths: monthToShow,
    mode: 'range',
    animate: false,
    minDate: 'today',
    altInput: true,
    altFormat: 'd.m.Y',
    dateFormat: 'd.m.Y',
    defaultDate: [arrive, departure],
    disableMobile: true,
    static: true,
    locale: {
      ...calendarLang,
      rangeSeparator: ' - ',
    },
    inline: calendarInlined,
    allowInput: false,
    closeOnSelect: false,
    clickOpens: false,
    onDayCreate: function (dObj, dStr, fp, dayElem) {
      // add custom attribute with date value month date and year 
      const day = dayElem.dateObj.getDate();
      const month = dayElem.dateObj.getMonth() + 1;
      const year = dayElem.dateObj.getFullYear();
      const customDateValue = `${month}-${day}-${year}`;
      dayElem.setAttribute('data-date', customDateValue);

    },
    onReady: function (selectedDates, dateStr, instance) {
      const altInputElem = instance.altInput;
      instance.calendarContainer.classList.add('flatpickr-calendar--flexible-dates');
      altInputElem.setAttribute('aria-disabled', 'true');
      if (instance.element.className.includes('flatpickr-range--custom-note') && (typeof possibleCheckInOut !== 'undefined')) {
        $(instance.calendarContainer)
        .append("<div class='flatpickr-content-after'>" +
          "<div class='flatpickr-content-after__left flatpickr-content-after__left--information'>" + possibleCheckInOut + "</div>" +
          "</div>");
        }
      flexibleDatesInit('.flatpickr-calendar', '.flatpickr-calendar');
      if(selectedDates.length === 2) {
        handleDateValuesInBottomAction(selectedDates, 'show');
      }
    },
    onChange: function (selectedDates) {

      //Clear selection
      if (selectedDates.length === 0) {
        $('#booking-date-arrival').val('');
        $('#booking-date-departure').val('');
      }

      // Choose arrival date
      if (selectedDates.length === 1) {
        $('.search-block__label--dates-box--flexible').remove();
      }

      // Choose departure date
      if (selectedDates.length === 2) {
        var booking_arrival_date = flatpickr.formatDate(selectedDates[0], 'd.m.Y'),
          booking_departure_date = flatpickr.formatDate(selectedDates[1], 'd.m.Y');
        highlightFlexibleDates(selectedDates[0], selectedDates[1], parseInt($(`.flatpickr-calendar--flexible-dates .toggle-calendar-btn-flexible-day.active`).data('value'), 10));
        handleDateValuesInBottomAction(selectedDates, 'show');
        $('#booking-date-arrival').val(booking_arrival_date);
        $('#booking-date-departure').val(booking_departure_date);

        if (defaultDatePickerRange.length === 1) {
          defaultDatePickerRange.setDate([selectedDates[0], selectedDates[1]], false);
        }

        if (defaultDatePickerRange.length > 1) {
          $.each(defaultDatePickerRange, function (index, value) {
            value.setDate([selectedDates[0], selectedDates[1]], false, 'd.m.Y');
          });
        }

        if (inquiryDatePickerRange.length === 1) {
          inquiryDatePickerRange.setDate([selectedDates[0], selectedDates[1]], false);
        }

        if (inquiryDatePickerRange.length > 1) {
          $.each(inquiryDatePickerRange, function (index, value) {
            value.setDate([selectedDates[0], selectedDates[1]], false, 'd.m.Y');
          });
        }

        if ($(panel_persons_active).length) {
          $(panel_persons).trigger('click');
          trigger_dropdown = true;
        }
        let days = parseInt($(`.flatpickr-calendar--flexible-dates .toggle-calendar-btn-flexible-day.active`).data('value'), 10);
        let flexibleDayClass = $('.search-listing-block__item').length > 0 && 'flexible-listing-page';
        $('.search-block__label--dates-box--flexible').remove();
        if(days > 0 && $('.flatpickr-input').val().length > 10) {
          $('.flatpickr-wrapper').append(`<div class="search-block__label--dates-box--flexible ${flexibleDayClass}">(±${days})</div>`);
        }
        // find flatpickr wrapper inside .inquiry-form.hubspot-form and delete flexi indicator
        $('.inquiry-form.hubspot-form').find('.flatpickr-wrapper').each(function() {
          $(this).find('.search-block__label--dates-box--flexible').remove();
        });
      }
    },
    onOpen: function (selectedDates, dateStr, instance) {
      if($('#frm-homepageSearchForm').length === 1) {
        if(window.innerWidth < 992) {
          $('main-search-input').animate({
            scrollTop: $('.flatpickr-calendar').offset().top - 200
          }, 500);
        }else {
          $('html, body').animate({
            scrollTop: $('.flatpickr-calendar').offset().top - 200
          }, 500);
        }
      }
      if(selectedDates.length === 2) {
        highlightFlexibleDates(selectedDates[0], selectedDates[1], parseInt($(`.toggle-calendar-btn-flexible-day.active`).data('value'), 10));
        handleDateValuesInBottomAction(selectedDates, 'show');
      }
      if(window.innerWidth < 992 && $('.search-listing-block__item--calendar').length == 0) {
        $('.main-search-input--overlayed').animate({
          scrollTop: $('.flatpickr-calendar--flexible-dates').offset().top
        }, 500);

        $('body').addClass('overflow-hidden');
      }
      IS_DEFAULT_DATEPICKER_OPENED = true;
    },
    onMonthChange: function (selectedDates, dateStr, instance) {
      highlightFlexibleDates(selectedDates[0], selectedDates[1], parseInt($('.toggle-calendar-btn-flexible-day.active').data('value'), 10));
    },
    onClose: function (selectedDates, dateStr, instance) {
      $('body').removeClass('overflow-hidden');
    }
  });
  $('.flatpickr-range').on("click", () => {
    if (defaultDatePickerRange.isOpen) {
      defaultDatePickerRange.close();
      IS_DEFAULT_DATEPICKER_OPENED = false;
    } else {
      defaultDatePickerRange.open();
      IS_DEFAULT_DATEPICKER_OPENED = true;
    }
  });
}

// Utility to check if the click is on the scrollbar
function isClickOnScrollbar(e) {
  const isVerticalScrollbar = e.clientX >= document.documentElement.clientWidth;
  const isHorizontalScrollbar = e.clientY >= document.documentElement.clientHeight;
  return isVerticalScrollbar || isHorizontalScrollbar;
}

$(document).on("mousedown", function (e) {
  if (IS_DEFAULT_DATEPICKER_OPENED && $('#frm-homepageSearchForm').length === 0) {
      const isOutsideClick = !$(e.target).closest('.flatpickr-calendar').length;
      const isNotScrollbarClick = !isClickOnScrollbar(e);

      if (isOutsideClick && isNotScrollbarClick) {
          IS_DEFAULT_DATEPICKER_OPENED = false;
      }else {
        if(!$(e.target).hasClass('btn-calendar-confirm')) {
          IS_DEFAULT_DATEPICKER_OPENED = true;
          setTimeout(() => {
            defaultDatePickerRange.open();
          }, 0);
        }
      }
  }
});


window.addEventListener('scroll', function () {
  if (IS_DEFAULT_DATEPICKER_OPENED && $('#frm-homepageSearchForm').length === 0) {
    defaultDatePickerRange.open();
  }
}, true);

function handleDateValuesInBottomAction(selectedDates, action) {
  if(action === 'show') {
    $('.calendar-bottom-action__bottom-number-of-nights-wrapper').removeClass('visible-0');
    $('.calendar-bottom-action__bottom-number-of-nights > span').html(selectedDates.length === 2 ? Math.round((selectedDates[1] - selectedDates[0]) / (1000 * 60 * 60 * 24)) : '');
    // now on calendar-bottom-action__bottom-number-of-nights-extended-info in this format Settembre 12 - Settembre 17, 2023
    if(selectedDates.length === 2) {
      // take first date and read aria-label from this
      const firstDate = new Date(selectedDates[0]);
      const firstDateAriaLabel = `${firstDate.toLocaleDateString($language, { month: 'long' })} ${firstDate.getDate()}`
      // take second date and read aria-label from this
      const secondDate = new Date(selectedDates[1]);
      const secondDateAriaLabel = `${secondDate.toLocaleDateString($language, { month: 'long' })} ${secondDate.getDate()}, ${secondDate.getFullYear()}`;
      // set text to calendar-bottom-action__bottom-number-of-nights-extended-info
      $('.calendar-bottom-action__bottom-number-of-nights-extended-info').html(`${firstDateAriaLabel} - ${secondDateAriaLabel}`);
    }
  } else {
    $('.calendar-bottom-action__bottom-number-of-nights-wrapper').addClass('visible-0');
  }

}

function flexibleDatesInit(calendarWrapper, calendarDivWrapper) {
  const toggleCalendarMainWrapper = document.querySelector('.toggle-calendar-main-wrapper');
  const flexibleWrapper = document.querySelector('.flexible-wrapper');
  const flatpickrCalendarWrapper = document.querySelector(calendarWrapper);
  const calendarDiv = document.querySelector(calendarDivWrapper);
  const calendarBottomAction = document.querySelector('.calendar-bottom-action');
  
  if(flexibleWrapper) {
    calendarDiv.insertBefore(flexibleWrapper, calendarDiv.firstChild);
    flexibleWrapper.style.display = '';
  }
  
  if (toggleCalendarMainWrapper) {
    toggleCalendarMainWrapper.classList.remove('d-none');
    if (calendarDiv) {
      calendarDiv.insertBefore(toggleCalendarMainWrapper, calendarDiv.firstChild);
      toggleCalendarMainWrapper.style.display = '';
    }
  }
  // add calendarBottomAction to the bottom of the calendar
  if (calendarBottomAction) {
    calendarDiv.appendChild(calendarBottomAction);
    calendarBottomAction.classList.remove('d-none');
    calendarBottomAction.style.display = '';
  }

  // general event listener for toggle active class
  $('.toggle-calendar-btn').on('click', function (e) {
    e.preventDefault();
    $(this).parent().find('.toggle-calendar-btn').removeClass('active');
    $(this).addClass('active');
  });

  // event listener for fixed dates / flexible dates toggle
  $('.toggle-calendar-main-btn').on('click', function (e) {
    e.preventDefault();
    if($(this).data('value') === 'flexible') {
      $('.flexible-wrapper').removeClass('d-none');
      $('.flatpickr-months').addClass('d-none');
      $(flatpickrCalendarWrapper).addClass('flatpickr-calendar-flexible-dates');
      $('.flatpickr-innerContainer').addClass('d-none');
      $('.calendar-bottom-action').addClass('d-none');
      $('.calendar-bottom-action__bottom-number-of-nights-wrapper').addClass('d-none');
      $('.flexible-months-bottom-left--mobile').removeClass('d-none');
    }else {
      $('.flexible-wrapper').addClass('d-none');
      $(flatpickrCalendarWrapper).removeClass('flatpickr-calendar-flexible-dates');
      $('.flatpickr-months').removeClass('d-none');
      $('.flatpickr-innerContainer').removeClass('d-none');
      $('.calendar-bottom-action').removeClass('d-none');
      $('.calendar-bottom-action__bottom-number-of-nights-wrapper').removeClass('d-none');
      $('.flexible-months-bottom-left--mobile').addClass('d-none');
    }
    $('#date_type').val($(this).data('value'));
  });

  // Event listener for flexible date options
  $('.toggle-calendar-btn-flexible-day').on('click', function() {
    const days = parseInt($(this).data('value'), 10); // Get number of days from data attribute

    highlightFlexibleDates(defaultDatePickerRange.selectedDates[0], defaultDatePickerRange.selectedDates[1], days);

    $('.search-block__label--dates-box--flexible').remove();
    let flexibleDayClass = $('.search-listing-block__item').length > 0 && 'flexible-listing-page';
    // find if this flatpickr-wrapper parent has class .inquiry-form.hubspot-form
    if(days > 0 && $('.flatpickr-input').val().length > 10) {
      $('.flatpickr-wrapper').append(`<div class="search-block__label--dates-box--flexible ${flexibleDayClass}">(±${days})</div>`);
    }
    $('#date_tolerance').val(days);

    // find flatpickr wrapper inside .inquiry-form.hubspot-form and delete flexi indicator
    $('.inquiry-form.hubspot-form').find('.flatpickr-wrapper').each(function() {
      $(this).find('.search-block__label--dates-box--flexible').remove();
    });
  });

  // event listener for flexible weeks/weekends (months list)
  $('.flexible-month-label').on('click', function() {
    if(!$(this).hasClass('disabled')) {
      const month = $(this).data('month');
      const year = $(this).data('year');

      // if month is already selected, remove it from the array
      const index = FLEXIBLE_MONTHS.findIndex(item => item.month === month && item.year === year);
      if(index > -1) {
        FLEXIBLE_MONTHS.splice(index, 1);
        $(this).removeClass('active');
      } else {
        FLEXIBLE_MONTHS.push({ month, year });
        $(this).addClass('active');
      }


      if(FLEXIBLE_MONTHS.length > 2) {
        // disable all elements that dont have active class
        $('.flexible-month-label').each(function() {
          if(!$(this).hasClass('active')) {
            $(this).addClass('disabled');
          }
        });
      }else {
        $('.flexible-month-label').removeClass('disabled');
      }

      if(FLEXIBLE_MONTHS.length > 0) {
        $('.flexible-months-bottom-left').removeClass('visible-0');
        // list all selected months, and find word for them and put in flexible-months-bottom-extended-info-months
        const months = FLEXIBLE_MONTHS.map(item => {
          return `${new Date(item.year, monthNames[item.month] ).toLocaleString($language, { month: 'long' })}`;
        });
        $('.flexible-months-bottom-extended-info-months').html(months.join(', '));
      }else {
        $('.flexible-months-bottom-left').addClass('visible-0');
      }
      const jsonString = JSON.stringify(FLEXIBLE_MONTHS);
      $('#flexible_months').val(encodeURIComponent(jsonString));
    }
  });


  // event handler for flexible months buttons (weekends/weeks)
  $('.toggle-week-weekend-btn').on('click', function() {
    const label = '.flexible-months-bottom-extended-info-months-label'
    const value = $(this).data('value');
    if(value === 'weekend') {
      $('.flexible-months-bottom-main-label-number-of-nights').html('2');
      $(label).html($(label).data('weekend'));
    }else {
      $('.flexible-months-bottom-main-label-number-of-nights').html('7');
      $(label).html($(label).data('week'));
    }
    $('#flexible_type').val(value);
  });

  $('.btn-calendar-confirm').on('click', function(e) {
    e.preventDefault();
    // if rangeCalendar is defined, we are on the main page
    if(typeof defaultDatePickerRange !== 'undefined' && defaultDatePickerRange) {
      $('.main-search-input-item--persons > input').trigger('click');
      defaultDatePickerRange.close();
      IS_DEFAULT_DATEPICKER_OPENED = false;
    }
  });

  
  // init values
  setTimeout(() => {
      const dateType = $('#date_type').val();
      const toggleCalendarMainBtn = document.querySelector(`.toggle-calendar-main-btn[data-value="${dateType}"]`);
      if(toggleCalendarMainBtn) {
        toggleCalendarMainBtn.click();
      }
    
      const dateTolerance = $('#date_tolerance').val();
      const toggleCalendarBtnFlexibleDay = document.querySelector(`.toggle-calendar-btn-flexible-day[data-value="${dateTolerance}"]`);
      if(toggleCalendarBtnFlexibleDay) {
        toggleCalendarBtnFlexibleDay.click();
      }

      const flexibleType = $('#flexible_type').val();
      const toggleWeekWeekendBtn = document.querySelector(`.toggle-week-weekend-btn[data-value="${flexibleType}"]`);
      if(toggleWeekWeekendBtn) {
        toggleWeekWeekendBtn.click();
      }


      function getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
      }
    
      // Get the encoded JSON string from the URL
      const encodedJsonString = getQueryParam('flexible_months');
    
    // Decode and parse the JSON string
      if (encodedJsonString) {
        const jsonString = decodeURIComponent(encodedJsonString);
        try {
            const flexibleMonthsArray = JSON.parse(jsonString);
            flexibleMonthsArray.forEach(item => {
              const month = item.month;
              const year = item.year;
              const monthLabel = document.querySelector(`.flexible-month-label[data-month="${month}"][data-year="${year}"]`);
              if(monthLabel) {
                monthLabel.click();
              }
            });
            FLEXIBLE_MONTHS = flexibleMonthsArray;
        } catch (error) {
            // Handle the error
        }
      }
  }, 0);

}


// Function to highlight flexible dates
function highlightFlexibleDates(selectedStartDate, selectedEndDate, days) {
  const startDate = new Date(selectedStartDate);
  const endDate = new Date(selectedEndDate);

  // Normalize start and end dates to midnight
  startDate.setHours(0, 0, 0, 0);
  endDate.setHours(0, 0, 0, 0);

  // Calculate before and after ranges
  const beforeStartDate = new Date(startDate.getTime() - days * 24 * 60 * 60 * 1000);
  const afterEndDate = new Date(endDate.getTime() + days * 24 * 60 * 60 * 1000);
// asd
  // Clear previous highlights
  $('.flatpickr-day').removeClass('flexible-date');

  // Highlight the dates before, within, and after the selected range
  if (days > 0) {
    $('.flatpickr-day').each(function () {
      let dayDate = new Date($(this).data('date'));

      // iOS-specific handling for dates
      if (isIOS()) {
        const dateStr = $(this).data('date');
        if (dateStr !== undefined) {
          const [month, day, year] = dateStr.split('-'); // Split the string by dashes
          dayDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`); // Reformat to "YYYY-MM-DD"
        }
      }

      // Normalize dayDate to midnight
      dayDate.setHours(0, 0, 0, 0);

      if (
        (dayDate >= beforeStartDate && dayDate < startDate) || 
        (dayDate > endDate && dayDate <= afterEndDate)
      ) {
        $(this).addClass('flexible-date');
      }
    });
  }
}

function toggleContent(isClickFromTranslation = false) {
  const expandableContent = document.getElementsByClassName('own-review-comment-text');
  const expandButton = document.getElementsByClassName('own-review-comment-read-more-btn');
  const NUMBER_OF_CHARACTERS = 180;

  for (let i = 0; i < expandableContent.length; i++) {
    const originalContent = expandableContent[i].getAttribute('data-comment');
    const trimmedContent = originalContent.slice(0, NUMBER_OF_CHARACTERS) + '...';

    if (originalContent.length > NUMBER_OF_CHARACTERS){
      expandableContent[i].textContent = trimmedContent;
      expandButton[i].classList.remove('d-none');
    } else {
      expandableContent[i].textContent = originalContent;
      expandButton[i].classList.add('d-none');
    }

    if( isClickFromTranslation && expandableContent[i].classList.contains('expanded-comment')) {
      if (originalContent.length > NUMBER_OF_CHARACTERS){
        expandButton[i].classList.remove('d-none');
        expandButton[i].textContent = expandButton[i].getAttribute('data-read-less');
      }
      expandableContent[i].textContent = originalContent;
    }else {
      expandableContent[i].classList.remove('expanded-comment');
      expandButton[i].textContent = expandButton[i].getAttribute('data-read-more');
    }

    // this is to prevent the event listener from being duplicated
    expandButton[i].replaceWith(expandButton[i].cloneNode(true));

    expandButton[i].addEventListener('click', () => {
      expandableContent[i].classList.toggle('expanded-comment');

      if (expandableContent[i].classList.contains('expanded-comment')) {
        expandableContent[i].textContent = originalContent;
        expandButton[i].textContent = expandButton[i].getAttribute('data-read-less');
      } else {
        expandableContent[i].textContent = trimmedContent;
        expandButton[i].textContent = expandButton[i].getAttribute('data-read-more');
      }
    });
  }
}


function toggleTranslate(element) {
  $(element).click(function(e) {
    e.preventDefault();
    const comment = $(this).parent().find('.own-review-comment-text');
      const isOriginal = comment.attr('data-isoriginal');
      const originalComment = comment.attr('data-original');
      const translatedComment = comment.attr('data-translated');
      const googleTranslateBlock = $(this).parent().find('.own-review-comment__translated-label');
      if(isOriginal == 'true') {
          comment.text(translatedComment);
          $(this).text($(this).attr('data-show-original'));
          $(googleTranslateBlock).removeClass('d-none');
          comment.attr('data-isoriginal', 'false');
          comment.attr('data-comment', translatedComment);
          toggleContent(true);
      } else {
          comment.text(originalComment);
          $(this).text($(this).attr('data-show-translation'));
          $(googleTranslateBlock).addClass('d-none');
          comment.attr('data-isoriginal', 'true');
          comment.attr('data-comment', originalComment);
          toggleContent(true);
      }
  });
}

function openReviewModal() {
  $('.own-review-all-reviews-anchor-js').click(function(e) {
      e.preventDefault();
      scrollLockOnReviewModal('lock')
      const id = $(this).data('id');
      const modalSelector = `.own-review-modal[data-for="${id}"]`;
      const overlaySelector = `.own-review-modal-overlay[data-for="${id}"]`;

      const modal = $(modalSelector);
      const overlay = $(overlaySelector);
      
      modal.removeClass('d-none').addClass('own-review-modal--opening');
      overlay.removeClass('d-none').addClass('own-review-modal-overlay--opening');
      
      setTimeout(() => {
          overlay.removeClass('own-review-modal-overlay--opening');   
          modal.removeClass('own-review-modal--opening');
      }, 301);
  });

  hideModalListener();
  document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
          if($('.own-review-collecting-modal').hasClass('d-none')) {
              hideModal(e);
          }
      }
  });
}

function hideModalListener()
{
  $('.own-review-modal-overlay, .own-review-modal-close-btn').click(function(e) {
      hideModal(e);
  });
}

function toggleReviewCollectingDataModal() {
  $('.review-about-collecting-modal-opener').click(function(e) {
      const modal = $('.own-review-collecting-modal');
      const overlay = $('.own-review-collecting-modal-overlay');

      overlay.removeClass('d-none').addClass('own-review-collecting-modal-overlay--opening');
      modal.removeClass('d-none').addClass('own-review-collecting-modal--opening');


      setTimeout(() => {
        overlay.removeClass('own-review-collecting-modal-overlay--opening');
        modal.removeClass('own-review-collecting-modal--opening');
      }, 201);

      if(!$('body').hasClass('review-modal-open')) {
          e.preventDefault();
          scrollLockOnReviewModal('lock');
      }
  });

  $('.own-review-collecting-modal-overlay, .own-review-collecting-modal-close-btn').click(function(e) {
      hideCollectingDataModal(e);
  });

  document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
          e.preventDefault();
          e.stopImmediatePropagation();
          hideCollectingDataModal(e);
      }
  });
}

function hideCollectingDataModal(e) {
  e.preventDefault();
  if($('.own-review-modal').hasClass('d-none')) {
     scrollLockOnReviewModal('unlock');
  }
  $('.own-review-collecting-modal-overlay').addClass('own-review-collecting-modal-overlay--closing');
  $('.own-review-collecting-modal').addClass('own-review-collecting-modal--opening--closing');
  setTimeout(() => {
    $('.own-review-collecting-modal-overlay').addClass('d-none').removeClass('own-review-collecting-modal-overlay--closing');
    $('.own-review-collecting-modal').addClass('d-none').removeClass('own-review-collecting-modal--opening--closing');
  }, 190);
}

var scrollDistanceReview = 0;

function scrollLockOnReviewModal(task) {
  if(task === 'lock') {
    scrollDistanceReview = $window.scrollTop();
    $('body')
    .css('top', scrollDistanceReview * -1)
    .addClass('review-modal-open');
    if(isIOS() && window.innerWidth > 1200) {
      $(body).css('padding-right', '15px');
    }
  }else if(task === 'unlock') {
    $('body').removeClass('review-modal-open')
    $window.scrollTop(scrollDistanceReview);
    if(isIOS() && window.innerWidth > 1200) {
      $(body).css('padding-right', '0');
    }
  }
}

function hideModal(e) {
  e.preventDefault();
  $('.own-review-modal').removeClass('own-review-modal--opening').addClass('own-review-modal--closing');
  $('.own-review-modal-overlay').removeClass('own-review-modal-overlay--opening').addClass('own-review-modal-overlay--closing');
  scrollLockOnReviewModal('unlock');
  setTimeout(() => {
      $('.own-review-modal-overlay').addClass('d-none').removeClass('own-review-modal-overlay--closing');   
      $('.own-review-modal').addClass('d-none').removeClass('own-review-modal--closing');
  }, 290);
}

function isIOS() {
  return /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent) && !window.MSStream;
}

jQuery(document).ready(function ($) {

  $('.more-filters').on('click', function (e) {
    e.preventDefault();

    if ($(this).parent().is('.active')) {
      close_panel_dropdown();
    } else {
      $(this).parent().addClass('active');
      if (wi < 768) {
        enableScrollLock();
      }
      activeModalSearchResults(true);
    }
  });

  // "All" checkbox
  $('.checkboxes.categories input').on('change', function() {
    if ($(this).hasClass('all')) {
      $(this).parents('.checkboxes').find('input').prop('checked', false);
      $(this).prop('checked', true);
    } else {
      $('.checkboxes input.all').prop('checked', false);
    }
  });


  /* Initialization of functions */
  searchBarActions();
  headerMainMenu();
  toggleStickybarMobile();
  magnificPopupInit();
  keenCarousel();
  keenGrid();
  showCookieSettings();
  languageSwitcherSelector();
  lightGalleryInit();
  flatpickrCalendars();
  flatpickrCalendarsInquiry();
  select2OnSelect();
  disabledButton();
  formSubmitLoader();
  actionButtonsFilters();
  addButtonLoader();
  modalFiltering();
  iconFilteredItems();
  faqAccordion();
  toggleContent();
  toggleTranslate('.own-review-comment__show-translation');
  openReviewModal();
  toggleReviewCollectingDataModal();
  if($('.evisitor-container-wrapper').length > 0) {
    eVisitor();
  }

  if (wi > 991) {
    headerBarSlide();
  }

  if (wi <= 1024) {
    mobileMenu();
  }

  if (wi > 1024) {
    backToTop();
  }

  initializeSelect2();
  quantityButtonLoadCheck();
  quantityButtonUpdate();
  gridThumbnailCustomShowAll();
  panelDropdown();

  if ($('.reevoo-badge--inject-css').length > 0 ) {
    reevooInjectCSS();
  }
});

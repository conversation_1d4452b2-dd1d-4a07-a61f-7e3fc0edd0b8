<?xml version="1.0" encoding="UTF-8" ?>
<booking test="true">
    <bookingid>
        <![CDATA[
7044384
]]>
    </bookingid>
    <propertyid>
        <![CDATA[
CIO223
]]>
    </propertyid>
    <custname>
        <![CDATA[
SKUNDRIC
]]>
    </custname>
    <street />
    <number>
        <![CDATA[
0
]]>
    </number>
    <floor />
    <location />
    <zip />
    <city />
    <custcountry>
        <![CDATA[
HR
]]>
    </custcountry>
    <phone />
    <email><EMAIL></email>
    <persons>2</persons>
    <children>
        <![CDATA[
0
]]>
    </children>
    <pets>
        <![CDATA[
0
]]>
    </pets>
    <arrival>20190420</arrival>
    <departure>20190427</departure>
    <season>
        <![CDATA[
2019
]]>
    </season>
    <apartment>
        <![CDATA[
1
]]>
    </apartment>
    <username>
        <![CDATA[
WEB
]]>
    </username>
    <subgroup />
    <booked>20190104</booked>
    <booktime>
        <![CDATA[
213301
]]>
    </booktime>
    <documentdate />
    <company>
        <![CDATA[
NOV
]]>
    </company>
    <language>
        <![CDATA[
191
]]>
    </language>
    <department>
        <![CDATA[
40
]]>
    </department>
    <salesmarket>
        <![CDATA[
191
]]>
    </salesmarket>
    <currency>
        <![CDATA[
HRK
]]>
    </currency>
    <price>
        <![CDATA[
2380
]]>
    </price>
    <payments>
        <paymentid>1</paymentid>
        <paymentdate>20190112</paymentdate>
        <amount>
            <![CDATA[
524
]]>
        </amount>
    </payments>
    <payments>
        <paymentid>2</paymentid>
        <paymentdate>20190309</paymentdate>
        <amount>
            <![CDATA[
1570
]]>
        </amount>
    </payments>
    <paid>
        <![CDATA[
0
]]>
    </paid>
    <cancellationpossible>true</cancellationpossible>
    <cancellationfee>
        <![CDATA[
0
]]>
    </cancellationfee>
    <commission>
        <![CDATA[
286
]]>
    </commission>
    <optiondate />
    <type>booking</type>
    <document>false</document>
    <external />
    <contactAddressLines>
        <addressLine>
            <![CDATA[
		Zoran Mihajlovic
		]]>
        </addressLine>
        <addressLine>
            <![CDATA[
		Banovceva 21
		]]>
        </addressLine>
        <addressLine>
            <![CDATA[
		HR-52100 Pula
		]]>
        </addressLine>
    </contactAddressLines>
    <conditionText>
        <conditionType>
            <![CDATA[
REN
]]>
        </conditionType>
        <text>
            <![CDATA[
Zavrsno ciscenje ukljuceno.
]]>
        </text>
    </conditionText>
    <conditionText>
        <conditionType>
            <![CDATA[
LIX
]]>
        </conditionType>
        <text>
            <![CDATA[
Posteljina i rucnici su ukljuceni
]]>
        </text>
    </conditionText>
    <routeInstruction>
        <keyAddressText1>
            <![CDATA[
Zoran Mihajlovic
]]>
        </keyAddressText1>
        <keyAddressText2>
            <![CDATA[
Marcana 525 D
]]>
        </keyAddressText2>
        <keyAddressZipCity>
            <![CDATA[
HR-52206 Marcana
]]>
        </keyAddressZipCity>
        <keyAddressExtraInformation>
            <![CDATA[
TEL 00385 989310510
]]>
        </keyAddressExtraInformation>
        <routeInstructionText>
            <![CDATA[
Ovo su GPS koordinate za Vas objekt za odmor: Latitude: 44.94464763352716 Longitude: 13.946432769298553
]]>
        </routeInstructionText>
        <geoCodeText>
            <![CDATA[
Ovo su GPS koordinate Vaseg objektaza odmor: Latitude:44.9446476, Longitude:13.9464327.
]]>
        </geoCodeText>
    </routeInstruction>
</booking>
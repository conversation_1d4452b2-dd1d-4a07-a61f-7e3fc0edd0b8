.sweet-overlay {
  background: rgba(41, 47, 51, 0.9); }

.sweet-alert {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  padding: 15px;
  padding-top: 55px;
  text-align: right;
  border-radius: 6px;
  box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.11), 0px 6px 30px rgba(0, 0, 0, 0.14); }
  .sweet-alert h2 {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    font-weight: 400;
    color: #8899a6;
    margin: 0;
    color: #66757f;
    border-bottom: 1px solid #e1e8ed; }
  .sweet-alert p {
    display: block;
    text-align: center;
    color: #66757f;
    font-weight: 400;
    font-size: 13px;
    margin-top: 7px; }
  .sweet-alert .sa-button-container {
    background-color: #f5f8fa;
    border-top: 1px solid #e1e8ed;
    box-shadow: 0px -1px 0px white;
    margin: -15px;
    margin-top: 0; }
  .sweet-alert[data-has-confirm-button=false][data-has-cancel-button=false] {
    padding-bottom: 10px; }
    .sweet-alert[data-has-confirm-button=false][data-has-cancel-button=false] .sa-button-container {
      display: none; }
  .sweet-alert button {
    border-radius: 2px;
    box-shadow: none !important;
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.3);
    margin: 17px 0px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 16px;
    position: relative; }
    .sweet-alert button:focus, .sweet-alert button.cancel:focus {
      box-shadow: none !important; }
      .sweet-alert button:focus::before, .sweet-alert button.cancel:focus::before {
        content: "";
        position: absolute;
        left: -5px;
        top: -5px;
        right: -5px;
        bottom: -5px;
        border: 2px solid #a5b0b4;
        border-radius: 8px; }
    .sweet-alert button.confirm {
      background-color: #55acee !important;
      background-image: linear-gradient(transparent, rgba(0, 0, 0, 0.05));
      -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#00000000, endColorstr=#0C000000)";
      border: 1px solid #3b88c3;
      box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15);
      margin-right: 15px; }
      .sweet-alert button.confirm:hover {
        background-color: #55acee;
        background-image: linear-gradient(transparent, rgba(0, 0, 0, 0.15));
        -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#00000000, endColorstr=#26000000)";
        border-color: #3b88c3; }
    .sweet-alert button.cancel {
      color: #66757e;
      background-color: #f5f8fa;
      background-image: linear-gradient(#fff, #f5f8fa);
      text-shadow: 0px -1px 0px white;
      margin-right: 9px;
      border: 1px solid #e1e8ed; }
      .sweet-alert button.cancel:hover, .sweet-alert button.cancel:focus:hover {
        background-color: #e1e8ed;
        background-image: linear-gradient(#fff, #e1e8ed);
        -ms-filter: "progid:DXImageTransform.Microsoft.gradient(enabled=false)";
        border-color: #e1e8ed; }
      .sweet-alert button.cancel:focus {
        background: #fff;
        border-color: #fff; }
  .sweet-alert .sa-icon:not(.sa-custom) {
    transform: scale(0.72);
    margin-bottom: -2px;
    margin-top: -10px; }
  .sweet-alert input {
    border: 1px solid #e1e8ed;
    border-radius: 3px;
    padding: 10px 7px;
    height: auto;
    box-shadow: none;
    font-size: 13px;
    margin: 10px 0; }
    .sweet-alert input:focus {
      border-color: #94A1A6;
      box-shadow: inset 0 0 0 1px rgba(77, 99, 107, 0.7); }
  .sweet-alert fieldset .sa-input-error {
    display: none; }
  .sweet-alert .sa-error-container {
    text-align: center;
    border: none;
    background-color: #fbedc0;
    margin-bottom: 6px; }
    .sweet-alert .sa-error-container.show {
      border: 1px solid #f0e1b9; }
    .sweet-alert .sa-error-container .icon {
      display: none; }
    .sweet-alert .sa-error-container p {
      color: #292f33;
      font-weight: 600;
      margin-top: 0; }

@-webkit-keyframes animateErrorIcon {
  0% {
    transform: rotateX(100deg), scale(0.5);
    -webkit-transform: rotateX(100deg), scale(0.5);
    opacity: 0; }

  100% {
    transform: rotateX(0deg), scale(0.5);
    -webkit-transform: rotateX(0deg), scale(0.5);
    opacity: 1; } }

@keyframes animateErrorIcon {
  0% {
    transform: rotateX(100deg), scale(0.5);
    -webkit-transform: rotateX(100deg), scale(0.5);
    opacity: 0; }

  100% {
    transform: rotateX(0deg), scale(0.5);
    -webkit-transform: rotateX(0deg), scale(0.5);
    opacity: 1; } }

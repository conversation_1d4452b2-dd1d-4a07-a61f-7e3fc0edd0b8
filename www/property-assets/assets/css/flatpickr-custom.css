/* Flatpickr */
.daterange-picker + input {
  display: none;
}

.flatpickr-hidden {
  display: none;
}

.flatpickr-day {
  line-height: 47px !important;
  height: 47px !important;
  max-width: 47px !important;
  border-radius: 0 !important;
}

.flatpickr-day.booked {
  color: #d4d4d4 !important;
  text-decoration: line-through;
  background-color: #f1f1f1;
}

.flatpickr-day.booked:hover {
  background-color: #f1f1f1;
}

.flatpickr-day.optional-arrival-bolded {
  font-weight: 600;
}

.flatpickr-day.optional-arrival.disabled.startRange {
  color: #404848 !important;
}

.flatpickr-day.only-saturday.disabled {
  cursor: pointer !important;
  background: #fff !important;
  color: #252525 !important;
}

.flatpickr-day.only-saturday.disabled:hover {
  background: #fff !important;
}

.flatpickr-day.only-saturday.disabled.inRange {
  background: #f3e8b3 !important;
}

.flatpickr-day.only-saturday.disabled.inRange:hover {
  color: #252525 !important;
}

.flatpickr-day.only-saturday.disabled.notAllowed,
.flatpickr-day.saturday.notAllowed,
.flatpickr-day.only-saturday.notAllowed:hover,
.flatpickr-day.saturday.notAllowed:hover,
.flatpickr-day.optional-arrival.notAllowed,
.flatpickr-day.optional-arrival.notAllower:hover {
  cursor: not-allowed !important;
  color: rgba(64,72,72,0.1) !important;
  background: #fdfdfd !important;
}

.flatpickr-day.optional-arrival.disabled.startRange:hover,
.flatpickr-day.selected.disabled.startRange:hover {
  background: linear-gradient(to right bottom, #fff 50%, #9fc87a 0) !important;
}

.flatpickr-day.selected.startRange {
  color: #404848 !important;
  background: linear-gradient(to right bottom, #fff 50%, #9fc87a 0) !important;
}

.flatpickr-day.selected.endRange {
  color: #404848 !important;
  background: linear-gradient(to right bottom, #9fc87a 50%, #fff 0) !important;
}

.flatpickr-day.selected.available.startRange,
.flatpickr-day.selected.available.startRange:hover {
  color: #404848 !important;
  background: linear-gradient(to right bottom, #fff 50%, #9fc87a 0) !important;
}

.flatpickr-day.selected.available.endRange,
.flatpickr-day.selected.available.endRange:hover,
.flatpickr-day.selected.disabled.endRange:hover {
  color: #404848 !important;
  background: linear-gradient(to right bottom, #d6c671 50%, #fff 0) !important;
}

.flatpickr-day.selected.available.startRange.hidden,
.flatpickr-day.selected.available.endRange.hidden,
.flatpickr-day.selected.available.startRange.hidden:hover,
.flatpickr-day.endRange.hidden:hover {
  background: #fdfdfd !important;
}

.flatpickr-day.startRange {
  color: #404848 !important;
  background: #f4e8b3 !important;
  font-weight: 600 !important;
}

.flatpickr-day.selected,
.flatpickr-day.inRange,
.flatpickr-day.endRange {
  font-weight: 600;
}

span.flatpickr-weekday {
  font-size: 13px !important;
  font-weight: 400 !important;
  color: #252525 !important;
}

.flatpickr-day-price {
  font-size: 11px;
  font-weight: 700;
  display: block;
  line-height: 8px;
}

.date-rangepicker + .flatpickr-calendar.inline .flatpickr-days .dayContainer {
  border-top: 1px solid #fff;
  border-left: 1px solid #fff;
}

.flatpickr-months .flatpickr-month {
  color: #252525 !important;
}

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
  color: #252525 !important;
  font-weight: 600 !important;
}

.flatpickr-month .numInputWrapper span {
  display: none;
}

.date-rangepicker + .flatpickr-calendar.inline {
  padding: 10px;
  border-radius: 3px;
  border: 1px solid #D8D6DE;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day {
  max-width: 62px !important;
  width: 62px !important;
  margin-top: 0 !important;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.disabled {
  color: #d4d4d4;
  background: #fafafa;
  text-decoration: line-through;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.disabled.selected {
  text-decoration: none;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.disabled.startRange {
  text-decoration: none;
}

.date-rangepicker + .flatpickr-calendar .flatpickr-day.available {
  font-weight: 600;
}

.date-rangepicker + .flatpickr-calendar.inline,
.date-rangepicker + .flatpickr-calendar.inline .flatpickr-days {
  width: 100% !important;
}

.dayContainer + .dayContainer {
  box-shadow: none !important;
}

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
  box-shadow: none !important;
}

.numInputWrapper:hover,
.flatpickr-current-month span.cur-month:hover{
  background: transparent !important;
}

.date-rangepicker + .flatpickr-calendar.inline .flatpickr-weekdays .flatpickr-weekdaycontainer {
  padding: 0 10px;
}

.date-rangepicker + .flatpickr-calendar.inline .flatpickr-day.inRange {
  box-shadow: none !important;
}

.flatpickr-rContainer {
  width: 100%;
  padding: 0;
}

.date-rangepicker + .flatpickr-calendar.inline .dayContainer {
  width: 50%;
  max-width: 50%;
  border-left: 1px solid #ddd;
  margin: 0 10px;
  border-top: 1px solid #ddd;
}

.flatpickr-months .flatpickr-prev-month svg,
.flatpickr-months .flatpickr-next-month svg {
  width: 16px !important;
  height: 16px !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  height: 36px !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  right: 15px;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  left: 15px;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  top: 12px !important;
  border: 1px solid #dbdbdb;
  padding: 5px 6px;
  font-size: 15px;
  height: 29px !important;
  border-radius: 3px;
}

.date-rangepicker + .flatpickr-calendar.inline .flatpickr-months .flatpickr-prev-month:hover,
.date-rangepicker + .flatpickr-calendar.inline .flatpickr-months .flatpickr-next-month:hover {
  background: #252525;
  border: 1px solid #252525;
  color: #fff;
}

.date-rangepicker + .flatpickr-calendar.inline .flatpickr-months .flatpickr-prev-month:hover svg,
.date-rangepicker + .flatpickr-calendar.inline .flatpickr-months .flatpickr-next-month:hover svg {
  fill: #fff;
}

span.flatpickr-day,
span.flatpickr-day.prevMonthDay,
span.flatpickr-day.nextMonthDay {
  border: 0 !important;
}

span.flatpickr-day,
span.flatpickr-day.prevMonthDay,
span.flatpickr-day.nextMonthDay {
  border-right: 1px solid #fff !important;
  border-bottom: 1px solid #fff !important;
}

.flatpickr-day.inRange,
.flatpickr-day:hover {
  background: #d9eec5 !important;
  box-shadow: none !important;
}

.flatpickr-day.inRange:hover,
.flatpickr-day.inRange.disabled:hover {
  color: #404848 !important;
  background: #d9eec5 !important;
}

.flatpickr-day.inRange.disabled:hover,
.flatpickr-day.inRange.disabled {
  color: rgba(64,72,72,.1) !important;
  text-decoration: none !important;
}

.flatpickr-day.hidden {
  display: inline-block !important;
  text-indent: -9999px;
  visibility: visible !important;
  background: #fff !important;
  cursor: not-allowed !important;
  color: rgba(64,72,72,.1) !important;
}

.flatpickr-day.hidden.only-saturday.disabled {
  background: #fdfdfd !important;
}

.flatpickr-day.hidden.only-saturday.disabled:hover {
  cursor: not-allowed !important;
  background: #fdfdfd !important;
}

.flatpickr-day.hidden.endRange,
.flatpickr-day.hidden.selected,
.flatpickr-day.hidden.selected:hover {
  background: #fdfdfd !important;
}

.flatpickr-day.selected,
.flatpickr-day.selected:hover,
.flatpickr-day.endRange,
.flatpickr-day.endRange:hover {
  color: #404848 !important;
  background: #d9eec5 !important;
}

.flatpickr-day:hover {
  background: #d9eec5 !important
}

.flatpickr-day.hidden:hover {
  background: #fff !important;
}

.flatpickr-day.disabled:hover {
	background: #fafafa !important;
}

.flatpickr-day.disabled.no-action:hover {
	background: #ffffff !important;
}

.flatpickr-day.disabled.booked {
  background-color: #f5f5f5 !important;
}

.flatpickr-day.hidden.disabled.booked {
  background: #fff !important;
}

.flatpickr-calendar:after {
  border-width: 9px !important;
  margin: 0 -9px !important;
}

.flatpickr-day.today {
	font-weight: 400;
	background: #fff;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
  box-shadow: none !important;
}

.flatpickr-wrapper {
  width: 100%;
}

.flatpickr-wrapper .flatpickr-input {
  margin-bottom: 0 !important;
}

.arival-departure-date-container {
  margin-bottom: 10px;
}

.flatpickr-calendar.static {
  top: 100% !important;
}

.date-mainpicker + .flatpickr-calendar.static {
  right: 0;
  width: 640px !important;
}

.flatpickr-innerContainer .object-not-available {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -60%);
  width: 80%;
  border-width: 2px;
  text-align: center;
  opacity: 0.9;
  z-index: 100;
}

.flatpickr-innerContainer .object-not-available:hover {
  opacity: 1;
}

.flatpickr-month .numInputWrapper .numInput {
  box-shadow: none;
}

.flatpickr-content-after {
  font-size: 12px;
  display: flex;
  flex-direction: row;
  color: #333;
  margin: 10px 20px;
  padding-top: 10px;
  border-top: 1px solid #f1f1f1;
  justify-content: space-between;
}

.flatpickr-content-after__left--information:before {
  content: '';
  filter: invert(15%) sepia(0%) saturate(0%) hue-rotate(172deg) brightness(88%) contrast(82%);
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background-image: url('/assets/img/svg/info.svg');
  background-size: 16px 16px;
  background-repeat: no-repeat;
}

.flatpickr-content-after__legend-item--not-available {
  margin-right: 20px;
}

.flatpickr-content-after__legend-item--not-available:before,
.flatpickr-content-after__legend-item--closed-for-arrival:before {
  content: '';
  width: 18px;
  height: 18px;
  display: inline-block;
  margin-right: 5px;
  background: #f5f5f5;
  border: 1px solid #dddee4;
  vertical-align: middle;
}

.flatpickr-content-after__legend-item--closed-for-arrival:before {
  content: '14';
  color: #d4d4d4;
  background: #fff;
  font-size: 12px;
  line-height: 18px;
}

.flatpickr-content-after .legend.minimum-stay:before {
  content: '';
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #d8c66c;
  display: inline-block;
  margin-right: 7px;
}

.calendar--borderless .flatpickr-calendar {
  padding: 0 !important;
  margin-top: 5px;
  border: 1px solid #ddd;
  border-radius: 3px !important;
  box-shadow: 0 0 10px 2px rgba(0,0,0,.1);
}

.main-search-input-item.calendar--borderless .flatpickr-calendar {
  margin-top: 20px;
  width: 640px !important;
}

.search__column--arrival-departure.calendar--borderless .flatpickr-calendar,
.search-listing-block .flatpickr-calendar {
  width: 640px !important;
}

.calendar--borderless .flatpickr-calendar {
  width: calc(307.875px + 20px);
}

.block-quiz-flex-block--calendar.calendar--borderless .flatpickr-calendar,
.block-quiz-flex-block--calendar.calendar--borderless .flatpickr-days {
  width: 640px !important;
}

.calendar--borderless .flatpickr-calendar:before,
.calendar--borderless .flatpickr-calendar:after {
  content: none;
}

.calendar--borderless span.flatpickr-day,
.calendar--borderless .dayContainer {
  border: 0 !important;
  border-bottom: 0 !important;
  border-right: 0 !important;
}

.calendar--borderless .dayContainer + .dayContainer {
  margin-left: 3px;
}

.calendar--borderless .flatpickr-months {
  margin: 5px 0;
}

.minimum-stay-day.disabled {
  position: relative;
}

.arival-departure-date-container .flatpickr-calendar .flatpickr-content-after {
  font-size: 12px;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 0;
}

.arival-departure-date-container .flatpickr-calendar .flatpickr-content-after .flatpickr-content-after__left--legend {
  margin-top: 5px;
}

.default-mainpicker + .flatpickr-calendar span.flatpickr-day,
.default-mainpicker + .flatpickr-calendar span.flatpickr-day.prevMonthDay,
.default-mainpicker + .flatpickr-calendar span.flatpickr-day.nextMonthDay {
  height: 38px !important;
  line-height: 38px !important;
}

.flatpickr-day.notAllowed {
	color: #404848 !important;
}

.flatpickr-day.disabled.notAllowed {
	color: #d4d4d4 !important;
}

/* OwnReservation */
.flatpickr-day.disabled.ownReservation,
.flatpickr-day.notAllowed.ownReservation {
	color: #404848 !important;
	background: #a2ccf6 !important;
}

.flatpickr-day.disabled.ownReservation.startDate,
.flatpickr-day.disabled.ownReservation + .flatpickr-day.disabled.ownReservation.startDate {
	background: linear-gradient(to right bottom, #fafafa 50%, #a2ccf6 0) !important;
}

.flatpickr-day.ownReservation.startDate {
	background: linear-gradient(to right bottom, #fff 50%, #a2ccf6 0) !important;
}

.flatpickr-day.ownReservation.startDate.endDate,
.flatpickr-day.ownReservation.startDate.endDate:hover {
	background: #a2ccf6 !important;
}

.flatpickr-day.selected.ownReservation.endRange,
.flatpickr-day.ownReservation.startDate:hover {
	background: linear-gradient(to right bottom, #9fc87a 50%, #a2ccf6 0) !important;
}

.flatpickr-day.disabled.ownReservation.endDate {
	background: linear-gradient(to right bottom, #a2ccf6 50%, #fafafa 0) !important;
}

.flatpickr-day.ownReservation.endDate {
	background: linear-gradient(to right bottom, #a2ccf6 50%, #fff 0) !important;
}

.flatpickr-day.selected.ownReservation.startRange,
.flatpickr-day.selected.ownReservation.startRange:hover,
.flatpickr-day.ownReservation.endDate:hover {
	background: linear-gradient(to right bottom, #a2ccf6 50%, #9fc87a 0) !important;
}

.flatpickr-day.disabled.ownReservation + .flatpickr-day.disabled.ownReservation.agencyReservation,
.flatpickr-day.ownReservation + .flatpickr-day.ownReservation.agencyReservation {
	background: linear-gradient(to right bottom, #a2ccf6 50%, #deda76 0) !important;
}



/* AgencyReservation */
.flatpickr-day.disabled.agencyReservation,
.flatpickr-day.notAllowed.agencyReservation {
	color: #404848 !important;
	background: #deda76 !important;
}

.flatpickr-day.disabled.agencyReservation.startDate,
.flatpickr-day.disabled.agencyReservation + .flatpickr-day.disabled.agencyReservation.startDate {
	background: linear-gradient(to right bottom, #fafafa 50%, #deda76 0) !important;
}

.flatpickr-day.agencyReservation.startDate {
	background: linear-gradient(to right bottom, #fff 50%, #deda76 0) !important;
}

.flatpickr-day.agencyReservation.startDate.endDate,
.flatpickr-day.agencyReservation.startDate.endDate:hover {
	background: #deda76 !important;
}

.flatpickr-day.selected.agencyReservation.endRange,
.flatpickr-day.agencyReservation.startDate:hover {
	background: linear-gradient(to right bottom, #9fc87a 50%, #deda76 0) !important;
}

.flatpickr-day.disabled.agencyReservation.endDate {
	background: linear-gradient(to right bottom, #deda76 50%, #fafafa 0) !important;
}

.flatpickr-day.agencyReservation.endDate {
	background: linear-gradient(to right bottom, #deda76 50%, #fff 0) !important;
}

.flatpickr-day.selected.agencyReservation.startRange,
.flatpickr-day.selected.agencyReservation.startRange:hover,
.flatpickr-day.agencyReservation.endDate:hover {
	background: linear-gradient(to right bottom, #deda76 50%, #9fc87a 0) !important;
}


.flatpickr-day.disabled.agencyReservation + .flatpickr-day.disabled.ownReservation.agencyReservation,
.flatpickr-day.agencyReservation + .flatpickr-day.ownReservation.agencyReservation {
	background: linear-gradient(to right bottom, #deda76 50%, #a2ccf6 0) !important;
}

.flatpickr-day.disabled.agencyReservation + .flatpickr-day.disabled.agencyReservation.ownReservation {
	background: linear-gradient(to right bottom, #deda76 50%, #fafafa 0) !important;
}

.flatpickr-day.disabled.ownReservation,
.flatpickr-day.disabled.agencyReservation {
	text-decoration: none !important;
}

.date-legend-container {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 5px 0 15px 0;
}

.date-legend {
	width: 20px;
	height: 20px;
	border: 1px solid transparent;
	display: inline-block;
	margin: 0 10px;
	border-radius: 3px;
}

.date-legend--unavailable {
	background: #fafafa;
	border-color: #dbdbdb;
}

.date-legend--own-reservation {
	background: #a2ccf6;
	border-color: #508bc9;
}

.date-legend--agency-reservation {
	background: #deda76;
	border-color: #c0bb4b;
}

.date-legend--selection {
	background: #d9eec5;
	border-color: #9fc87a;
}

.daterange-picker-info {
	display: none;
}

@media (max-width: 769px) {
	.date-rangepicker + .flatpickr-calendar.inline .dayContainer {
		margin: 0;
		width: 100%;
		max-width: 100%;
	}

	.date-rangepicker + .flatpickr-calendar .flatpickr-day {
		max-width: 100% !important;
		width: 100% !important;
	}

	.flatpickr-day {
		max-width: 100% !important;
	}

	.date-rangepicker + .flatpickr-calendar.inline {
		padding: 5px 0 0 0;

	}

	.flatpickr-rContainer {
		padding: 10px 0;
	}

	.date-legend-container {
		flex-wrap: nowrap;
		justify-content: flex-start;
		overflow-x: auto;
		width: 100%;
		padding: 10px;
	}

	.date-legend {
		margin-right: 10px;
		flex-shrink: 0;
		webkit-overflow-scrolling: touch;
	}

	.date-legend-content {
		display: inline-block;
		flex-shrink: 0;
		margin-right: 5px;
	}

	.date-legend:first-of-type {
		margin-left: 0;
	}

	.flatpickr-calendar:before,
	.flatpickr-calendar:after {
		content: none;
	}

	.date-rangepicker + .flatpickr-calendar.inline .flatpickr-weekdays .flatpickr-weekdaycontainer {
		padding: 0;
	}
}

@media (max-width: 380px) {
	.dayContainer {
		min-width: 0;
	}
}

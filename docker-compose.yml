version: '3'
services:
  web:
    image: nginx:alpine
    working_dir: /var/www
    volumes:
      - "./docker/etc/nginx/default.conf:/etc/nginx/conf.d/default.conf"
      - ".:/var/www/villas-guide:cached"
    ports:
      - "80:80"
    environment:
      - NGINX_HOST=villas-guide.local
    restart: always
    links:
      - vg_php
    depends_on:
      - vg_php
  vg_php:
    image: 'bitnami/php-fpm:8.4'
    working_dir: /var/www
    restart: always
    volumes:
      - './docker/etc/php/php.ini:/usr/local/etc/php/php.ini'
      - './docker/etc/php/xxx-devilbox-default-php.ini:/usr/local/etc/php/conf.d/xxx-devilbox-default-php.ini'
      - "./:/var/www/villas-guide/:cached"
    links:
      - db
      - redis
  db:
    image: mariadb:10.3.23
    ports:
      - 3308:3306
    volumes:
      - ./docker/data/mariadb:/var/lib/mysql
      - ./docker/etc/mariadb/my.cnf:/etc/mysql/my.cnf
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: villas_guide
      MYSQL_PASSWORD: villas_guide
      MYSQL_DATABASE: villas_guide
  redis:
    image: 'redis:6.0'
    ports:
      - '6379:6379'
    volumes:
      - ./docker/data/redis:/data
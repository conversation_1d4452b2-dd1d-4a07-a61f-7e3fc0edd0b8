<?php declare(strict_types = 1);

namespace Tests\Common\GoogleCalendar;

use App\Common\GoogleCalendar\GoogleCalendarService;
use App\Common\GoogleCalendar\GoogleClient;
use App\Common\GoogleCalendar\UpdateGoogleCalendarService;
use App\Models\Entities\ProductGoogleCalendarEntity;
use App\Repositories\AvailabilitiesRepository;
use App\Repositories\ProductGoogleCalendarRepository;
use Google\Http\Batch;
use Google\Service\Calendar;
use Google_Service_Calendar;
use Google_Service_Calendar_CalendarList;
use Google_Service_Calendar_CalendarListEntry;
use Google_Service_Calendar_Event;
use Google_Service_Calendar_Events;
use Monolog\Logger;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Tests\BaseTest;

class UpdateGoogleCalendarServiceTest extends BaseTest
{

	protected MockObject|UpdateGoogleCalendarService $updateGoogleCalendarService;

	protected MockObject|GoogleCalendarService $googleCalendarService;

	protected MockObject|ProductGoogleCalendarRepository $productGoogleCalendarRepository;

	protected MockObject|AvailabilitiesRepository $availabilitiesRepository;

	protected MockObject|Google_Service_Calendar_CalendarList $googleCalendarList;

	protected MockObject|GoogleClient $googleClient;

	protected MockObject|Calendar $googleCalendarApiService;

	protected MockObject|Google_Service_Calendar_Events $googleCalendarEvents;

	protected MockObject|Batch $batch;

	protected MockObject|LoggerInterface $logger;

	protected function setUp(): void
	{
		$this->googleCalendarService = $this->createDefaultMock(GoogleCalendarService::class);
		$this->productGoogleCalendarRepository = $this->createDefaultMock(ProductGoogleCalendarRepository::class);
		$this->availabilitiesRepository = $this->createDefaultMock(AvailabilitiesRepository::class);
		$this->logger = $this->createDefaultMock(Logger::class);
		$this->updateGoogleCalendarService = new UpdateGoogleCalendarService(
			$this->googleCalendarService,
			$this->productGoogleCalendarRepository,
			$this->availabilitiesRepository,
			$this->logger
		);
		$this->googleCalendarList = new Google_Service_Calendar_CalendarList();
		$this->googleClient = new GoogleClient();
		$this->googleCalendarApiService = new Google_Service_Calendar($this->googleClient);
		$this->googleCalendarEvents = new Google_Service_Calendar_Events();
		$service = new Google_Service_Calendar($this->googleClient);
		$this->batch = $this->googleCalendarService->createBatch($service);
	}


	public function testUpdate_WhenNoCalendarInDB_ShouldNotGetClient()
	{
		$calendars = [];
		$this->productGoogleCalendarRepository->expects($this->once())->method('getCalendarsForUpdate')->willReturn($calendars);
		$this->googleCalendarService->expects($this->never())->method('getClient');
		$this->updateGoogleCalendarService->update();
	}


	public function testUpdate_WhenDateIsAvailableInDbAndNotOnICal_ShouldDeleteEventFromICal()
	{
		$calendar1GoogleId = 'gcid1';
		$calendar1 = ['ID' => 1, 'product_id' => 12, 'google_calendar_id' => $calendar1GoogleId, 'calendar_name' => '123-vg'];
		$calendars = [$calendar1];
		$eventStatus = 'A';

		$listEntry = new Google_Service_Calendar_CalendarListEntry();
		$listEntry->setId($calendar1GoogleId);
		$this->googleCalendarList->setItems([$listEntry]);

		$this->productGoogleCalendarRepository->expects($this->once())->method('getCalendarsForUpdate')->willReturn($calendars);
		$this->googleCalendarService->expects($this->atLeastOnce())->method('getClient')->willReturn($this->googleClient);

		$this->productGoogleCalendarRepository->expects($this->once())->method('getBy')->willReturn(new ProductGoogleCalendarEntity());

		$this->googleCalendarService->expects($this->atLeastOnce())
			->method('getGoogleCalendarApiService')
			->with($this->googleClient)
			->willReturn($this->googleCalendarApiService);
		$this->googleCalendarService
			->expects($this->once())
			->method('getGoogleCalendarList')
			->with($this->googleCalendarApiService)
			->willReturn($this->googleCalendarList);

		$itemsEntry = new Google_Service_Calendar_Event();
		$dateTime = new \Google_Service_Calendar_EventDateTime();
		$eventItemDate = '2020-16-01';
		$dateTime->setDate($eventItemDate);
		$itemsEntry->setStart($dateTime);
		$itemsEntry->setEnd($dateTime);
		$itemsEntry->setId('id_event');
		$this->googleCalendarEvents->setItems([$itemsEntry]);

		$this->googleCalendarService
			->expects($this->once())
			->method('createBatch')
			->with($this->googleCalendarApiService)
			->willReturn($this->batch);

		$this->googleCalendarService
			->expects($this->once())
			->method('fillEventsBatchWithDeleteRequest')
			->with($this->googleCalendarApiService)
			->willReturn($this->batch);

		$this->googleCalendarService
			->method('fillEventsBatchWithInsertRequest')
			->with($this->googleCalendarApiService)
			->willReturn($this->batch);

		$this->googleCalendarService
			->expects($this->once())
			->method('getGoogleCalendarEvents')
			->with($this->googleCalendarApiService, $calendar1GoogleId)
			->willReturn($this->googleCalendarEvents);

		$this->availabilitiesRepository
			->expects($this->once())
			->method('getAvailabilitiesDateStatusPairs')
			->willReturn([$eventItemDate => $eventStatus]);

		$this->updateGoogleCalendarService->update();
	}


	public function testUpdate_WhenDateIsNotAvailableInDbAndOnICal_ShouldNotCreateEventOnICal()
	{
		$calendar1GoogleId = 'gcid1';
		$calendar1 = ['ID' => 1, 'product_id' => 12, 'google_calendar_id' => $calendar1GoogleId, 'calendar_name' => '123-vg'];
		$calendars = [$calendar1];

		$listEntry = new Google_Service_Calendar_CalendarListEntry();
		$listEntry->setId($calendar1GoogleId);
		$this->googleCalendarList->setItems([$listEntry]);

		$this->productGoogleCalendarRepository->expects($this->once())->method('getBy')->willReturn(new ProductGoogleCalendarEntity());
		$this->productGoogleCalendarRepository->expects($this->once())->method('getCalendarsForUpdate')->willReturn($calendars);
		$this->googleCalendarService->expects($this->atLeastOnce())->method('getClient')->willReturn($this->googleClient);
		$this->googleCalendarService->expects($this->atLeastOnce())
			->method('getGoogleCalendarApiService')
			->with($this->googleClient)
			->willReturn($this->googleCalendarApiService);
		$this->googleCalendarService
			->expects($this->once())
			->method('getGoogleCalendarList')
			->with($this->googleCalendarApiService)
			->willReturn($this->googleCalendarList);

		$itemsEntry = new Google_Service_Calendar_Event();

		$dateTime = new \Google_Service_Calendar_EventDateTime();
		$eventItemDate = '2023-06-01';
		$dateTime->setDate($eventItemDate);
		$dateTime2 = new \Google_Service_Calendar_EventDateTime();
		$eventItemDate2 = '2023-06-03';
		$dateTime2->setDate($eventItemDate2);
		$itemsEntry->setStart($dateTime);
		$itemsEntry->setEnd($dateTime2);
		$itemsEntry->setId('id_event');
		$this->googleCalendarEvents->setItems([$itemsEntry]);

		$this->googleCalendarService
			->expects($this->once())
			->method('createBatch')
			->with($this->googleCalendarApiService)
			->willReturn($this->batch);

		$this->googleCalendarService
			->expects($this->once())
			->method('getGoogleCalendarEvents')
			->with($this->googleCalendarApiService, $calendar1GoogleId)
			->willReturn($this->googleCalendarEvents);

		$this->availabilitiesRepository
			->expects($this->once())
			->method('getAvailabilitiesDateStatusPairs')
			->willReturn([$eventItemDate => 'X', $eventItemDate2 => 'A']);

		$this->googleCalendarService
			->method('fillEventsBatchWithInsertRequest')
			->with($this->googleCalendarApiService)
			->willReturn($this->batch);

		$this->updateGoogleCalendarService->update();
	}


	public function testUpdate_WhenDateIsNotAvailableInDbAndEventOnICalIsNotCreated_ShouldCreateEventOnICal()
	{
		$calendar1GoogleId = 'gcid1';
		$calendar1 = ['ID' => 1, 'product_id' => 12, 'google_calendar_id' => $calendar1GoogleId, 'calendar_name' => '123-vg'];
		$calendars = [$calendar1];

		$listEntry = new Google_Service_Calendar_CalendarListEntry();
		$listEntry->setId($calendar1GoogleId);
		$this->googleCalendarList->setItems([$listEntry]);

		$this->productGoogleCalendarRepository->expects($this->once())->method('getCalendarsForUpdate')->willReturn($calendars);
		$this->googleCalendarService->expects($this->atLeastOnce())->method('getClient')->willReturn($this->googleClient);
		$this->googleCalendarService->expects($this->atLeastOnce())
			->method('getGoogleCalendarApiService')
			->with($this->googleClient)
			->willReturn($this->googleCalendarApiService);
		$this->googleCalendarService
			->expects($this->once())
			->method('getGoogleCalendarList')
			->with($this->googleCalendarApiService)
			->willReturn($this->googleCalendarList);
		$this->productGoogleCalendarRepository->expects($this->once())->method('getBy')->willReturn(new ProductGoogleCalendarEntity());

		$itemsEntry = new Google_Service_Calendar_Event();
		$dateTime = new \Google_Service_Calendar_EventDateTime();
		$eventItemDate = '2023-06-01';
		$dateTime->setDate($eventItemDate);
		$itemsEntry->setStart($dateTime);
		$itemsEntry->setEnd($dateTime);
		$itemsEntry->setId('id_event');
		$this->googleCalendarEvents->setItems([$itemsEntry]);

		$this->googleCalendarService
			->expects($this->once())
			->method('createBatch')
			->with($this->googleCalendarApiService)
			->willReturn($this->batch);

		$this->googleCalendarService
			->expects($this->once())
			->method('getGoogleCalendarEvents')
			->with($this->googleCalendarApiService, $calendar1GoogleId)
			->willReturn($this->googleCalendarEvents);

		$availabilityDate = '2023-02-02';
		$availabilityDate2 = '2023-02-05';
		$this->availabilitiesRepository
			->expects($this->once())
			->method('getAvailabilitiesDateStatusPairs')
			->willReturn([$availabilityDate => 'X', $availabilityDate2 => 'A']);

		$this->googleCalendarService
			->method('fillEventsBatchWithInsertRequest')
			->with($this->googleCalendarApiService)
			->willReturn($this->batch);

		$this->updateGoogleCalendarService->update();
	}

}

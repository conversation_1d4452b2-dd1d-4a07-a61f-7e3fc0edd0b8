<?php

namespace Tests\Common\Novasol\Services\Update;

use App\Common\Novasol\Services\ImagesUpdateHelper;
use App\Common\Novasol\Services\Update\ImagesUpdateService;
use App\Repositories\PicturesRepository;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\BaseTest;

class ImagesUpdateServiceTest extends BaseTest
{

	private MockObject| ImagesUpdateHelper $imagesUpdateHelper;

	private MockObject | PicturesRepository $picturesRepository;

	protected ImagesUpdateService $service;

	protected function setUp(): void
	{
		$this->picturesRepository = $this->createDefaultMock(PicturesRepository::class);
		$this->imagesUpdateHelper = $this->createDefaultMock(ImagesUpdateHelper::class);
		$this->service = new ImagesUpdateService($this->imagesUpdateHelper);
	}

	public function testSavePictures_WhenAllImagesAreNew_InsertAllImages()
	{
		$node = new \SimpleXMLElement('<product></product>');
		$nodeChild = $node->addChild('pictures');

		$imagesCount = 7;

		for ($i = 1; $i <= $imagesCount; $i++) {
			$picturesNodeChild = $nodeChild->addChild('picture');
			$picturesNodeChild->addChild('file', 'filename' . $i . '.jpg');
		}

		$productPictures = [];
		$oldImagesArray = [];
		$productId = 1;

		$this->imagesUpdateHelper->expects($this->once())->method('getPicturesRepository')->willReturn($this->picturesRepository);
		$this->picturesRepository->expects($this->once())->method('getProductPictures')->with($productId)->willReturn($productPictures);
		$this->picturesRepository->expects($this->exactly($imagesCount))->method('getPictureByProductIdAndFilename')->willReturn(NULL);

		$this->imagesUpdateHelper->expects($this->exactly($imagesCount))->method('insertNewImage');
		$this->picturesRepository->expects($this->once())->method('getImagesIdsThatAreNotInArray')->willReturn($oldImagesArray);

		$this->service->savePictures($node, $productId);
	}

}

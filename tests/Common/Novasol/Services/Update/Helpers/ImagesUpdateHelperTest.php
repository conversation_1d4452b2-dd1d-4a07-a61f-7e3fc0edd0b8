<?php declare(strict_types = 1);

namespace Tests\Common\Novasol\Services\Update\Helpers;

use App\Common\Images\ImagesImportHelper;
use App\Common\Novasol\Services\ImagesUpdateHelper;
use App\Common\Novasol\Services\PicturesDownloadService;
use App\Common\Novasol\Services\Update\Helpers\Md5HashCalculator;
use App\Repositories\PicturesRepository;
use PHPUnit\Framework\MockObject\MockObject;
use SimpleXMLElement;
use stdClass;
use Tests\BaseTest;

class ImagesUpdateHelperTest extends BaseTest
{

	private MockObject | PicturesRepository $picturesRepository;

	private MockObject | PicturesDownloadService $picturesDownloadService;

	private MockObject | ImagesImportHelper $imagesImportHelper;

	private MockObject | Md5HashCalculator $md5HashCalculator;

	protected ImagesUpdateHelper $service;

	protected function setUp(): void
	{
		$this->picturesRepository = $this->createDefaultMock(PicturesRepository::class);
		$this->picturesDownloadService = $this->createDefaultMock(PicturesDownloadService::class);
		$this->imagesImportHelper = $this->createDefaultMock(ImagesImportHelper::class);
		$this->md5HashCalculator = $this->createDefaultMock(Md5HashCalculator::class);

		$this->service = new ImagesUpdateHelper(
			$this->picturesRepository,
			$this->picturesDownloadService,
			$this->imagesImportHelper,
			$this->md5HashCalculator
		);
	}

	public function testInsertNewImage_WhenNewImageExistInDb_DoNothing()
	{
		$productId = 1;
		$picture = new SimpleXMLElement('<picture></picture>');
		//$filename = 'https://image.novasol.com/pic/e04/e04111_floorplan_01.png';
		//$deprecatedFilename = 'https://sdc.novasol.com/pic/e04/e04111_floorplan_01.png';
		$hashInDb = 'hash123';
		$newImageHash = $hashInDb;

		$images = [];
		$image = new stdClass();
		$image->hash = $hashInDb;
		$images[] = $image;

		$picture->addChild('domain', 'https://image.novasol.com');
		$picture->addChild('path', '/pic/e04/');
		$picture->addChild('file', 'e04111_floorplan_01.png');

		$this->md5HashCalculator->expects($this->exactly(1))->method('calculate')->willReturn($newImageHash);
		$this->picturesDownloadService->expects($this->never())->method('downloadAndResize');
		$result = $this->service->insertNewImage($productId, $picture, $images);
		$this->assertEquals(null, $result);
	}

	public function testInsertNewImage_WhenNewImageDoesntExistInDb_DoInsertInDb()
	{
		$productId = 3;
		$picture = new SimpleXMLElement('<picture></picture>');
		$picture->addChild('domain', 'https://image.novasol.com');
		$picture->addChild('path', '/pic/e04/');
		$picture->addChild('file', 'e04111_floorplan_01.png');

		$newImageId = 1;
		$images = [];

		$this->md5HashCalculator->expects($this->exactly(1))->method('calculate');
		$this->picturesDownloadService->expects($this->exactly(1))->method('downloadAndResize');
		$this->picturesRepository->expects($this->exactly(1))->method('insert')->willReturn($newImageId);
		$result = $this->service->insertNewImage($productId, $picture, $images);
		$this->assertEquals($newImageId, $result);
	}

	public function testInsertNewImage_WhenOldImageDoesntExistInDb_DoInsertInDb()
	{
		$productId = 4;
		$picture = new SimpleXMLElement('<picture></picture>');
		$picture->addChild('domain', 'https://sdc.novasol.com');
		$picture->addChild('path', '/pic/e04/');
		$picture->addChild('file', 'e04111_floorplan_01.png');

		$newImageId = 2;
		$images = [];

		$this->md5HashCalculator->expects($this->exactly(1))->method('calculate');
		$this->picturesDownloadService->expects($this->exactly(1))->method('downloadAndResize');
		$this->picturesRepository->expects($this->exactly(1))->method('insert')->willReturn($newImageId);
		$result = $this->service->insertNewImage($productId, $picture, $images);
		$this->assertEquals($newImageId, $result);
	}

}

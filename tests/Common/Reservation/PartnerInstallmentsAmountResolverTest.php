<?php declare(strict_types = 1);

namespace Tests\Common\Reservation;

use App\Common\Enums\DealStagesEnum;
use App\Common\Enums\InstallmentsPaymentStatusEnum;
use App\Common\Reservation\BookingConditionsFinder;
use App\Common\Reservation\PartnerInstallmentsAmountResolver;
use App\Models\Entities\BookingPaymentAmountConditionEntity;
use App\Models\Entities\GeneratedOfferDealEntity;
use App\Models\Entities\ProductOwnerEntity;
use App\Models\Entities\ReservationEntity;
use App\Modules\Property\Logic\InvoiceLogic;
use App\Repositories\GeneratedOffersDealsRepository;
use App\Repositories\ReservationsRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class PartnerInstallmentsAmountResolverTest extends TestCase
{

	protected MockObject|InvoiceLogic $invoiceLogic;

	protected MockObject|ReservationsRepository $reservationRepository;

	protected MockObject|BookingConditionsFinder $bookingConditionsFinder;

	protected MockObject|GeneratedOffersDealsRepository $generatedOffersDealsRepository;

	protected PartnerInstallmentsAmountResolver $resolver;

	protected function setUp(): void
	{
		$this->reservationRepository = $this->createMock(ReservationsRepository::class);
		$this->invoiceLogic = $this->createMock(InvoiceLogic::class);
		$this->bookingConditionsFinder = $this->createMock(BookingConditionsFinder::class);
		$this->generatedOffersDealsRepository = $this->createMock(GeneratedOffersDealsRepository::class);

		$this->resolver = new PartnerInstallmentsAmountResolver(
			$this->invoiceLogic,
			$this->bookingConditionsFinder,
			$this->generatedOffersDealsRepository,
			$this->reservationRepository
		);
	}

	public function testResolveCloseWon(): void
	{
		$this->invoiceLogic->expects($this->once())
			->method('getAgencyProvisionAmount')
			->with(123)
			->willReturn(100.0);

		$this->invoiceLogic->expects($this->once())
			->method('getOwner')
			->willReturn(new ProductOwnerEntity([ProductOwnerEntity::SSN => '80510488253']));

		$conditionEntity = new BookingPaymentAmountConditionEntity();
		$conditionEntity->first_installment_percentage = 50;
		$this->bookingConditionsFinder->expects($this->once())
			->method('getAmountPercentagesForPartner')
			->willReturn($conditionEntity);

		$this->generatedOffersDealsRepository->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(456)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::CLOSE_WON]));

		$this->reservationRepository->expects($this->once())
			->method('getBy')
			->with(['id' => 123])
			->willReturn(new ReservationEntity([
				'deal_id' => 456,
				ReservationEntity::PRODUCT_ID => 1,
			]));

		$this->resolver->resolve(123, 1000.0);
		$this->assertEquals(375.0, $this->resolver->getFirstInstallment());
		$this->assertEquals(500.0, $this->resolver->getSecondInstallment());
	}

	public function testResolveWithNonExistingReservation(): void
	{
		$this->reservationRepository->expects($this->once())
			->method('getBy')
			->with(['id' => 123])
			->willReturn(null);

		$this->resolver->resolve(123, 1000.0);

		$this->assertEquals(0.0, $this->resolver->getFirstInstallment());
		$this->assertEquals(0.0, $this->resolver->getSecondInstallment());
	}

	public function testResolveWithNullAmount(): void
	{
		$this->reservationRepository->expects($this->once())
			->method('getBy')
			->with(['id' => 123])
			->willReturn(new ReservationEntity(['deal_id' => 456]));

		$this->bookingConditionsFinder->expects($this->never())
			->method('getAmountPercentagesForPartner');
		$this->generatedOffersDealsRepository->expects($this->never())
			->method('getDealByHubspotDealId');

		$this->resolver->resolve(123, null);

		// Check that the first installment and second installment are both 0.0
		$this->assertEquals(0.0, $this->resolver->getFirstInstallment());
		$this->assertEquals(0.0, $this->resolver->getSecondInstallment());
	}

	public function testResolveStornoWithPaidInstallments(): void
	{
		$conditionEntity = new BookingPaymentAmountConditionEntity();
		$conditionEntity->first_installment_percentage = 50;
		$this->bookingConditionsFinder->expects($this->once())
			->method('getAmountPercentagesForPartner')
			->willReturn($conditionEntity);

		$this->invoiceLogic->expects($this->once())
			->method('getOwner')
			->willReturn(new ProductOwnerEntity([ProductOwnerEntity::SSN => '80510488253']));

		$this->generatedOffersDealsRepository->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(456)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::STORNO]));

		$reservation = new ReservationEntity([
			'product_id' => 1,
			'deal_id' => 456,
			'partner_first_installment_payment_status' => InstallmentsPaymentStatusEnum::PAID,
			'partner_second_installment_payment_status' => InstallmentsPaymentStatusEnum::PAID,
		]);
		$this->reservationRepository->expects($this->once())
			->method('getBy')
			->with(['id' => 123])
			->willReturn($reservation);

		$this->resolver->resolve(123, 1000.0);
		$this->assertEquals(500.0, $this->resolver->getFirstInstallment());
		$this->assertEquals(500.0, $this->resolver->getSecondInstallment());
	}


	public function testResolveStornoWithPaidFirstInstallment(): void
	{
		$conditionEntity = new BookingPaymentAmountConditionEntity();
		$conditionEntity->first_installment_percentage = 50;
		$this->bookingConditionsFinder->expects($this->once())
			->method('getAmountPercentagesForPartner')
			->willReturn($conditionEntity);

		$this->invoiceLogic->expects($this->once())
			->method('getOwner')
			->willReturn(new ProductOwnerEntity([ProductOwnerEntity::SSN => '80510488253']));

		$this->generatedOffersDealsRepository->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(456)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::STORNO]));

		$reservation = new ReservationEntity([
			'product_id' => 1,
			'deal_id' => 456,
			'partner_first_installment_payment_status' => InstallmentsPaymentStatusEnum::PAID,
			'partner_second_installment_payment_status' => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
		]);
		$this->reservationRepository->expects($this->once())
			->method('getBy')
			->with(['id' => 123])
			->willReturn($reservation);

		$this->resolver->resolve(123, 1000.0);
		$this->assertEquals(500.0, $this->resolver->getFirstInstallment());
		$this->assertEquals(0, $this->resolver->getSecondInstallment());
	}

	public function testResolveStornoWithNotPaidInstallents(): void
	{
		$conditionEntity = new BookingPaymentAmountConditionEntity();
		$conditionEntity->first_installment_percentage = 50;
		$this->bookingConditionsFinder->expects($this->once())
			->method('getAmountPercentagesForPartner')
			->willReturn($conditionEntity);

		$this->invoiceLogic->expects($this->once())
			->method('getOwner')
			->willReturn(new ProductOwnerEntity([ProductOwnerEntity::SSN => '80510488253']));

		$this->generatedOffersDealsRepository->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(456)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::STORNO]));

		$reservation = new ReservationEntity([
			'product_id' => 1,
			'deal_id' => 456,
			'partner_first_installment_payment_status' => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
			'partner_second_installment_payment_status' => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
		]);
		$this->reservationRepository->expects($this->once())
			->method('getBy')
			->with(['id' => 123])
			->willReturn($reservation);

		$this->resolver->resolve(123, 1000.0);
		$this->assertEquals(0, $this->resolver->getFirstInstallment());
		$this->assertEquals(0, $this->resolver->getSecondInstallment());
	}

}

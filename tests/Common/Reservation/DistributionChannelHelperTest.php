<?php declare(strict_types = 1);

namespace Tests\Common\Reservation;

use App\Common\Reservation\BookingConditionsFinder;
use App\Common\Reservation\Helpers\DistributionChannelHelper;
use App\Models\Entities\custom\DistributionChannelDetailEntity;
use App\Repositories\ProductDistributionChannelRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class DistributionChannelHelperTest extends TestCase
{

	protected MockObject|BookingConditionsFinder $bookingConditionsFinder;

	protected MockObject|ProductDistributionChannelRepository $distributionChannelRepository;

	protected DistributionChannelHelper $distributionChannelHelper;

	protected function setUp(): void
	{
		$this->bookingConditionsFinder = $this->createMock(BookingConditionsFinder::class);
		$this->distributionChannelRepository = $this->createMock(ProductDistributionChannelRepository::class);

		$this->distributionChannelHelper = new DistributionChannelHelper(
			$this->distributionChannelRepository,
			$this->bookingConditionsFinder
		);
	}

	public function testGetAmountWithoutChannelIdReturnsTotalAmount(): void
	{
		$result = $this->distributionChannelHelper->getAmount(null, 100.0, 200.0, 10.0);
		$this->assertEquals(200.0, $result);
	}

	public function testGetAmountWithChannelIdCalculatesCorrectly(): void
	{
		$channelDetail = new DistributionChannelDetailEntity();
		$channelDetail->percentage = 10.0; // Example percentage

		$this->distributionChannelRepository->method('getChannelDetailById')
			->willReturn($channelDetail);

		$result = $this->distributionChannelHelper->getAmount(1, 100.0, null, 10.0);
		$expected = 100.0 + (100.0 * 0.1) + 10.0; // baseAmount + (baseAmount * percentage / 100) + insurance
		$this->assertEquals($expected, $result);
	}

}

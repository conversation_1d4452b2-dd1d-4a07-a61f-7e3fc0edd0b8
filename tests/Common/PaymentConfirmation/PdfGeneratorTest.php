<?php declare(strict_types = 1);

namespace Tests\Common\PaymentConfirmation;

use App\Common\Enums\InstallmentsPaymentStatusEnum;
use App\Common\PaymentConfirmation\ConfirmationTypes;
use App\Common\PaymentConfirmation\PdfGenerator;
use App\Common\Reservation\PartnerInstallmentsAmountResolver;
use App\Common\Translator;
use App\Models\Entities\ReservationEntity;
use App\Modules\Property\Logic\InvoiceLogic;
use App\Repositories\ReservationsRepository;
use Latte\Engine;
use Nette\Application\BadRequestException;
use Nette\Bridges\ApplicationLatte\Template;
use Nette\Bridges\ApplicationLatte\TemplateFactory;
use Nette\Utils\DateTime;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class PdfGeneratorTest extends TestCase
{

	private PdfGenerator $pdfGenerator;

	private MockObject|ReservationsRepository $reservationsRepository;

	private MockObject|InvoiceLogic $invoiceLogic;

	private MockObject|PartnerInstallmentsAmountResolver $partnerInstallmentsAmountResolver;

	private MockObject|TemplateFactory $templateFactory;

	private MockObject|Translator $translator;

	protected function setUp(): void
	{
		$this->reservationsRepository = $this->createMock(ReservationsRepository::class);
		$this->invoiceLogic = $this->createMock(InvoiceLogic::class);
		$this->partnerInstallmentsAmountResolver = $this->createMock(PartnerInstallmentsAmountResolver::class);
		$this->templateFactory = $this->createMock(TemplateFactory::class);
		$this->translator = $this->createMock(Translator::class);
		$this->pdfGenerator = new PdfGenerator(
			$this->reservationsRepository,
			$this->invoiceLogic,
			$this->partnerInstallmentsAmountResolver,
			$this->templateFactory,
			$this->translator
		);
	}

	public function testGenerateWithInvalidReservationId()
	{
		$this->expectException(BadRequestException::class);
		$this->expectExceptionMessage("Invoice can't be generated");

		$reservationId = 1;
		$language = 'en';
		$type = ConfirmationTypes::FULL;

		$this->translator->expects($this->once())->method('setFrontLanguage')->with($language);

		$this->reservationsRepository->expects($this->once())->method('getBy')->with([ReservationEntity::ID => $reservationId])->willReturn(null);

		$this->pdfGenerator->generate($reservationId, $type, $language);
	}

	public function testGenerateWithUnpaidFirstInstallment()
	{
		$this->expectException(BadRequestException::class);
		$this->expectExceptionMessage("Invoice can't be generated");

		$reservationId = 1;
		$language = 'en';
		$type = ConfirmationTypes::FULL;

		$this->translator->expects($this->once())->method('setFrontLanguage')->with($language);

		$reservation = new ReservationEntity();
		$reservation->payment_first_installment_amount = null;
		$reservation->first_installment_paid_date = null;
		$reservation->first_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;

		$this->reservationsRepository->expects($this->once())->method('getBy')
			->with([ReservationEntity::ID => $reservationId])
			->willReturn($reservation);

		$this->pdfGenerator->generate($reservationId, $type, $language);
	}

	public function testFullPaymentGenerate()
	{
		$reservation = new ReservationEntity();
		$reservation->id = 1;
		$reservation->contact_lastname = 'test';
		$reservation->deal_id = 123;
		$reservation->payment_first_installment_amount = 100;
		$reservation->first_installment_paid_date = new \DateTime('2023-02-26');
		$reservation->first_installment_payment_status = InstallmentsPaymentStatusEnum::PAID;
		$reservation->product_id = 1;
		$reservation->amount = 100;
		$reservation->two_installments = false;

		$pdfGenerator = new PdfGenerator(
			$this->reservationsRepository,
			$this->invoiceLogic,
			$this->partnerInstallmentsAmountResolver,
			$this->templateFactory,
			$this->translator
		);

		$type = ConfirmationTypes::FULL;
		$this->reservationsRepository->expects($this->once())
			->method('getBy')
			->with([ReservationEntity::ID => 1])
			->willReturn($reservation);
		$this->invoiceLogic->expects($this->once())
			->method('getFixedExchangeRate')
			->willReturn(1.7);
		$this->partnerInstallmentsAmountResolver->expects($this->once())
			->method('resolve')
			->with(1, 100);
		$this->invoiceLogic->expects($this->once())
			->method('getProductName')
			->with($reservation->product_id)
			->willReturn('Product Name');
		$engine = new Engine();
		$template = new Template($engine);
		$this->templateFactory->expects($this->once())
			->method('createTemplate')
			->willReturn($this->createMock($template::class));
		$this->translator->expects($this->once())
			->method('translate')
			->with('Račun')
			->willReturn('Invoice');
		$dir = WWW_DIR . '/documents/payment-confirmations';
		$pdfGenerator->generate(1, $type, 'en');
		$now = new DateTime();
		$year = $now->format('Y');
		$filename = '1-2-vg-1-test-123.pdf';
		$this->assertFileExists($dir . '/' . $year . '/' . $filename);
		unlink($dir . '/' . $year . '/' . $filename);
	}

	public function testGenerateRestPaymentNotPaid()
	{
		$reservation = new ReservationEntity();
		$reservation->id = 1;
		$reservation->payment_first_installment_amount = 100;
		$reservation->first_installment_paid_date = new \DateTime('2023-02-26');
		$reservation->first_installment_payment_status = InstallmentsPaymentStatusEnum::PAID;
		$reservation->two_installments = true;
		$reservation->second_installment_paid_date = null;
		$reservation->second_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;

		$pdfGenerator = new PdfGenerator(
			$this->reservationsRepository,
			$this->invoiceLogic,
			$this->partnerInstallmentsAmountResolver,
			$this->templateFactory,
			$this->translator
		);

		$type = ConfirmationTypes::REST;
		$this->expectException(BadRequestException::class);
		$this->templateFactory->expects($this->never())
			->method('createTemplate');
		$this->reservationsRepository->expects($this->once())
			->method('getBy')
			->with([ReservationEntity::ID => 1])
			->willReturn($reservation);
		$this->partnerInstallmentsAmountResolver->expects($this->never())
			->method('resolve');
		$this->invoiceLogic->expects($this->never())
			->method('getProductName');
		$pdfGenerator->generate(1, $type, 'de');
	}


	public function testGenerateFullPaymentTwoInstallments()
	{
		$reservation = new ReservationEntity();
		$reservation->id = 1;
		$reservation->payment_first_installment_amount = 100;
		$reservation->first_installment_paid_date = new \DateTime('2023-02-26');
		$reservation->first_installment_payment_status = InstallmentsPaymentStatusEnum::PAID;
		$reservation->two_installments = true;
		$reservation->second_installment_paid_date = null;
		$reservation->second_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;

		$pdfGenerator = new PdfGenerator(
			$this->reservationsRepository,
			$this->invoiceLogic,
			$this->partnerInstallmentsAmountResolver,
			$this->templateFactory,
			$this->translator
		);

		$type = ConfirmationTypes::FULL;
		$this->expectException(BadRequestException::class);
		$this->templateFactory->expects($this->never())
			->method('createTemplate');
		$this->reservationsRepository->expects($this->once())
			->method('getBy')
			->with([ReservationEntity::ID => 1])
			->willReturn($reservation);
		$this->partnerInstallmentsAmountResolver->expects($this->never())
			->method('resolve');
		$this->invoiceLogic->expects($this->never())
			->method('getProductName');
		$pdfGenerator->generate(1, $type, 'de');
	}

}

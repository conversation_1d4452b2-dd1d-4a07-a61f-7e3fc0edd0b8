<?php

namespace Tests\Common;

use App\Common\Translator;
use App\Models\Entities\TranslationMessageEntity;
use App\Repositories\TranslationRepository;
use Nette\Caching\Cache;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\BaseTest;

class TranslatorTest extends BaseTest
{

	/**
	 * @var Translator
	 */
	protected $translator;

	/**
	 * @var MockObject|TranslationRepository
	 */
	protected $repository;

	/**
	 * @var MockObject|Cache
	 */
	protected $cache;

	/**
	 * @var MockObject|string[]
	 */
	protected $translations;

	protected function setUp(): void
	{
		$this->repository = $this->getMockBuilder(TranslationRepository::class)->disableOriginalConstructor()->getMock();
		$this->cache = $this->getMockBuilder(Cache::class)->disableOriginalConstructor()->getMock();
		$this->translator = new Translator($this->repository, $this->cache);
	}


	public function testTranslate_WithStringNumbers_ShouldNotTranslateThem()
	{
		$result = $this->translator->translate('3');
		$this->assertEquals('3', $result);
	}


	public function testTranslate_WithEmptyKey_WillReturnKey()
	{
		$key = $this->translator->translate(NULL);
		$this->assertEmpty($key);
	}


	/**
	 * If key and message exist return message
	 */
	public function testTranslate_WithSetedTranslations_WillReturnMessage()
	{
		$translation = new TranslationMessageEntity();
		$translation->language_id = 1;
		$translation->key = 'key';
		$translation->message = 'kljuc';

		$this->cache->method('call')->willReturn([$translation->key => $translation->message]);

		$value = $this->translator->translate('key');
		$this->assertEquals('kljuc', $value);
	}


	/**
	 * if missing key with empty message exist in database return message as key value and don't insert
	 */
	public function testTranslate_IfKeyExist_WillReturnMessage()
	{
		$key = 'key';
		$this->cache->method('call')->willReturn(['key' => NULL]);
		$message = $this->translator->translate($key);

		$this->assertEquals('key', $message);
	}


	/**
	 * if missing key don't exist in database insert
	 */
	public function testTranslate_IfKeyDontExist_WillReturnMessage()
	{
		$key = 'key';
		$this->cache->method('call')->willReturn(['key2' => 'kljuc']);
		$message = $this->translator->translate($key);

		$this->assertEquals('key', $message);
	}

	/**
	 * formatting message with $count (string or int)
	 */
	public function testTranslate_WithCount_WillReturnMessageWithCount()
	{
		$message = 'kljuc2 %s';
		$count = 'za bravu';
		$this->cache->method('call')->willReturn(['key2' => $message]);

		$result = $this->translator->translate('key2', $count);

		$this->assertEquals('kljuc2 ' . $count, $result);
	}

}

<?php

namespace Tests\Common;

use App\Common\BookingHelper;
use App\Common\Enums\AvailabilityStatusEnum;
use App\Common\Hubspot;
use App\Common\Novasol\Request\BookingRequest;
use App\Common\ProductInfo\ProductOwnerServices;
use App\Common\Reservation\BookingConditionsFinder;
use App\Common\Reservation\CancellationPolicyResolver;
use App\Common\Reservation\Helpers\CustomProductReservationInfoHelper;
use App\Common\Reservation\ReservationCode;
use App\Mails\MailFactory;
use App\Models\Entities\AvailabilityEntity;
use App\Models\Entities\ReservationEntity;
use App\Repositories\AvailabilitiesRepository;
use App\Repositories\GeneratedOffersDealsRepository;
use App\Repositories\PartnersRepository;
use App\Repositories\ReservationInsuranceRepository;
use App\Repositories\ReservationsRepository;
use App\Repositories\TransactionsRepository;
use Dibi\DateTime;
use PHPUnit\Framework\MockObject\MockObject;
use SevenShores\Hubspot\Exceptions\BadRequest;
use SevenShores\Hubspot\Http\Response;
use Tests\BaseTest;

class BookingHelperTest extends BaseTest
{

	protected BookingHelper $helper;

	protected MockObject|CustomProductReservationInfoHelper $customProductReservationInfoHelper;

	protected MockObject|AvailabilitiesRepository $availabilitiesRepository;

	protected MockObject| ReservationsRepository $reservationsRepository;

	protected MockObject| TransactionsRepository $transactionRepository;

	protected MockObject| Hubspot $hubspot;

	protected MockObject| GeneratedOffersDealsRepository $generatedOffersDealsRepository;

	protected MockObject| MailFactory $mailFactory;

	protected BookingRequest $bookingRequest;

	protected MockObject| Response $contactResponse;

	protected MockObject|BookingConditionsFinder $bookingConditionsFinder;

	protected MockObject|CancellationPolicyResolver $cancellationPolicyResovler;

	protected MockObject|PartnersRepository $partnersRepository;

	protected MockObject|ReservationInsuranceRepository $reservationInsuranceRepository;

	protected MockObject|ProductOwnerServices $productOwnerServices;

	protected MockObject|ReservationCode $reservationCode;

	protected function setUp(): void
	{
		$this->contactResponse = $this->getMockBuilder(Response::class)->disableOriginalConstructor()->getMock();
		$this->availabilitiesRepository = $this->getMockBuilder(AvailabilitiesRepository::class)->disableOriginalConstructor()->getMock();
		$this->reservationsRepository = $this->getMockBuilder(ReservationsRepository::class)->disableOriginalConstructor()->getMock();
		$this->customProductReservationInfoHelper = $this->getMockBuilder(CustomProductReservationInfoHelper::class)->disableOriginalConstructor()->getMock();
		$this->transactionRepository = $this->getMockBuilder(TransactionsRepository::class)->disableOriginalConstructor()->getMock();
		$this->hubspot = $this->getMockBuilder(Hubspot::class)->disableOriginalConstructor()->getMock();
		$this->generatedOffersDealsRepository = $this->getMockBuilder(GeneratedOffersDealsRepository::class)->disableOriginalConstructor()->getMock();
		$this->mailFactory = $this->getMockBuilder(MailFactory::class)->disableOriginalConstructor()->getMock();
		$this->bookingRequest = $this->getMockBuilder(BookingRequest::class)->disableOriginalConstructor()->getMock();
		$this->bookingConditionsFinder = $this->getMockBuilder(BookingConditionsFinder::class)->disableOriginalConstructor()->getMock();
		$this->reservationCode = $this->getMockBuilder(ReservationCode::class)->disableOriginalConstructor()->getMock();
		$this->helper = new BookingHelper(
			$this->customProductReservationInfoHelper,
			$this->availabilitiesRepository,
			$this->reservationsRepository,
			$this->transactionRepository,
			$this->bookingRequest,
			$this->hubspot,
			$this->generatedOffersDealsRepository,
			$this->mailFactory,
			$this->bookingConditionsFinder,
			$this->reservationCode,
		);
	}


	public function testCloseAvailableDates_WithDifferenceBetweenArrivalInDepartureOf7Days_WillCloseThis7DatesInDatabase()
	{
		$productId = 1;
		$arrival = new DateTime();
		$departure = $arrival->modify('+7 days');
		$entity = new AvailabilityEntity();
		$entity->id = $productId;
		$this->availabilitiesRepository->expects($this->exactly(7))->method('getBy')->willReturn($entity);
		$this->helper->updateAvailableDates($productId, $arrival, $departure, AvailabilityStatusEnum::X);
	}

	public function testGetBookingRequest_WillReturnBookingRequestClass()
	{
		$result = $this->helper->getBookingRequest();
		$this->assertEquals($result, $this->bookingRequest);
	}

	public function testCreateHsContact_OnSuccess_WillReturnContactId()
	{
		$reservationEntity = new ReservationEntity();
		$reservationEntity->contact_email = '<EMAIL>';
		$reservationEntity->adults = 2;
		$reservationEntity->children = 3;
		$reservationEntity->arrive = new DateTime();
		$reservationEntity->departure = $reservationEntity->arrive->modify('+7 days');
		$firstname = 'Nick';
		$lastname = 'Praskaton';
		$refferal = 'VG';

		$contactResponse = $this->contactResponse;
		$vid = 123;

		$this->hubspot->expects($this->once())->method('createContact')->willReturn($contactResponse);
		$this->contactResponse->expects($this->once())->method('toArray')->willReturn(['vid' => $vid]);

		$result = $this->helper->createHsContact($reservationEntity, $firstname, $lastname, $refferal);

		$this->assertEquals($vid, $result);
	}


	public function testCreateHsContact_IfContactIdIsNotInResponse_WillReturnNull()
	{
		$reservationEntity = new ReservationEntity();
		$reservationEntity->contact_email = '<EMAIL>';
		$reservationEntity->adults = 2;
		$reservationEntity->children = 3;
		$reservationEntity->arrive = new DateTime();
		$reservationEntity->departure = $reservationEntity->arrive->modify('+7 days');
		$firstname = 'Nick';
		$lastname = 'Praskaton';
		$refferal = 'VG';

		$contactResponse = $this->contactResponse;
		$vid = NULL;

		$this->hubspot->expects($this->once())->method('createContact')->willReturn($contactResponse);
		$this->contactResponse->expects($this->once())->method('toArray')->willReturn(['vid' => $vid]);

		$result = $this->helper->createHsContact($reservationEntity, $firstname, $lastname, $refferal);

		$this->assertEquals($vid, $result);
	}


	public function testCreateHsContact_IfContactIsNotCreated_WillReturnNull()
	{
		$reservationEntity = new ReservationEntity();
		$reservationEntity->contact_email = '<EMAIL>';
		$reservationEntity->adults = 2;
		$reservationEntity->children = 3;
		$reservationEntity->arrive = new DateTime();
		$reservationEntity->departure = $reservationEntity->arrive->modify('+7 days');
		$firstname = 'Nick';
		$lastname = 'Praskaton';
		$refferal = 'VG';

		$this->hubspot->expects($this->once())->method('createContact')->willThrowException(new BadRequest());

		$result = $this->helper->createHsContact($reservationEntity, $firstname, $lastname, $refferal);

		$this->assertEquals(null, $result);
	}

	public function testGetReservationsRepository_WillReturnReservationsRepositoryClass()
	{
		$result = $this->helper->getReservationsRepository();
		$this->assertEquals($result, $this->reservationsRepository);
	}

	public function testGetCustomProductHelper_WillReturncustomProductReservationInfoHelperClass()
	{
		$result = $this->helper->getCustomProductHelper();
		$this->assertEquals($result, $this->customProductReservationInfoHelper);
	}


	public function testUpdateHsContact_WillSendRequestToHs()
	{
		$reservationEntity = new ReservationEntity();
		$reservationEntity->contact_email = '<EMAIL>';
		$reservationEntity->adults = 2;
		$reservationEntity->children = 3;
		$reservationEntity->arrive = new DateTime();
		$reservationEntity->departure = $reservationEntity->arrive->modify('+7 days');
		$firstname = 'Nick';
		$lastname = 'Praskaton';
		$refferal = 'VG';

		$this->hubspot->expects($this->once())->method('updateContactByEmail')->willThrowException(new BadRequest());

		$this->helper->updateHsContact($reservationEntity, $firstname, $lastname, $refferal);
	}

}

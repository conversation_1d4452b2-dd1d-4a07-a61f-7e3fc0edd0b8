<?php declare(strict_types = 1);

namespace Tests\Common\ProductInfo;

use App\Common\Enums\PriceRuleTypesEnum;
use App\Common\ProductInfo\PriceCalculator;
use App\Models\Entities\PriceRuleEntity;
use App\Repositories\PriceRulesRepository;
use App\Repositories\SettingsRepository;
use Nette\Utils\DateTime;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class PriceCalculatorTest extends TestCase
{

	private MockObject|SettingsRepository $settingsRepoMock;

	private MockObject|PriceRulesRepository $priceRulesRepoMock;

	private PriceCalculator $priceCalculator;

	protected function setUp(): void
	{
		$this->settingsRepoMock = $this->createMock(SettingsRepository::class);
		$this->priceRulesRepoMock = $this->createMock(PriceRulesRepository::class);
		$this->priceCalculator = new PriceCalculator($this->settingsRepoMock, $this->priceRulesRepoMock);
	}

	public function testCalculateWithEmptyResults()
	{
		$results = [];
		$arrival = new DateTime('2024-05-01');
		$departure = new DateTime('2024-05-05');

		$calculatedResults = $this->priceCalculator->calculate($results, $arrival, $departure, 2);

		$this->assertEmpty($calculatedResults);
	}

	public function testCalculateWithPrivatePartner()
	{
		$results = [
			1 => (object) ['total_price' => 200, 'price_with_discount' => 200, 'partner' => 'private'],
		];
		$arrival = new DateTime('2024-05-01');
		$departure = new DateTime('2024-05-05');

		// Mock the responses from repositories
		$priceRule = new PriceRuleEntity();
		$priceRule->discount_percentage = 10;
		$priceRule->product_id = 1;
		$priceRule->price_decrease = true;
		$priceRule->price_increase = false;
		$priceRule->is_custom_rule = true;
		$priceRule->type = PriceRuleTypesEnum::DEFAULT;

		$this->priceRulesRepoMock->method('findRules')
			->willReturn([$priceRule]);

		// Mock the responses from repositories
		$priceRule = new PriceRuleEntity();
		$priceRule->product_id = 1;
		$priceRule->date_from = new DateTime('2024-05-01');
		$priceRule->date_to = new DateTime('2024-05-05');
		$priceRule->arrival_days = '1,2,3,4,5,6,7';
		$priceRule->departure_days = '1,2,3,4,5,6,7';
		$priceRule->min_stay_days = 1;
		$priceRule->max_stay_days = 84;
		$priceRule->max_days_to_arrival = null;

		$this->priceRulesRepoMock->method('findBy')
			->willReturn([$priceRule]);

		// Method under test
		$calculatedResults = $this->priceCalculator->calculate($results, $arrival, $departure, 2);

		$this->assertEquals(200, $calculatedResults[1]->total_price);
		$this->assertEquals(180, $calculatedResults[1]->price_with_discount);
	}

}

<?php declare(strict_types = 1);

namespace Tests\Common\ProductInfo\Helpers;

use App\Common\ProductInfo\Helpers\ProductSpecialOfferPriceCalculationHelper;
use App\Repositories\OffersRepository;
use Dibi\DateTime;
use Dibi\Row;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\BaseTest;

class ProductSpecialOfferPriceCalculationHelperTest extends BaseTest
{

	/**
	 * @var MockObject|OffersRepository
	 */
	protected $offersRepository;

	protected $helper;

	/**
	 * @var MockObject|Row
	 */
	protected $row;

	protected function setUp(): void
	{
		$this->row = $this->createDefaultMock(Row::class);
		$this->offersRepository = $this->createDefaultMock(OffersRepository::class);
		$this->helper = new ProductSpecialOfferPriceCalculationHelper($this->offersRepository);
	}


	public function testGetSpecialOfferDiscount_IfSpecialOfferDoesntExist_WillReturn0()
	{
		$this->offersRepository->expects($this->once())->method('getSpecialOfferRow')->willReturn(NULL);
		$value = $this->helper->getSpecialOfferDiscount(1, new DateTime());
		$this->assertEquals(0, $value);
	}

	public function testGetSpecialOfferDiscount_SpecialOfferExist_WillReturnFloat()
	{
		$this->row['discount_percentage'] = 10;
		$expectedResult = 10 / 100;
		$this->offersRepository->expects($this->once())->method('getSpecialOfferRow')->willReturn($this->row);
		$result = $this->helper->getSpecialOfferDiscount(1, new DateTime());
		$this->assertEquals($expectedResult, $result);
	}


	public function testGetPrice_IfSpecialOfferDiscountIsEmpty_WillReturnPrice()
	{
		$specialOfferDiscount = 0;
		$price = 10;
		$result = $this->helper->getPriceWithSpecialOfferDiscount($specialOfferDiscount, $price);
		$this->assertEquals($price, $result);
	}

	public function testGetPrice_IfSpecialOfferDiscountIsNotEmpty_WillCalculatePrice()
	{
		$specialOfferDiscount = 1.5;
		$price = 10;
		$expectedResult = $price - ($price * $specialOfferDiscount);
		$result = $this->helper->getPriceWithSpecialOfferDiscount($specialOfferDiscount, $price);
		$this->assertEquals($result, $expectedResult);
	}

}

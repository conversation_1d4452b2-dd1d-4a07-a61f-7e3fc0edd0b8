<?php declare(strict_types = 1);

namespace Tests\Common\ProductInfo;

use App\Common\ProductInfo\PrivateProductSeasonOverview;
use App\Repositories\OffersRepository;
use App\Repositories\PriceRulesRepository;
use App\Repositories\ProductPricesRepository;
use Dibi\Row;
use Nette\Utils\DateTime;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class PrivateProductSeasonOverviewTest extends TestCase
{

	private MockObject|ProductPricesRepository $productPricesRepository;

	private MockObject|OffersRepository $offersRepository;

	private PriceRulesRepository $priceRuleRepository;

	private PrivateProductSeasonOverview $productSeasonOverview;

	protected function setUp(): void
	{
		$this->offersRepository = $this->createMock(OffersRepository::class);
		$this->productPricesRepository = $this->createMock(ProductPricesRepository::class);
		$this->priceRuleRepository = $this->createMock(PriceRulesRepository::class);
		$this->productSeasonOverview = new PrivateProductSeasonOverview($this->productPricesRepository, $this->offersRepository, $this->priceRuleRepository);
	}


	public function testGet_IfNoPrices_WillReturnEmptyArray()
	{
		$prices = [];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);
		$this->assertEquals($result, $prices);
	}


	public function testGet_IfNoSpecialOffers_WillReturnRowsFromDatabase()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-05-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'price' => 100,
				'adjusted_price' => 100,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'price' => 150,
				'adjusted_price' => 150,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-08-01'),
				'price' => 200,
				'adjusted_price' => 200,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);

		$this->assertEquals(array_key_first($result), DateTime::createFromFormat('Y-m-d', '2022-05-01')->format('Ymd'));
		$lastArrayKey = DateTime::createFromFormat('Y-m-d', '2022-07-01')->format('Ymd');
		$this->assertEquals(array_key_last($result), $lastArrayKey);
		$this->assertEquals($result[$lastArrayKey]['to']->format('Ymd'), '20220801');
		$this->assertEquals($result['20220601']['to']->format('Ymd'), '20220701');
		$this->assertEquals($result['20220501']['to']->format('Ymd'), '20220601');

		$this->assertEquals(100, $result['20220501']['price']);
		$this->assertEquals(150, $result['20220601']['price']);
		$this->assertEquals(200, $result['20220701']['price']);
		$this->assertEquals(100, $result['20220501']['adjusted_price']);
		$this->assertEquals(150, $result['20220601']['adjusted_price']);
		$this->assertEquals(200, $result['20220701']['adjusted_price']);
		$this->assertEquals($this->productSeasonOverview->formatReturnArray($prices), $result);
	}


	public function testGet_IfOneSpecialOfferExistInRange_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-05-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-05-15'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-05-20'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);

		$this->assertEquals(count($result), 4);
		$this->assertEquals($result['20220501']['to']->format('Ymd'), '20220515');
		$this->assertEquals($result['20220515']['to']->format('Ymd'), '20220520');
		$this->assertEquals($result['20220520']['to']->format('Ymd'), '20220601');
		$this->assertEquals($result['20220601']['to']->format('Ymd'), '20220701');
		$this->assertEquals(array_key_first($result), DateTime::createFromFormat('Y-m-d', '2022-05-01')->format('Ymd'));
		$this->assertEquals(array_key_last($result), DateTime::createFromFormat('Y-m-d', '2022-06-01')->format('Ymd'));
	}

	public function testGet_IfOneSpecialOfferExistOutOfRange_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-05-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-05-15'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-06-05'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);
		$this->assertEquals($result['20220501']['to']->format('Ymd'), '20220515');
		$this->assertEquals($result['20220515']['to']->format('Ymd'), '20220601');
		$this->assertEquals($result['20220601']['to']->format('Ymd'), '20220605');
		$this->assertEquals($result['20220605']['to']->format('Ymd'), '20220701');
		$this->assertEquals(count($result), 4);
		$this->assertEquals(array_key_first($result), DateTime::createFromFormat('Y-m-d', '2022-05-01')->format('Ymd'));
		$this->assertEquals(array_key_last($result), DateTime::createFromFormat('Y-m-d', '2022-06-05')->format('Ymd'));
	}

	public function testGet_IfSpecialOfferIsAppliableOnFirstDay_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-05-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-05-01'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-05-05'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);
		$this->assertEquals($result['20220501']['to']->format('Ymd'), '20220505');
		$this->assertEquals($result['20220505']['to']->format('Ymd'), '20220601');
		$this->assertEquals($result['20220601']['to']->format('Ymd'), '20220701');
		$this->assertEquals(count($result), 3);
		$this->assertEquals(array_key_first($result), DateTime::createFromFormat('Y-m-d', '2022-05-01')->format('Ymd'));
		$this->assertEquals(array_key_last($result), DateTime::createFromFormat('Y-m-d', '2022-06-01')->format('Ymd'));
	}

	public function testGet_IfSpecialOfferIsAppliableOnFirstDayAndOutOfRange_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-05-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-05-01'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-06-05'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);
		$this->assertEquals($result['20220501']['to']->format('Ymd'), '20220601');
		$this->assertEquals($result['20220601']['to']->format('Ymd'), '20220605');
		$this->assertEquals($result['20220605']['to']->format('Ymd'), '20220701');
		$this->assertEquals(count($result), 3);
		$this->assertEquals(array_key_first($result), DateTime::createFromFormat('Y-m-d', '2022-05-01')->format('Ymd'));
		$this->assertEquals(array_key_last($result), DateTime::createFromFormat('Y-m-d', '2022-06-05')->format('Ymd'));
	}


	public function testGet_IfMoreThanOneOfferExistInRange_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-05-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-06-05'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-06-10'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-06-20'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-06-27'),
				'price' => 100,
				'adjusted_price' => 100,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);

		$this->assertEquals(6, count($result));
		$this->assertEquals($result['20220501']['to']->format('Ymd'), '20220601');
		$this->assertEquals($result['20220601']['to']->format('Ymd'), '20220605');
		$this->assertEquals($result['20220605']['to']->format('Ymd'), '20220610');
		$this->assertEquals($result['20220610']['to']->format('Ymd'), '20220620');
		$this->assertEquals($result['20220620']['to']->format('Ymd'), '20220627');
		$this->assertEquals($result['20220627']['to']->format('Ymd'), '20220701');
		$this->assertEquals(array_key_first($result), DateTime::createFromFormat('Y-m-d', '2022-05-01')->format('Ymd'));
		$this->assertEquals(array_key_last($result), DateTime::createFromFormat('Y-m-d', '2022-06-27')->format('Ymd'));
	}


	public function testGet_IfMoreThanOneOfferExistOutOfRange_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-24'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'price' => 4340,
				'adjusted_price' => 4340,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-02'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-05'),
				'price' => 4753,
				'adjusted_price' => 4753,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-05'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-22'),
				'price' => 4753,
				'adjusted_price' => 4753,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-08-26'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-24'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-29'),
				'price' => 6230,
				'adjusted_price' => 6230,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-08-26'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-09-10'),
				'price' => 4753,
				'adjusted_price' => 4753,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-09-10'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-09-24'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-09-15'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-09-27'),
				'price' => 3283,
				'adjusted_price' => 3283,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-09-24'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-09-30'),
				'price' => 2730,
				'adjusted_price' => 2730,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);

		$this->assertEquals(13, count($result));
		$this->assertEquals($result['20220624']['to']->format('Ymd'), '20220701');
		$this->assertEquals($result['20220701']['to']->format('Ymd'), '20220702');
		$this->assertEquals($result['20220702']['to']->format('Ymd'), '20220705');
		$this->assertEquals($result['20220705']['to']->format('Ymd'), '20220708');
		$this->assertEquals($result['20220708']['to']->format('Ymd'), '20220722');
		$this->assertEquals($result['20220722']['to']->format('Ymd'), '20220724');
		$this->assertEquals($result['20220724']['to']->format('Ymd'), '20220729');
		$this->assertEquals($result['20220729']['to']->format('Ymd'), '20220826');
		$this->assertEquals($result['20220826']['to']->format('Ymd'), '20220910');
		$this->assertEquals($result['20220910']['to']->format('Ymd'), '20220915');
		$this->assertEquals($result['20220915']['to']->format('Ymd'), '20220924');
		$this->assertEquals($result['20220924']['to']->format('Ymd'), '20220927');
		$this->assertEquals($result['20220927']['to']->format('Ymd'), '20220930');

		$this->assertEquals(4340, $result['20220624']['price']);
		$this->assertEquals(4753, $result['20220701']['price']);
		$this->assertEquals(4753, $result['20220702']['price']);
		$this->assertEquals(4753, $result['20220705']['price']);
		$this->assertEquals(6230, $result['20220708']['price']);
		$this->assertEquals(6230, $result['20220722']['price']);
		$this->assertEquals(6230, $result['20220724']['price']);
		$this->assertEquals(6230, $result['20220729']['price']);
		$this->assertEquals(4753, $result['20220826']['price']);
		$this->assertEquals(3283, $result['20220910']['price']);
		$this->assertEquals(3283, $result['20220915']['price']);
		$this->assertEquals(2730, $result['20220924']['price']);
		$this->assertEquals(2730, $result['20220927']['price']);

		$this->assertEquals(array_key_first($result), '20220624');
		$this->assertEquals(array_key_last($result), '20220927');
	}


	public function testGet_IfMoreThanOneOfferExistOutOfRange4_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-05'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-22'),
				'price' => 4753,
				'adjusted_price' => 4753,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-08-26'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-24'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-29'),
				'price' => 6230,
				'adjusted_price' => 6230,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-08-26'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-08-10'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-08-30'),
				'price' => 6230,
				'adjusted_price' => 6230,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);

		$this->assertEquals($result['20220701']['to']->format('Ymd'), '20220705');
		$this->assertEquals($result['20220705']['to']->format('Ymd'), '20220708');
		$this->assertEquals($result['20220708']['to']->format('Ymd'), '20220722');
		$this->assertEquals($result['20220722']['to']->format('Ymd'), '20220724');
		$this->assertEquals($result['20220724']['to']->format('Ymd'), '20220729');
		$this->assertEquals($result['20220729']['to']->format('Ymd'), '20220810');
		$this->assertEquals($result['20220810']['to']->format('Ymd'), '20220826');
		$this->assertEquals($result['20220826']['to']->format('Ymd'), '20220830');
	}


	public function testGet_OutOfRange_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-05'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-22'),
				'price' => 4753,
				'adjusted_price' => 4753,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-08-26'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-24'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-29'),
				'price' => 6230,
				'adjusted_price' => 6230,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);
		$this->assertEquals($result['20220701']['to']->format('Ymd'), '20220705');
		$this->assertEquals($result['20220705']['to']->format('Ymd'), '20220708');
		$this->assertEquals($result['20220708']['to']->format('Ymd'), '20220722');
		$this->assertEquals($result['20220722']['to']->format('Ymd'), '20220724');
		$this->assertEquals($result['20220724']['to']->format('Ymd'), '20220729');
		$this->assertEquals($result['20220729']['to']->format('Ymd'), '20220826');
	}


	public function testGet_IfMoreThanOneOfferExistOutOfRange1_WillReturnFormattedArray()
	{
		$prices = [
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-06-24'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'price' => 4340,
				'adjusted_price' => 4340,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-02'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-05'),
				'price' => 4753,
				'adjusted_price' => 4753,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-01'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-05'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-22'),
				'price' => 4753,
				'adjusted_price' => 4753,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-08-26'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-07-24'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-07-29'),
				'price' => 6230,
				'adjusted_price' => 6230,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-08-26'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-09-10'),
				'price' => 4753,
				'adjusted_price' => 4753,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-09-10'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-09-24'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-09-15'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-09-27'),
				'price' => 3283,
				'adjusted_price' => 3283,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-09-24'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-09-30'),
				'price' => 2730,
				'adjusted_price' => 2730,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
			new Row([
				'from' => DateTime::createFromFormat('Y-m-d', '2022-07-08'),
				'to' => DateTime::createFromFormat('Y-m-d', '2022-08-26'),
				'offer_from' => DateTime::createFromFormat('Y-m-d', '2022-08-10'),
				'offer_to' => DateTime::createFromFormat('Y-m-d', '2022-08-30'),
				'price' => 6230,
				'adjusted_price' => 6230,
				'min_stay_days' => 2,
				'season' => 2022,
			]),
		];
		$this->productPricesRepository->expects($this->once())
			->method('getPrivateSeasonOverviewPricesMinStayByProductIdYear')
			->willReturn($prices);
		$result = $this->productSeasonOverview->get(1);

		$this->assertEquals(array_key_first($result), '20220624');
		$this->assertEquals(array_key_last($result), '20220927');
	}

}

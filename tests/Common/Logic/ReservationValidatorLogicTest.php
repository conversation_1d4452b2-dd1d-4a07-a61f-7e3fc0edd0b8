<?php declare(strict_types = 1);

namespace Tests\App\Common\Logic;

use App\Common\Logic\ReservationValidatorLogic;
use App\Models\Entities\ReservationEntity;
use App\Repositories\ReservationsRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class ReservationValidatorLogicTest extends TestCase
{

	protected MockObject|ReservationsRepository $repositoryMock;

	protected ReservationValidatorLogic $reservationValidatorLogic;

	protected function setUp(): void
	{
		// Create a mock for the ReservationsRepository class
		$this->repositoryMock = $this->createMock(ReservationsRepository::class);

		// Initialize the ReservationValidatorLogic class with the mocked repository
		$this->reservationValidatorLogic = new ReservationValidatorLogic($this->repositoryMock);
	}

	public function testGetActiveReservationForDatesReturnsNullWhenNoReservationsExist(): void
	{
		$productId = 1;
		$arrive = '2024-10-01';
		$departure = '2024-10-05';
		$reservationId = null;

		// Mock the repository to return an empty array (no reservations)
		$this->repositoryMock->method('findBy')->willReturn([]);

		$result = $this->reservationValidatorLogic->getActiveReservationForDates($productId, $arrive, $departure, $reservationId);

		// Assert that the result is null when no active reservation exists
		$this->assertNull($result);
	}

	public function testGetActiveReservationForDatesReturnsReservationWhenOverlapping(): void
	{
		$productId = 1;
		$arrive = '2024-10-01';
		$departure = '2024-10-05';
		$reservationId = null;

		// Create a ReservationEntity object with overlapping dates
		$reservation = new ReservationEntity();
		$reservation->arrive = new \DateTime('2024-10-03');
		$reservation->departure = new \DateTime('2024-10-06');
		$reservation->id = 234;

		// Mock the repository to return a reservation
		$this->repositoryMock->method('findBy')->willReturn([$reservation]);

		$result = $this->reservationValidatorLogic->getActiveReservationForDates($productId, $arrive, $departure, $reservationId);

		// Assert that the returned reservation is the expected one
		$this->assertSame($reservation, $result);
	}

	public function testGetActiveReservationForDatesReturnsNullWhenNoOverlap(): void
	{
		$productId = 1;
		$arrive = '2024-10-01';
		$departure = '2024-10-05';
		$reservationId = null;

		// Create a ReservationEntity object with non-overlapping dates
		$reservation = new ReservationEntity();
		$reservation->arrive = new \DateTime('2024-10-06');
		$reservation->departure = new \DateTime('2024-10-10');
		$reservation->id = 234;

		// Mock the repository to return a reservation
		$this->repositoryMock->method('findBy')->willReturn([$reservation]);

		$result = $this->reservationValidatorLogic->getActiveReservationForDates($productId, $arrive, $departure, $reservationId);

		// Assert that the result is null since the reservation does not overlap
		$this->assertNull($result);
	}

	public function testGetActiveReservationForDatesExcludesGivenReservationId(): void
	{
		$productId = 1;
		$arrive = '2024-10-01';
		$departure = '2024-10-05';
		$reservationId = 123;

		// Create a ReservationEntity object with overlapping dates but with the excluded ID
		$reservation = new ReservationEntity();
		$reservation->id = 123;
		$reservation->arrive = new \DateTime('2024-10-03');
		$reservation->departure = new \DateTime('2024-10-06');

		// Mock the repository to return this reservation
		$this->repositoryMock->method('findBy')->willReturn([$reservation]);

		$result = $this->reservationValidatorLogic->getActiveReservationForDates($productId, $arrive, $departure, $reservationId);

		// Assert that the result is null because the reservation should be excluded by its ID
		$this->assertNull($result);
	}

}

<?php

namespace Tests\Common;

use App\Common\Authenticator;
use App\Models\Entities\UserDetailEntity;
use App\Repositories\AuthRepository;
use Nette\Security\Identity;
use Nette\Security\Passwords;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\BaseTest;

class AuthenticatorTest extends BaseTest
{

	protected Authenticator $authenticator;

	protected MockObject|AuthRepository $repository;

	protected function setUp(): void
	{
		$this->repository = $this->getMockBuilder(AuthRepository::class)->disableOriginalConstructor()->getMock();
		$this->authenticator = new Authenticator($this->repository);
	}


	public function testAuthenticate_WithWrongUsername_WillThrowAuthenticationException()
	{
		$this->expectException('Nette\Security\AuthenticationException');
		$this->expectExceptionCode('1');
		$this->authenticator->authenticate(['user', 'pass']);
	}


	public function testAuthenticate_WithInactiveUser_WillThrowAuthenticationException()
	{
		$this->expectException('Nette\Security\AuthenticationException');
		$this->expectExceptionCode('4');
		$user = new UserDetailEntity();
		$user->email = '<EMAIL>';
		$user->password = 'mksjdksjd';
		$user->active = FALSE;

		$this->repository->method('getUserByEmail')->with('<EMAIL>')->willReturn($user);
		$this->authenticator->authenticate(['<EMAIL>', 'pass']);
	}


	public function testAuthenticate_WithWrongPassword_WillThrowAuthenticationException()
	{
		$this->expectException('Nette\Security\AuthenticationException');
		$this->expectExceptionCode('2');
		$user = new UserDetailEntity();
		$user->email = '<EMAIL>';
		$user->password = 'mksjdksjd';
		$user->active = TRUE;

		$this->repository->method('getUserByEmail')->with('<EMAIL>')->willReturn($user);
		$this->authenticator->authenticate(['<EMAIL>', 'pass']);
	}


	public function testAuthenticate_WithCorrectData_WillReturnIdentity()
	{
		$user = new UserDetailEntity();
		$user->id = 3;
		$user->email = '<EMAIL>';
		$passwords = new Passwords();
		$user->password = $passwords->hash('password789');
		$user->active = TRUE;

		$this->repository->method('getUserByEmail')->with('<EMAIL>')->willReturn($user);
		$this->repository->method('getUserRoles')->willReturn(['user', 'admin']);
		$result = $this->authenticator->authenticate(['<EMAIL>', 'password789']);
		$this->assertInstanceOf(Identity::class, $result);
		$this->assertEquals('<EMAIL>', $result->email);
	}

}

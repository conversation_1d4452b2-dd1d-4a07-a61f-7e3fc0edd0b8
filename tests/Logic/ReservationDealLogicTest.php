<?php declare(strict_types = 1);

namespace Tests\Logic;

use App\Common\Enums\DealStagesEnum;
use App\Common\ProductInfo\ProductDealService;
use App\Models\Entities\GeneratedOfferDealEntity;
use App\Models\Entities\ReservationEntity;
use App\Modules\Front\Logic\ReservationDealLogic;
use App\Repositories\GeneratedOffersDealsRepository;
use App\Repositories\GeneratedOffersItemsRepository;
use App\Repositories\ReservationsRepository;
use PHPUnit\Framework\TestCase;

class ReservationDealLogicTest extends TestCase
{

	private ReservationDealLogic $logic;

	private GeneratedOffersDealsRepository $dealsRepository;

	private GeneratedOffersItemsRepository $itemsRepository;

	private ReservationsRepository $reservationsRepository;

	private ProductDealService $productDealService;

	protected function setUp(): void
	{
		$this->dealsRepository = $this->createMock(GeneratedOffersDealsRepository::class);
		$this->itemsRepository = $this->createMock(GeneratedOffersItemsRepository::class);
		$this->reservationsRepository = $this->createMock(ReservationsRepository::class);
		$this->productDealService = $this->createMock(ProductDealService::class);

		$this->logic = new ReservationDealLogic(
			$this->dealsRepository,
			$this->itemsRepository,
			$this->reservationsRepository,
			$this->productDealService
		);
	}

	public function testGetDealIdReturnsNullWhenDealIdIsMissingFromSession(): void
	{
		$this->productDealService
			->expects($this->once())
			->method('getDealId')
			->willReturn(null);

		$this->assertNull($this->logic->getDealId(123));
	}

	public function testGetDealIdReturnsDealIdWhenDealNotFoundInDatabase(): void
	{
		$this->productDealService
			->expects($this->once())
			->method('getDealId')
			->willReturn(123);

		$this->dealsRepository
			->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(123)
			->willReturn(null);

		$this->assertTrue($this->logic->getDealId(123) === 123);
	}

	public function testGetDealIdReturnsNullWhenDealIsClosedWon(): void
	{
		$this->productDealService
			->expects($this->once())
			->method('getDealId')
			->willReturn(123);

		$this->dealsRepository
			->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(123)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::CLOSE_WON]));

		$this->assertNull($this->logic->getDealId(123));
	}

	public function testGetDealIdReturnsNullWhenProductIsNotInOffer(): void
	{
		$this->productDealService
			->expects($this->once())
			->method('getDealId')
			->willReturn(123);

		$this->dealsRepository
			->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(123)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::OPTION, 'offer_id' => 456]));

		$this->itemsRepository
			->expects($this->once())
			->method('getOfferItem')
			->with(456, 789)
			->willReturn(null);

		$this->assertNull($this->logic->getDealId(789));
	}


	public function testGetDealIdReturnsNullWhenReservationNotFound(): void
	{
		$this->productDealService
			->expects($this->once())
			->method('getDealId')
			->willReturn(123);

		$this->dealsRepository
			->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(123)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::OPTION, 'offer_id' => null]));

		$this->reservationsRepository
			->expects($this->once())
			->method('getReservationByHubspotDealId')
			->with(123)
			->willReturn(null);

		$this->assertEquals(123, $this->logic->getDealId(456));
	}

	public function testGetDealIdReturnsNullWhenDealStageIsNotOption(): void
	{
		$this->productDealService
			->expects($this->once())
			->method('getDealId')
			->willReturn(123);

		$this->dealsRepository
			->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(123)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::OFFER_SENT, 'offer_id' => null]));

		$this->reservationsRepository
			->expects($this->once())
			->method('getReservationByHubspotDealId')
			->with(123)
			->willReturn(['id' => 456]);

		$this->assertNull($this->logic->getDealId(456));
	}

	public function testGetDealIdUpdatesConnectedReservationAndReturnsDealId(): void
	{
		$this->productDealService
			->expects($this->once())
			->method('getDealId')
			->willReturn(123);

		$this->dealsRepository
			->expects($this->once())
			->method('getDealByHubspotDealId')
			->with(123)
			->willReturn(new GeneratedOfferDealEntity(['deal_stage' => DealStagesEnum::OPTION, 'offer_id' => null]));

		$this->reservationsRepository
			->expects($this->once())
			->method('getReservationByHubspotDealId')
			->with(123)
			->willReturn(['id' => 456]);

		$this->reservationsRepository
			->expects($this->once())
			->method('update')
			->with(456, [ReservationEntity::DEAL_ID => null]);

		$this->assertSame(123, $this->logic->getDealId(789));
	}

}

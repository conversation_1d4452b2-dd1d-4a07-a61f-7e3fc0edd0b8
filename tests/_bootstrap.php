<?php

use Nette\Configurator;
use Nette\Utils\FileSystem;

require __DIR__ . '/../vendor/autoload.php';
require __DIR__ . '/BaseTest.php';
define('WWW_DIR', __DIR__ . '/../www');
$configurator = new Configurator();
$configurator->setDebugMode(FALSE);
$configurator->setTempDirectory(__DIR__ . '/../temp/tests');
@FileSystem::createDir(__DIR__ . '/../temp/tests/cache');
$configurator->createRobotLoader()
	->addDirectory(__DIR__ . '/../app')
	->addDirectory(__DIR__)
	->register();

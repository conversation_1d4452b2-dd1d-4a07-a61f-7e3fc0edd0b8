<?php declare(strict_types = 1);

namespace Tests\Property\Logic;

use App\Common\PaymentConfirmation\ConfirmationTypes;
use App\Common\Queue\Producers\PaymentConfirmationInvoiceProducer;
use App\Models\Entities\ReservationEntity;
use App\Modules\Property\Logic\PaymentConfirmationLogic;
use App\Modules\Property\Logic\PaymentConfirmationTypeLogic;
use PHPUnit\Framework\TestCase;

class PaymentConfirmationLogicTest extends TestCase
{

	public function testSendPaymentConfirmationNotification(): void
	{
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->website_lang = 'en';
		$reservationEntity->contact_email = '<EMAIL>';
		$formValues = ['key' => 'value'];

		$producerMock = $this->createMock(PaymentConfirmationInvoiceProducer::class);
		$producerMock->expects($this->once())
			->method('publish')
			->with($this->callback(fn ($params) => $params->reservationId === $reservationEntity->id
					&& $params->language === $reservationEntity->website_lang
					&& $params->confirmationType === ConfirmationTypes::FULL));

		$typeLogicMock = $this->createMock(PaymentConfirmationTypeLogic::class);
		$typeLogicMock->expects($this->once())
			->method('getPaymentConfirmationNotificationType')
			->with($formValues, $reservationEntity)
			->willReturn(ConfirmationTypes::FULL);

		$logic = new PaymentConfirmationLogic($producerMock, $typeLogicMock);
		$logic->sendPaymentConfirmationNotification($reservationEntity, $formValues);
		// We're not expecting any return value or exception, so we can just check that the mocks were called as expected
	}

	public function testWhenConfirmationTypeIsNull(): void
	{
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->website_lang = 'en';
		$reservationEntity->contact_email = '<EMAIL>';
		$formValues = ['key' => 'value'];

		$producerMock = $this->createMock(PaymentConfirmationInvoiceProducer::class);
		$producerMock->expects($this->never())->method('publish');
		$typeLogicMock = $this->createMock(PaymentConfirmationTypeLogic::class);
		$typeLogicMock->expects($this->once())
			->method('getPaymentConfirmationNotificationType')
			->with($formValues, $reservationEntity)
			->willReturn(null);

		$logic = new PaymentConfirmationLogic($producerMock, $typeLogicMock);
		$logic->sendPaymentConfirmationNotification($reservationEntity, $formValues);
		// We're not expecting any return value or exception, so we can just check that the mocks were called as expected
	}

}

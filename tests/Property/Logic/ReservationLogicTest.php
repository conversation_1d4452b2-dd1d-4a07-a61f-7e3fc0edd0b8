<?php declare(strict_types = 1);

namespace Tests\Property\Logic;

use App\Common\EnvironmentDetector;
use App\Common\Hubspot;
use App\Common\Logic\ReservationValidatorLogic;
use App\Common\Queue\Producers\AvailabilityProducer;
use App\Common\Reservation\Helpers\DistributionChannelHelper;
use App\Common\Reservation\PartnerInstallmentsAmountResolver;
use App\Common\Reservation\ReservationAgencyProvisionResolver;
use App\Common\Services\ProductOwnerNotificationService;
use App\Models\Entities\ReservationEntity;
use App\Modules\Property\Logic\PaymentConfirmationLogic;
use App\Modules\Property\Logic\ReservationLogic;
use App\Repositories\GeneratedOffersDealsRepository;
use App\Repositories\ProductRepository;
use App\Repositories\ReservationInsuranceRepository;
use App\Repositories\ReservationLogRepository;
use App\Repositories\ReservationsRepository;
use Nette\Utils\DateTime;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\EventDispatcher\EventDispatcherInterface;

class ReservationLogicTest extends TestCase
{

	private ReservationLogic $logic;

	private ReservationsRepository|MockObject $repository;

	private ProductRepository|MockObject $productRepository;

	private ReservationInsuranceRepository|MockObject $insuranceRepository;

	private ProductOwnerNotificationService|MockObject $notificationService;

	private Hubspot|MockObject $hubspot;

	private PaymentConfirmationLogic|MockObject $paymentConfirmationLogic;

	private GeneratedOffersDealsRepository|MockObject $generatedOffersDealsRepository;

	private ReservationLogRepository|MockObject $reservationLogRepository;

	private EnvironmentDetector|MockObject $environmentDetector;

	private EventDispatcherInterface|MockObject $dispatcher;

	private DistributionChannelHelper|MockObject $distributionChannelHelper;

	private PartnerInstallmentsAmountResolver|MockObject $partnerInstallmentsAmountResolver;

	private AvailabilityProducer|MockObject $availabilityProducer;

	private ReservationValidatorLogic|MockObject $reservationValidatorLogic;

	private ReservationAgencyProvisionResolver|MockObject $reservationAgencyProvisionResolver;

	protected function setUp(): void
	{
		$this->repository = $this->createMock(ReservationsRepository::class);
		$this->productRepository = $this->createMock(ProductRepository::class);
		$this->insuranceRepository = $this->createMock(ReservationInsuranceRepository::class);
		$this->notificationService = $this->createMock(ProductOwnerNotificationService::class);
		$this->hubspot = $this->createMock(Hubspot::class);
		$this->paymentConfirmationLogic = $this->createMock(PaymentConfirmationLogic::class);
		$this->generatedOffersDealsRepository = $this->createMock(GeneratedOffersDealsRepository::class);
		$this->reservationLogRepository = $this->createMock(ReservationLogRepository::class);
		$this->environmentDetector = $this->createMock(EnvironmentDetector::class);
		$this->dispatcher = $this->createMock(EventDispatcherInterface::class);
		$this->distributionChannelHelper = $this->createMock(DistributionChannelHelper::class);
		$this->partnerInstallmentsAmountResolver = $this->createMock(PartnerInstallmentsAmountResolver::class);
		$this->availabilityProducer = $this->createMock(AvailabilityProducer::class);
		$this->reservationValidatorLogic = $this->createMock(ReservationValidatorLogic::class);
		$this->reservationAgencyProvisionResolver = $this->createMock(ReservationAgencyProvisionResolver::class);
		$this->logic = new ReservationLogic(
			$this->repository,
			$this->productRepository,
			$this->insuranceRepository,
			$this->notificationService,
			$this->hubspot,
			$this->generatedOffersDealsRepository,
			$this->paymentConfirmationLogic,
			$this->reservationLogRepository,
			$this->dispatcher,
			$this->environmentDetector,
			$this->distributionChannelHelper,
			$this->partnerInstallmentsAmountResolver,
			$this->availabilityProducer,
			$this->reservationValidatorLogic,
			$this->reservationAgencyProvisionResolver
		);
	}

	public function testReservationHasChanges_WithNewDates_ShouldReturnTrue(): void
	{
		$reservationEntity = $this->getReservationEntity();
		$newArriveDate = (new DateTime())->modify('+11 day');
		$newDepartureDate = (new DateTime())->modify('+20 day');
		$formValues = [ReservationEntity::ARRIVE => $newArriveDate->format('Y-m-d'), ReservationEntity::DEPARTURE => $newDepartureDate->format('Y-m-d')];

		$this->assertTrue($this->logic->reservationHasChanges($reservationEntity, $formValues));
	}

	public function testReservationHasChanges_WithAdultsCount_ShouldReturnTrue(): void
	{
		$reservationEntity = $this->getReservationEntity();
		$reservationEntity->contact_firstname = 'Tim';
		$reservationEntity->contact_lastname = 'Barkley';
		$reservationEntity->adults = 5;
		$newArriveDate = (new DateTime())->modify('+10 day');
		$newDepartureDate = (new DateTime())->modify('+20 day');
		$formValues = [
			ReservationEntity::ARRIVE => $newArriveDate->format('Y-m-d'),
			ReservationEntity::DEPARTURE => $newDepartureDate->format('Y-m-d'),
			ReservationEntity::CONTACT_FIRSTNAME => 'Tim',
			ReservationEntity::CONTACT_LASTNAME => 'Barkley',
			ReservationEntity::ADULTS => '4',
		];

		$this->assertTrue($this->logic->reservationHasChanges($reservationEntity, $formValues));
	}

	public function testReservationHasChanges_WithPetsCount_ShouldReturnTrue(): void
	{
		$reservationEntity = $this->getReservationEntity();
		$reservationEntity->contact_firstname = 'Tim';
		$reservationEntity->contact_lastname = 'Barkley';
		$reservationEntity->adults = 5;
		$reservationEntity->pets = 2;
		$newArriveDate = (new DateTime())->modify('+10 day');
		$newDepartureDate = (new DateTime())->modify('+20 day');
		$formValues = [
			ReservationEntity::ARRIVE => $newArriveDate->format('Y-m-d'),
			ReservationEntity::DEPARTURE => $newDepartureDate->format('Y-m-d'),
			ReservationEntity::CONTACT_FIRSTNAME => 'Tim',
			ReservationEntity::CONTACT_LASTNAME => 'Barkley',
			ReservationEntity::ADULTS => 5,
			ReservationEntity::PETS => 1,
		];

		$this->assertTrue($this->logic->reservationHasChanges($reservationEntity, $formValues));
	}

	public function testReservationHasChanges_WithChildrenCount_ShouldReturnTrue(): void
	{
		$reservationEntity = $this->getReservationEntity();
		$reservationEntity->contact_firstname = 'Tim';
		$reservationEntity->contact_lastname = 'Barkley';
		$reservationEntity->adults = 5;
		$reservationEntity->pets = 2;
		$reservationEntity->children = 2;
		$newArriveDate = (new DateTime())->modify('+10 day');
		$newDepartureDate = (new DateTime())->modify('+20 day');
		$formValues = [
			ReservationEntity::ARRIVE => $newArriveDate->format('Y-m-d'),
			ReservationEntity::DEPARTURE => $newDepartureDate->format('Y-m-d'),
			ReservationEntity::CONTACT_FIRSTNAME => 'Tim',
			ReservationEntity::CONTACT_LASTNAME => 'Barkley',
			ReservationEntity::ADULTS => 5,
			ReservationEntity::PETS => 2,
			ReservationEntity::CHILDREN => 3,
		];

		$this->assertTrue($this->logic->reservationHasChanges($reservationEntity, $formValues));
	}

	public function testReservationHasChanges_WithPhone_ShouldReturnTrue(): void
	{
		$reservationEntity = $this->getReservationEntity();
		$reservationEntity->contact_firstname = 'Tim';
		$reservationEntity->contact_lastname = 'Barkley';
		$reservationEntity->adults = 5;
		$reservationEntity->pets = 2;
		$reservationEntity->children = 2;
		$reservationEntity->amount = 100;
		$reservationEntity->phone = '+38563312555';
		$newArriveDate = (new DateTime())->modify('+10 day');
		$newDepartureDate = (new DateTime())->modify('+20 day');
		$formValues = [
			ReservationEntity::ARRIVE => $newArriveDate->format('Y-m-d'),
			ReservationEntity::DEPARTURE => $newDepartureDate->format('Y-m-d'),
			ReservationEntity::CONTACT_FIRSTNAME => 'Tim',
			ReservationEntity::CONTACT_LASTNAME => 'Barkley',
			ReservationEntity::ADULTS => 5,
			ReservationEntity::PETS => 2,
			ReservationEntity::CHILDREN => 2,
			ReservationEntity::AMOUNT => 100,
			ReservationEntity::PHONE => '+38563312222',
		];

		$this->assertTrue($this->logic->reservationHasChanges($reservationEntity, $formValues));
	}

	public function testReservationHasNoChanges_WithData_ShouldReturnFalse(): void
	{
		$reservationEntity = $this->getReservationEntity();
		$reservationEntity->contact_firstname = 'Tim';
		$reservationEntity->contact_lastname = 'Barkley';
		$reservationEntity->adults = 5;
		$reservationEntity->pets = 2;
		$reservationEntity->children = 2;
		$reservationEntity->amount = 100;
		$reservationEntity->phone = '+38563312555';
		$newArriveDate = (new DateTime())->modify('+10 day');
		$newDepartureDate = (new DateTime())->modify('+20 day');
		$formValues = [
			ReservationEntity::ARRIVE => $newArriveDate->format('Y-m-d'),
			ReservationEntity::DEPARTURE => $newDepartureDate->format('Y-m-d'),
			ReservationEntity::CONTACT_FIRSTNAME => 'Tim',
			ReservationEntity::CONTACT_LASTNAME => 'Barkley',
			ReservationEntity::ADULTS => 5,
			ReservationEntity::PETS => 2,
			ReservationEntity::CHILDREN => 2,
			ReservationEntity::AMOUNT => 100,
			ReservationEntity::PHONE => '+38563312555',
		];

		$this->assertFalse($this->logic->reservationHasChanges($reservationEntity, $formValues));
	}

	public function getReservationEntity(): ReservationEntity
	{
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->arrive = (new DateTime())->modify('+10 day');
		$reservationEntity->departure = (new DateTime())->modify('+20 day');

		return $reservationEntity;
	}

}

<?php declare(strict_types = 1);

namespace Tests\Property\Logic;

use App\Common\Enums\InstallmentsPaymentStatusEnum;
use App\Common\PaymentConfirmation\ConfirmationTypes;
use App\Models\Entities\ReservationEntity;
use App\Modules\Property\Logic\PaymentConfirmationTypeLogic;
use PHPUnit\Framework\TestCase;

class PaymentConfirmationTypeLogicTest extends TestCase
{

	public function testGetPaymentConfirmationNotificationTypeWithBothInstallmentsPaid(): void
	{
		$formValues = [
			ReservationEntity::FIRST_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::PAID,
			ReservationEntity::SECOND_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::PAID,
		];
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->two_installments = true;
		$reservationEntity->first_installment_payment_status = InstallmentsPaymentStatusEnum::PAID;
		$reservationEntity->second_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$logic = new PaymentConfirmationTypeLogic();
		$result = $logic->getPaymentConfirmationNotificationType($formValues, $reservationEntity);
		$this->assertSame(ConfirmationTypes::REST, $result);
	}

	public function testGetPaymentConfirmationNotificationTypeWithFirstInstallmentPaid(): void
	{
		$formValues = [
			ReservationEntity::FIRST_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::PAID,
			ReservationEntity::SECOND_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
		];
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->two_installments = false;
		$reservationEntity->first_installment_payment_status = InstallmentsPaymentStatusEnum::PAID;
		$logic = new PaymentConfirmationTypeLogic();
		$result = $logic->getPaymentConfirmationNotificationType($formValues, $reservationEntity);
		$this->assertSame(null, $result);
	}


	public function testGetPaymentConfirmationNotificationTypeWithFirstInstallmentNotPaidAndTwoInstallments(): void
	{
		$formValues = [
			ReservationEntity::FIRST_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
			ReservationEntity::SECOND_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
		];
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->two_installments = true;
		$reservationEntity->first_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$logic = new PaymentConfirmationTypeLogic();
		$result = $logic->getPaymentConfirmationNotificationType($formValues, $reservationEntity);
		$this->assertSame(null, $result);
	}

	public function testGetPaymentConfirmationNotificationTypeWithFirstInstallmentIsPaidAndTwoInstallments(): void
	{
		$formValues = [
			ReservationEntity::FIRST_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::PAID,
			ReservationEntity::SECOND_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
		];
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->two_installments = true;
		$reservationEntity->first_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$logic = new PaymentConfirmationTypeLogic();
		$result = $logic->getPaymentConfirmationNotificationType($formValues, $reservationEntity);
		$this->assertSame(ConfirmationTypes::ADVANCE, $result);
	}

	public function testGetPaymentConfirmationNotificationTypeWithFirstInstallmentIsPaidAndOneInstallment(): void
	{
		$formValues = [
			ReservationEntity::FIRST_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::PAID,
			ReservationEntity::SECOND_INSTALLMENT_PAYMENT_STATUS => null,
		];
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->two_installments = false;
		$reservationEntity->first_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$logic = new PaymentConfirmationTypeLogic();
		$result = $logic->getPaymentConfirmationNotificationType($formValues, $reservationEntity);
		$this->assertSame(ConfirmationTypes::FULL, $result);
	}

	public function testGetPaymentConfirmationNotificationTypeWithSecondInstallmentPaid(): void
	{
		$formValues = [
			ReservationEntity::FIRST_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
			ReservationEntity::SECOND_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::PAID,
		];
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->two_installments = true;
		$reservationEntity->first_installment_payment_status = InstallmentsPaymentStatusEnum::PAID;
		$reservationEntity->second_installment_payment_status = InstallmentsPaymentStatusEnum::PAID;
		$logic = new PaymentConfirmationTypeLogic();
		$result = $logic->getPaymentConfirmationNotificationType($formValues, $reservationEntity);
		$this->assertNull($result);
	}


	public function testGetPaymentConfirmationNotificationTypeWithBothInstallmentsNotPaid(): void
	{
		$formValues = [
			ReservationEntity::FIRST_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
			ReservationEntity::SECOND_INSTALLMENT_PAYMENT_STATUS => InstallmentsPaymentStatusEnum::WAITING_PAYMENT,
		];
		$reservationEntity = new ReservationEntity();
		$reservationEntity->id = 123;
		$reservationEntity->two_installments = true;
		$reservationEntity->first_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$reservationEntity->second_installment_payment_status = InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$logic = new PaymentConfirmationTypeLogic();
		$result = $logic->getPaymentConfirmationNotificationType($formValues, $reservationEntity);
		$this->assertNull($result);
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="Default ruleset">
    <rule ref="../../vendor/contributte/qa/ruleset.xml">
        <exclude name="Generic.PHP.LowerCaseConstant" />
        <exclude name="SlevomatCodingStandard.Commenting.RequireOneLinePropertyDocComment" />
        <exclude name="SlevomatCodingStandard.Classes.SuperfluousTraitNaming.SuperfluousSuffix" />
        <exclude name="SlevomatCodingStandard.ControlStructures.RequireMultiLineTernaryOperator" />
        <exclude name="SlevomatCodingStandard.Variables.UselessVariable.UselessVariable" /> <!-- we want to first assign variable and then do return, it makes debugging easier -->
        <exclude name="SlevomatCodingStandard.PHP.RequireExplicitAssertion.RequiredExplicitAssertion" /> <!-- doesn't make sense in 90% of our use cases -->
        <exclude name="SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing.IncorrectLinesCountAfterControlStructure" /> <!-- not really sure do we want this -->
        <exclude name="SlevomatCodingStandard.ControlStructures.UselessIfConditionWithReturn.UselessIfCondition" /> <!-- worth thinking about it and analysing -->
        <exclude name="SlevomatCodingStandard.PHP.DisallowReference.DisallowedPassingByReference" /> <!-- really low priority we're using this in very rare ocasions and it even makes sense, although this is allways discouraged -->

        <exclude name="SlevomatCodingStandard.Classes.ClassStructure.IncorrectGroupOrder" />
    </rule>

    <rule name="SlevomatCodingStandard.Commenting.DisallowOneLinePropertyDocComment" />
    <rule ref="Generic.WhiteSpace.ScopeIndent">
        <properties>
            <property name="indent" value="4"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Namespaces.ReferenceUsedNamesOnly">
        <properties>
            <property name="allowFullyQualifiedGlobalClasses" value="true"/>
        </properties>
    </rule>
    <rule ref="Generic.WhiteSpace.DisallowSpaceIndent"/>
    <rule ref="Generic.Metrics.CyclomaticComplexity">
        <properties>
            <property name="complexity" value="14" />
            <property name="absoluteComplexity" value="14" />
        </properties>
    </rule>
    <rule ref="Generic.Metrics.NestingLevel">
        <properties>
            <property name="nestingLevel" value="3"/>
            <property name="absoluteNestingLevel" value="4"/>
        </properties>
    </rule>

    <!-- tests are named differently -->
    <rule ref="Generic.NamingConventions.CamelCapsFunctionName.ScopeNotCamelCaps">
        <exclude-pattern>*/tests/*</exclude-pattern>
    </rule>
    <rule ref="PSR1.Methods.CamelCapsMethodName.NotCamelCaps">
        <exclude-pattern>*/tests/*</exclude-pattern>
    </rule>
    <rule ref="PSR1.Classes.ClassDeclaration.MultipleClasses">
        <exclude-pattern>*/tests/*</exclude-pattern>
    </rule>
    <rule ref="Squiz.Classes.ClassFileName.NoMatch">
        <exclude-pattern>*/tests/*</exclude-pattern>
    </rule>
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="140"/>
            <property name="absoluteLineLimit" value="140"/>
        </properties>
        <exclude-pattern>*/tests/*</exclude-pattern>
    </rule>
</ruleset>

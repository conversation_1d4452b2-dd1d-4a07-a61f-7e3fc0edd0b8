version: 2.1
jobs:
  build:
    docker:
      - image: circleci/php:7.4.1
    working_directory: ~/repo
    steps:
      - checkout
      - run: sudo chmod 777 /usr/local/lib/php/extensions/no-debug-non-zts-20190902
      - restore_cache:
          keys:
            - v9-php-ext3
      - run: if [ ! -f /usr/local/lib/php/extensions/no-debug-non-zts-20190902/gd.so ]; then sudo apt-get update -y && sudo apt-get install -y zlib1g-dev libicu-dev g++ libxml2-dev && sudo apt-get install -y libpng-dev && sudo docker-php-ext-configure intl &&  sudo docker-php-ext-install bcmath soap intl pdo pdo_mysql gd; fi
      - run: sudo pecl install -o -f redis && sudo rm -rf /tmp/pear && sudo docker-php-ext-enable redis
      - save_cache:
          paths:
            - /usr/local/lib/php/extensions/no-debug-non-zts-20190902/bcmath.so
            - /usr/local/lib/php/extensions/no-debug-non-zts-20190902/intl.so
            - /usr/local/lib/php/extensions/no-debug-non-zts-20190902/soap.so
            - /usr/local/lib/php/extensions/no-debug-non-zts-20190902/gd.so
          key: v9-php-ext3
      - run: sudo docker-php-ext-enable bcmath intl soap gd
      - run: echo -e "memory_limit = 3G" | sudo tee /usr/local/etc/php/php.ini > /dev/null
    # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "composer.json" }}
            # fallback to using the latest cache if no exact match is found
            - v1-dependencies-

      - run: composer install -n --prefer-dist

      - save_cache:
          paths:
            - ./vendor
          key: v1-dependencies-{{ checksum "composer.json" }}

      # run tests!
      - run: vendor/bin/phpunit --configuration tests/phpunit.xml tests --coverage-clover=coverage.xml
      - run: vendor/bin/phpcs --standard=ruleset.xml app --ignore=*/autogenerated/*
      - run: vendor/bin/phpstan analyze www/index.php app -l 1 -c phpstan.neon --memory-limit 3072M
  #    - run: vendor/bin/phpmd app/ text coding-standards/phpmd-ruleset.xml -s -e --exclude */autogenerated/*

<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="Villas guide ruleset">
    <rule ref="./packages/coding-standard/ruleset.xml">
        <exclude name="SlevomatCodingStandard.Classes.MethodSpacing.IncorrectLinesCountBetweenMethods" />
        <exclude name="SlevomatCodingStandard.Classes.ParentCallSpacing.IncorrectLinesCountAfterControlStructure" />
        <exclude name="SlevomatCodingStandard.Classes.ParentCallSpacing.IncorrectLinesCountBeforeControlStructure" />
        <exclude name="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceEqualOperator.RequiredNullCoalesceEqualOperator" />
        <exclude name="SlevomatCodingStandard.Functions.StrictCall.StrictParameterMissing" />
        <exclude name="SlevomatCodingStandard.Variables.DisallowSuperGlobalVariable.DisallowedSuperGlobalVariable" />
        <exclude name="SlevomatCodingStandard.Functions.DisallowEmptyFunction.EmptyFunction" />
        <exclude name="SlevomatCodingStandard.Commenting.DeprecatedAnnotationDeclaration.MissingDescription" />
        <exclude name="SlevomatCodingStandard.Classes.SuperfluousErrorNaming.SuperfluousSuffix" />
        <!-- We really want this but are just not there yet. -->
        <exclude name="SlevomatCodingStandard.TypeHints.PropertyTypeHint.MissingAnyTypeHint" />
        <exclude name="SlevomatCodingStandard.TypeHints.DeclareStrictTypes.DeclareStrictTypesMissing" />
        <exclude name="SlevomatCodingStandard.Files.TypeNameMatchesFileName.NoMatchBetweenTypeNameAndFileName" />

        <!-- NEW coding stuff, we want it but not there yet -->
        <exclude name="SlevomatCodingStandard.TypeHints.ReturnTypeHint.MissingAnyTypeHint" />
        <exclude name="SlevomatCodingStandard.TypeHints.ReturnTypeHint.MissingTraversableTypeHintSpecification" />
        <exclude name="SlevomatCodingStandard.TypeHints.ParameterTypeHint.MissingAnyTypeHint" />
        <exclude name="SlevomatCodingStandard.TypeHints.PropertyTypeHint.MissingTraversableTypeHintSpecification" />
        <exclude name="SlevomatCodingStandard.TypeHints.PropertyTypeHint.MissingNativeTypeHint" />
        <exclude name="SlevomatCodingStandard.TypeHints.ReturnTypeHint.MissingNativeTypeHint" />
        <exclude name="SlevomatCodingStandard.TypeHints.ParameterTypeHint.MissingNativeTypeHint" />
        <exclude name="SlevomatCodingStandard.TypeHints.ParameterTypeHint.MissingTraversableTypeHintSpecification" />
        <!--this should be included-->
        <exclude name="SlevomatCodingStandard.ControlStructures.UselessTernaryOperator.UselessTernaryOperator" />
        <!--this should be included-->
        <exclude name="SlevomatCodingStandard.Arrays.DisallowImplicitArrayCreation.ImplicitArrayCreationUsed" />
        <!--this should be included-->
        <exclude name="Generic.Metrics.CyclomaticComplexity.MaxExceeded" />
    </rule>

    <!-- tests are named differently -->
    <rule ref="Generic.NamingConventions.CamelCapsFunctionName.ScopeNotCamelCaps">
        <exclude-pattern>*/tests/*</exclude-pattern>
        <exclude-pattern>app/Models/Entities/*</exclude-pattern>
    </rule>
    <rule ref="PSR1.Methods.CamelCapsMethodName.NotCamelCaps">
        <exclude-pattern>*/tests/*</exclude-pattern>
        <exclude-pattern>app/Models/Entities/*</exclude-pattern>
    </rule>
    <rule ref="Generic.Metrics.NestingLevel">
        <properties>
            <property name="absoluteNestingLevel" value="6" />
            <property name="nestingLevel" value="6" />
        </properties>
    </rule>
    <!-- this should come to 140 top -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="170"/>
            <property name="absoluteLineLimit" value="170"/>
        </properties>
        <exclude-pattern>*/tests/*</exclude-pattern>
        <exclude-pattern>app/Repositories/*</exclude-pattern>
    </rule>
</ruleset>

{"name": "adriacamps/villas-guide", "description": "Villas guide project", "type": "project", "config": {"platform": {"php": "8.1"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true}}, "repositories": [{"type": "vcs", "url": "https://github.com/nidjo17/countries/"}, {"type": "vcs", "url": "https://github.com/nidjo17/coollection/"}, {"type": "vcs", "url": "https://github.com/nidjo17/ia-collection/"}], "require": {"php": ">=8.1", "nette/application": "^3.0", "nette/bootstrap": "^3.0", "nette/tokenizer": "^3.1", "nette/safe-stream": "^2.4", "nette/security": "^3.0", "latte/latte": "^2.8", "nette/mail": "^3.1", "nette/forms": "^3.0", "tracy/tracy": "^2.7", "nextras/migrations": "^3.1", "dibi/dibi": "~v4.0", "guzzlehttp/guzzle": "^7.3", "contributte/guzzlette": "^3.0", "ublaboo/datagrid": "^6.9", "ublaboo/mailing": "^1.2.0", "contributte/console": "^0.9", "contributte/event-dispatcher": "^0.8", "psr/event-dispatcher": "^1.0.0", "contributte/monolog": "^0.5", "contributte/redis": "^0.5", "hobnob/xml-stream-reader": "^1.0", "hubspot/hubspot-php": "^4.0", "pragmarx/countries": "dev-upd/php8", "cbschuld/browser.php": "dev-master", "dodo-it/dibi-entity-generator": "^1.1", "google/apiclient": "^2.12", "wedo/openapi-generator": "^2.0", "contributte/forms-bootstrap": "^0.5", "om/icalparser": "^v1.0.1", "contributte/elastica": "^1.0", "hubspot/api-client": "^8.0", "milo/embedded-svg": "v1.1.0", "wedo/utilities": "^v2.0", "wedo/api": "^2.1", "contributte/forms-wizard": "^3.1", "contributte/forms-multiplier": "^3.2", "contributte/rabbitmq": "^9.1", "mpdf/mpdf": "^8.1", "contributte/pdf": "^6.1", "jschaedl/iban-validation": "^1.8", "contributte/menu-control": "^2.2", "phpro/soap-client": "^3.1", "cjario/omnipay-saferpay": "v2.0", "phpoffice/phpspreadsheet": "^4.2", "ramsey/uuid": "^4.8"}, "require-dev": {"nextras/mail-panel": "^2.5", "phpunit/phpunit": "^9.2", "phpmd/phpmd": "@stable", "phpstan/phpstan-nette": "^1.2", "contributte/qa": "^0.1", "dg/bypass-finals": "^1.2"}, "scripts": {"qa": ["codesniffer app tests --cache=.phpcs-cache.json"], "codefixer": ["codefixer"], "cs": ["codesniffer"], "phpstan": ["vendor/bin/phpstan.phar analyse -l 1 -c phpstan.neon app --memory-limit 3072M"]}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"App\\": "app/", "App\\Api\\": "packages/api/src"}}, "autoload-dev": {"psr-4": {"App\\Api\\Tests\\": "packages/api/tests", "Tests\\": "tests/"}}}
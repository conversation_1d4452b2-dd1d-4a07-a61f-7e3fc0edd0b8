CREATE TABLE istria_homes_features (
    id INT(11) auto_increment NOT NULL,
    name varchar(100) NOT NULL,
    value varchar(100) NULL,
    `mapped` TINYINT(1) DEFAULT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE istria_homes_places (
  id INT(11) auto_increment NOT NULL,
  internal_id INT(11) DEFAULT NULL,
  name varchar(100) NOT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE istria_homes_regions (
    id INT(11) auto_increment NOT NULL,
    internal_id INT(11) DEFAULT NULL,
    name varchar(100) NOT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
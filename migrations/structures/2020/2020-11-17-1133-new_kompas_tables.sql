ALTER TABLE products ADD partner ENUM('private', 'novasol', 'kompas', 'istria_home') DEFAULT 'novasol' NOT NULL AFTER `name`;

CREATE TABLE kompas_villas_features (
	id INT(11) auto_increment NOT NULL,
	kompas_id INT(11) NOT NULL,
	name varchar(100) NOT NULL,
	for_accommodation TINYINT(1) NULL,
	for_object TINYINT(1) NULL,
	suffix varchar(30) NULL,
	`mapped` TINYINT(1) DEFAULT NULL,
	`updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;


CREATE TABLE kompas_villas_places (
	id INT(11) auto_increment NOT NULL,
	internal_id INT(11) DEFAULT NULL,
	kompas_id INT(11) NOT NULL,
	region_id int(11) NOT NULL,
	name varchar(100) NOT NULL,
	latitude DECIMAL NULL,
	longitude DECIMAL NULL,
	`updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE kompas_villas_regions (
	id INT(11) auto_increment NOT NULL,
	internal_id INT(11) DEFAULT NULL,
	kompas_id INT(11) NOT NULL,
	name varchar(100) NOT NULL,
	latitude DECIMAL NULL,
	longitude DECIMAL NULL,
	`updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE kompas_villas_products (
	id int(11) auto_increment NOT NULL,
	product_id int(11) NOT NULL,
	kompas_id int(11) NOT NULL,
	kompas_object_type_id int(6) NULL,
	kompas_region_id int(6) NULL,
	kompas_place_id int(11) NULL,
	code_name varchar(30) NULL,
	`updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE partner_features_mapping (
	id int(11) auto_increment NOT NULL,
	partner varchar (25) NOT NULL,
	partner_feature_id int(11) NOT NULL,
	feature_group_id int(11) NOT NULL,
	feature_subgroup_id int(11) NOT NULL,
	`updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE kompas_villas_feature_subgroups (
	id INT(11) auto_increment NOT NULL,
	kompas_feature_id INT(11) NOT NULL,
	name varchar(300) DEFAULT NULL,
	`updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

ALTER TABLE kompas_villas_features ADD for_mapping TINYINT(1) DEFAULT 1 AFTER suffix;
ALTER TABLE partner_features_mapping ADD partner_feature_subgroup_id int(11) DEFAULT NULL AFTER partner_feature_id;
ALTER TABLE kompas_villas_feature_subgroups ADD internal_value int(11) DEFAULT NULL AFTER name;

CREATE TABLE partner_point_of_interest_mapping (
	id int(11) auto_increment NOT NULL,
	partner varchar(100) NOT NULL,
	partner_feature_id int(11) NOT NULL,
	point_of_interest_id int(11) NOT NULL,
	`updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

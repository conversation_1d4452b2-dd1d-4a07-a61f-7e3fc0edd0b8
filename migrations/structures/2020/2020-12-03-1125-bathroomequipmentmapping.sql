CREATE TABLE partner_bathroom_equipment_mapping (
	id INT(11) auto_increment NOT NULL,
	partner var<PERSON>r(25) NOT NULL,
	partner_feature_id INT(11) NOT NULL,
	internal_facility_id INT(11) NOT NULL,
    internal_facility_type INT(11) NOT NULL,
    internal_facility_detail_id INT(11) NOT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
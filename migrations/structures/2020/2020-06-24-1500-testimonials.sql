CREATE TABLE `testimonials` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `guest_name` VARCHAR(60) NULL,
  `comment` TEXT NULL,
  `language_id` INT NULL,
  `score` DECIMAL(3,1) NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `id_UNIQUE` (`id` ASC));

ALTER TABLE `testimonials`
CHARACTER SET = utf8mb4 ,
<PERSON><PERSON><PERSON> COLUMN `guest_name` `guest_name` VA<PERSON>HA<PERSON>(60) CHARACTER SET 'utf8mb4' NULL DEFAULT NULL ,
CHAN<PERSON> COLUMN `comment` `comment` TEXT CHARACTER SET 'utf8mb4' NULL DEFAULT NULL ;
CREATE TABLE `pre_reservations` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `id_UNIQUE` (`id` ASC),
  INDEX `fk_prereservation__product_id_idx` (`product_id` ASC),
  CONSTRAINT `fk_prereservation__product_id`
    FOREIGN KEY (`product_id`)
    REFERENCES `products` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

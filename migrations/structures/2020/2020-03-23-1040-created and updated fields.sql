ALTER TABLE `villas_denormalized_features`
ADD COLUMN `updated` datetime DEFAULT NULL,
ADD COLUMN  `updated_by` int(11) DEFAULT NULL,
ADD COLUMN  `created` datetime DEFAULT NULL,
ADD COLUMN  `created_by` int(11) DEFAULT NULL;


ALTER TABLE `product_search`
ADD COLUMN `updated` datetime DEFAULT NULL,
ADD COLUMN  `updated_by` int(11) DEFAULT NULL,
ADD COLUMN  `created` datetime DEFAULT NULL,
ADD COLUMN  `created_by` int(11) DEFAULT NULL;


ALTER TABLE `products`
ADD COLUMN `updated` datetime DEFAULT NULL,
ADD COLUMN  `updated_by` int(11) DEFAULT NULL,
ADD COLUMN  `created` datetime DEFAULT NULL,
ADD COLUMN  `created_by` int(11) DEFAULT NULL;

CREATE TABLE page_and_country_connections (
      id int(11) auto_increment NOT NULL,
      page_id int(11) NOT NULL,
      country_id int(11) NOT NULL,
      `updated` datetime DEFAULT NULL,
      `updated_by` int(11) DEFAULT NULL,
      `created` datetime DEFAULT NULL,
      `created_by` int(11) DEFAULT NULL,
      PRIMARY KEY (`id`),
      CONSTRAINT `FK_pac_connections__page_id__id` FOREIGN KEY (`page_id`) REFERENCES `pages` (`id`)
        ON DELETE NO ACTION
        ON UPDATE NO ACTION,
      CONSTRAINT `FK_pac_connections__country_id__id` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`)
        ON DELETE NO ACTION
        ON UPDATE NO ACTION
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE vpi (
     id int(11) auto_increment NOT NULL,
     name varchar(50) NOT NULL,
     `updated` datetime DEFAULT NULL,
     `updated_by` int(11) DEFAULT NULL,
     `created` datetime DEFAULT NULL,
     `created_by` int(11) DEFAULT NULL,
     PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

INSERT INTO vpi (name,updated,updated_by,created,created_by) VALUES
('NEW_MODERN_VILLAS',NULL,NULL,NULL,NULL),
('VILLAS_BY_THE_SEA',NULL,NULL,NULL,NULL),
('VILLAS_WITH_POOL',NULL,NULL,NULL,NULL),
('VILLAS_WITH_OUTDOOR_POOL',NULL,NULL,NULL,NULL),
('VILLAS_WITH_HEATED_POOL',NULL,NULL,NULL,NULL),
('VILLAS_WITH_INDOOR_POOL',NULL,NULL,NULL,NULL),
('VILLAS_WITH_XXL_POOL',NULL,NULL,NULL,NULL),
('VILLAS_WITH_JACUZZI',NULL,NULL,NULL,NULL),
('FAMILY_VILLAS',NULL,NULL,NULL,NULL),
('PET_FRIENDLY_VILLAS',NULL,NULL,NULL,NULL);
INSERT INTO vpi (name,updated,updated_by,created,created_by) VALUES
('ACTIVITY_VILLAS',NULL,NULL,NULL,NULL),
('XXL_VILLAS',NULL,NULL,NULL,NULL),
('STONE_VILLAS',NULL,NULL,NULL,NULL),
('STONE_VILLAS_IN_ISTRIA',NULL,NULL,NULL,NULL),
('STONE_VILLAS_IN_DALMATIA',NULL,NULL,NULL,NULL),
('STONE_VILLAS_ON_THE_ISLAND',NULL,NULL,NULL,NULL),
('VILLAS_IN_NATURE',NULL,NULL,NULL,NULL),
('LUXURY',NULL,NULL,NULL,NULL),
('PANORAMA_VIEW',NULL,NULL,NULL,NULL),
('PANORAMIC_VIEWS_ON_SEA_VILLAS',NULL,NULL,NULL,NULL);
INSERT INTO vpi (name,updated,updated_by,created,created_by) VALUES
('PANORAMIC_VIEWS_ON_NATURE_VILLAS',NULL,NULL,NULL,NULL),
('PRIVATE_VILLAS',NULL,NULL,NULL,NULL),
('VILLAS_WITH_PANORAMIC_LAKE_VIEW',NULL,NULL,NULL,NULL);


CREATE TABLE page_and_interest_connections (
    id int(11) auto_increment NOT NULL,
    page_id int(11) NOT NULL,
    vpi_id int(11) NOT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `FK_pai_connections__page_id__id` FOREIGN KEY (`page_id`) REFERENCES `pages` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_pai_connections__vpi_id__id` FOREIGN KEY (`vpi_id`) REFERENCES `vpi` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

ALTER TABLE `areas_groups`
    ADD COLUMN `image` VARCHAR (100) DEFAULT NULL AFTER `name`;

ALTER TABLE `areas_groups`
    ADD COLUMN `text` TEXT DEFAULT NULL AFTER `image`;

ALTER TABLE `areas_groups`
    ADD COLUMN `alt_text` TEXT DEFAULT NULL AFTER `text`;
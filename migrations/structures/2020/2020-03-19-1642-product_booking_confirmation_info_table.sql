CREATE TABLE `product_booking_confirmation_info` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT NULL,
  `cancellation_possible` TINYINT(1) NULL,
  `cancellation_fee_percentage` INT NULL,
  `contact_name` VARCHAR(80) NULL,
  `contact_address_street` VARCHAR(250) NULL,
  `contact_address_city` VARCHAR(250) NULL,
  `key_owner_name` VARCHAR(80) NULL,
  `key_owner_address_street` VARCHAR(250) NULL,
  `key_owner_address_city` VARCHAR(250) NULL,
  `route_instruction_text` TEXT NULL,
  `geo_code_text` TEXT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `id_UNIQUE` (`id` ASC),
  INDEX `fk_booking_confirmation__product_id_idx` (`product_id` ASC),
  CONSTRAINT `fk_booking_confirmation__product_id`
    FOREIGN KEY (`product_id`)
    REFERENCES `products` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

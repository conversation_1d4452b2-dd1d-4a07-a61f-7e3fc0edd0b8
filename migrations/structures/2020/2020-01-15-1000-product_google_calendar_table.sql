CREATE TABLE `product_google_calendars` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT NULL,
  `google_calendar_id` VARCHAR(120) NULL,
  `calendar_name` VARCHAR(120) NULL,
  `calendar_link` VARCHAR(150) NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE INDEX `ID_UNIQUE` (`ID` ASC) ,
  INDEX `fk_product_id_idx` (`product_id` ASC) ,
  CONSTRAINT `fk_id_product_id_product_google_calendars`
    FOREIGN KEY (`product_id`)
    REFERENCES `products` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

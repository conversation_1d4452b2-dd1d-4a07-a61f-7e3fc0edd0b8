CREATE TABLE `google_calendar_config` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `secret_key` VARCHAR(100) NULL,
  `client_id` VARCHAR(120) NULL,
  `redirect_uri` VARCHAR(120) NULL,
  `access_token` TEXT NULL,
  `client_secret_json` TEXT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE INDEX `ID_UNIQUE` (`ID` ASC));


INSERT INTO `google_calendar_config` (`secret_key`, `client_id`, `redirect_uri`, `client_secret_json`)
VALUES ('oQDlqutmznoCtv8omqGIepEd', '**********************************************.apps.googleusercontent.com', 'http://127.0.0.1', '{"web":{"client_id":"**********************************************.apps.googleusercontent.com","project_id":"villasguide-ical-sync","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_secret":"oQDlqutmznoCtv8omqGIepEd","redirect_uris":["https://villas-guide.com/admin/google-calendar","http://staging.villas-guide.com/admin/google-calendar","http://127.0.0.1"]}}');

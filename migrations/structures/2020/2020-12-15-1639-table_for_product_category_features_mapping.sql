CREATE TABLE partner_product_category_features_mapping (
    id int(11) auto_increment NOT NULL,
    partner var<PERSON>r (25) NOT NULL,
    partner_feature_id int(11) NOT NULL,
    column_name VARCHAR(50) NOT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
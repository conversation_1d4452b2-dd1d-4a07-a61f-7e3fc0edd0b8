CREATE TABLE partner_rooms_mapping (
	id int(11) auto_increment NOT NULL,
	partner varchar (25) NOT NULL,
	partner_feature_id int(11) NOT NULL,
	internal_room_id int(11) NOT NULL,
	`updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

ALTER TABLE partner_rooms_mapping ADD bed_type ENUM('single_bed','bunk_bed','double_bed') default NULL AFTER internal_room_id;

INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id,bed_type)
	VALUES ('kompas',148,1,'single_bed');
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id,bed_type)
	VALUES ('kompas',149,1,'single_bed');
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id,bed_type)
	VALUES ('kompas',151,1,'double_bed');
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id,bed_type)
	VALUES ('kompas',152,1,'single_bed');
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id,bed_type)
	VALUES ('kompas',153,1,'bunk_bed');
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id)
	VALUES ('kompas',158,8);
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id)
	VALUES ('kompas',162,2);
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id)
	VALUES ('kompas',165,2);
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id)
	VALUES ('kompas',167,11);
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id)
	VALUES ('kompas',168,2);
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id)
	VALUES ('kompas',169,9);
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id)
	VALUES ('kompas',171,5);
INSERT INTO partner_rooms_mapping (partner,partner_feature_id,internal_room_id)
	VALUES ('kompas',173,5);

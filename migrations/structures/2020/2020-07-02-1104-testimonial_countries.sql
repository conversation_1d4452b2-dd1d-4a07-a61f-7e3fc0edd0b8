CREATE TABLE `testimonial_countries` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `language_id` INT(2) NULL,
  `name` VARCHAR(45) NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `id_UNIQUE` (`id` ASC) );


INSERT INTO `testimonial_countries` (`id`, `language_id`, `name`) VALUES (5, '5', 'Croatia');
INSERT INTO `testimonial_countries` (`id`, `language_id`, `name`) VALUES (6, '6', 'England');
INSERT INTO `testimonial_countries` (`id`, `language_id`, `name`) VALUES (8, '8', 'Germany');
INSERT INTO `testimonial_countries` (`id`, `language_id`, `name`) VALUES (7, '7', 'Italy');
INSERT INTO `testimonial_countries` (`id`, `language_id`, `name`) VALUES (9, '8', 'Austria');

ALTER TABLE `testimonials`
<PERSON><PERSON><PERSON> COLUMN `language_id` `country_id` INT(11) NULL DEFAULT NULL ;

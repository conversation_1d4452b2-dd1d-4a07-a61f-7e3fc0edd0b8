ALTER TABLE `availabilities`
ADD INDEX `indx_price` (`price` ASC) ;

ALTER TABLE `availabilities`
ADD INDEX `indx_date_status_price_product_id` (`date` ASC, `status` ASC, `price` ASC, `product_id` ASC);

ALTER TABLE `availabilities`
CHANGE COLUMN `id` `id` INT(20) NOT NULL ,
CHANGE COLUMN `apartment` `apartment` INT(1) NULL DEFAULT NULL ;

ALTER TABLE `availabilities`
CHANGE COLUMN `special_offer_discount` `special_offer_discount` DECIMAL(8,7) NULL DEFAULT NULL ;
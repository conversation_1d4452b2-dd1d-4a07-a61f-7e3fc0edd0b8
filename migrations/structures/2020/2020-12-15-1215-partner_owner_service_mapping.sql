CREATE TABLE partner_owner_services_mapping (
      id int(11) auto_increment NOT NULL,
      partner varchar (25) NOT NULL,
      partner_feature_id int(11) NOT NULL,
      owner_service_id int(11) NOT NULL,
      `updated` datetime DEFAULT NULL,
      `updated_by` int(11) DEFAULT NULL,
      `created` datetime DEFAULT NULL,
      `created_by` int(11) DEFAULT NULL,
      PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
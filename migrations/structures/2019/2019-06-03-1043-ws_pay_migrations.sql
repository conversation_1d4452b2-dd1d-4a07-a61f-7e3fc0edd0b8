ALTER TABLE `reservations`
ADD COLUMN `street` TEXT NULL DEFAULT NULL AFTER `two_installments`,
ADD COLUMN `street_number` INT NULL DEFAULT NULL AFTER `street`,
ADD COLUMN `floor` INT NULL DEFAULT NULL AFTER `street_number`,
ADD COLUMN `location` TEXT NULL DEFAULT NULL AFTER `floor`,
ADD COLUMN `zip` INT NULL DEFAULT NULL AFTER `location`,
ADD COLUMN `city` VARCHAR(100) NULL DEFAULT NULL AFTER `zip`,
ADD COLUMN `booked_date` DATETIME NULL DEFAULT NULL AFTER `city`,
ADD COLUMN `booked_time` VARCHAR(30) NULL DEFAULT NULL AFTER `booked_date`,
ADD COLUMN `price_hrk` INT NULL DEFAULT NULL AFTER `booked_time`,
ADD COLUMN `price_eur` INT NULL DEFAULT NULL AFTER `price_hrk`,
ADD COLUMN `price_currency` VARCHAR(45) NULL DEFAULT NULL AFTER `price_eur`,
ADD COLUMN `cancellation_possible` TINYINT(2) NULL DEFAULT NULL AFTER `price_currency`,
ADD COLUMN `cancellation_fee` INT NULL DEFAULT NULL AFTER `cancellation_possible`,
ADD COLUMN `contact_address_line_1` TEXT NULL DEFAULT NULL AFTER `cancellation_fee`,
ADD COLUMN `contact_address_line_2` TEXT NULL DEFAULT NULL AFTER `contact_address_line_1`,
ADD COLUMN `conctact_address_line_3` TEXT NULL DEFAULT NULL AFTER `contact_address_line_2`,
ADD COLUMN `key_address_text_1` TEXT NULL DEFAULT NULL AFTER `conctact_address_line_3`,
ADD COLUMN `key_address_text_2` TEXT NULL DEFAULT NULL AFTER `key_address_text_1`,
ADD COLUMN `key_address_text_3` TEXT NULL DEFAULT NULL AFTER `key_address_text_2`,
ADD COLUMN `route_instruction_text` TEXT NULL DEFAULT NULL AFTER `key_address_text_3`,
ADD COLUMN `geo_code_text` TEXT NULL DEFAULT NULL AFTER `route_instruction_text`,
ADD COLUMN `payment_first_installment_date` DATETIME NULL DEFAULT NULL AFTER `geo_code_text`,
ADD COLUMN `payment_first_intallment_amount` INT NULL DEFAULT NULL AFTER `payment_first_installment_date`,
ADD COLUMN `payment_second_installment_date` DATETIME NULL DEFAULT NULL AFTER `payment_first_intallment_amount`,
ADD COLUMN `payment_second_installment_amount` INT NULL DEFAULT NULL AFTER `payment_second_installment_date`;

ALTER TABLE `reservations`
CHANGE COLUMN `key_address_text_3` `key_address_zip_city` TEXT NULL DEFAULT NULL ;

ALTER TABLE `reservations`
DROP COLUMN `city`,
DROP COLUMN `zip`,
DROP COLUMN `location`,
DROP COLUMN `street_number`,
DROP COLUMN `street`;

ALTER TABLE `reservations`
CHANGE COLUMN `conctact_address_line_3` `contact_address_line_3` TEXT NULL DEFAULT NULL ;

ALTER TABLE `reservations`
CHANGE COLUMN `payment_first_intallment_amount` `payment_first_installment_amount` INT(11) NULL DEFAULT NULL ;

ALTER TABLE `reservations`
ADD COLUMN `contact_email` VARCHAR(90) NULL DEFAULT NULL AFTER `amount`;

CREATE TABLE `transactions` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `reservation_id` INT NULL,
  `shopping_cart_id` INT NULL,
  `payment_type` VARCHAR(50) NULL DEFAULT NULL,
  `date_time` DATETIME NULL DEFAULT NULL,
  `approval_code` INT NULL DEFAULT NULL,
  `amount` INT NULL DEFAULT NULL,
  `eci` INT NULL DEFAULT NULL,
  `stan` INT NULL DEFAULT NULL,
  `customer_firstname` VARCHAR(60) NULL DEFAULT NULL,
  `customer_lastname` VARCHAR(60) NULL DEFAULT NULL,
  `customer_address` VARCHAR(80) NULL DEFAULT NULL,
  `customer_country` VARCHAR(60) NULL DEFAULT NULL,
  `customer_zip` INT NULL DEFAULT NULL,
  `customer_phone` VARCHAR(45) NULL DEFAULT NULL,
  `customer_email` VARCHAR(60) NULL DEFAULT NULL,
  `success` VARCHAR(45) NULL DEFAULT NULL,
  `error_message` TEXT NULL DEFAULT NULL,
  `lang` VARCHAR(10) NULL DEFAULT NULL,
  `signature` VARCHAR(45) NULL DEFAULT NULL,
  `shop_posted_lang` VARCHAR(10) NULL DEFAULT NULL,
  `shop_posted_payment_plan` VARCHAR(10) NULL DEFAULT NULL,
  `payment_plan` VARCHAR(10) NULL DEFAULT NULL,
  `ws_pay_order_id` VARCHAR(45) NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `shopping_cart_id_UNIQUE` (`shopping_cart_id` ASC),
  UNIQUE INDEX `reservation_id_UNIQUE` (`reservation_id` ASC),
  UNIQUE INDEX `id_UNIQUE` (`id` ASC));

ALTER TABLE `transactions`
ADD COLUMN `test` TINYINT NULL DEFAULT NULL AFTER `ws_pay_order_id`;

ALTER TABLE `reservations`
ADD COLUMN `test` TINYINT NULL DEFAULT NULL AFTER `payment_second_installment_amount`;

ALTER TABLE  `transactions`
CHARACTER SET = utf8mb4 ;

ALTER TABLE `transactions`
COLLATE = utf8mb4_bin ,
CHANGE COLUMN `payment_type` `payment_type` VARCHAR(50) NULL DEFAULT NULL ,
CHANGE COLUMN `customer_firstname` `customer_firstname` VARCHAR(60) NULL DEFAULT NULL ,
CHANGE COLUMN `customer_lastname` `customer_lastname` VARCHAR(60) NULL DEFAULT NULL ,
CHANGE COLUMN `customer_address` `customer_address` VARCHAR(80) NULL DEFAULT NULL ,
CHANGE COLUMN `customer_country` `customer_country` VARCHAR(60) NULL DEFAULT NULL ,
CHANGE COLUMN `customer_phone` `customer_phone` VARCHAR(45) NULL DEFAULT NULL ,
CHANGE COLUMN `customer_email` `customer_email` VARCHAR(60) NULL DEFAULT NULL ,
CHANGE COLUMN `success` `success` VARCHAR(45) NULL DEFAULT NULL ,
CHANGE COLUMN `error_message` `error_message` TEXT NULL DEFAULT NULL ,
CHANGE COLUMN `lang` `lang` VARCHAR(10) NULL DEFAULT NULL ,
CHANGE COLUMN `signature` `signature` VARCHAR(45) NULL DEFAULT NULL ,
CHANGE COLUMN `shop_posted_lang` `shop_posted_lang` VARCHAR(10) NULL DEFAULT NULL ,
CHANGE COLUMN `shop_posted_payment_plan` `shop_posted_payment_plan` VARCHAR(10) NULL DEFAULT NULL ,
CHANGE COLUMN `payment_plan` `payment_plan` VARCHAR(10) NULL DEFAULT NULL ,
CHANGE COLUMN `ws_pay_order_id` `ws_pay_order_id` VARCHAR(45) NULL DEFAULT NULL ;

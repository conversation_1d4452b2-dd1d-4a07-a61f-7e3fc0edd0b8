CREATE TABLE `villas_denormalized_features` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT NULL DEFAULT NULL,
  `bedrooms_count` INT NULL DEFAULT NULL,
  `bathrooms_count` INT NULL DEFAULT NULL,
  `house_unit_size` INT NULL DEFAULT NULL,
  `parcel_unit_size` INT NULL DEFAULT NULL,
  `pool_type` INT NULL DEFAULT NULL,
  `pool_size` INT NULL DEFAULT NULL,
  `double_beds_count` INT NULL DEFAULT NULL,
  `single_beds_count` INT NULL DEFAULT NULL,
  `bunk_beds_count` INT NULL DEFAULT NULL,
  `sofabeds_count` INT NULL DEFAULT NULL,
  PRIMARY KEY (`id`));


insert into villas_denormalized_features (product_id, house_unit_size, pool_size, parcel_unit_size, bedrooms_count, bathrooms_count, single_beds_count, double_beds_count, sofabeds_count, bunk_beds_count)
(select ps.product_id, pfg.unit_size as house_unit_size, pfg2.unit_size as pool_unit_size, pfg3.unit_size as parcel_unit_size,
 b.bedrooms_count, c.bathrooms_count, d.single_beds_count, e.double_beds_count, f.sofabeds_count, g.bunk_beds_count
from product_search ps
left join product_feature_group pfg on ps.product_id = pfg.product_id and pfg.feature_group_id = 1
left join product_feature_group pfg2 on ps.product_id = pfg2.product_id and pfg2.feature_group_id = 24
left join product_feature_group pfg3 on ps.product_id = pfg3.product_id and pfg3.feature_group_id = 4
left join
	(select  ps.product_id ,bedrooms_count
	from product_search ps
	left join
	(select count(product_id) as bedrooms_count, product_id
	from product_room_types
	where room_type_id = 1
	group by product_id) as b on ps.product_id = b.product_id) as b
    on b.product_id = ps.product_id
left join
	(select  c.product_id, bathrooms_count
	from product_search ps
	left join
	(select count(product_id) as bathrooms_count, product_id
	from product_room_types
	where room_type_id = 4
	group by product_id) as c on ps.product_id = c.product_id) as c on ps.product_id = c.product_id
left join
	(select d.product_id, single_beds_count
	from product_search ps
	left join
	(select product_id, count(room_object_id) as single_beds_count
	from product_room_objects
	where room_object_id = 2
	group by product_id) as d on ps.product_id = d.product_id) as d on ps.product_id = d.product_id
left join
	(select e.product_id, double_beds_count
	from product_search ps
	left join
	(select product_id, count(room_object_id) as double_beds_count
	from product_room_objects
	where room_object_id = 1
	group by product_id) as e on ps.product_id = e.product_id) as e on ps.product_id = e.product_id
left join
	(select f.product_id, sofabeds_count
	from product_search ps
	left join
	(select product_id, count(room_object_id) as sofabeds_count
	from product_room_objects
	where room_object_id = 3
	group by product_id) as f on ps.product_id = f.product_id) as f on ps.product_id = f.product_id
left join
	(select g.product_id, bunk_beds_count
	from product_search ps
	left join
	(select product_id, count(room_object_id) as bunk_beds_count
	from product_room_objects
	where room_object_id = 4
	group by product_id) as g on ps.product_id = g.product_id) as g on ps.product_id = g.product_id
order by ps.product_id);

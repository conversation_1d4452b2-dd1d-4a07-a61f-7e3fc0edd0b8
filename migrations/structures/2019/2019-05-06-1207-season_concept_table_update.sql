UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '1');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '2');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '3');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '4');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '5');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '6');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '7');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '8');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '9');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '10');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '11');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '12');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '13');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '14');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '15');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '16');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '17');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '18');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '19');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '20');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '21');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '22');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '23');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '24');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '25');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '26');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '27');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '28');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '29');
UPDATE `season_concepts` SET `month_before_arrival_rule` = '1' WHERE (`id` = '51');
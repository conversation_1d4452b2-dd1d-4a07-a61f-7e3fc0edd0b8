UPDATE `countries_translations` SET `code` = 'AT' WHERE (`id` = '30');
UPDATE `countries_translations` SET `code` = 'PL' WHERE (`id` = '31');
UPDATE `countries_translations` SET `code` = 'NL' WHERE (`id` = '32');
UPDATE `countries_translations` SET `code` = 'LU' WHERE (`id` = '29');
UPDATE `countries_translations` SET `code` = 'IS' WHERE (`id` = '28');
UPDATE `countries_translations` SET `code` = 'TR' WHERE (`id` = '27');
UPDATE `countries_translations` SET `code` = 'HU' WHERE (`id` = '26');
UPDATE `countries_translations` SET `code` = 'CH' WHERE (`id` = '25');
UPDATE `countries_translations` SET `code` = 'DK' WHERE (`id` = '24');
UPDATE `countries_translations` SET `code` = 'PR' WHERE (`id` = '23');
UPDATE `countries_translations` SET `code` = 'ME' WHERE (`id` = '22');
UPDATE `countries_translations` SET `code` = 'CR' WHERE (`id` = '21');
UPDATE `countries_translations` SET `code` = 'EG' WHERE (`id` = '20');
UPDATE `countries_translations` SET `code` = 'AL' WHERE (`id` = '19');
UPDATE `countries_translations` SET `code` = 'SE' WHERE (`id` = '18');
UPDATE `countries_translations` SET `code` = 'GR' WHERE (`id` = '17');
UPDATE `countries_translations` SET `code` = 'CZ' WHERE (`id` = '16');
UPDATE `countries_translations` SET `code` = 'FR' WHERE (`id` = '15');
UPDATE `countries_translations` SET `code` = 'FI' WHERE (`id` = '14');
UPDATE `countries_translations` SET `code` = 'BG' WHERE (`id` = '13');
UPDATE `countries_translations` SET `code` = 'SI' WHERE (`id` = '12');
UPDATE `countries_translations` SET `code` = 'NO' WHERE (`id` = '11');
UPDATE `countries_translations` SET `code` = 'SK' WHERE (`id` = '10');
UPDATE `countries_translations` SET `code` = 'PT' WHERE (`id` = '9');
UPDATE `countries_translations` SET `code` = 'ES' WHERE (`id` = '8');
UPDATE `countries_translations` SET `code` = 'NI' WHERE (`id` = '7');
UPDATE `countries_translations` SET `code` = 'IT' WHERE (`id` = '6');
UPDATE `countries_translations` SET `code` = 'HR' WHERE (`id` = '5');
UPDATE `countries_translations` SET `code` = 'CY' WHERE (`id` = '4');
UPDATE `countries_translations` SET `code` = 'DE' WHERE (`id` = '3');
UPDATE `countries_translations` SET `code` = 'BE' WHERE (`id` = '2');
UPDATE `countries_translations` SET `code` = 'GB' WHERE (`id` = '1');

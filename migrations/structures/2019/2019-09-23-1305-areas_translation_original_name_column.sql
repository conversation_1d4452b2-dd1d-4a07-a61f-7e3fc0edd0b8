ALTER TABLE `areas_translations`
ADD COLUMN `original_name` VARCHAR(100) NULL DEFAULT NULL AFTER `country_code`;

UPDATE `areas_translations` SET `original_name` = 'Šolta' WHERE (`id` = '315');
UPDATE `areas_translations` SET `original_name` = 'Omiš' WHERE (`id` = '320');
UPDATE `areas_translations` SET `original_name` = 'Čiovo' WHERE (`id` = '325');
UPDATE `areas_translations` SET `original_name` = 'Šibenik' WHERE (`id` = '332');
UPDATE `areas_translations` SET `original_name` = 'Primošten' WHERE (`id` = '334');
UPDATE `areas_translations` SET `original_name` = 'Drniš' WHERE (`id` = '336');
UPDATE `areas_translations` SET `original_name` = 'Pašman' WHERE (`id` = '345');
UPDATE `areas_translations` SET `original_name` = 'Korčula' WHERE (`id` = '355');
UPDATE `areas_translations` SET `original_name` = 'Pelješac peninsula' WHERE (`id` = '357');
UPDATE `areas_translations` SET `original_name` = 'Poreč' WHERE (`id` = '360');
UPDATE `areas_translations` SET `original_name` = 'Lovreč' WHERE (`id` = '368');
UPDATE `areas_translations` SET `original_name` = 'Višnjan' WHERE (`id` = '370');
UPDATE `areas_translations` SET `original_name` = 'Fažana' WHERE (`id` = '372');
UPDATE `areas_translations` SET `original_name` = 'Učka' WHERE (`id` = '373');
UPDATE `areas_translations` SET `original_name` = 'Žminj' WHERE (`id` = '377');
UPDATE `areas_translations` SET `original_name` = 'Ližnjan' WHERE (`id` = '383');
UPDATE `areas_translations` SET `original_name` = 'Lošinj' WHERE (`id` = '397');


CREATE TABLE `traum_tasks` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT NULL DEFAULT NULL,
  `traum_id` INT NULL DEFAULT NULL,
  `insert_id` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE INDEX `ID_UNIQUE` (`ID` ASC) ,
  INDEX `FK_product_id_idx` (`product_id` ASC) ,
  INDEX `FK_traum_id_idx` (`traum_id` ASC) ,
  CONSTRAINT `FK_products_product_id`
    FOREIGN KEY (`product_id`)
    REFERENCES `products` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);


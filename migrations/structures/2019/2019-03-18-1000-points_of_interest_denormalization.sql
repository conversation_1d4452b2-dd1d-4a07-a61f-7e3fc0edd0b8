ALTER TABLE `villas_denormalized_features`
ADD COLUMN `neighbour_distance` INT NULL DEFAULT NULL AFTER `sofabeds_count`,
ADD COLUMN `town_distance` INT NULL DEFAULT NULL AFTER `neighbour_distance`,
ADD COLUMN `airport_distance` INT NULL DEFAULT NULL AFTER `town_distance`,
ADD COLUMN `sea_distance` INT NULL DEFAULT NULL AFTER `airport_distance`;

delete from villas_denormalized_features;

insert into villas_denormalized_features (product_id, house_unit_size, pool_size, parcel_unit_size, bedrooms_count, bathrooms_count, single_beds_count, double_beds_count, sofabeds_count, bunk_beds_count, neighbour_distance, town_distance, airport_distance, sea_distance)
(select ps.product_id, pfg.unit_size as house_unit_size, pfg2.unit_size as pool_unit_size, pfg3.unit_size as parcel_unit_size,
 b.bedrooms_count, c.bathrooms_count, d.single_beds_count, e.double_beds_count, f.sofabeds_count, g.bunk_beds_count, distance1.distance as neighbour_distance, distance2.distance as town_distance, distance4.distance as airport_distance, distance5.distance as sea_distance
from product_search ps
left join product_feature_group pfg on ps.product_id = pfg.product_id and pfg.feature_group_id = 1
left join product_feature_group pfg2 on ps.product_id = pfg2.product_id and pfg2.feature_group_id = 24
left join product_feature_group pfg3 on ps.product_id = pfg3.product_id and pfg3.feature_group_id = 4
LEFT JOIN `product_location_points_of_interest` AS distance1 ON distance1.product_id =
ps.product_id and distance1.location_id = 11
LEFT JOIN `product_location_points_of_interest` AS `distance2` ON distance2.product_id =
ps.product_id and distance2.location_id = 5
LEFT JOIN `product_location_points_of_interest` AS `distance4` ON distance4.product_id =
ps.product_id and distance4.location_id = 6
LEFT JOIN `product_location_points_of_interest` AS `distance5` ON distance5.product_id =
ps.product_id and distance5.location_id = 7 and distance5.location_subgroup_id in (15, 8, 3, 10, 13)
left join
	(select  ps.product_id ,bedrooms_count
	from product_search ps
	left join
	(select count(product_id) as bedrooms_count, product_id
	from product_room_types
	where room_type_id = 1
	group by product_id) as b on ps.product_id = b.product_id) as b
    on b.product_id = ps.product_id
left join
	(select  c.product_id, bathrooms_count
	from product_search ps
	left join
	(select count(product_id) as bathrooms_count, product_id
	from product_room_types
	where room_type_id = 4
	group by product_id) as c on ps.product_id = c.product_id) as c on ps.product_id = c.product_id
left join
	(select d.product_id, single_beds_count
	from product_search ps
	left join
	(select product_id, sum(pro.count) as single_beds_count
	from product_room_objects pro
	where room_object_id = 2
	group by product_id) as d on ps.product_id = d.product_id) as d on ps.product_id = d.product_id
left join
	(select e.product_id, double_beds_count
	from product_search ps
	left join
	(select product_id, sum(pro.count) as double_beds_count
	from product_room_objects pro
	where room_object_id = 1
	group by product_id) as e on ps.product_id = e.product_id) as e on ps.product_id = e.product_id
left join
	(select f.product_id, sofabeds_count
	from product_search ps
	left join
	(select product_id, sum(pro.count) as sofabeds_count
	from product_room_objects pro
	where room_object_id = 3
	group by product_id) as f on ps.product_id = f.product_id) as f on ps.product_id = f.product_id
left join
	(select g.product_id, bunk_beds_count
	from product_search ps
	left join
	(select product_id, sum(pro.count) as bunk_beds_count
	from product_room_objects pro
	where room_object_id = 4
	group by product_id) as g on ps.product_id = g.product_id) as g on ps.product_id = g.product_id
order by ps.product_id);
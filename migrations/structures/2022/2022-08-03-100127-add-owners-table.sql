create table product_owner
(
    id                       int auto_increment,
    name                     varchar(255) not null,
    address                  varchar(255) not null,
    ssn                      varchar(25)  not null,
    phone_contact            varchar(255) not null,
    email                    varchar(255) not null,
    reservation_notify_phone varchar(255) null,
    reservation_notify_mail  varchar(255) null,
    invoice_address          varchar(255) not null,
    invoice_name             varchar(255) not null,
    invoice_iban             varchar(255) not null,
    created                  datetime     null,
    created_by               int          null,
    constraint product_owner_pk
        primary key (id)
);


CREATE TABLE `product_detail` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `product_id` int(11) NOT NULL,
                                  `security_deposit` decimal(11,2) DEFAULT NULL,
                                  `payment_in_cash` tinyint(1) DEFAULT NULL,
                                  `still_reservation` varchar(255) DEFAULT NULL,
                                  `description` text DEFAULT NULL,
                                  `microlocation_description` text DEFAULT NULL,
                                  `distance_from_beach` varchar(255) DEFAULT NULL,
                                  `beach_type` varchar(255) DEFAULT NULL,
                                  `distance_from_restaurant` varchar(255) DEFAULT NULL,
                                  `distance_from_first_coffee_bar` varchar(255) DEFAULT NULL,
                                  `distance_from_store` varchar(255) DEFAULT NULL,
                                  `distance_from_supermarket` varchar(255) DEFAULT NULL,
                                  `distance_from_airport` varchar(255) DEFAULT NULL,
                                  `distance_from_first_big_place` varchar(255) DEFAULT NULL,
                                  `living_area_in_m2` int(6) DEFAULT NULL,
                                  `area_of_the_garden_in_m2` int(6) DEFAULT NULL,
                                  `maximum_capacity_of_people_categorization` int(6) DEFAULT NULL,
                                  `detached_object` tinyint(1) DEFAULT NULL,
                                  `separate_residential_units` tinyint(1) DEFAULT NULL,
                                  `number_of_residential_units` int(6) DEFAULT NULL,
                                  `garden_of_the_villa_is_completely_fenced` tinyint(1) DEFAULT NULL,
                                  `fence_type` varchar(255) DEFAULT NULL,
                                  `details_regarding_size_of_the_facility_and_basic_contents` text DEFAULT NULL,
                                  `allow_celebrations_and_events` tinyint(1) DEFAULT NULL,
                                  `can_be_booked_by_a_group_of_young_people` tinyint(1) DEFAULT NULL,
                                  `height_of_the_fence` varchar(10) DEFAULT NULL,
                                  `house_type` varchar(255) DEFAULT NULL,
                                  `automatic_lawn_irrigation` tinyint(1) DEFAULT NULL,
                                  `water_filtration` tinyint(1) DEFAULT NULL,
                                  `water_softener` tinyint(1) DEFAULT NULL,
                                  `internet_available_interior` tinyint(1) DEFAULT NULL,
                                  `internet_type_interior` varchar(100) DEFAULT NULL,
                                  `internet_wifi_name_interior` varchar(255) DEFAULT NULL,
                                  `internet_wifi_password_interior` varchar(255) DEFAULT NULL,
                                  `internet_available_exterior` tinyint(1) DEFAULT NULL,
                                  `internet_type_exterior` varchar(100) DEFAULT NULL,
                                  `internet_wifi_name_exterior` varchar(255) DEFAULT NULL,
                                  `internet_wifi_password_exterior` varchar(255) DEFAULT NULL,
                                  `internet_speed_download` varchar(20) DEFAULT NULL,
                                  `internet_speed_upload` varchar(20) DEFAULT NULL,
                                  `window_have_insects_protection` tinyint(1) DEFAULT NULL,
                                  `facility_adapted_for_disabled_person` tinyint(1) DEFAULT NULL,
                                  `heating_available_in_all_rooms` tinyint(1) DEFAULT NULL,
                                  `heating_type` varchar(255) DEFAULT NULL,
                                  `cooling_available_in_all_rooms` tinyint(1) DEFAULT NULL,
                                  `cooling_type` varchar(255) DEFAULT NULL,
                                  `number_of_air_conditioners` int(6) DEFAULT NULL,
                                  `pets_allowed` tinyint(1) DEFAULT NULL,
                                  `allowed_pet_number` varchar(20) DEFAULT NULL,
                                  `pets_fee_exists` tinyint(1) DEFAULT NULL,
                                  `pets_fee` int(6) DEFAULT NULL,
                                  `pets_descriptions` text DEFAULT NULL,
                                  `document_solution` varchar(255) DEFAULT NULL,
                                  `usage_permission` varchar(255) DEFAULT NULL,
                                  `updated` datetime DEFAULT NULL,
                                  `updated_by` int(11) DEFAULT NULL,
                                  `created` datetime DEFAULT NULL,
                                  `created_by` int(11) DEFAULT NULL,
                                  PRIMARY KEY (`id`),
                                  KEY `FK_product_detail_productId_idx` (`product_id`),
                                  CONSTRAINT `FK_product_detail_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_living_room` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `product_id` int(11) NOT NULL,
                                       `heating` tinyint(1) DEFAULT NULL,
                                       `heating_type` varchar(255) DEFAULT NULL,
                                       `cooling` tinyint(1) DEFAULT NULL,
                                       `cooling_type` varchar(255) DEFAULT NULL,
                                       `thermostat_for_temperature_regulation` tinyint(1) DEFAULT NULL,
                                       `internet_available` tinyint(1) DEFAULT NULL,
                                       `internet_type` varchar(100) DEFAULT NULL,
                                       `updated` datetime DEFAULT NULL,
                                       `updated_by` int(11) DEFAULT NULL,
                                       `created` datetime DEFAULT NULL,
                                       `created_by` int(11) DEFAULT NULL,
                                       `smart_tv` tinyint(1) DEFAULT NULL,
                                       `netflix` tinyint(1) DEFAULT NULL,
                                       `tv_size` int(6) DEFAULT NULL,
                                       `sat_tv` tinyint(1) DEFAULT NULL,
                                       `stereo_speakers` tinyint(1) DEFAULT NULL,
                                       `bluetooth_speaker` tinyint(1) DEFAULT NULL,
                                       `safe` tinyint(1) DEFAULT NULL,
                                       `fireplace` tinyint(1) DEFAULT NULL,
                                       `total_number_of_seats` int(6) DEFAULT NULL,
                                       `extendable_couch` tinyint(1) DEFAULT NULL,
                                       `meeting_room` tinyint(1) DEFAULT NULL,
                                       `wine_cellar` tinyint(1) DEFAULT NULL,
                                       `space_darkening` tinyint(1) DEFAULT NULL,
                                       `space_darkening_type` varchar(255) DEFAULT NULL,
                                       PRIMARY KEY (`id`),
                                       KEY `FK_product_living_room_productId_idx` (`product_id`),
                                       CONSTRAINT `FK_product_living_room_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_kitchen` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `product_id` int(11) NOT NULL,
                                   `heating` tinyint(1) DEFAULT NULL,
                                   `heating_type` varchar(255) DEFAULT NULL,
                                   `cooling` tinyint(1) DEFAULT NULL,
                                   `cooling_type` varchar(255) DEFAULT NULL,
                                   `thermostat_for_temperature_regulation` tinyint(1) DEFAULT NULL,
                                   `refrigerator` tinyint(1) DEFAULT NULL,
                                   `freezer` tinyint(1) DEFAULT NULL,
                                   `ice_machine` tinyint(1) DEFAULT NULL,
                                   `toaster` tinyint(1) DEFAULT NULL,
                                   `kettle` tinyint(1) DEFAULT NULL,
                                   `mixer` tinyint(1) DEFAULT NULL,
                                   `coffee_machine` tinyint(1) DEFAULT NULL,
                                   `coffee_type` varchar(100) DEFAULT NULL,
                                   `cooking_utensils` tinyint(1) DEFAULT NULL,
                                   `oven` tinyint(1) DEFAULT NULL,
                                   `stove` tinyint(1) DEFAULT NULL,
                                   `stove_type` varchar(100) DEFAULT NULL,
                                   `microwave` tinyint(1) DEFAULT NULL,
                                   `washing_machine` tinyint(1) DEFAULT NULL,
                                   `cloths_and_dish_detergent` tinyint(1) DEFAULT NULL,
                                   `dining_table` tinyint(1) DEFAULT NULL,
                                   `number_of_seats_at_dining_table` int(6) DEFAULT NULL,
                                   `child_seat` tinyint(1) DEFAULT NULL,
                                   `number_of_child_seat` int(6) DEFAULT NULL,
                                   `additional_content_of_kitchens` text DEFAULT NULL,
                                   `basic_spices` tinyint(1) DEFAULT NULL,
                                   `cutlery` varchar(20) DEFAULT NULL,
                                   `updated` datetime DEFAULT NULL,
                                   `updated_by` int(11) DEFAULT NULL,
                                   `created` datetime DEFAULT NULL,
                                   `created_by` int(11) DEFAULT NULL,
                                   PRIMARY KEY (`id`),
                                   KEY `FK_product_kitchen_productId_idx` (`product_id`),
                                   CONSTRAINT `FK_product_kitchen_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_bedroom` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `product_id` int(11) NOT NULL,
                                   `number` int(6) NOT NULL,
                                   `location` varchar(50) DEFAULT NULL,
                                   `size_in_m2` int(6) DEFAULT NULL,
                                   `bed_size_90x200` varchar(100) DEFAULT NULL,
                                   `bed_size_160x200` varchar(100) DEFAULT NULL,
                                   `bed_size_180x200` varchar(100) DEFAULT NULL,
                                   `bed_size_out_of_standard` varchar(255) DEFAULT NULL,
                                   `heating` tinyint(1) DEFAULT NULL,
                                   `heating_type` varchar(255) DEFAULT NULL,
                                   `cooling` tinyint(1) DEFAULT NULL,
                                   `cooling_type` varchar(255) DEFAULT NULL,
                                   `thermostat_for_temperature_regulation` tinyint(1) DEFAULT NULL,
                                   `adapted_for_the_disabled` tinyint(1) DEFAULT NULL,
                                   `content` varchar(255) DEFAULT NULL,
                                   `updated` datetime DEFAULT NULL,
                                   `updated_by` int(11) DEFAULT NULL,
                                   `created` datetime DEFAULT NULL,
                                   `created_by` int(11) DEFAULT NULL,
                                   PRIMARY KEY (`id`),
                                   KEY `FK_product_bedroom_productId_idx` (`product_id`),
                                   CONSTRAINT `FK_product_bedroom_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_bathroom` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `product_id` int(11) NOT NULL,
                                    `number` int(6) NOT NULL,
                                    `size_in_m2` int(6) DEFAULT NULL,
                                    `heating` tinyint(1) DEFAULT NULL,
                                    `heating_type` varchar(255) DEFAULT NULL,
                                    `cooling` tinyint(1) DEFAULT NULL,
                                    `cooling_type` varchar(255) DEFAULT NULL,
                                    `thermostat_for_temperature_regulation` tinyint(1) DEFAULT NULL,
                                    `adapted_for_the_disabled` tinyint(1) DEFAULT NULL,
                                    `content` varchar(255) DEFAULT NULL,
                                    `number_of_guest_toilets` varchar(20) DEFAULT NULL,
                                    `updated` datetime DEFAULT NULL,
                                    `updated_by` int(11) DEFAULT NULL,
                                    `created` datetime DEFAULT NULL,
                                    `created_by` int(11) DEFAULT NULL,
                                    PRIMARY KEY (`id`),
                                    KEY `FK_product_bathroom_productId_idx` (`product_id`),
                                    CONSTRAINT `FK_product_bathroom_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_laundry` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `product_id` int(11) NOT NULL,
                                   `washing_machine` tinyint(1) DEFAULT NULL,
                                   `clothes_dryer` tinyint(1) DEFAULT NULL,
                                   `clothes_horse` tinyint(1) DEFAULT NULL,
                                   `iron_board` tinyint(1) DEFAULT NULL,
                                   `content` text DEFAULT NULL,
                                   `updated` datetime DEFAULT NULL,
                                   `updated_by` int(11) DEFAULT NULL,
                                   `created` datetime DEFAULT NULL,
                                   `created_by` int(11) DEFAULT NULL,
                                   PRIMARY KEY (`id`),
                                   KEY `FK_product_laundry_productId_idx` (`product_id`),
                                   CONSTRAINT `FK_product_laundry_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_bbq` (
                               `id` int(11) NOT NULL AUTO_INCREMENT,
                               `product_id` int(11) NOT NULL,
                               `covered_terrace` tinyint(1) DEFAULT NULL,
                               `dining_table` tinyint(1) DEFAULT NULL,
                               `number_of_seats_at_the_dining_table` int(6) DEFAULT NULL,
                               `summer_kitchen` tinyint(1) DEFAULT NULL,
                               `barbecue` tinyint(1) DEFAULT NULL,
                               `barbecue_type` varchar(100) DEFAULT NULL,
                               `wood_available` tinyint(1) DEFAULT NULL,
                               `coal_available` tinyint(1) DEFAULT NULL,
                               `grill_accessories` tinyint(1) DEFAULT NULL,
                               `tavern` tinyint(1) DEFAULT NULL,
                               `dining_table_in_tavern` tinyint(1) DEFAULT NULL,
                               `number_of_seats_at_the_dining_table_in_tavern` int(6) DEFAULT NULL,
                               `toilet` tinyint(1) DEFAULT NULL,
                               `bathroom` tinyint(1) DEFAULT NULL,
                               `content` text DEFAULT NULL,
                               `lounge` tinyint(1) DEFAULT NULL,
                               `number_of_seats_in_the_dining_lounge` int(6) DEFAULT NULL,
                               `updated` datetime DEFAULT NULL,
                               `updated_by` int(11) DEFAULT NULL,
                               `created` datetime DEFAULT NULL,
                               `created_by` int(11) DEFAULT NULL,
                               PRIMARY KEY (`id`),
                               KEY `FK_product_bbq_productId_idx` (`product_id`),
                               CONSTRAINT `FK_product_bbq_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_parking` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `product_id` int(11) NOT NULL,
                                   `parking_available` tinyint(1) DEFAULT NULL,
                                   `parking_type` varchar(255) DEFAULT NULL,
                                   `number_of_parking_spaces` int(6) DEFAULT NULL,
                                   `covered_max_height` int(10) DEFAULT NULL,
                                   `fee_for_public_space` tinyint(1) DEFAULT NULL,
                                   `price_fee_for_public_space` tinyint(1) DEFAULT NULL,
                                   `adapted_for_electric_cars` tinyint(1) DEFAULT NULL,
                                   `car_charger_type` varchar(255) DEFAULT NULL,
                                   `safe_access_to_a_sports_car` tinyint(1) DEFAULT NULL,
                                   `updated` datetime DEFAULT NULL,
                                   `updated_by` int(11) DEFAULT NULL,
                                   `created` datetime DEFAULT NULL,
                                   `created_by` int(11) DEFAULT NULL,
                                   PRIMARY KEY (`id`),
                                   KEY `FK_product_parking_productId_idx` (`product_id`),
                                   CONSTRAINT `FK_product_parking_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_pool` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `product_id` int(11) NOT NULL,
                                `outdoor_pool` tinyint(1) DEFAULT NULL,
                                `outdoor_spa` tinyint(1) DEFAULT NULL,
                                `indoor_pool` tinyint(1) DEFAULT NULL,
                                `indoor_spa` tinyint(1) DEFAULT NULL,
                                `internet_available` tinyint(1) DEFAULT NULL,
                                `internet_type` varchar(100) DEFAULT NULL,
                                `adapted_for_the_disabled` tinyint(1) DEFAULT NULL,
                                `interior_size` varchar(50) DEFAULT NULL,
                                `interior_depth` varchar(50) DEFAULT NULL,
                                `interior_type` varchar(50) DEFAULT NULL,
                                `interior_access` varchar(50) DEFAULT NULL,
                                `interior_handrail_for_the_entrance` tinyint(1) DEFAULT NULL,
                                `interior_fence_for_children` tinyint(1) DEFAULT NULL,
                                `interior_hydromassage` tinyint(1) DEFAULT NULL,
                                `interior_counter_current_swimming` tinyint(1) DEFAULT NULL,
                                `interior_heated` tinyint(1) DEFAULT NULL,
                                `interior_heating_is_paid_additionally` tinyint(1) DEFAULT NULL,
                                `interior_heating_fee` int(6) DEFAULT NULL,
                                `interior_number_of_sunbeds` int(6) DEFAULT NULL,
                                `interior_towels` tinyint(1) DEFAULT NULL,
                                `interior_lounge` tinyint(1) DEFAULT NULL,
                                `interior_number_of_seats_in_the_lounge` int(6) DEFAULT NULL,
                                `outdoor_shower` tinyint(1) DEFAULT NULL,
                                `spa_interior_sauna` tinyint(1) DEFAULT NULL,
                                `spa_interior_sauna_type` varchar(100) DEFAULT NULL,
                                `spa_interior_cold_bath` tinyint(1) DEFAULT NULL,
                                `spa_interior_jacuzzi` tinyint(1) DEFAULT NULL,
                                `spa_interior_jacuzzi_number_of_people` int(6) DEFAULT NULL,
                                `spa_interior_massage_table` tinyint(1) DEFAULT NULL,
                                `spa_interior_bathroom_available` tinyint(1) DEFAULT NULL,
                                `spa_interior_bathroom` varchar(100) DEFAULT NULL,
                                `spa_interior_heating_available` tinyint(1) DEFAULT NULL,
                                `spa_interior_heating_type` varchar(255) DEFAULT NULL,
                                `spa_interior_cooling_available` tinyint(1) DEFAULT NULL,
                                `spa_interior_cooling_type` varchar(255) DEFAULT NULL,
                                `spa_interior_thermostat_for_temperature_regulation` tinyint(1) DEFAULT NULL,
                                `spa_interior_adapted_for_the_disabled` tinyint(1) DEFAULT NULL,
                                `spa_interior_internet_available` tinyint(1) DEFAULT NULL,
                                `spa_interior_internet_type` varchar(50) DEFAULT NULL,
                                `exterior_size` varchar(50) DEFAULT NULL,
                                `exterior_depth` varchar(50) DEFAULT NULL,
                                `exterior_type` varchar(50) DEFAULT NULL,
                                `exterior_access` varchar(50) DEFAULT NULL,
                                `exterior_handrail_for_the_entrance` tinyint(1) DEFAULT NULL,
                                `exterior_fence_for_children` tinyint(1) DEFAULT NULL,
                                `exterior_hydromassage` tinyint(1) DEFAULT NULL,
                                `exterior_counter_current_swimming` tinyint(1) DEFAULT NULL,
                                `exterior_heated` tinyint(1) DEFAULT NULL,
                                `exterior_heating_is_paid_additionally` tinyint(1) DEFAULT NULL,
                                `exterior_heating_fee` int(6) DEFAULT NULL,
                                `exterior_number_of_sunbeds` int(6) DEFAULT NULL,
                                `exterior_towels` tinyint(1) DEFAULT NULL,
                                `exterior_lounge` tinyint(1) DEFAULT NULL,
                                `exterior_number_of_seats_in_the_lounge` int(6) DEFAULT NULL,
                                `spa_exterior_sauna` tinyint(1) DEFAULT NULL,
                                `spa_exterior_sauna_type` varchar(100) DEFAULT NULL,
                                `spa_exterior_cold_bath` tinyint(1) DEFAULT NULL,
                                `spa_exterior_jacuzzi` tinyint(1) DEFAULT NULL,
                                `spa_exterior_jacuzzi_number_of_people` int(6) DEFAULT NULL,
                                `spa_exterior_massage_table` tinyint(1) DEFAULT NULL,
                                `spa_exterior_bathroom_available` tinyint(1) DEFAULT NULL,
                                `spa_exterior_bathroom` varchar(100) DEFAULT NULL,
                                `updated` datetime DEFAULT NULL,
                                `updated_by` int(11) DEFAULT NULL,
                                `created` datetime DEFAULT NULL,
                                `created_by` int(11) DEFAULT NULL,
                                PRIMARY KEY (`id`),
                                KEY `FK_product_pool_productId_idx` (`product_id`),
                                CONSTRAINT `FK_product_pool_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

CREATE TABLE `product_additional_content` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `product_id` int(11) NOT NULL,
                                              `bicycle_available` tinyint(1) DEFAULT NULL,
                                              `bicycle_type` varchar(50) DEFAULT NULL,
                                              `bicycle_number` int(6) DEFAULT NULL,
                                              `bicycle_fee` tinyint(1) DEFAULT NULL,
                                              `bicycle_fee_price` int(6) DEFAULT NULL,
                                              `fitness` tinyint(1) DEFAULT NULL,
                                              `kids_playground` tinyint(1) DEFAULT NULL,
                                              `mini_golf` tinyint(1) DEFAULT NULL,
                                              `library` tinyint(1) DEFAULT NULL,
                                              `board_games` tinyint(1) DEFAULT NULL,
                                              `playroom` tinyint(1) DEFAULT NULL,
                                              `gaming_console` varchar(100) DEFAULT NULL,
                                              `pool_table` tinyint(1) DEFAULT NULL,
                                              `table_soccer` tinyint(1) DEFAULT NULL,
                                              `table_tennis` tinyint(1) DEFAULT NULL,
                                              `darts` tinyint(1) DEFAULT NULL,
                                              `badminton` tinyint(1) DEFAULT NULL,
                                              `tennis_court` tinyint(1) DEFAULT NULL,
                                              `basketball_court` tinyint(1) DEFAULT NULL,
                                              `field_for_small_football` tinyint(1) DEFAULT NULL,
                                              `volleyball_field` tinyint(1) DEFAULT NULL,
                                              `soccer_goals` tinyint(1) DEFAULT NULL,
                                              `description` text DEFAULT NULL,
                                              `updated` datetime DEFAULT NULL,
                                              `updated_by` int(11) DEFAULT NULL,
                                              `created` datetime DEFAULT NULL,
                                              `created_by` int(11) DEFAULT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `FK_product_additional_content_productId_idx` (`product_id`),
                                              CONSTRAINT `FK_product_additional_content_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);


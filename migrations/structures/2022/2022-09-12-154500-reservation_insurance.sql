CREATE TABLE reservation_insurance (
      id int(11) auto_increment NOT NULL,
      reservation_code VARCHAR(20),
      insurance_id VARCHAR(50) default NULL,
      type VARCHAR(100) DEFAULT NULL,
      price DECIMAL(11) DEFAULT NULL,
      `updated` datetime DEFAULT NULL,
      `updated_by` int(11) DEFAULT NULL,
      `created` datetime DEFAULT NULL,
      `created_by` int(11) DEFAULT NULL,
      PRIMARY KEY (`id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
ALTER TABLE product_detail DROP COLUMN beach_type;
ALTER TABLE product_detail DROP COLUMN distance_from_beach;
ALTER TABLE product_detail DROP COLUMN distance_from_restaurant;
ALTER TABLE product_detail DROP COLUMN distance_from_first_coffee_bar;
ALTER TABLE product_detail DROP COLUMN distance_from_store;
ALTER TABLE product_detail DROP COLUMN distance_from_supermarket;
ALTER TABLE product_detail DROP COLUMN distance_from_airport;
ALTER TABLE product_detail DROP COLUMN distance_from_first_big_place;
ALTER TABLE product_detail DROP COLUMN distance_from_atm;
ALTER TABLE product_detail DROP COLUMN distance_from_dentist;
ALTER TABLE product_detail DROP COLUMN distance_from_doctor;
ALTER TABLE product_detail DROP COLUMN distance_from_marine;
ALTER TABLE product_detail DROP COLUMN distance_from_pharmacy;
ALTER TABLE product_detail DROP COLUMN distance_from_gas;
ALTER TABLE product_detail DROP COLUMN distance_from_tourist_information_center;
ALTER TABLE product_detail DROP COLUMN distance_from_piazza;

CREATE TABLE `product_distance_types` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `name` varchar(255) DEFAULT NULL,
                                          `repeater` int(2) NOT NULL DEFAULT 1,
                                          `updated` datetime DEFAULT NULL,
                                          `updated_by` int(11) DEFAULT NULL,
                                          `created` datetime DEFAULT NULL,
                                          `created_by` int(11) DEFAULT NULL,
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;


CREATE TABLE `product_distances` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `product_id` int(11) NOT NULL,
                                     `product_distance_type_id` int(11) NOT NULL,
                                     `position` int(3) DEFAULT NULL,
                                     `name` varchar(255) DEFAULT NULL,
                                     `distance` decimal(11,2) DEFAULT NULL,
                                     `metric_type` varchar(100) DEFAULT NULL,
                                     `beach_type` varchar(100) DEFAULT NULL,
                                     `updated` datetime DEFAULT NULL,
                                     `updated_by` int(11) DEFAULT NULL,
                                     `created` datetime DEFAULT NULL,
                                     `created_by` int(11) DEFAULT NULL,
                                     PRIMARY KEY (`id`),
                                     KEY `FK_product_distances_productId_idx` (`product_id`),
                                     CONSTRAINT `FK_product_distances_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
                                     KEY `FK_product_distances_productDistanceTypeId_idx` (`product_distance_type_id`),
                                     CONSTRAINT `FK_product_distances_productDistanceTypeId` FOREIGN KEY (`product_distance_type_id`) REFERENCES `product_distance_types` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;

INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(1, 'Plaža', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(2, 'Restoran', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(3, 'Prvi kafić', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(4, 'Dućan', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(5, 'Supermarket', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(6, 'Zračna luka', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(7, 'Prvi veći grad ili mjesto', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(8, 'Bankomat', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(9, 'Zubar', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(10, 'Liječnik', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(11, 'Marina', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(12, 'Ljekarna', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(13, 'Turistički info centar', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(14, 'Benziska postaja', NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, updated, updated_by, created, created_by)
VALUES(15, 'Tržnica', NULL, NULL, NULL, NULL);

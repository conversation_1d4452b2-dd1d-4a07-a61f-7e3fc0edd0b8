CREATE TABLE `product_seasons` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_id` int(11) NOT NULL,
    `date_from` datetime NOT NULL,
    `date_to` datetime NOT NULL,
    `price_per_day` int(11) DEFAULT NULL,
    `price_per_week` int(11) DEFAULT NULL,
    `min_stay_days` int(2) NOT NULL,
    `arrival_days` varchar(25) NOT NULL,
    `departure_days` varchar(25) NOT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
PRIMARY KEY (`id`),
KEY `product_seasons_FK` (`product_id`),
CONSTRAINT `product_seasons_FK` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
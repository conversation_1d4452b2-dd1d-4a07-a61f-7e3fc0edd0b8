-- villas_guide.product_welcome_book definition

CREATE TABLE `product_welcome_book` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `product_id` int(11) NOT NULL,
                                        `meet_the_owner` text DEFAULT NULL,
                                        `wifi_description` text DEFAULT NULL,
                                        `what_to_do` text DEFAULT NULL,
                                        `what_to_see` text DEFAULT NULL,
                                        `restaurants_nearby` text DEFAULT NULL,
                                        `wineries_nearby` text DEFAULT NULL,
                                        `updated` datetime DEFAULT NULL,
                                        `updated_by` int(11) DEFAULT NULL,
                                        `created` datetime DEFAULT NULL,
                                        `created_by` int(11) DEFAULT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `FK_product_welcome_book_productId_idx` (`product_id`),
                                        CONSTRAINT `FK_product_welcome_book_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
)

create table reservation_agency_provision
(
    id int auto_increment,
    reservation_id int not null ,
    percentage decimal not null,
    price decimal not null,
    updated datetime DEFAULT NULL,
    updated_by int DEFAULT NULL,
    created datetime DEFAULT NULL,
    created_by int DEFAULT NULL,
    constraint reservation_agency_provision_pk
    primary key (id)
);


ALTER TABLE reservation_agency_provision
ADD CONSTRAINT FK_reservation_agency_provision___reservations___reservation_id
  FOREIGN KEY (`reservation_id`)
  REFERENCES `reservations` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

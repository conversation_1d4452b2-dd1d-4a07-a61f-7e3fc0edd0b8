create table product_agency_provision
(
    id int auto_increment,
    product_id int not null ,
    price_from decimal not null,
    price_to decimal not null,
    percentage_provision int not null,
    updated datetime DEFAULT NULL,
    updated_by int DEFAULT NULL,
    created datetime DEFAULT NULL,
    created_by int DEFAULT NULL,
    constraint product_agency_provision_pk
    primary key (id)
);


ALTER TABLE product_agency_provision
ADD CONSTRAINT FK_product_agency_provision___products___product_id
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE products
    ADD COLUMN max_turnover decimal DEFAULT NULL AFTER is_private;
UPDATE areas_translations
SET area_id =
        (select id from areas_groups where name = 'DALMATIA')
WHERE area_id in (26, 27, 28);


INSERT IGNORE INTO translation_messages (language_id,`key`) VALUES (5,'dalmatia');
INSERT IGNORE INTO translation_messages (language_id,`key`) VALUES (6,'dalmatia');
INSERT IGNORE INTO translation_messages (language_id,`key`) VALUES (7,'dalmatia');
INSERT IGNORE INTO translation_messages (language_id,`key`) VALUES (8,'dalmatia');


UPDATE `translation_messages` as t
SET t.`message`='Dalmatia'
WHERE t.key = 'dalmatia' and `language_id`=6;

UPDATE `translation_messages` as t
SET t.`message`='Dalmazia'
WHERE t.key = 'dalmatia' and `language_id`=7;

UPDATE `translation_messages` as t
SET t.`message`='Dalmacija'
WHERE t.key = 'dalmatia' and `language_id`=5;

UPDATE `translation_messages` as t
SET t.`message`='Dalmatien'
WHERE t.key = 'dalmatia' and `language_id`=8;
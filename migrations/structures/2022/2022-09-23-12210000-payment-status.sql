ALTER TABLE `reservations`
    ADD COLUMN `first_installment_payment_status` ENUM('paid', 'waiting_for_payment') NULL DEFAULT NULL AFTER `payment_second_installment_amount`;

ALTER TABLE `reservations`
    ADD COLUMN `second_installment_payment_status` ENUM('paid', 'waiting_for_payment') NULL DEFAULT NULL AFTER `first_installment_payment_status`;

ALTER TABLE `reservations`
    ADD COLUMN `first_installment_paid_date` DATETIME NULL DEFAULT NULL AFTER `second_installment_payment_status`;

ALTER TABLE `reservations`
    ADD COLUMN `second_installment_paid_date` DATETIME NULL DEFAULT NULL AFTER `first_installment_paid_date`;
CREATE TABLE `users_comments` (
                                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                  `user_id` int(11) NOT NULL,
                                  `text` mediumtext DEFAULT NULL,
                                  `updated` datetime DEFAULT NULL,
                                  `updated_by` int(11) DEFAULT NULL,
                                  `created` datetime NOT NULL,
                                  `created_by` int(11) DEFAULT NULL,
                                  PRIMARY KEY (`id`),
                                  KEY `users_comments_user_id_idx` (`user_id`),
                                  CONSTRAINT `users_comments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

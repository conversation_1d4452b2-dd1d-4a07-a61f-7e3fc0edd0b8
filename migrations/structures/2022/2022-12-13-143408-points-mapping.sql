DELETE FROM partner_point_of_interest_mapping
WHERE partner = 'private';

DELETE FROM owner_dashboard_features WHERE id=7;
DELETE FROM owner_dashboard_features WHERE id=11;
DELETE FROM owner_dashboard_features WHERE id=12;
DELETE FROM owner_dashboard_features WHERE id=13;
DELETE FROM owner_dashboard_features WHERE id=14;
DELETE FROM owner_dashboard_features WHERE id=15;
DELETE FROM owner_dashboard_features WHERE id=16;

ALTER TABLE `product_distance_types`ADD COLUMN  `point_of_interest_id` int(11) DEFAULT NULL AFTER `name`;
ALTER TABLE product_distance_types
    ADD CONSTRAINT product_distances__location_trans__point_id
        FOREIGN KEY (`point_of_interest_id`)
            REFERENCES `location_translations` (`location_id`)
            ON DELETE NO ACTION
            ON UPDATE NO ACTION;

UPDATE product_distance_types SET point_of_interest_id=7 WHERE id=1;
UPDATE product_distance_types SET point_of_interest_id=4 WHERE id=2;
UPDATE product_distance_types SET point_of_interest_id=1 WHERE id=4;
UPDATE product_distance_types SET point_of_interest_id=6 WHERE id=6;
UPDATE product_distance_types SET point_of_interest_id=5 WHERE id=7;
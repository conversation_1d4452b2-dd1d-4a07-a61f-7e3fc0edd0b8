ALTER TABLE product_kitchen ADD refrigerator_for_wine TINYINT(1) DEFAULT NULL NULL;
ALTER TABLE product_kitchen CHANGE refrigerator_for_wine refrigerator_for_wine TINYINT(1) DEFAULT NULL NULL AFTER refrigerator;
ALTER TABLE product_kitchen ADD chair_for_children TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_kitchen CHANGE chair_for_children chair_for_children TINYINT(1) DEFAULT null NULL AFTER number_of_seats_at_dining_table;
ALTER TABLE product_kitchen ADD coffee_machine_for_expresso TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_kitchen CHANGE coffee_machine_for_expresso coffee_machine_for_expresso TINYINT(1) DEFAULT null NULL AFTER coffee_type;
ALTER TABLE product_pool ADD exterior_access_between varchar(50) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE exterior_access_between exterior_access_between varchar(50) DEFAULT null NULL AFTER exterior_type;
ALTER TABLE product_pool ADD exterior_for_one_unit TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE exterior_for_one_unit exterior_for_one_unit TINYINT(1) DEFAULT null NULL AFTER exterior_number_of_seats_in_the_lounge;
ALTER TABLE product_pool ADD interior_access_between varchar(50) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE interior_access_between interior_access_between varchar(50) DEFAULT null NULL AFTER interior_type;
ALTER TABLE product_bedroom ADD cot TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bedroom CHANGE cot cot TINYINT(1) DEFAULT null NULL AFTER bed_size_out_of_standard;
ALTER TABLE product_bedroom ADD bed_size_140x200 varchar(100) DEFAULT null NULL;
ALTER TABLE product_bedroom CHANGE bed_size_140x200 bed_size_140x200 varchar(100) DEFAULT null NULL AFTER bed_size_90x200;
ALTER TABLE product_bathroom ADD `means_for_personal_hygiene` TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bathroom CHANGE `means_for_personal_hygiene` `means_for_personal_hygiene` TINYINT(1) DEFAULT null NULL AFTER adapted_for_the_disabled;
ALTER TABLE product_bathroom ADD mantle TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bathroom CHANGE mantle mantle TINYINT(1) DEFAULT null NULL AFTER `means_for_personal_hygiene`;
ALTER TABLE product_detail ADD distance_from_atm varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE distance_from_atm distance_from_atm varchar(100) DEFAULT null NULL AFTER distance_from_first_big_place;
ALTER TABLE product_detail ADD distance_from_dentist varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE distance_from_dentist distance_from_dentist varchar(100) DEFAULT null NULL AFTER distance_from_atm;
ALTER TABLE product_detail ADD distance_from_doctor varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE distance_from_doctor distance_from_doctor varchar(100) DEFAULT null NULL AFTER distance_from_dentist;
ALTER TABLE product_detail ADD distance_from_marine varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE distance_from_marine distance_from_marine varchar(100) DEFAULT null NULL AFTER distance_from_doctor;
ALTER TABLE product_detail ADD distance_from_pharmacy varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE distance_from_pharmacy distance_from_pharmacy varchar(100) DEFAULT null NULL AFTER distance_from_marine;
ALTER TABLE product_detail ADD distance_from_gas varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE distance_from_gas distance_from_gas varchar(100) DEFAULT null NULL AFTER distance_from_pharmacy;
ALTER TABLE product_detail ADD distance_from_tourist_information_center varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE distance_from_tourist_information_center distance_from_tourist_information_center varchar(100) DEFAULT null NULL AFTER distance_from_gas;
ALTER TABLE product_detail ADD distance_from_piazza varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE distance_from_piazza distance_from_piazza varchar(100) DEFAULT null NULL AFTER distance_from_tourist_information_center;
ALTER TABLE product_detail ADD half_of_semi_detached_building TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE half_of_semi_detached_building half_of_semi_detached_building TINYINT(1) DEFAULT null NULL AFTER detached_object;
ALTER TABLE product_detail ADD terraced_house TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE terraced_house terraced_house TINYINT(1) DEFAULT null NULL AFTER half_of_semi_detached_building;
ALTER TABLE product_detail ADD non_smoking_room TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE non_smoking_room non_smoking_room TINYINT(1) DEFAULT null NULL AFTER terraced_house;
ALTER TABLE product_detail ADD owner_does_not_live_on_property TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE owner_does_not_live_on_property owner_does_not_live_on_property TINYINT(1) DEFAULT null NULL AFTER non_smoking_room;
ALTER TABLE product_detail ADD adapted_for_people_in_wheelchairs TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE adapted_for_people_in_wheelchairs adapted_for_people_in_wheelchairs TINYINT(1) DEFAULT null NULL AFTER facility_adapted_for_disabled_person;
ALTER TABLE product_detail ADD toilet_bowl_with_handles TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE toilet_bowl_with_handles toilet_bowl_with_handles TINYINT(1) DEFAULT null NULL AFTER adapted_for_people_in_wheelchairs;
ALTER TABLE product_detail ADD raised_toilet_bowl TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE raised_toilet_bowl raised_toilet_bowl TINYINT(1) DEFAULT null NULL AFTER toilet_bowl_with_handles;
ALTER TABLE product_detail ADD lowered_sink TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE lowered_sink lowered_sink TINYINT(1) DEFAULT null NULL AFTER raised_toilet_bowl;
ALTER TABLE product_detail ADD sos_switch TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE sos_switch sos_switch TINYINT(1) DEFAULT null NULL AFTER lowered_sink;
ALTER TABLE product_detail ADD braille_visual_aid TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE braille_visual_aid braille_visual_aid TINYINT(1) DEFAULT null NULL AFTER sos_switch;
ALTER TABLE product_detail ADD visual_aid_tactile_signs TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE visual_aid_tactile_signs visual_aid_tactile_signs TINYINT(1) DEFAULT null NULL AFTER braille_visual_aid;
ALTER TABLE product_detail ADD audioguide TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE audioguide audioguide TINYINT(1) DEFAULT null NULL AFTER visual_aid_tactile_signs;
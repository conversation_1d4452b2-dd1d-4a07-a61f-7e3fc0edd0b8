CREATE TABLE `invoices` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `reservation_id` int(11) NOT NULL,
    `number_numeric_part` int(10) NOT NULL,
    `number_fixed_part` varchar(10) NOT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `FK_invoices_reservationId_idx` (`reservation_id`),
    CONSTRAINT `FK_invoices_reservationId_idx` FOREIGN KEY (`reservation_id`) REFERENCES `reservations` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
)

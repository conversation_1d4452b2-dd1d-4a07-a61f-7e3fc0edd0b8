CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(50) DEFAULT NULL UNIQUE,
  `password` varchar(200) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_login_ip` varbinary(16) DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` INT(11) DEFAULT NULL,
  `active` tinyint(4) DEFAULT '1',
  `created` datetime DEFAULT NULL,
  `created_by`varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- -----------------------------------------------------
-- Table `roles`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;


-- -----------------------------------------------------
-- Table `article`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `articles` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(25) NULL DEFAULT NULL,
  `slug` VARCHAR(50) NULL DEFAULT NULL,
  `article_text` TEXT NULL DEFAULT NULL,
  `author_id` INT(11) NULL DEFAULT NULL,
  `created` DATETIME NULL DEFAULT NULL,
  `edited` DATETIME NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
  )
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

-- -----------------------------------------------------
-- Table `user_roles`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `user_roles` (
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_by` varchar(50) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `edited_by` varchar(50) DEFAULT NULL,
  `edited` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- -----------------------------------------------------
-- Table `translation_languages`
-- -----------------------------------------------------
CREATE TABLE `translation_languages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `module` enum('admin','front') CHARACTER SET utf8 NOT NULL,
  `name` varchar(50),
  `published` tinyint(1) NOT NULL,
  `is_default` tinyint(1) NOT NULL,
   `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `translation_messages`
-- -----------------------------------------------------
CREATE TABLE `translation_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `language_id` int(11) NOT NULL,
  `key` varchar(200) NOT NULL,
  `message` varchar(5000) DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UQ_key_lang_id` (`language_id`,`key`),
  CONSTRAINT `FK_tm_language_id` FOREIGN KEY (`language_id`) REFERENCES `translations_languages` (`id`)
) ENGINE=InnoDB;


INSERT INTO `translation_languages` (`id`, `module`, `name`, `published`, `is_default`) VALUES (1, 'admin', 'Croatian', '1', '1');
INSERT INTO `translation_languages` (`id`, `module`, `name`, `published`, `is_default`) VALUES (2, 'admin', 'English', '1', '0');


INSERT INTO `translation_messages` VALUES (1,1,'Language','Jezik',NULL,NULL,'2017-11-24 23:38:14','1'),(3,1,'Message','Poruka',NULL,NULL,'2017-11-24 23:43:56','1'),(4,1,'Created','stvoreno',NULL,NULL,'2017-11-24 23:52:43','1'),(5,1,'Last change','Ažurirano',NULL,NULL,'2017-11-24 23:59:16','1'),(6,1,'List','Popis','2017-11-25 00:10:18',1,'2017-11-25 00:00:27','1'),(7,1,'Users','Korisnici',NULL,NULL,'2017-11-25 00:10:37','1'),(8,1,'Translations','Prijevodi',NULL,NULL,'2017-11-25 00:15:15','1'),(9,1,'Admin','Admin',NULL,NULL,'2017-11-25 00:38:45','1'),(10,1,'user role','Rola',NULL,NULL,'2017-11-25 00:38:45','1'),(11,1,'logged in since','Ulogican od',NULL,NULL,'2017-11-25 00:38:45','1'),(12,1,'e-mail','E-mail',NULL,NULL,'2017-11-25 00:38:45','1'),(13,1,'Sign out','Odjava',NULL,NULL,'2017-11-25 00:38:45','1'),(14,1,'Dashboard','Dashboard',NULL,NULL,'2017-11-25 00:38:46','1'),(15,1,'Web content','Web sadržaj',NULL,NULL,'2017-11-25 00:38:49','1'),(16,1,'Articles','Stranice',NULL,NULL,'2017-11-25 00:38:49','1'),(17,1,'Article categories','Kategorije stranica',NULL,NULL,'2017-11-25 00:38:49','1'),(18,1,'Images','Slike',NULL,NULL,'2017-11-25 00:38:49','1'),(19,1,'Albums','Albumi',NULL,NULL,'2017-11-25 00:38:49','1'),(20,1,'Languages','Jezici',NULL,NULL,'2017-11-25 00:38:49','1');

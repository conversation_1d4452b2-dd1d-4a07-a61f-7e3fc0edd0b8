CREATE TABLE `product_search` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `product_id` INT(11) NULL,
  `propertyID` VARCHAR(6) NULL,
  `area` VARCHAR(6) NULL,
  `location` VARCHAR(30) NULL,
  `coordinate` VARCHAR(60) NULL,
  `pets_allowed` TINYINT(1) NULL,
  `swimming_pool` TINYINT(1) NULL,
  `swimming_pool_type` INT(1) NULL,
  `people_number` INT(2) NULL,
  `spa` TINYINT(1) NULL,
  `distance_to_water` INT(8) NULL,
  `thumbnail` VARCHAR(100) NULL,
  `url` VARCHAR(100) NULL,
  `quality` INT(1) NULL,
  `sleepingrooms_number` INT(2) NULL,
  `bathrooms_number` INT(2) NULL,
  `smoking_allowed` TINYINT(1) NULL,
  `three_km_from_beach` TINYINT(1) NULL,
  `whirlpool` TINYINT(1) NULL,
  `sauna` TINYINT(1) NULL,
  `air_condition` TINYINT(1) NULL,
  `tv` TINYINT(1) NULL,
  `wasching_machine` TINYINT(1) NULL,
  `dischwascher` TINYINT(1) NULL,
  `internet` TINYINT(1) NULL,
  `boat` TINYINT(1) NULL,
  `xxl_house` TINYINT(1) NULL,
  `all_inclusive` TINYINT(1) NULL,
  `quality_garden_furniture` TINYINT(1) NULL,
  `panorama` TINYINT(1) NULL,
  `berth_for_boat` TINYINT(1) NULL,
  PRIMARY KEY (`id`));
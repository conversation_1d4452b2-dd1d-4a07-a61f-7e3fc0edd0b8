CREATE TABLE `concepts_categories` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NULL,
  `description` TEXT NULL,
  PRIMARY KEY (`id`));


ALTER TABLE `concepts`
ADD COLUMN `concept_category_id` INT(11) NULL AFTER `novasol_code`;

ALTER TABLE `concept_translations`
ADD COLUMN `concept_category_id` INT(11) NULL AFTER `concept_id`;


ALTER TABLE `availabilities`
DROP INDEX `INDX_date` ,
ADD INDEX `INDX_product_id_status_date` (`date` ASC, `product_id` ASC, `status` ASC);

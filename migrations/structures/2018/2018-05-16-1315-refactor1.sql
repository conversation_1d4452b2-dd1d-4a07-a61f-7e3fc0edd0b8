ALTER TABLE `availabilities`
<PERSON>AN<PERSON> COLUMN `project_id` `product_id` INT(11) NULL DEFAULT NULL;
ALTER TABLE `prices`
<PERSON>ANGE COLUMN `project_id` `product_id` INT(11) NULL DEFAULT NULL;
ALTER TABLE `pictures`
CHANGE COLUMN `project_id` `product_id` INT(11) NULL DEFAULT NULL;
ALTER TABLE `texts`
CHANGE COLUMN `project_id` `product_id` INT(11) NULL DEFAULT NULL;

CREATE TABLE `videos` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` INT NULL,
  `information` TEXT NULL,
  PRIMARY KEY (`id`)
  )ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `object_types` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `novasol_code` INT NULL,
  PRIMARY KEY (`id`)
  )ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `product_room_facilities` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `room_type_id` INT NULL,
  `type` INT NULL,
  PRIMARY KEY (`id`)
  )ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `product_object_types` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `object_id` INT NULL,
  `type` INT NULL,
  `count` INT NULL,
  `capacity` INT NULL,
  `total_capacity` INT NULL,
  PRIMARY KEY (`id`)
  )ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `room_facilities` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `novasol_code` INT NULL,
  PRIMARY KEY (`id`)
  )ENGINE=InnoDB DEFAULT CHARSET=utf8;
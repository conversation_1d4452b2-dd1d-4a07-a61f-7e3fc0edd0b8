CREATE TABLE `last_rental_availability` (
  `id` INT NULL AUTO_INCREMENT,
  `product_id` INT NULL,
  `last_rental_date` DATETIME NULL DEFAULT NULL,
  `last_availability_date` D<PERSON>ETIME NULL DEFAULT NULL,
  `created` <PERSON><PERSON><PERSON><PERSON><PERSON> NULL DEFAULT NULL,
  `created_by` INT NULL DEFAULT NULL,
  `updated` DATETIME NULL DEFAULT NULL,
  `updated_by` INT NULL DEFAULT NULL,
  PRIMARY KEY (`id`));

INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('1', '191', 'MI11A', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('2', '191', 'MI11H', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('3', '191', 'MI11P', '2019', 'D');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('4', '191', 'MI12A', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('5', '191', 'MI12H', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('6', '191', 'MI12P', '2019', 'D');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('7', '191', 'MI13A', '2019', 'B');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('8', '191', 'MI13H', '2019', 'B');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('9', '191', 'MI13P', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('10', '191', 'MI14A', '2019', 'B');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('11', '191', 'MI14H', '2019', 'B');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('12', '191', 'MI14P', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('13', '191', 'MI15A', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('14', '191', 'MI15H', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('15', '191', 'MI15P', '2019', 'D');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('16', '191', 'OAA1A', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('17', '191', 'OAA1H', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('18', '191', 'OAA1P', '2019', 'D');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('19', '191', 'OAA2A', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('20', '191', 'OAA2H', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('21', '191', 'OAA2P', '2019', 'D');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('22', '191', 'OAA3H', '2019', 'B');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('23', '191', 'OAA3P', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('24', '191', 'OAA4A', '2019', 'B');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('25', '191', 'OAA4H', '2019', 'B');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('26', '191', 'OAA4P', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('27', '191', 'OAA5A', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('28', '191', 'OAA5H', '2019', 'C');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`, `restriction_to`) VALUES ('29', '191', 'OAA5P', '2019', 'D');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('30', '191', 'OAC1A', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('31', '191', 'OAC1H', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('32', '191', 'OAC1P', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('33', '191', 'OAC2A', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('34', '191', 'OAC2H', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('35', '191', 'OAC2P', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('36', '191', 'OAC3A', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('37', '191', 'OAC3H', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('38', '191', 'OAC3P', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('39', '191', 'OAC4A', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('40', '191', 'OAC4H', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('41', '191', 'OAC4P', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('42', '191', 'OAC5A', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('43', '191', 'OAC5H', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('44', '191', 'OAC5P', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('45', '191', 'SP010070', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('46', '191', 'SP010071', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('47', '191', 'SP010072', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('48', '191', 'SP010073', '2019');
INSERT INTO `season_concepts` (`id`, `sales_market_code`, `novasol_code`, `season`) VALUES ('49', '191', 'SP010074', '2019');

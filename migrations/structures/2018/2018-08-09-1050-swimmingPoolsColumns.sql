ALTER TABLE `product_search`
ADD COLUMN `outdoor_swimming_pool` TINYINT(1) NULL AFTER `parking_places_count`,
ADD COLUMN `indoor_swimming_pool` TINYINT(1) NULL AFTER `outdoor_swimming_pool`,
ADD COLUMN `heated_swimming_pool` TINYINT(1) NULL AFTER `indoor_swimming_pool`;

INSERT INTO `filter_groups` (`name`) VALUES ('OutdoorSwimmingPool');
INSERT INTO `filter_groups` (`name`) VALUES ('IndoorSwimmingPool');
INSERT INTO `filter_groups` (`name`) VALUES ('HeatedSwimmingPool');

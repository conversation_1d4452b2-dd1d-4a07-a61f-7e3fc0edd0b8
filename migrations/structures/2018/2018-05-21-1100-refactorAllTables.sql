ALTER TABLE `building_types`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);

ALTER TABLE `areas`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);

ALTER TABLE `address`
ADD INDEX `FK_address_productId_idx` (`product_id` ASC);
ALTER TABLE `address`
ADD CONSTRAINT `FK_address_productId`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `concepts`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);

ALTER TABLE `countries`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);

ALTER TABLE `country_areas`
ADD INDEX `FK_country_areas_country_id_idx` (`country_id` ASC);
ALTER TABLE `country_areas`
ADD CONSTRAINT `FK_country_areas_country_id`
  FOREI<PERSON>N KEY (`country_id`)
  REFERENCES `countries` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `feature_groups`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);


ALTER TABLE `features`
ADD INDEX `FK_features_feature_group_id_idx` (`feature_group_id` ASC),
ADD INDEX `FK_features_feature_subgroup_idx` (`feature_subgroup_id` ASC);
ALTER TABLE `features`
ADD CONSTRAINT `FK_features_feature_group_id`
  FOREIGN KEY (`feature_group_id`)
  REFERENCES `feature_groups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_features_feature_subgroup`
  FOREIGN KEY (`feature_subgroup_id`)
  REFERENCES `features_subgroups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `features_subgroups`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);

ALTER TABLE `location`
DROP COLUMN `description`,
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);

ALTER TABLE `product_location`
ADD COLUMN `description` TEXT NULL AFTER `location_id`;

ALTER TABLE `owner_services`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);


ALTER TABLE `pictures`
ADD INDEX `FK_pictures_product_id_idx` (`product_id` ASC);
ALTER TABLE `pictures`
ADD CONSTRAINT `FK_pictures_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `points_of_interest`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);


ALTER TABLE `price_types`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);


ALTER TABLE `prices`
ADD INDEX `FK_prices_product_id_idx` (`product_id` ASC);
ALTER TABLE `prices`
ADD CONSTRAINT `FK_prices_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `product_building_type`
ADD INDEX `FK_product_building_type_id_idx` (`building_type_id` ASC),
ADD INDEX `FK_product_building_type_product_id_idx` (`product_id` ASC);
ALTER TABLE `product_building_type`
ADD CONSTRAINT `FK_product_building_type_id`
  FOREIGN KEY (`building_type_id`)
  REFERENCES `building_types` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_building_type_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_concepts`
ADD INDEX `FK_product_concepts_product_id_idx` (`product_id` ASC),
ADD INDEX `FK_product_concepts_concept_id_idx` (`concept_id` ASC);
ALTER TABLE `product_concepts`
ADD CONSTRAINT `FK_product_concepts_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_concepts_concept_id`
  FOREIGN KEY (`concept_id`)
  REFERENCES `concepts` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_feature_group`
ADD INDEX `FK_product_features_feature_subgroup_id_idx` (`feature_subgroup_id` ASC),
ADD INDEX `FK_product_features_feature_group_id_idx` (`feature_group_id` ASC),
ADD INDEX `FK_product_features_product_id_idx` (`product_id` ASC);
ALTER TABLE `product_feature_group`
ADD CONSTRAINT `FK_product_features_feature_group_id`
  FOREIGN KEY (`feature_group_id`)
  REFERENCES `feature_groups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_features_feature_subgroup_id`
  FOREIGN KEY (`feature_subgroup_id`)
  REFERENCES `features_subgroups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_features_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_location`
ADD INDEX `FK_product_location_product_id_idx` (`product_id` ASC),
ADD INDEX `FK_product_location_location_id_idx` (`location_id` ASC);
ALTER TABLE `product_location`
ADD CONSTRAINT `FK_product_location_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_location_location_id`
  FOREIGN KEY (`location_id`)
  REFERENCES `location` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_location_points_of_interest`
ADD INDEX `FK_product_location_pointofinterest_pointofinterest_id_idx` (`point_of_interest_id` ASC),
ADD INDEX `FK_product_location_pointofinterest_location_id_idx` (`location_id` ASC);
ALTER TABLE `product_location_points_of_interest`
ADD CONSTRAINT `FK_product_location_pointofinterest_location_id`
  FOREIGN KEY (`location_id`)
  REFERENCES `location` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_location_pointofinterest_pointofinterest_id`
  FOREIGN KEY (`point_of_interest_id`)
  REFERENCES `points_of_interest` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_owner_services`
ADD INDEX `FK_product_owner_service_product_id_idx` (`product_id` ASC),
ADD INDEX `FK_product_owner_service_owner_service_id_idx` (`owner_service_id` ASC);
ALTER TABLE `product_owner_services`
ADD CONSTRAINT `FK_product_owner_service_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_owner_service_owner_service_id`
  FOREIGN KEY (`owner_service_id`)
  REFERENCES `owner_services` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_price_types`
ADD INDEX `FK_product_price_types_price_type_id_idx` (`price_type_id` ASC),
ADD INDEX `FK_product_price_types_product_id_idx` (`product_id` ASC);
ALTER TABLE `product_price_types`
ADD CONSTRAINT `FK_product_price_types_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_price_types_price_type_id`
  FOREIGN KEY (`price_type_id`)
  REFERENCES `price_types` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_room_types`
ADD INDEX `FK_product_room_types_room_type_id_idx` (`room_type_id` ASC),
ADD INDEX `FK_product_room_types_product_id_idx` (`product_id` ASC);
ALTER TABLE `product_room_types`
ADD CONSTRAINT `FK_product_room_types_room_type_id`
  FOREIGN KEY (`room_type_id`)
  REFERENCES `room_types` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_room_types_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_services`
ADD INDEX `FK_product_services_product_id_idx` (`product_id` ASC),
ADD INDEX `FK_product_services_service_id_idx` (`service_id` ASC);
ALTER TABLE `product_services`
ADD CONSTRAINT `FK_product_services_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_product_services_service_id`
  FOREIGN KEY (`service_id`)
  REFERENCES `services` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `room_facilities`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);


ALTER TABLE `room_objects`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);


ALTER TABLE `room_types`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);

ALTER TABLE `services`
ADD UNIQUE INDEX `novasol_code_UNIQUE` (`novasol_code` ASC);


ALTER TABLE `texts`
ADD INDEX `FK_texts_product_id_idx` (`product_id` ASC);
ALTER TABLE `texts`
ADD CONSTRAINT `FK_texts_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;



ALTER TABLE `videos`
ADD INDEX `FK_videos_product_id_idx` (`product_id` ASC);
ALTER TABLE `videos`
ADD CONSTRAINT `FK_videos_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;



ALTER TABLE `product_location_points_of_interest`
DROP FOREIGN KEY `FK_product_location_pointofinterest_location_id`;
ALTER TABLE `product_location_points_of_interest`
ADD INDEX `FK_product_location_pointofinterest_location_id_idx` (`product_location_id` ASC),
DROP INDEX `FK_product_location_pointofinterest_location_id_idx` ;
ALTER TABLE `product_location_points_of_interest`
ADD CONSTRAINT `FK_product_location_pointofinterest_id`
  FOREIGN KEY (`product_location_id`)
  REFERENCES `product_location` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

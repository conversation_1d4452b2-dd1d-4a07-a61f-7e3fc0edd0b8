ALTER TABLE `filter_groups`
ADD COLUMN `filter_category` INT(2) NULL AFTER `name`;


UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '1');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '2');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '3');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '4');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '5');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '6');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '7');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '8');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '9');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '10');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '11');
UPDATE `filter_groups` SET `filter_category` = '1' WHERE (`id` = '12');
UPDATE `filter_groups` SET `filter_category` = '2' WHERE (`id` = '14');
UPDATE `filter_groups` SET `filter_category` = '2' WHERE (`id` = '15');
UPDATE `filter_groups` SET `filter_category` = '2' WHERE (`id` = '16');
UPDATE `filter_groups` SET `filter_category` = '2' WHERE (`id` = '17');
UPDATE `filter_groups` SET `filter_category` = '5' WHERE (`id` = '18');
UPDATE `filter_groups` SET `filter_category` = '5' WHERE (`id` = '19');
UPDATE `filter_groups` SET `filter_category` = '3' WHERE (`id` = '20');
UPDATE `filter_groups` SET `filter_category` = '3' WHERE (`id` = '21');
UPDATE `filter_groups` SET `filter_category` = '3' WHERE (`id` = '22');
INSERT INTO `filter_groups` (`name`, `filter_category`) VALUES ('BabyCot', '3');
INSERT INTO `filter_groups` (`name`, `filter_category`) VALUES ('BabyChair', '3');

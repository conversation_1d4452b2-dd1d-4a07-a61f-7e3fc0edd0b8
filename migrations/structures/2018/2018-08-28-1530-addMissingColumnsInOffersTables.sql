ALTER TABLE `generated_offers`
ADD COLUMN `updated` <PERSON>ATETIME NULL AFTER `pets_count`,
ADD COLUMN `created` DATETIME NULL AFTER `updated`,
ADD COLUMN `updated_by` INT(11) NULL AFTER `created`,
ADD COLUMN `created_by` INT(11) NULL AFTER `updated_by`;

ALTER TABLE `generated_offers_items`
ADD COLUMN `updated` DATETIME NULL AFTER `product_id`,
ADD COLUMN `created` DATETIME NULL AFTER `updated`,
ADD COLUMN `updated_by` INT(11) NULL AFTER `created`,
ADD COLUMN `created_by` INT(11) NULL AFTER `updated_by`;

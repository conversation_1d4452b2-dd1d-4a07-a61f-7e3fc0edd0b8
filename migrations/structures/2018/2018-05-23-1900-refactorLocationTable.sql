ALTER TABLE `product_location`
DROP FOREIGN KEY `FK_product_location_location_id`;
ALTER TABLE `product_location`
CHANGE COLUMN `location_id` `area_id` INT(11) NULL DEFAULT NULL ,
ADD INDEX `FK_product_location_area_id_idx` (`area_id` ASC),
DROP INDEX `FK_product_location_location_id_idx` ;
ALTER TABLE `product_location`
ADD CONSTRAINT `FK_product_location_area_id`
  FOREIGN KEY (`area_id`)
  REFERENCES `areas` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

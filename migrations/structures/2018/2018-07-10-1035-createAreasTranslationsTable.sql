CREATE TABLE `areas_translations` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `area_id` INT(11) NULL,
  `area_subgroup_id` INT(11) NULL,
  `name` VARCHAR(100) NULL,
  `country_code` INT(3) NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `areas`
RENAME TO  `areas_subgroups` ;

CREATE TABLE `areas_groups` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `novasol_code` VARCHAR(12) NULL,
  `name` VARCHAR(60) NULL,
  PRIMARY KEY (`id`));
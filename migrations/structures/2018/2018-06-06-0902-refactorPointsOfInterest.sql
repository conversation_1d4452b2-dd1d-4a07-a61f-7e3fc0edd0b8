ALTER TABLE `product_location_points_of_interest`
DROP FOREIGN KEY `FK_product_location_pointofinterest_id`;
ALTER TABLE `product_location_points_of_interest`
CHANGE COLUMN `product_location_id` `location_id` INT(11) NULL DEFAULT NULL ,
ADD INDEX `FK_location_pointofinterest_id_idx` (`location_id` ASC),
DROP INDEX `FK_product_location_pointofinterest_location_id_idx` ;
ALTER TABLE `product_location_points_of_interest`
ADD CONSTRAINT `FK_location_pointofinterest_id`
  FOREIGN KEY (`location_id`)
  REFERENCES `location` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

DROP TABLE IF EXISTS `articles`;

CREATE TABLE `pages` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `internal_name` VARCHAR(50) NULL,
  PRIMARY KEY (`id`));


CREATE TABLE `page_translations` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `page_id` INT NULL,
  `language` VARCHAR(5) NULL,
  `title` VARCHAR(250) NULL,
  `content` TEXT NULL,
  `meta_description` VARCHAR(500) NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `page_id_UNIQUE` (`page_id` ASC),
  UNIQUE INDEX `language_UNIQUE` (`language` ASC),
  CONSTRAINT `page_translation_page_id`
    FOREIGN KEY (`page_id`)
    REFERENCES `pages` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

CREATE TABLE `generated_offers` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `language_id` INT(11) NULL,
  `user_id` INT(11) NULL,
  `contact_firstname` <PERSON><PERSON><PERSON><PERSON>(45) NULL,
  `contact_lastname` <PERSON><PERSON><PERSON><PERSON>(45) NULL,
  `contact_email` VARCHAR(45) NULL,
  `link_to_contact_in_hubspot` TEXT NULL,
  `adults_count` INT(2) NULL,
  `children_count` INT(2) NULL,
  `pets_count` INT(2) NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `generated_offers_items` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `offer_id` INT NULL,
  `product_id` INT NULL,
  PRIMARY KEY (`id`));

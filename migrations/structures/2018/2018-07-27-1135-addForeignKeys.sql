ALTER TABLE `areas_translations`
ADD INDEX `FK_areaId_idx` (`area_id` ASC),
ADD INDEX `FK_areaSubgroupId_idx` (`area_subgroup_id` ASC);
ALTER TABLE `areas_translations`
ADD CONSTRAINT `FK_areaId`
  FOREIGN KEY (`area_id`)
  REFERENCES `areas_groups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_areaSubgroupId`
  FOREIGN KEY (`area_subgroup_id`)
  REFERENCES `areas_subgroups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `availabilities`
ADD INDEX `FK_productID_idx` (`product_id` ASC);
ALTER TABLE `availabilities`
ADD CONSTRAINT `FK_productID`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `building_translations`
ADD INDEX `FK_buildingID_idx` (`building_id` ASC);
<PERSON>TER TABLE `building_translations`
ADD CONSTRAINT `FK_buildingID`
  FOREIGN KEY (`building_id`)
  REFERENCES `building_types` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `concept_translations`
ADD INDEX `FK_conceptId_idx` (`concept_id` ASC),
ADD INDEX `FK_conceptCategoryId_idx` (`concept_category_id` ASC);
ALTER TABLE `concept_translations`
ADD CONSTRAINT `FK_conceptId`
  FOREIGN KEY (`concept_id`)
  REFERENCES `concepts` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_conceptCategoryId`
  FOREIGN KEY (`concept_category_id`)
  REFERENCES `concepts_categories` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `countries_translations`
ADD INDEX `FK_countryId_idx` (`country_id` ASC);
ALTER TABLE `countries_translations`
ADD CONSTRAINT `FK_countryId`
  FOREIGN KEY (`country_id`)
  REFERENCES `countries` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `feature_groups_translations`
ADD INDEX `FK_featureId_idx` (`feature_id` ASC);
ALTER TABLE `feature_groups_translations`
ADD CONSTRAINT `FK_featureId`
  FOREIGN KEY (`feature_id`)
  REFERENCES `feature_groups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `feature_subgroups_translations`
ADD INDEX `FK_feature_sub_id_idx` (`feature_subgroup_id` ASC),
ADD INDEX `FK_feature_id_idx` (`feature_id` ASC);
ALTER TABLE `feature_subgroups_translations`
ADD CONSTRAINT `FK_feature_sub_id`
  FOREIGN KEY (`feature_subgroup_id`)
  REFERENCES `features_subgroups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_feature_id`
  FOREIGN KEY (`feature_id`)
  REFERENCES `feature_groups_translations` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `location_translations`
ADD INDEX `FK_locationId_idx` (`location_id` ASC);
ALTER TABLE `location_translations`
ADD CONSTRAINT `FK_locationId`
  FOREIGN KEY (`location_id`)
  REFERENCES `location` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `offers`
ADD INDEX `FK_product_id_idx` (`product_id` ASC);
ALTER TABLE `offers`
ADD CONSTRAINT `FK_product_id`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `owner_service_translations`
ADD INDEX `FK_serviceId_idx` (`service_id` ASC);
ALTER TABLE `owner_service_translations`
ADD CONSTRAINT `FK_serviceId`
  FOREIGN KEY (`service_id`)
  REFERENCES `owner_services` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `price_type_translations`
ADD INDEX `FK_price_type_id_idx` (`price_type_id` ASC);
ALTER TABLE `price_type_translations`
ADD CONSTRAINT `FK_price_type_id`
  FOREIGN KEY (`price_type_id`)
  REFERENCES `price_types` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;



ALTER TABLE `product_room_facilities`
ADD INDEX `FK_productId_idx` (`product_id` ASC),
ADD INDEX `FK_productRoomTypeId_idx` (`product_room_type_id` ASC),
ADD INDEX `FK_roomFacilityId_idx` (`room_facility_id` ASC);
ALTER TABLE `product_room_facilities`
ADD CONSTRAINT `FK_productIdFacilities`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_productRoomTypeIdFacilities`
  FOREIGN KEY (`product_room_type_id`)
  REFERENCES `product_room_types` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `FK_roomFacilityId`
  FOREIGN KEY (`room_facility_id`)
  REFERENCES `room_facilities` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `product_location_points_of_interest`
ADD INDEX `FK_productIdInterest_idx` (`product_id` ASC);
ALTER TABLE `product_location_points_of_interest`
ADD CONSTRAINT `FK_productIdInterest`
  FOREIGN KEY (`product_id`)
  REFERENCES `products` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;
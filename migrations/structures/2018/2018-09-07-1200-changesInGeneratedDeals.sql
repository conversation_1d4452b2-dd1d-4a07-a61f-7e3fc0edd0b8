ALTER TABLE `generated_offers_deals`
<PERSON><PERSON><PERSON> COLUMN `updated` `updated` <PERSON>ATETIME NULL DEFAULT NULL ,
CH<PERSON><PERSON> COLUMN `updated_by` `updated_by` INT NULL DEFAULT NULL ,
<PERSON><PERSON><PERSON> COLUMN `created` `created` <PERSON><PERSON><PERSON><PERSON><PERSON> NULL DEFAULT NULL ,
<PERSON><PERSON><PERSON> COLUMN `created_by` `created_by` INT NULL DEFAULT NULL ;


ALTER TABLE `generated_offers_deals`
CHAN<PERSON> COLUMN `deal_name` `deal_name` VARCHAR(150) NULL DEFAULT NULL ,
ADD COLUMN `hubspot_deal_id` INT(20) NULL AFTER `deal_name`;

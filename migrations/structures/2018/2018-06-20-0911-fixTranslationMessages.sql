ALTER TABLE `translation_messages`
DROP FOREIGN KEY `fk_language_id`;
ALTER TABLE `translation_messages`
DROP INDEX `UQ_langid_key` ;


ALTER TABLE `translation_messages`
<PERSON><PERSON><PERSON> COLUMN `key` `key` VARCHAR(200) CHARACTER SET 'utf8' NOT NULL ,
<PERSON><PERSON><PERSON> COLUMN `message` `message` VARCHAR(5000) CHARACTER SET 'utf8' NULL DEFAULT NULL ,
<PERSON><PERSON><PERSON> COLUMN `created_by` `created_by` VARCHAR(45) CHARACTER SET 'utf8' NULL DEFAULT NULL ;

ALTER TABLE `translation_messages`
ADD UNIQUE INDEX `UQ_langid_key` (`language_id` ASC, `key` ASC);

ALTER TABLE `translation_messages`
ADD CONSTRAINT `fk_language_id`
  FOREIGN KEY (`language_id`)
  REFERENCES `translation_languages` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;
ALTER TABLE `product_search`
ADD COLUMN `safe` TINYINT(1) NULL AFTER `garden_furniture`,
ADD COLUMN `oven` TINYINT(1) NULL AFTER `safe`,
ADD COLUMN `refrigerator` TINYINT(1) NULL AFTER `oven`,
ADD COLUMN `parking` TINYINT(1) NULL AFTER `refrigerator`,
ADD COLUMN `parking_places_count` TINYINT(1) NULL AFTER `parking`;

INSERT INTO `filter_groups` (`name`) VALUES ('Safe');
INSERT INTO `filter_groups` (`name`) VALUES ('Oven');
INSERT INTO `filter_groups` (`name`) VALUES ('Refrigerator');
INSERT INTO `filter_groups` (`name`) VALUES ('Parking');
INSERT INTO `filter_groups` (`name`) VALUES ('ParkingPlacesCount');


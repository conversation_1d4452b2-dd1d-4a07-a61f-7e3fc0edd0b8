CREATE TABLE `product_owner_services` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT NULL,
  `owner_service_id` INT NULL,
  `price` FLOAT NULL,
  `minimum_price` FLOAT NULL,
  `price_unit_code` INT(2) NULL,
  `service_type` INT(1) NULL,
  `currency` VARCHAR(5) NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `product_services`
ADD COLUMN `available_from` DATETIME NULL AFTER `service_id`,
ADD COLUMN `available_to` DATETIME NULL AFTER `available_from`,
ADD COLUMN `price_unit_code` INT(1) NULL AFTER `available_to`,
ADD COLUMN `service_group` INT(3) NULL AFTER `price_unit_code`,
ADD COLUMN `price` FLOAT NULL AFTER `service_group`,
ADD COLUMN `currency` VARCHAR(5) NULL AFTER `price`,
ADD COLUMN `service_type` INT(1) NULL AFTER `currency`,
ADD COLUMN `max_units` INT(2) NULL AFTER `service_type`;


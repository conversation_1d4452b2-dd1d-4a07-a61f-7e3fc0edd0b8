ALTER TABLE `location`
<PERSON>AN<PERSON> COLUMN `novasol_code` `novasol_code` VARCHAR(10) NULL DEFAULT NULL ;

DROP TABLE `points_of_interest`;

ALTER TABLE `product_location_points_of_interest`
DROP FOREIGN KEY `FK_product_location_pointofinterest_location_id`,
DROP FOREIGN KEY `FK_product_location_pointofinterest_pointofinterest_id`;
ALTER TABLE `product_location_points_of_interest`
DROP COLUMN `point_of_interest_id`,
CHANGE COLUMN `location_id` `product_location_id` INT(11) NULL DEFAULT NULL ,
DROP INDEX `FK_product_location_pointofinterest_pointofinterest_id_idx` ;
ALTER TABLE `product_location_points_of_interest`
ADD CONSTRAINT `FK_product_location_pointofinterest_location_id`
  FOREIGN KEY (`product_location_id`)
  REFERENCES `location` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


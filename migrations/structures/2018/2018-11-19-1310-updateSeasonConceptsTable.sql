UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '1');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '2');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '3');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '4');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '5');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '6');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '7');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '8');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '9');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '10');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '11');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '12');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '13');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '14');
UPDATE `season_concepts` SET `name` = 'Mini vacation' WHERE (`id` = '15');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '16');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '17');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '18');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '19');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '20');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '21');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '22');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '23');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '24');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '25');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '26');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '27');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '28');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '29');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '30');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '31');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '32');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '33');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '34');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '35');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '36');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '37');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '38');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '39');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '40');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '41');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '42');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '43');
UPDATE `season_concepts` SET `name` = 'Optional arrival' WHERE (`id` = '44');
UPDATE `season_concepts` SET `name` = 'Long term rental' WHERE (`id` = '45');
UPDATE `season_concepts` SET `name` = 'Long term rental' WHERE (`id` = '46');
UPDATE `season_concepts` SET `name` = 'Long term rental' WHERE (`id` = '47');
UPDATE `season_concepts` SET `name` = 'Long term rental' WHERE (`id` = '48');
UPDATE `season_concepts` SET `name` = 'Long term rental' WHERE (`id` = '49');

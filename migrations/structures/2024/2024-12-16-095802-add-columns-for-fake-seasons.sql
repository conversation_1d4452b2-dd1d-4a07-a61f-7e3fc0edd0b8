ALTER TABLE product_seasons ADD is_hidden tinyint(1) NOT NULL DEFAULT 0;
ALTER TABLE product_seasons CHANGE is_hidden is_hidden tinyint(1) NOT NULL DEFAULT 0 AFTER departure_days;
ALTER TABLE product_seasons ADD reservation_id int(11) DEFAULT NULL NULL;
ALTER TABLE product_seasons CHANGE reservation_id reservation_id int(11) DEFAULT NULL NULL AFTER is_hidden;
ALTER TABLE product_seasons ADD is_deleted TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE product_seasons CHANGE is_deleted is_deleted TINYINT(1) DEFAULT 0 NOT NULL AFTER reservation_id;

ALTER TABLE reservations ADD is_hidden tinyint(1) NOT NULL DEFAULT 0;


CREATE INDEX product_seasons_reservation_id_IDX USING BTREE ON product_seasons (reservation_id);
CREATE TABLE `product_owner_invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `owner_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `invoice_name` varchar(255) DEFAULT NULL,
  `invoice_address` varchar(255) DEFAULT NULL,
  `invoice_iban` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `FK_product_owner_owner_id_idx` (`owner_id`),
  CONSTRAINT `FK_product_owner_owner_id_idx` FOREIGN KEY (`owner_id`) REFERENCES `product_owner` (`id`) ON DELETE NO ACTION ON UPDATE NO action,
  <PERSON>EY `FK_product_owner_product_id_idx` (`product_id`),
  CONSTRAINT `FK_product_owner_product_id_idx` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


INSERT INTO product_owner_invoices
(owner_id,  product_id, invoice_name, invoice_address, invoice_iban, is_active)
select po.id, p.id, po.invoice_name, po.invoice_address, po.invoice_iban, 1  from products p
inner join product_owner po on po.id = p.owner_id;

ALTER TABLE product_owner DROP COLUMN invoice_address;
ALTER TABLE product_owner DROP COLUMN invoice_name;
ALTER TABLE product_owner DROP COLUMN invoice_iban;
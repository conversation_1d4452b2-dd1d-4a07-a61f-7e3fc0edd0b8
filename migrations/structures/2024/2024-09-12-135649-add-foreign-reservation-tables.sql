-- villas_guide.foreign_reservations definition

CREATE TABLE `foreign_reservations` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `product_id` int(11) NOT NULL,
                                        `booking_language` varchar(100) DEFAULT NULL,
                                        `board` varchar(100) DEFAULT NULL,
                                        `booking_date` datetime NOT NULL,
                                        `arrive` datetime NOT NULL,
                                        `departure` datetime NOT NULL,
                                        `web` varchar(500) DEFAULT NULL,
                                        `booking_type` varchar(50) DEFAULT NULL,
                                        `payment_method` int(11) DEFAULT NULL,
                                        `adults_number` int(2) DEFAULT NULL,
                                        `child1_age` int(2) DEFAULT NULL,
                                        `child2_age` int(2) DEFAULT NULL,
                                        `child3_age` int(2) DEFAULT NULL,
                                        `child4_age` int(2) DEFAULT NULL,
                                        `child5_age` int(2) DEFAULT NULL,
                                        `child6_age` int(2) DEFAULT NULL,
                                        `name` varcha<PERSON>(255) DEFAULT NULL,
                                        `surname` varchar(255) DEFAULT NULL,
                                        `dni` varchar(255) DEFAULT NULL,
                                        `address` varchar(255) DEFAULT NULL,
                                        `locality` varchar(255) DEFAULT NULL,
                                        `post_code` varchar(50) DEFAULT NULL,
                                        `city` varchar(100) DEFAULT NULL,
                                        `iso_country_code` varchar(50) DEFAULT NULL,
                                        `country` varchar(50) DEFAULT NULL,
                                        `telephone` varchar(100) DEFAULT NULL,
                                        `telephone2` varchar(100) DEFAULT NULL,
                                        `email` varchar(255) DEFAULT NULL,
                                        `fax` varchar(100) DEFAULT NULL,
                                        `language` varchar(50) DEFAULT NULL,
                                        `fiscal_code` varchar(50) DEFAULT NULL,
                                        `check_in_done` tinyint(1) DEFAULT NULL,
                                        `check_in_schedule` varchar(100) DEFAULT NULL,
                                        `check_out_schedule` varchar(100) DEFAULT NULL,
                                        `creation_date` datetime DEFAULT NULL,
                                        `last_modified_date` datetime DEFAULT NULL,
                                        `booking_code` varchar(100) DEFAULT NULL,
                                        `localizator` varchar(100) DEFAULT NULL,
                                        `agent_localizator` varchar(100) DEFAULT NULL,
                                        `total_price` float DEFAULT NULL,
                                        `rental_price` float DEFAULT NULL,
                                        `currency` varchar(100) DEFAULT NULL,
                                        `accommodation_code` int(11) DEFAULT NULL,
                                        `comments` text DEFAULT NULL,
                                        `comments_date` varchar(100) DEFAULT NULL,
                                        `approved` tinyint(1) NOT NULL DEFAULT 0,
                                        `declined` tinyint(1) NOT NULL DEFAULT 0,
                                        `reservation_id` int(11) DEFAULT NULL,
                                        `updated` datetime DEFAULT NULL,
                                        `updated_by` int(11) DEFAULT NULL,
                                        `created` datetime DEFAULT NULL,
                                        `created_by` int(11) DEFAULT NULL,
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- villas_guide.foreign_reservation_payments definition

CREATE TABLE `foreign_reservation_payments` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `foreign_id` int(11) NOT NULL,
                                                `payment_date` varchar(100) DEFAULT NULL,
                                                `amount` float DEFAULT NULL,
                                                `payment_method` varchar(100) DEFAULT NULL,
                                                `payment_status` varchar(100) DEFAULT NULL,
                                                `security_deposit` tinyint(1) NOT NULL DEFAULT 0,
                                                `payment_id` int(11) NOT NULL,
                                                `updated` datetime DEFAULT NULL,
                                                `updated_by` int(11) DEFAULT NULL,
                                                `created` datetime DEFAULT NULL,
                                                `created_by` int(11) DEFAULT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `foreign_reservation_payments_FK` (`foreign_id`),
                                                CONSTRAINT `foreign_reservation_payments_FK` FOREIGN KEY (`foreign_id`) REFERENCES `foreign_reservations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- villas_guide.foreign_reservation_services definition

CREATE TABLE `foreign_reservation_services` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `code` int(11) NOT NULL,
                                                `foreign_id` int(11) NOT NULL,
                                                `name` varchar(100) DEFAULT NULL,
                                                `amount` int(3) DEFAULT NULL,
                                                `price` float DEFAULT NULL,
                                                `applied_tax_percentage` float DEFAULT NULL,
                                                `category` varchar(255) DEFAULT NULL,
                                                `application_date` datetime DEFAULT NULL,
                                                `updated` datetime DEFAULT NULL,
                                                `updated_by` int(11) DEFAULT NULL,
                                                `created` datetime DEFAULT NULL,
                                                `created_by` int(11) DEFAULT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `foreign_reservation_services_FK` (`foreign_id`),
                                                CONSTRAINT `foreign_reservation_services_FK` FOREIGN KEY (`foreign_id`) REFERENCES `foreign_reservations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
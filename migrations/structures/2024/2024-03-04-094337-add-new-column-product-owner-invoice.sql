ALTER TABLE invoices ADD product_owner_invoice_id INT(11) DEFAULT NULL NULL;
ALTER TABLE invoices CHANGE product_owner_invoice_id product_owner_invoice_id INT(11) DEFAULT NULL NULL AFTER sent;
ALTER TABLE invoices ADD CONSTRAINT FK_product_owner_invoice_idx FOREIGN KEY (product_owner_invoice_id) REFERENCES product_owner_invoices(id);
CREATE INDEX invoices_product_owner_invoice_id_IDX USING BTREE ON invoices (product_owner_invoice_id);
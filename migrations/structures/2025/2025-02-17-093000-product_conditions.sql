ALTER TABLE booking_terms ADD COLUMN product_id int(11) NOT NULL after id;
ALTER TABLE booking_terms ADD CONSTRAINT fk_booking_terms_product FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;

ALTER TABLE booking_terms MODIFY COLUMN partner_id int(11) NULL;

INSERT INTO booking_terms (product_id,language_id,terms,cancellation_policy)
VALUES (36343,'hr','<p>Ukupan iznos smještaja se plaća po izvršenoj rezervaciji putem kreditne kartice.</p>','<p>Besplatno otkazivanje rezervacije nije moguće</p>
<p>U slučaju otkaza rezervacije naplaćuje se ukupan iznos rezervacije</p>');
INSERT INTO booking_terms (product_id,language_id,terms,cancellation_policy)
VALUES (36343,'en','<p>The total amount of the reservation should be paid via credit card upon confirmation.</p>','<p>Free cancellation is not possible</p>
<p>In case of cancellation, the total amount of the reservation will be charged</p>');
INSERT INTO booking_terms (product_id,language_id,terms,cancellation_policy)
VALUES (36343,'de','<p>Der Gesamtbetrag der Reservierung soll nach der Buchung per Kreditkarte bezahlt werden.</p>','<p>Eine kostenlose Stornierung ist nicht möglich</p>
<p>Im Falle einer Stornierung wird der Gesamtbetrag der Reservierung berechnet</p>');
INSERT INTO booking_terms (product_id,language_id,terms,cancellation_policy)
VALUES (36343,'it','<p>L''importo totale della prenotazione deve essere pagato con carta di credito al momento della conferma della prenotazione.</p>','<p>La cancellazione gratuita non è possibile</p>
<p>Nel caso di cancellazione, sarà addebitata la somma totale della prenotazione</p>');

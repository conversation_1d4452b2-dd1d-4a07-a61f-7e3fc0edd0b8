INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES
    (8, 'evisitor.step1.title', 'Schritt 1 – <PERSON><PERSON><PERSON> der Gäste', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.step1.info', 'Für jeden registrierten Gast muss ein separates Formular ausgefüllt werden. Die genaue Anzahl der Gäste sowie deren Reihenfolge (z. B. Gast 1, Gast 2 usw.) wird in der linken oberen Ecke des Bildschirms angezeigt.', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.step2.title', 'Schritt 2 – Eingabe der Daten', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.step2.info', 'Lesen Sie alle erforderlichen Felder sorgfältig. Pflichtfelder sind mit einem Sternchen (*) gekennzeichnet und müssen ausgefüllt werden. <PERSON>te stellen <PERSON> sicher, dass die von Ihnen angegebenen Daten mit den Angaben in Ihrem gültigen Ausweisdokument (z. B. Personalausweis, Reisepass usw.) übereinstimmen.', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.step3.title', 'Schritt 3 – Nächste Gäste', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.step3.info', 'Nachdem Sie die Daten für den ersten Gast eingegeben haben, fahren Sie mit der Eingabe der Informationen für den zweiten Gast fort und so weiter, bis alle Gäste registriert sind.', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.step4.title', 'Schritt 4 – Registrierung bestätigen', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.step4.info', 'Nachdem alle Formulare ausgefüllt wurden, bestätigen Sie Ihre Eingabe. Sie sind nun erfolgreich im e-Visitor-System registriert. Ihr Gastgeber erhält automatisch eine Benachrichtigung über Ihre Registrierung.', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.success.title', 'Vielen Dank für Ihre Registrierung!', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.success.message', 'Hiermit bestätigen wir, dass Sie sich erfolgreich im e-Visitor-System registriert und Ihren Aufenthalt in der Republik Kroatien offiziell gemeldet haben.', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.success.info', 'Sie können dieses Fenster schließen.', NOW(), 1, NOW(), '1'),
    (8, 'evisitor.title', 'Willkommen im e-Visitor-Registrierungssystem über Villas Guide!', NOW(), 1, NOW(), '1')
    ON DUPLICATE KEY UPDATE
                         message = VALUES(message),
                         updated = NOW(),
                         updated_by = VALUES(updated_by);

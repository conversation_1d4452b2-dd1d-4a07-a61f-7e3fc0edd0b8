ALTER TABLE product_agency_provision ADD `type` enum('direct','direct_end_year','distribution') DEFAULT null NULL;
ALTER TABLE product_agency_provision CHANGE `type` `type` enum('direct','direct_end_year','distribution') DEFAULT null NULL AFTER product_id;

update product_agency_provision set type = 'direct' where season >= 2024 and price_from = 0;

update product_agency_provision set type = 'direct_end_year' where season >= 2024 and price_from > 0;

INSERT INTO product_agency_provision
(product_id, `type`, season, price_from, price_to, percentage_provision, updated, updated_by, created, created_by)
SELECT
    p.id, 'distribution', 2025, 0, 10000000, 24, NULL, NULL, NULL, NULL
FROM products p where p.partner = 'private' and p.is_deleted = 0;
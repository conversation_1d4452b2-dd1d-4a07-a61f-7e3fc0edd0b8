CREATE TABLE `policies` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) DEFAULT NULL,
                            `file_path` varchar(255) DEFAULT NULL,
                            `updated` datetime DEFAULT NULL,
                            `updated_by` int(11) DEFAULT NULL,
                            `created` datetime DEFAULT NULL,
                            `created_by` int(11) DEFAULT NULL,
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

INSERT INTO policies
(name, file_path, updated, updated_by, created, created_by)
VALUES('Rule 1', '/documents/policies/en/contract-of-accommodation-unit-rental-agency-guests_1.pdf', NULL, NULL, NULL, NULL);
INSERT INTO policies
(name, file_path, updated, updated_by, created, created_by)
VALUES('Rule 2', '/documents/policies/en/contract-of-accommodation-unit-rental-agency-guests_2.pdf', NULL, NULL, NULL, NULL);
INSERT INTO policies
(name, file_path, updated, updated_by, created, created_by)
VALUES('Rule 3', '/documents/policies/en/contract-of-accommodation-unit-rental-agency-guests_3.pdf', NULL, NULL, NULL, NULL);
INSERT INTO policies
(name, file_path, updated, updated_by, created, created_by)
VALUES('Rule 4', '/documents/policies/en/contract-of-accommodation-unit-rental-agency-guests_4.pdf', NULL, NULL, NULL, NULL);
INSERT INTO policies
(name, file_path, updated, updated_by, created, created_by)
VALUES('Rule 5', '/documents/policies/en/contract-of-accommodation-unit-rental-agency-guests_5.pdf', NULL, NULL, NULL, NULL);

ALTER TABLE booking_terms ADD policy_id INT(11) DEFAULT null NULL;
ALTER TABLE booking_terms CHANGE policy_id policy_id INT(11) DEFAULT null NULL AFTER special_case_cancellation_policy;

CREATE INDEX booking_terms_policy_id_IDX USING BTREE ON booking_terms (policy_id);

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(10, 'db cancellation policy rule 5', '<b>Policy 5</b><br/>
Up to 7 days before arrival, the cancellation is free of charge.<br/>
Within 7 days before arrival, first night''s stay will be charged.<br/>
In case of a no-show, full reservation amount will be charged.

', NULL, NULL, '2025-03-18 13:41:35', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(10, 'db cancellation policy rule 4', '<b>Policy 4</b><br/>
Up to 7 days before arrival, the cancellation is free of charge.<br/>
Within 7 days before arrival, 50% of the total reservation amount will be charged.

', NULL, NULL, '2025-03-18 13:41:35', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(10, 'db cancellation policy rule 3', '<b>Policy 3</b><br/>
Up to 7 days before arrival, the cancellation is free of charge.<br/>
Within 7 days before arrival, full reservation amount will be charged.
', NULL, NULL, '2025-03-18 13:41:35', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(10, 'db cancellation policy rule 2', '<b>Policy 2</b><br/>
Up to 14 days before arrival, the cancellation is free of charge.<br/>
From 14 to 7 days before arrival, 50% of the total reservation amount will be charged.<br/>
Within 7 days before arrival, full reservation amount will be charged .
', NULL, NULL, '2025-03-18 13:41:35', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(10, 'db cancellation policy rule 1', '<b>Policy 1</b><br/>
Up to 30 days before arrival, the cancellation is free of charge.<br/>
From 29 to 14 days before arrival, 50% of the total reservation amount will be charged.<br/>
Within 14 days of the arrival date, full reservation amount will be charged.
', NULL, NULL, '2025-03-18 13:41:35', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(10, 'cancellation policy', 'Cancellation policy', NULL, NULL, '2025-03-18 13:41:35', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'pravila otkazivanja', 'Politica di cancellazione', NULL, NULL, NULL, NULL);
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'cancellation policy rule 1', '<li>Fino a 30 giorni prima dell''arrivo, la cancellazione è gratuita.</li>
<li>Da 29 a 14 giorni prima dell''arrivo, verrà addebitato il 50% dell''importo totale della prenotazione.</li>
<li>Entro 14 giorni dalla data di arrivo, verrà addebitato l''intero importo della prenotazione.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'cancellation policy rule 2', '<li>Fino a 14 giorni prima dell''arrivo, la cancellazione è gratuita.</li>
<li>Da 14 a 7 giorni prima dell''arrivo, verrà addebitato il 50% dell''importo totale della prenotazione.</li>
<li>Entro 7 giorni prima dell''arrivo, verrà addebitato l''intero importo della prenotazione.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'cancellation policy rule 3', '<li>Fino a 7 giorni prima dell''arrivo, la cancellazione è gratuita.</li>
<li>Entro 7 giorni prima dell''arrivo, verrà addebitato l''intero importo della prenotazione.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'cancellation policy rule 4', '<li>Fino a 7 giorni prima dell''arrivo, la cancellazione è gratuita.</li>
<li>Entro 7 giorni prima dell''arrivo, verrà addebitato il 50% dell''importo totale della prenotazione.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'cancellation policy rule 5', '<li>Fino a 7 giorni prima dell''arrivo, la cancellazione è gratuita.</li>
<li>Entro 7 giorni prima dell''arrivo, verrà addebitato l''importo della prima notte.</li>
<li>In caso di no-show, verrà addebitato l''intero importo della prenotazione.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'cancellation policy rule 1', '<li>Bis zu 30 Tage vor der Anreise ist die Stornierung kostenlos.</li>
<li>Von 29 bis 14 Tage vor der Anreise werden 50 % des Gesamtbetrags der Reservierung berechnet.</li>
<li>Innerhalb von 14 Tagen vor dem Anreisedatum wird der gesamte Reservierungsbetrag berechnet.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'cancellation policy rule 2', '<li>Bis zu 14 Tage vor der Anreise ist die Stornierung kostenlos.</li>
<li>Von 14 bis 7 Tage vor der Anreise werden 50 % des Gesamtbetrags der Reservierung berechnet.</li>
<li>Innerhalb von 7 Tagen vor der Anreise wird der gesamte Reservierungsbetrag berechnet.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'cancellation policy rule 3', '<li>Bis zu 7 Tage vor der Anreise ist die Stornierung kostenlos.</li>
<li>Innerhalb von 7 Tagen vor der Anreise wird der gesamte Reservierungsbetrag berechnet.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'cancellation policy rule 4', '<li>Bis zu 7 Tage vor der Anreise ist die Stornierung kostenlos.</li>
<li>Innerhalb von 7 Tagen vor der Anreise werden 50 % des Gesamtbetrags der Reservierung berechnet.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'cancellation policy rule 5', '<li>Bis zu 7 Tage vor der Anreise ist die Stornierung kostenlos.</li>
<li>Innerhalb von 7 Tagen vor der Anreise wird der Betrag für die erste Übernachtung berechnet.</li>
<li>Bei Nichterscheinen (No-Show) wird der gesamte Reservierungsbetrag berechnet.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'pravila otkazivanja', 'Stornierungsbedingungen', NULL, NULL, '2025-03-18 12:48:52', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'cancellation policy rule 1', '<li>Up to 30 days before arrival, the cancellation is free of charge.</li>
<li>From 29 to 14 days before arrival, 50% of the total reservation amount will be charged.</li>
<li>Within 14 days of the arrival date, full reservation amount will be charged.</li>
', NULL, NULL, '2025-03-18 12:47:11', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'cancellation policy rule 2', '<li>Up to 14 days before arrival, the cancellation is free of charge.</li>
<li>From 14 to 7 days before arrival, 50% of the total reservation amount will be charged.</li>
<li>Within 7 days before arrival, full reservation amount will be charged.</li>
', NULL, NULL, '2025-03-18 12:47:11', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'cancellation policy rule 3', '<li>Up to 7 days before arrival, the cancellation is free of charge.</li>
<li>Within 7 days before arrival, full reservation amount will be charged.</li>
', NULL, NULL, '2025-03-18 12:47:11', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'cancellation policy rule 4', '<li>Up to 7 days before arrival, the cancellation is free of charge.</li>
<li>Within 7 days before arrival, 50% of the total reservation amount will be charged.</li>
', NULL, NULL, '2025-03-18 12:47:11', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'cancellation policy rule 5', '<li>Up to 7 days before arrival, the cancellation is free of charge.</li>
<li>Within 7 days before arrival, first night''s stay will be charged.</li>
<li>In case of a no-show, full reservation amount will be charged.</li>
', NULL, NULL, '2025-03-18 12:47:11', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'pravila otkazivanja', 'Cancellation policy', NULL, NULL, '2025-03-18 12:47:11', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'pravila otkazivanja', 'Pravila otkazivanja', NULL, NULL, '2025-03-18 12:46:24', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'cancellation policy rule 5', '<li>Do 7 dana prije dolaska otkazivanje je besplatno.</li>
<li>Unutar 7 dana prije dolaska naplaćuje se iznos prve noći.</li>
<li>U slučaju nedolaska (no-show) naplaćuje se puni iznos rezervacije.</li>
', '2025-03-18 13:12:09', 1, '2025-03-18 12:40:02', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'cancellation policy rule 4', '<li>Do 7 dana prije dolaska otkazivanje je besplatno.</li>
<li>Unutar 7 dana prije dolaska naplaćuje se 50% ukupnog iznosa rezervacije.</li>
', '2025-03-18 12:59:04', 1, '2025-03-18 12:39:20', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'cancellation policy rule 2', '<li>Do 14 dana prije dolaska otkazivanje je besplatno.</li>
<li>Od 14 do 7 dana prije dolaska naplaćuje se 50% ukupnog iznosa rezervacije.</li>
<li>Unutar 7 dana prije dolaska naplaćuje se puni iznos rezervacije.</li>
', '2025-03-18 12:39:02', 1, '2025-03-18 12:38:37', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'cancellation policy rule 1', '<li>Do 30 dana prije dolaska otkazivanje je besplatno.</li>
<li>Od 29 do 14 dana prije dolaska naplaćuje se 50% ukupnog iznosa rezervacije.</li>
<li>Unutar 14 dana prije dolaska naplaćuje se puni iznos rezervacije.</li>
', '2025-03-18 12:38:13', 1, '2025-03-18 12:37:44', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'cancellation policy rule 3', '<li>Do 7 dana prije dolaska otkazivanje je besplatno.</li>
<li>Unutar 7 dana prije dolaska naplaćuje se puni iznos rezervacije.</li>
', '2025-03-18 12:35:31', 1, '2025-03-18 12:32:18', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(9, 'db cancellation policy rule 5', '<b>Pravilnik 5</b><br/>
Do 7 dana prije dolaska otkazivanje je besplatno.<br/>
Unutar 7 dana prije dolaska naplaćuje se iznos prve noći.<br/>
U slučaju nedolaska (no-show) naplaćuje se puni iznos rezervacije.
', '2025-03-18 09:38:27', 1, '2025-03-18 08:42:22', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(9, 'db cancellation policy rule 4', '<b>Pravilnik 4</b><br/>
Do 7 dana prije dolaska otkazivanje je besplatno.<br/>
Unutar 7 dana prije dolaska naplaćuje se 50% ukupnog iznosa rezervacije.
', '2025-03-18 09:37:44', 1, '2025-03-18 08:42:22', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(9, 'db cancellation policy rule 3', '<b>Pravilnik 3</b><br/>
Do 7 dana prije dolaska otkazivanje je besplatno.<br/>
Unutar 7 dana prije dolaska naplaćuje se puni iznos rezervacije.

', '2025-03-18 12:35:15', 1, '2025-03-18 08:42:22', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(9, 'db cancellation policy rule 2', '<b>Pravilnik 2 </b><br/>
Do 14 dana prije dolaska otkazivanje je besplatno.<br/>
Od 14 do 7 dana prije dolaska naplaćuje se 50% ukupnog iznosa rezervacije.<br/>
Unutar 7 dana prije dolaska naplaćuje se puni iznos rezervacije.
', '2025-03-18 10:37:55', 1, '2025-03-18 08:42:22', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(9, 'db cancellation policy rule 1', '<b>Pravilnik 1</b><br/>
Do 30 dana prije dolaska otkazivanje je besplatno.<br/>
Od 29 do 14 dana prije dolaska naplaćuje se 50% ukupnog iznosa rezervacije.<br/>
Unutar 14 dana prije dolaska naplaćuje se puni iznos rezervacije.
', '2025-03-18 09:18:37', 1, '2025-03-18 08:42:22', '1');
INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(9, 'cancellation policy', 'Pravila otkazivanja', '2025-03-18 09:20:08', 1, '2025-03-17 15:27:53', '1');

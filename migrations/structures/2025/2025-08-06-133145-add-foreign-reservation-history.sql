CREATE TABLE `foreign_reservation_history`
(
    `id`                     int(11) NOT NULL AUTO_INCREMENT,
    `product_id`             int(11) NOT NULL,
    `booking_language`       varchar(100) DEFAULT NULL,
    `board`                  varchar(100) DEFAULT NULL,
    `booking_date`           datetime NOT NULL,
    `arrive`                 datetime NOT NULL,
    `departure`              datetime NOT NULL,
    `web`                    varchar(500) DEFAULT NULL,
    `booking_type`           varchar(50)  DEFAULT NULL,
    `payment_method`         VARCHAR(50)  DEFAULT NULL,
    `adults_number`          int(2) DEFAULT NULL,
    `child1_age`             int(2) DEFAULT NULL,
    `child2_age`             int(2) DEFAULT NULL,
    `child3_age`             int(2) DEFAULT NULL,
    `child4_age`             int(2) DEFAULT NULL,
    `child5_age`             int(2) DEFAULT NULL,
    `child6_age`             int(2) DEFAULT NULL,
    `name`                   varchar(255) DEFAULT NULL,
    `surname`                varchar(255) DEFAULT NULL,
    `dni`                    varchar(255) DEFAULT NULL,
    `address`                varchar(255) DEFAULT NULL,
    `locality`               varchar(255) DEFAULT NULL,
    `post_code`              varchar(50)  DEFAULT NULL,
    `city`                   varchar(100) DEFAULT NULL,
    `iso_country_code`       varchar(50)  DEFAULT NULL,
    `country`                varchar(50)  DEFAULT NULL,
    `telephone`              varchar(100) DEFAULT NULL,
    `telephone2`             varchar(100) DEFAULT NULL,
    `email`                  varchar(255) DEFAULT NULL,
    `fax`                    varchar(100) DEFAULT NULL,
    `language`               varchar(50)  DEFAULT NULL,
    `fiscal_code`            varchar(50)  DEFAULT NULL,
    `check_in_done`          tinyint(1) DEFAULT NULL,
    `check_in_schedule`      varchar(100) DEFAULT NULL,
    `check_out_schedule`     varchar(100) DEFAULT NULL,
    `creation_date`          datetime     DEFAULT NULL,
    `last_modified_date`     datetime     DEFAULT NULL,
    `booking_code`           varchar(100) DEFAULT NULL,
    `localizator`            varchar(100) DEFAULT NULL,
    `agent_localizator`      varchar(100) DEFAULT NULL,
    `total_price`            float        DEFAULT NULL,
    `rental_price`           float        DEFAULT NULL,
    `currency`               varchar(100) DEFAULT NULL,
    `accommodation_code`     int(11) DEFAULT NULL,
    `comments`               text         DEFAULT NULL,
    `comments_date`          varchar(100) DEFAULT NULL,
    `approved`               tinyint(1) NOT NULL DEFAULT 0,
    `declined`               tinyint(1) NOT NULL DEFAULT 0,
    `foreign_reservation_id` int(11) DEFAULT NULL,
    `reservation_id`         int(11) DEFAULT NULL,
    `original_updated`       datetime     DEFAULT NULL,
    `original_updated_by`    int(11) DEFAULT NULL,
    `original_created`       datetime     DEFAULT NULL,
    `original_created_by`    int(11) DEFAULT NULL,
    `updated`                datetime     DEFAULT NULL,
    `updated_by`             int(11) DEFAULT NULL,
    `created`                datetime     DEFAULT NULL,
    `created_by`             int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

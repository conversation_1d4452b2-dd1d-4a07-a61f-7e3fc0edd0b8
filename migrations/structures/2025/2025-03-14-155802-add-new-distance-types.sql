ALTER TABLE product_distance_types ADD mandatory TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE product_distance_types CHANGE mandatory mandatory TINYINT(1) DEFAULT 0 NOT NULL AFTER repeater;

update product_distance_types set mandatory = 1 where id not in (11, 15);

INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(18, 'Biciklistička staza', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(19, 'Planinarska staza', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(20, 'Golf teren', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(21, 'Vodeni park', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(22, 'Kajakarenje', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(23, 'Tenis teren', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(24, 'Dječje igralište', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(25, 'Toplice', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(26, 'Vožnja kanu', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(27, 'Park prirode', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(28, 'Nacionalni park', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(29, 'Javni bazen', NULL, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO product_distance_types
(id, name, point_of_interest_id, repeater, mandatory, updated, updated_by, created, created_by)
VALUES(30, 'Ribarska tura', NULL, 1, 0, NULL, NULL, NULL, NULL);

ALTER TABLE product_distance_types ADD `position` INT(3) DEFAULT 1 NOT NULL;
ALTER TABLE product_distance_types CHANGE `position` `position` INT(3) DEFAULT 1 NOT NULL AFTER mandatory;


UPDATE product_distance_types
SET `position`=13
WHERE id=1;
UPDATE product_distance_types
SET `position`=2
WHERE id=3;
UPDATE product_distance_types
SET `position`=3
WHERE id=4;
UPDATE product_distance_types
SET `position`=4
WHERE id=5;
UPDATE product_distance_types
SET `position`=5
WHERE id=6;
UPDATE product_distance_types
SET `position`=6
WHERE id=7;
UPDATE product_distance_types
SET `position`=7
WHERE id=8;
UPDATE product_distance_types
SET `position`=8
WHERE id=9;
UPDATE product_distance_types
SET `position`=9
WHERE id=10;
UPDATE product_distance_types
SET `position`=10
WHERE id=12;
UPDATE product_distance_types
SET `position`=11
WHERE id=13;
UPDATE product_distance_types
SET `position`=12
WHERE id=14;

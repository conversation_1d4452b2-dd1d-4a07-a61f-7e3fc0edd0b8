ALTER TABLE products ADD product_type enum('apartment','apartment_with_pool','studio_apartment','studio_apartment_with_pool', 'villa_with_pool', 'villa') DEFAULT 'villa_with_pool' NOT NULL;
ALTER TABLE products CHANGE product_type product_type enum('apartment','apartment_with_pool','studio_apartment','studio_apartment_with_pool', 'villa_with_pool', 'villa') DEFAULT 'villa_with_pool' NOT NULL AFTER name;

ALTER TABLE product_detail ADD elevator tinyint(1) DEFAULT NULL NULL;
ALTER TABLE product_detail CHANGE elevator elevator tinyint(1) DEFAULT NULL NULL AFTER terraced_house;

ALTER TABLE product_kitchen ADD `number` INT(6) DEFAULT 1 NOT NULL;
ALTER TABLE product_kitchen CHANGE `number` `number` INT(6) DEFAULT 1 NOT NULL AFTER product_id;

ALTER TABLE product_living_room  ADD `number` INT(6) DEFAULT 1 NOT NULL;
ALTER TABLE product_living_room CHANGE `number` `number` INT(6) DEFAULT 1 NOT NULL AFTER product_id;

ALTER TABLE product_bedroom ADD location_on_the_attic TINYINT(1) DEFAULT NULL NULL;
ALTER TABLE product_bedroom CHANGE location_on_the_attic location_on_the_attic TINYINT(1) DEFAULT NULL NULL AFTER size_in_m2;


ALTER TABLE product_log ADD step_9_changes text DEFAULT NULL NULL;
ALTER TABLE product_log CHANGE step_9_changes step_9_changes text DEFAULT NULL NULL AFTER step_8_approved;
ALTER TABLE product_log ADD step_9_approved TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE product_log CHANGE step_9_approved step_9_approved TINYINT(1) DEFAULT 0 NOT NULL AFTER step_9_changes;

ALTER TABLE product_log ADD step_10_changes text DEFAULT NULL NULL;
ALTER TABLE product_log CHANGE step_10_changes step_10_changes text DEFAULT NULL NULL AFTER step_9_approved;
ALTER TABLE product_log ADD step_10_approved TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE product_log CHANGE step_10_approved step_10_approved TINYINT(1) DEFAULT 0 NOT NULL AFTER step_10_changes;

ALTER TABLE product_log ADD step_11_changes text DEFAULT NULL NULL;
ALTER TABLE product_log CHANGE step_11_changes step_11_changes text DEFAULT NULL NULL AFTER step_10_approved;
ALTER TABLE product_log ADD step_11_approved TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE product_log CHANGE step_11_approved step_11_approved TINYINT(1) DEFAULT 0 NOT NULL AFTER step_11_changes;

ALTER TABLE product_log ADD step_12_changes text DEFAULT NULL NULL;
ALTER TABLE product_log CHANGE step_12_changes step_12_changes text DEFAULT NULL NULL AFTER step_11_approved;
ALTER TABLE product_log ADD step_12_approved TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE product_log CHANGE step_12_approved step_12_approved TINYINT(1) DEFAULT 0 NOT NULL AFTER step_12_changes;

ALTER TABLE product_log ADD step_13_changes text DEFAULT NULL NULL;
ALTER TABLE product_log CHANGE step_13_changes step_13_changes text DEFAULT NULL NULL AFTER step_12_approved;
ALTER TABLE product_log ADD step_13_approved TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE product_log CHANGE step_13_approved step_13_approved TINYINT(1) DEFAULT 0 NOT NULL AFTER step_13_changes;

ALTER TABLE product_log ADD step_9 text DEFAULT null NULL;
ALTER TABLE product_log CHANGE step_9 step_9 text DEFAULT null NULL AFTER step_8_approved;

ALTER TABLE product_log ADD step_10 text DEFAULT null NULL;
ALTER TABLE product_log CHANGE step_10 step_10 text DEFAULT null NULL AFTER step_9_approved;

ALTER TABLE product_log ADD step_11 text DEFAULT null NULL;
ALTER TABLE product_log CHANGE step_11 step_11 text DEFAULT null NULL AFTER step_10_approved;

ALTER TABLE product_log ADD step_12 text DEFAULT null NULL;
ALTER TABLE product_log CHANGE step_12 step_12 text DEFAULT null NULL AFTER step_11_approved;

ALTER TABLE product_log ADD step_13 text DEFAULT null NULL;
ALTER TABLE product_log CHANGE step_13 step_13 text DEFAULT null NULL AFTER step_12_approved;

ALTER TABLE product_detail ADD new_object_on_vg TINYINT(1) DEFAULT NULL NULL;
ALTER TABLE product_detail CHANGE new_object_on_vg new_object_on_vg TINYINT(1) DEFAULT NULL NULL AFTER property_insurance_options;
ALTER TABLE product_detail ADD new_object_on_market TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE new_object_on_market new_object_on_market TINYINT(1) DEFAULT null NULL AFTER new_object_on_vg;

ALTER TABLE product_detail ADD object_located_within varchar(25) DEFAULT NULL NULL;
ALTER TABLE product_detail CHANGE object_located_within object_located_within varchar(25) DEFAULT NULL NULL AFTER property_insurance_options;

ALTER TABLE product_detail ADD object_on_floor varchar(100) DEFAULT NULL NULL;
ALTER TABLE product_detail CHANGE object_on_floor object_on_floor varchar(100) DEFAULT NULL NULL AFTER object_located_within;
ALTER TABLE product_detail ADD number_of_units INT(3) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE number_of_units number_of_units INT(3) DEFAULT null NULL AFTER object_on_floor;
ALTER TABLE product_detail ADD object_have_common_rooms TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE object_have_common_rooms object_have_common_rooms TINYINT(1) DEFAULT null NULL AFTER number_of_units;

ALTER TABLE product_detail ADD common_type_of_rooms varchar(255) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE common_type_of_rooms common_type_of_rooms varchar(255) DEFAULT null NULL AFTER object_have_common_rooms;

ALTER TABLE product_detail ADD type_of_elevator varchar(100) DEFAULT null NULL;
ALTER TABLE product_detail CHANGE type_of_elevator type_of_elevator varchar(100) DEFAULT null NULL AFTER common_type_of_rooms;

ALTER TABLE product_bbq ADD shared_terrace TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE shared_terrace shared_terrace TINYINT(1) DEFAULT null NULL AFTER covered_terrace;
ALTER TABLE product_bbq ADD common_yard TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE common_yard common_yard TINYINT(1) DEFAULT null NULL AFTER product_id;
ALTER TABLE product_bbq ADD shared_dining_table TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE shared_dining_table shared_dining_table TINYINT(1) DEFAULT null NULL AFTER dining_table;
ALTER TABLE product_bbq ADD shared_summer_kitchen TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE shared_summer_kitchen shared_summer_kitchen TINYINT(1) DEFAULT null NULL AFTER summer_kitchen;
ALTER TABLE product_bbq ADD shared_barbecue TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE shared_barbecue shared_barbecue TINYINT(1) DEFAULT null NULL AFTER barbecue;
ALTER TABLE product_bbq ADD shared_tavern TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE shared_tavern shared_tavern TINYINT(1) DEFAULT null NULL AFTER tavern;
ALTER TABLE product_bbq ADD shared_lounge TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE shared_lounge shared_lounge TINYINT(1) DEFAULT null NULL AFTER lounge;
ALTER TABLE product_bbq ADD balcony TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE balcony balcony TINYINT(1) DEFAULT null NULL AFTER number_of_seats_in_the_dining_lounge;
ALTER TABLE product_bbq ADD balcony_size INT(4) DEFAULT null NULL;
ALTER TABLE product_bbq CHANGE balcony_size balcony_size INT(4) DEFAULT null NULL AFTER balcony;
ALTER TABLE product_bbq ADD shared_toilet TINYINT(1) DEFAULT NULL NULL;
ALTER TABLE product_bbq CHANGE shared_toilet shared_toilet TINYINT(1) DEFAULT NULL NULL AFTER toilet;
ALTER TABLE product_bbq ADD shared_bathroom TINYINT(1) DEFAULT NULL NULL;
ALTER TABLE product_bbq CHANGE shared_bathroom shared_bathroom TINYINT(1) DEFAULT NULL NULL AFTER bathroom;


ALTER TABLE product_pool ADD shared_pool TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE shared_pool shared_pool TINYINT(1) DEFAULT null NULL AFTER spa_interior_internet_type;
ALTER TABLE product_pool ADD spa_exterior_shared_bath TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_exterior_shared_bath spa_exterior_shared_bath TINYINT(1) DEFAULT null NULL AFTER spa_exterior_cold_bath;
ALTER TABLE product_pool ADD spa_exterior_shared_jacuzzi TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_exterior_shared_jacuzzi spa_exterior_shared_jacuzzi TINYINT(1) DEFAULT null NULL AFTER spa_exterior_jacuzzi;
ALTER TABLE product_pool ADD spa_exterior_shared_massage_table TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_exterior_shared_massage_table spa_exterior_shared_massage_table TINYINT(1) DEFAULT null NULL AFTER spa_exterior_massage_table;
ALTER TABLE product_pool ADD spa_exterior_shared_bathroom_available TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_exterior_shared_bathroom_available spa_exterior_shared_bathroom_available TINYINT(1) DEFAULT null NULL AFTER spa_exterior_bathroom_available;
ALTER TABLE product_pool ADD spa_exterior_shared_sauna TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_exterior_shared_sauna spa_exterior_shared_sauna TINYINT(1) DEFAULT null NULL AFTER spa_exterior_sauna;

ALTER TABLE product_pool ADD spa_interior_shared_bath TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_interior_shared_bath spa_interior_shared_bath TINYINT(1) DEFAULT null NULL AFTER spa_interior_cold_bath;
ALTER TABLE product_pool ADD spa_interior_shared_jacuzzi TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_interior_shared_jacuzzi spa_interior_shared_jacuzzi TINYINT(1) DEFAULT null NULL AFTER spa_interior_jacuzzi;
ALTER TABLE product_pool ADD spa_interior_shared_massage_table TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_interior_shared_massage_table spa_interior_shared_massage_table TINYINT(1) DEFAULT null NULL AFTER spa_interior_massage_table;
ALTER TABLE product_pool ADD spa_interior_shared_bathroom_available TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_interior_shared_bathroom_available spa_interior_shared_bathroom_available TINYINT(1) DEFAULT null NULL AFTER spa_interior_bathroom_available;
ALTER TABLE product_pool ADD spa_interior_shared_sauna TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE spa_interior_shared_sauna spa_interior_shared_sauna TINYINT(1) DEFAULT null NULL AFTER spa_interior_sauna;

ALTER TABLE product_pool ADD interior_shared_pool TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_pool CHANGE interior_shared_pool interior_shared_pool TINYINT(1) DEFAULT null NULL AFTER adapted_for_the_disabled;

UPDATE product_log
SET
    step_1_approved = 1,
    step_2_approved = 1,
    step_3_approved = 1,
    step_4_approved = 1,
    step_5_approved = 1,
    step_6_approved = 1,
    step_7_approved = 1,
    step_8_approved = 1,
    step_9_approved = 1,
    step_10_approved = 1,
    step_11_approved = 1,
    step_12_approved = 1,
    step_13_approved = 1;

ALTER TABLE product_additional_content ADD shared_bicycle_available TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_bicycle_available shared_bicycle_available TINYINT(1) DEFAULT null NULL AFTER bicycle_available;
ALTER TABLE product_additional_content ADD shared_fitness TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_fitness shared_fitness TINYINT(1) DEFAULT null NULL AFTER fitness;
ALTER TABLE product_additional_content ADD shared_kids_playground TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_kids_playground shared_kids_playground TINYINT(1) DEFAULT null NULL AFTER kids_playground;
ALTER TABLE product_additional_content ADD shared_mini_golf TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_mini_golf shared_mini_golf TINYINT(1) DEFAULT null NULL AFTER mini_golf;
ALTER TABLE product_additional_content ADD shared_library TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_library shared_library TINYINT(1) DEFAULT null NULL AFTER library;
ALTER TABLE product_additional_content ADD shared_board_games TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_board_games shared_board_games TINYINT(1) DEFAULT null NULL AFTER board_games;
ALTER TABLE product_additional_content ADD shared_playroom TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_playroom shared_playroom TINYINT(1) DEFAULT null NULL AFTER playroom;
ALTER TABLE product_additional_content ADD shared_gaming_console TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_gaming_console shared_gaming_console TINYINT(1) DEFAULT null NULL AFTER gaming_console;
ALTER TABLE product_additional_content ADD shared_pool_table TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_pool_table shared_pool_table TINYINT(1) DEFAULT null NULL AFTER pool_table;
ALTER TABLE product_additional_content ADD shared_table_soccer TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_table_soccer shared_table_soccer TINYINT(1) DEFAULT null NULL AFTER table_soccer;
ALTER TABLE product_additional_content ADD shared_table_tennis TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_table_tennis shared_table_tennis TINYINT(1) DEFAULT null NULL AFTER table_tennis;
ALTER TABLE product_additional_content ADD shared_darts TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_darts shared_darts TINYINT(1) DEFAULT null NULL AFTER darts;
ALTER TABLE product_additional_content CHANGE darts darts tinyint(1) DEFAULT NULL NULL AFTER shared_table_tennis;
ALTER TABLE product_additional_content ADD shared_badminton TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_badminton shared_badminton TINYINT(1) DEFAULT null NULL AFTER badminton;
ALTER TABLE product_additional_content ADD shared_tennis_court TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_tennis_court shared_tennis_court TINYINT(1) DEFAULT null NULL AFTER tennis_court;
ALTER TABLE product_additional_content ADD shared_basketball_court TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_basketball_court shared_basketball_court TINYINT(1) DEFAULT null NULL AFTER basketball_court;
ALTER TABLE product_additional_content ADD shared_field_for_small_football TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_field_for_small_football shared_field_for_small_football TINYINT(1) DEFAULT null NULL AFTER field_for_small_football;
ALTER TABLE product_additional_content ADD shared_volleyball_field TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_volleyball_field shared_volleyball_field TINYINT(1) DEFAULT null NULL AFTER volleyball_field;
ALTER TABLE product_additional_content ADD shared_soccer_goals TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_soccer_goals shared_soccer_goals TINYINT(1) DEFAULT null NULL AFTER soccer_goals;
ALTER TABLE product_additional_content ADD shared_trampoline TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_additional_content CHANGE shared_trampoline shared_trampoline TINYINT(1) DEFAULT null NULL AFTER trampoline;

ALTER TABLE product_additional_content MODIFY COLUMN shared_gaming_console VARCHAR(100) DEFAULT NULL NULL;
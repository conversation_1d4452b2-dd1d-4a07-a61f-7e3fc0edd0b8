INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.step1.title', '1. kora<PERSON> – <PERSON><PERSON><PERSON> gostiju', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.step1.title', 'Step 1 – Number of guests', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.step1.info', 'Za svaku prijavljenu osobu potrebno je ispuniti z<PERSON>ban obra<PERSON>. To<PERSON><PERSON> broj gostiju i njihov redoslijed (npr. Gost 1, Gost 2, ...) prikazani su u lijevom kutu
ekrana.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.step1.info', 'A separate form must be completed for each registered guest. The exact number of guests and their order (e.g. Guest 1, Guest 2, ...) is shown in the top left corner of the
screen.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.step2.title', '2. korak – Unos podataka.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.step2.title', 'Step 2 – Entering information', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.step2.info', 'Pažljivo pročitajte sve tražene informacije. Obavezna polja označena su zvjezdicom (*) i moraju biti ispunjena. Molimo da unesete točne podatke koji odgovaraju vašem važećem
identifikacijskom dokumentu (npr. osobna iskaznica, putovnica i sl.).', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.step2.info', 'Carefully read all required fields. Mandatory fields are marked with an asterisk (*) and must be completed.
Please make sure that the information you provide matches the data in your valid identification document (e.g. ID card, passport, etc.).', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.step3.title', '3. korak – Sljedeći gosti', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.step3.title', 'Step 3 – Next guests', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.step3.info', 'Nakon što unesete podatke za prvog gosta, nastavite s unosom podataka za drugog gosta i tako redom, dok ne ispunite obrasce za sve prijavljene osobe.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.step3.info', 'Once you have entered the data for the first guest, proceed to fill in the information for the second guest, and so on, until all guests are registered.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.step4.title', '4. korak – Potvrda prijave', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.step4.title', 'Step 4 – Confirm registration', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.step4.info', 'Kada su svi obrasci ispunjeni, potvrdite unos. Time ste uspješno završili prijavu u sustav e-Visitor. Vaš domaćinće automatski dobiti obavijest o vašoj prijavi.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.step4.info', 'After completing all forms, confirm your submission. You have now successfully registered in the e-Visitor system. Your host will automatically receive a notification
of your registration.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.success.title', 'Zahvaljujemo na prijavi!', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.success.title', 'Thank you for your registration!', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.success.message', 'Ovim putem potvrđujemo kako ste se uspješno prijavili na sustav e-Visitor i time prijavili svoj boravak u Republici Hrvatskoj.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.success.message', 'We hereby confirm that you have successfully registered in the e-Visitor system and have officially reported your stay in the Republic of Croatia.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.success.info', 'Možete zatvoriti ovaj prozor.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.success.info', 'You can close this window.', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'evisitor.title', 'Dobrodošli u sustav e-Visitor prijave putem Villas Guide!', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'evisitor.title', 'Welcome to the e-Visitor registration system via Villas Guide!', '2025-07-23 10:20:08', 1, '2025-07-23 10:20:08', '1');

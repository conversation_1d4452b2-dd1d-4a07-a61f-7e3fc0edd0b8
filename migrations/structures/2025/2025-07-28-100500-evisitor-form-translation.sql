INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES
    (5, 'adult person', '<PERSON><PERSON><PERSON> osoba', NOW(), 1, NOW(), '1'),
    (6, 'adult person', 'Adult person', NOW(), 1, NOW(), '1'),
    (8, 'adult person', '', NOW(), 1, NOW(), '1'),
    (5, 'minor person', '<PERSON><PERSON><PERSON><PERSON> osoba', NOW(), 1, NOW(), '1'),
    (6, 'minor person', 'Minor person', NOW(), 1, NOW(), '1'),
    (8, 'minor person', '', NOW(), 1, NOW(), '1'),
    (5, 'registration complete', 'Registracija dovršena', NOW(), 1, NOW(), '1'),
    (6, 'registration complete', 'Registration complete', NOW(), 1, NOW(), '1'),
    (8, 'registration complete', '', NOW(), 1, NOW(), '1'),
    (5, 'enter the required information.', 'Unesite tražene obvezne informacije.', NOW(), 1, NOW(), '1'),
    (6, 'enter the required information.', 'Enter the required information.', NOW(), 1, NOW(), '1'),
    (8, 'enter the required information.', '', NOW(), 1, NOW(), '1'),
    (5, 'application procedure', 'Upute za prijavu', NOW(), 1, NOW(), '1'),
    (6, 'application procedure', 'Application procedure', NOW(), 1, NOW(), '1'),
    (8, 'application procedure', '', NOW(), 1, NOW(), '1'),
    (5, 'gender', 'Spol', NOW(), 1, NOW(), '1'),
    (6, 'gender', 'Gender', NOW(), 1, NOW(), '1'),
    (8, 'gender', '', NOW(), 1, NOW(), '1'),
    (5, 'man', 'Muško', NOW(), 1, NOW(), '1'),
    (6, 'man', 'Man', NOW(), 1, NOW(), '1'),
    (8, 'man', '', NOW(), 1, NOW(), '1'),
    (5, 'woman', 'Žensko', NOW(), 1, NOW(), '1'),
    (6, 'woman', 'Woman', NOW(), 1, NOW(), '1'),
    (8, 'woman', '', NOW(), 1, NOW(), '1'),
    (5, 'choose a country', 'Odaberi zemlju', NOW(), 1, NOW(), '1'),
    (6, 'choose a country', 'Choose a country', NOW(), 1, NOW(), '1'),
    (8, 'choose a country', '', NOW(), 1, NOW(), '1'),
    (5, 'city of residence', 'Grad prebivališta', NOW(), 1, NOW(), '1'),
    (6, 'city of residence', 'City of residence', NOW(), 1, NOW(), '1'),
    (8, 'city of residence', '', NOW(), 1, NOW(), '1'),
    (5, 'first choose country of residence', 'Prvo odaberi zemlju prebivališta', NOW(), 1, NOW(), '1'),
    (6, 'first choose country of residence', 'First choose country of residence', NOW(), 1, NOW(), '1'),
    (8, 'first choose country of residence', '', NOW(), 1, NOW(), '1'),
    (5, 'citizenship', 'Državljanstvo', NOW(), 1, NOW(), '1'),
    (6, 'citizenship', 'Citizenship', NOW(), 1, NOW(), '1'),
    (8, 'citizenship', '', NOW(), 1, NOW(), '1'),
    (5, 'country of birth', 'Zemlja rođenja', NOW(), 1, NOW(), '1'),
    (6, 'country of birth', 'Country of birth', NOW(), 1, NOW(), '1'),
    (8, 'country of birth', '', NOW(), 1, NOW(), '1'),
    (5, 'date of birth', 'Datum rođenja', NOW(), 1, NOW(), '1'),
    (6, 'date of birth', 'Date of birth', NOW(), 1, NOW(), '1'),
    (8, 'date of birth', '', NOW(), 1, NOW(), '1'),
    (5, 'children must be younger than 18 years', 'Djeca moraju biti mlađa od 18 godina', NOW(), 1, NOW(), '1'),
    (6, 'children must be younger than 18 years', 'Children must be younger than 18 years', NOW(), 1, NOW(), '1'),
    (8, 'children must be younger than 18 years', '', NOW(), 1, NOW(), '1'),
    (5, 'adult person must be older than 18 years', 'Odrasla osoba mora biti starija od 18 godina', NOW(), 1, NOW(), '1'),
    (6, 'adult person must be older than 18 years', 'Adult person must be older than 18 years', NOW(), 1, NOW(), '1'),
    (8, 'adult person must be older than 18 years', '', NOW(), 1, NOW(), '1'),
    (5, 'identity document', 'Dokument', NOW(), 1, NOW(), '1'),
    (6, 'identity document', 'Identity document', NOW(), 1, NOW(), '1'),
    (8, 'identity document', '', NOW(), 1, NOW(), '1'),
    (5, 'select a document', 'Odaberi dokument', NOW(), 1, NOW(), '1'),
    (6, 'select a document', 'Select a document', NOW(), 1, NOW(), '1'),
    (8, 'select a document', '', NOW(), 1, NOW(), '1'),
    (5, 'document number', 'Broj dokumenta', NOW(), 1, NOW(), '1'),
    (6, 'document number', 'Document number', NOW(), 1, NOW(), '1'),
    (8, 'document number', '', NOW(), 1, NOW(), '1'),
    (5, 'enter the document number', 'Unesi broj dokumenta', NOW(), 1, NOW(), '1'),
    (6, 'enter the document number', 'Enter the document number', NOW(), 1, NOW(), '1'),
    (8, 'enter the document number', '', NOW(), 1, NOW(), '1'),
    (5, 'choose one of the options offered.', 'Odaberi jednu od opcija', NOW(), 1, NOW(), '1'),
    (6, 'choose one of the options offered.', 'Choose one of the options offered.', NOW(), 1, NOW(), '1'),
    (8, 'choose one of the options offered.', '', NOW(), 1, NOW(), '1'),
    (5, 'accompanist of a person with a physical disability of 70% or more', 'Pratitelj osobe s invaliditetom od 70% ili više', NOW(), 1, NOW(), '1'),
    (6, 'accompanist of a person with a physical disability of 70% or more', 'Accompanist of a person with a physical disability of 70% or more', NOW(), 1, NOW(), '1'),
    (8, 'accompanist of a person with a physical disability of 70% or more', '', NOW(), 1, NOW(), '1'),
    (5, 'people with physical disabilities 70% and above', 'Osoba s invaliditetom od 70% ili više', NOW(), 1, NOW(), '1'),
    (6, 'people with physical disabilities 70% and above', 'People with physical disabilities 70% and above', NOW(), 1, NOW(), '1'),
    (8, 'people with physical disabilities 70% and above', '', NOW(), 1, NOW(), '1'),
    (5, 'none of the above', 'Ništa od navedenog', NOW(), 1, NOW(), '1'),
    (6, 'none of the above', 'None of the above', NOW(), 1, NOW(), '1'),
    (8, 'none of the above', '', NOW(), 1, NOW(), '1'),
    (5, 'finish entry', 'Završi unos', NOW(), 1, NOW(), '1'),
    (6, 'finish entry', 'Finish entry', NOW(), 1, NOW(), '1'),
    (8, 'finish entry', '', NOW(), 1, NOW(), '1'),
    (5, 'save and enter the next person', 'Spremi i prijeđi na drugu osobu', NOW(), 1, NOW(), '1'),
    (6, 'save and enter the next person', 'Save and enter the next person', NOW(), 1, NOW(), '1'),
    (8, 'save and enter the next person', '', NOW(), 1, NOW(), '1'),
    (5, 'you need help?', 'Trebaš pomoć?', NOW(), 1, NOW(), '1'),
    (6, 'you need help?', 'You need help?', NOW(), 1, NOW(), '1'),
    (8, 'you need help?', '', NOW(), 1, NOW(), '1')
    ON DUPLICATE KEY UPDATE
                         message = VALUES(message),
                         updated = NOW(),
                         updated_by = VALUES(updated_by);

CREATE TABLE `reservation_gaps` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_id` int(11) NOT NULL,
    `created` datetime DEFAULT current_timestamp,
    `date_from` datetime NOT NULL,
    `date_to` datetime NOT NULL,
    `admin_email_sent_date` datetime DEFAULT NULL,
    `owner_email_sent_date` datetime DEFAULT NULL,
    `reason` enum('min_night_stay_not_met','arrival_or_departure_not_match','other') NOT NULL DEFAULT 'other',
    PRIMARY KEY (`id`),
    KEY `reservation_gaps_product_id_IDX` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
CREATE TABLE `invoice_payments` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `invoice_id` int(11) NOT NULL,
                                    `ordinal` int(10) NOT NULL,
                                    `file_name` varchar(255) NOT NULL,
                                    `updated` datetime DEFAULT NULL,
                                    `updated_by` int(11) DEFAULT NULL,
                                    `created` datetime DEFAULT NULL,
                                    `created_by` int(11) DEFAULT NULL,
                                    PRIMARY KEY (`id`),
                                    KEY `FK_invoices_Id_idx` (`invoice_id`),
                                    CONSTRAINT `FK_invoices_Id_idx` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
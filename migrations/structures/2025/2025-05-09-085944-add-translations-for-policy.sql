INSERT INTO policies
(id, name, file_path, updated, updated_by, created, created_by)
VALUES(6, 'Rule 6', '/documents/policies/en/contract-of-accommodation-unit-rental-agency-guests_6.pdf', NULL, NULL, NULL, NULL);

INSERT INTO policies
(id, name, file_path, updated, updated_by, created, created_by)
VALUES(7, 'Rule 7', '/documents/policies/en/contract-of-accommodation-unit-rental-agency-guests_6.pdf', NULL, NULL, NULL, NULL);

UPDATE translation_messages
SET message='<b>Pravilnik 1</b><br/>
Do 60 dana prije dolaska, gost ima pravo na besplatno otkazivanje.<br/>
Od 59 do 29 dana prije dolaska, gosti ima pravo na povrat iznosa od 50% ukupno ugovorene cijene smještaja.<br/>
Od 29 dana do 1 dana prije dolaska, gost ima pravo na povrat iznosa od 20% ukupno ugovorene cijene smještaja.'
WHERE translation_messages.key='db cancellation policy rule 1' and language_id = 9;

UPDATE translation_messages
SET message='<b>Pravilnik 2 </b><br/>
Do 30 dana prije dolaska, gost ima pravo na besplatno otkazivanje.<br/>
Od 29 do 14 dana prije dolaska, gost ima pravo na povrat iznosa od 50% ukupno ugovorene cijene smještaja.<br/>
Otkazivanjem unutar 14 dana prije datuma dolaska, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.'
WHERE translation_messages.key='db cancellation policy rule 2' and language_id = 9;

UPDATE translation_messages
SET message='<b>Pravilnik 3</b><br/>
Oo 14 dana prije dolaska, gost ima pravo na besplatno otkazivanje.<br/>
Od 14 do 7 dana prije dolaska, gost ima pravo na povrat iznosa od 50% ukupno ugovorene cijene smještaja.<br/>
Otkazivanjem unutar 7 dana prije datuma dolaska, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.'
WHERE translation_messages.key='db cancellation policy rule 3' and language_id = 9;

UPDATE translation_messages
SET message='<b>Pravilnik 4</b><br/>
Do 7 dana prije dolaska, gost ima pravo na besplatno otkazivanje.<br/>
Otkazivanjem unutar 7 dana prije datuma dolaska, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.'
WHERE translation_messages.key='db cancellation policy rule 4' and language_id = 9;

UPDATE translation_messages
SET message='<b>Pravilnik 5</b><br/>
Do 7 dana prije dolaska, gost ima pravo na besplatno otkazivanje.<br/>
Otkazivanjem unutar 7 dana prije datuma dolaska, od gosta se naplaćuje 50% od ukupno ugovorene cijene smještaja.<br/>
U slučaju „no show“, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.'
WHERE translation_messages.key='db cancellation policy rule 5' and language_id = 9;

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(9, 'db cancellation policy rule 6', '<b>Pravilnik 6</b><br/>
Do 7 dana prije dolaska, gost ima pravo na besplatno otkazivanje.<br/>
Otkazivanjem unutar 7 dana prije datuma dolaska, od gosta se naplaćuje iznos prvog noćenja.<br/>
U slučaju „no show“, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.', NULL, NULL, '2025-05-07 10:14:02', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(9, 'db cancellation policy rule 7', '<b>Pravilnik 7</b><br/>
Otkazivanje od dana izvršenja rezervacije do 30 dana prije datuma dolaska naplaćuje se 30% od ukupnog iznosa rezervacije.<br/>
Otkazivanje unutar 29 dana prije datuma dolaska naplaćuje se 100% ukupnog iznosa rezervacije.', NULL, NULL, '2025-05-07 13:10:24', '1');


UPDATE translation_messages
SET message='<b>Policy 1</b><br/>
Up to 60 days before arrival, the guest is entitled to free cancellation.<br/>
From 59 to 29 days before arrival, the guest is entitled to a refund of 50% of the total agreed accommodation price.<br/>
From 29 to 1 day before arrival, the guest is entitled to a refund of 20% of the total agreed accommodation price.
'
WHERE translation_messages.key='db cancellation policy rule 1' and language_id = 10;

UPDATE translation_messages
SET message='<b>Policy 2</b><br/>
Up to 30 days before arrival, the guest is entitled to free cancellation.<br/>
From 29 to 14 days before arrival, the guest is entitled to a refund of 50% of the total agreed accommodation price.<br/>
Cancellation within 14 days before the arrival date will be charged 100% of the total agreed accommodation price.'
WHERE translation_messages.key='db cancellation policy rule 2' and language_id = 10;

UPDATE translation_messages
SET message='<b>Policy 3</b><br/>
Up to 14 days before arrival, the guest is entitled to free cancellation.<br/>
From 14 to 7 days before arrival, the guest is entitled to a refund of 50% of the total agreed accommodation price.<br/>
Cancellation within 7 days before the arrival date will be charged 100% of the total agreed accommodation price.'
WHERE translation_messages.key='db cancellation policy rule 3' and language_id = 10;

UPDATE translation_messages
SET message='<b>Policy 4</b><br/>
Up to 7 days before arrival, the guest is entitled to free cancellation.<br/>
Cancellation within 7 days before the arrival date will be charged 100% of the total agreed accommodation price.'
WHERE translation_messages.key='db cancellation policy rule 4' and language_id = 10;

UPDATE translation_messages
SET message='<b>Policy 5</b><br/>
Up to 7 days before arrival, the guest is entitled to free cancellation.<br/>
Cancellation within 7 days before the arrival date will be charged 50% of the total agreed accommodation price.<br/>
In case of a "no-show", the guest will be charged 100% of the total agreed accommodation price.'
WHERE translation_messages.key='db cancellation policy rule 5' and language_id = 10;

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(10, 'db cancellation policy rule 6', '<b>Policy 6</b><br/>
Up to 7 days before arrival, the guest is entitled to free cancellation.<br/>
Cancellation within 7 days before the arrival date will be charged the amount of the first night''s stay.<br/>
In case of a "no-show", the guest will be charged 100% of the total agreed accommodation price.', NULL, NULL, '2025-05-07 10:14:02', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(10, 'db cancellation policy rule 7', '<b>Policy 7</b><br/>
Cancellation from the date of reservation up to 30 days before the arrival date will be charged 30% of the total reservation amount.<br/>
Cancellation within 29 days before the arrival date will be charged 100% of the total reservation amount.', NULL, NULL, '2025-05-07 13:10:24', '1');




--  cancellation policy rule 1
UPDATE translation_messages
SET message='<li>Do 60 dana prije dolaska, gost ima pravo na besplatno otkazivanje.</li>
<li>Od 59 do 29 dana prije dolaska, gosti ima pravo na povrat iznosa od 50% ukupno ugovorene cijene smještaja.</li>
<li>Od 29 dana do 1 dana prije dolaska, gost ima pravo na povrat iznosa od 20% ukupno ugovorene cijene smještaja.</li>'
where translation_messages.key='cancellation policy rule 1' and language_id = 5;

--  cancellation policy rule 2
UPDATE translation_messages
SET message='<li>Do 30 dana prije dolaska, gost ima pravo na besplatno otkazivanje.</li>
<li>Od 29 do 14 dana prije dolaska, gost ima pravo na povrat iznosa od 50% ukupno ugovorene cijene smještaja.</li>
<li>Otkazivanjem unutar 14 dana prije datuma dolaska, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.</li>'
where translation_messages.key='cancellation policy rule 2' and language_id = 5;

--  cancellation policy rule 3
UPDATE translation_messages
SET message='<li>Do 14 dana prije dolaska, gost ima pravo na besplatno otkazivanje.</li>
<li>Od 14 do 7 dana prije dolaska, gost ima pravo na povrat iznosa od 50% ukupno ugovorene cijene smještaja.</li>
<li>Otkazivanjem unutar 7 dana prije datuma dolaska, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.</li>'
WHERE	translation_messages.key='cancellation policy rule 3' and language_id = 5;

--  cancellation policy rule 4
UPDATE translation_messages
SET message='<li>Do 7 dana prije dolaska, gost ima pravo na besplatno otkazivanje.</li>
<li>Otkazivanjem unutar 7 dana prije datuma dolaska, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.</li>'
where translation_messages.key='cancellation policy rule 4' and language_id = 5;

--  cancellation policy rule 5
UPDATE translation_messages
SET message='<li>Do 7 dana prije dolaska, gost ima pravo na besplatno otkazivanje.</li>
<li>Otkazivanjem unutar 7 dana prije datuma dolaska, od gosta se naplaćuje 50% od ukupno ugovorene cijene smještaja.</li>
<li>U slučaju „no show“, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.</li>'
where translation_messages.key='cancellation policy rule 5' and language_id = 5;


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'cancellation policy rule 6', '<li>Do 7 dana prije dolaska, gost ima pravo na besplatno otkazivanje.</li>
<li>Otkazivanjem unutar 7 dana prije datuma dolaska, od gosta se naplaćuje iznos prvog noćenja.</li>
<li>U slučaju „no show“, od gosta se naplaćuje 100% od ukupno ugovorene cijene smještaja.</li>
', '2025-05-08 09:51:23', 1, '2025-03-18 12:40:02', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'cancellation policy rule 7', '<li>Otkazivanje od dana izvršenja rezervacije do 30 dana prije datuma dolaska naplaćuje se 30% od ukupnog iznosa rezervacije.</li>
<li>Otkazivanje unutar 29 dana prije datuma dolaska naplaćuje se 100% ukupnog iznosa rezervacije.</li>
', '2025-05-08 09:51:23', 1, '2025-03-18 12:40:02', '1');



--  cancellation policy rule 1
UPDATE translation_messages
SET message='<li>Up to 60 days before arrival, the guest is entitled to free cancellation.</li>
<li>From 59 to 29 days before arrival, the guest is entitled to a refund of 50% of the total agreed accommodation price.</li>
<li>From 29 to 1 day before arrival, the guest is entitled to a refund of 20% of the total agreed accommodation price.</li>'
where translation_messages.key='cancellation policy rule 1' and language_id = 6;


--  cancellation policy rule 2
UPDATE translation_messages
SET message='<li>Up to 30 days before arrival, the guest is entitled to free cancellation.</li>
<li>From 29 to 14 days before arrival, the guest is entitled to a refund of 50% of the total agreed accommodation price.</li>
<li>Cancellation within 14 days before the arrival date will be charged 100% of the total agreed accommodation price.</li>'
where translation_messages.key='cancellation policy rule 2' and language_id = 6;

--  cancellation policy rule 3
UPDATE translation_messages
SET message='<li>Up to 14 days before arrival, the guest is entitled to free cancellation.</li>
<li>From 14 to 7 days before arrival, the guest is entitled to a refund of 50% of the total agreed accommodation price.</li>
<li>Cancellation within 7 days before the arrival date will be charged 100% of the total agreed accommodation price.</li>'
where translation_messages.key='cancellation policy rule 3' and language_id = 6;

--  cancellation policy rule 4
UPDATE translation_messages
SET message='<li>Up to 7 days before arrival, the guest is entitled to free cancellation.</li>
<li>Cancellation within 7 days before the arrival date will be charged 100% of the total agreed accommodation price.</li>'
where translation_messages.key='cancellation policy rule 4' and language_id = 6;

--  cancellation policy rule 5
UPDATE translation_messages
SET message='<li>Up to 7 days before arrival, the guest is entitled to free cancellation.</li>
<li>Cancellation within 7 days before the arrival date will be charged 50% of the total agreed accommodation price.</li>
<li>In case of a "no-show", the guest will be charged 100% of the total agreed accommodation price.</li>'
where translation_messages.key='cancellation policy rule 5' and language_id = 6;


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'cancellation policy rule 6', '<li>Up to 7 days before arrival, the guest is entitled to free cancellation.</li>
<li>Cancellation within 7 days before the arrival date will be charged the amount of the first night''s stay.</li>
<li>In case of a "no-show", the guest will be charged 100% of the total agreed accommodation price.</li>
', NULL, NULL, '2025-03-18 12:47:11', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'cancellation policy rule 7', '<li>Cancellation from the date of reservation up to 30 days before the arrival date will be charged 30% of the total reservation amount.</li>
<li>Cancellation within 29 days before the arrival date will be charged 100% of the total reservation amount.</li>
', NULL, NULL, '2025-03-18 12:47:11', '1');



--  cancellation policy rule 1
UPDATE translation_messages
SET message='<li>Bis 60 Tage vor der Ankunft hat der Gast das Recht auf kostenlose Stornierung.</li>
<li>Von 59 bis 29 Tage vor der Ankunft hat der Gast Anspruch auf eine Rückerstattung von 50 % des insgesamt vereinbarten Unterkunftspreises.</li>
<li>Von 29 bis 1 Tag vor der Ankunft hat der Gast Anspruch auf eine Rückerstattung von 20 % des insgesamt vereinbarten Unterkunftspreises.</li>
' where translation_messages.key='cancellation policy rule 1' and language_id = 8;


--  cancellation policy rule 2
UPDATE translation_messages
SET message='<li>Bis 30 Tage vor der Ankunft hat der Gast das Recht auf kostenlose Stornierung.</li>
<li>Von 29 bis 14 Tage vor der Ankunft hat der Gast Anspruch auf eine Rückerstattung von 50 % des insgesamt vereinbarten Unterkunftspreises.</li>
<li>Bei einer Stornierung innerhalb von 14 Tagen vor dem Ankunftsdatum werden 100 % des insgesamt vereinbarten Unterkunftspreises berechnet.</li>
' where translation_messages.key='cancellation policy rule 2' and language_id = 8;


--  cancellation policy rule 3
UPDATE translation_messages
SET message='<li>Bis 14 Tage vor der Ankunft hat der Gast das Recht auf kostenlose Stornierung.</li>
<li>Von 14 bis 7 Tage vor der Ankunft hat der Gast Anspruch auf eine Rückerstattung von 50 % des insgesamt vereinbarten Unterkunftspreises.</li>
<li>Bei einer Stornierung innerhalb von 7 Tagen vor dem Ankunftsdatum werden 100 % des insgesamt vereinbarten Unterkunftspreises berechnet.</li>
' where translation_messages.key='cancellation policy rule 3' and language_id = 8;

--  cancellation policy rule 4
UPDATE translation_messages
SET message='<li>Bis 7 Tage vor der Ankunft hat der Gast das Recht auf kostenlose Stornierung.</li>
<li>Bei einer Stornierung innerhalb von 7 Tagen vor dem Ankunftsdatum werden 100 % des insgesamt vereinbarten Unterkunftspreises berechnet.</li>
' where translation_messages.key='cancellation policy rule 4' and language_id = 8;


--  cancellation policy rule 5
UPDATE translation_messages
SET message='<li>Bis 7 Tage vor der Ankunft hat der Gast das Recht auf kostenlose Stornierung.</li>
<li>Bei einer Stornierung innerhalb von 7 Tagen vor dem Ankunftsdatum werden 50 % des insgesamt vereinbarten Unterkunftspreises berechnet.</li>
<li>Bei Nichterscheinen („No Show“) werden 100 % des insgesamt vereinbarten Unterkunftspreises berechnet.</li>
' where translation_messages.key='cancellation policy rule 5' and language_id = 8;



INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'cancellation policy rule 6', '<li>Bis 7 Tage vor der Ankunft hat der Gast das Recht auf kostenlose Stornierung.</li>
<li>Bei einer Stornierung innerhalb von 7 Tagen vor dem Ankunftsdatum wird der Preis für die erste Nacht berechnet.</li>
<li>Bei Nichterscheinen („No Show“) werden 100 % des insgesamt vereinbarten Unterkunftspreises berechnet.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'cancellation policy rule 7', '<li>Stornierungen vom Tag der Buchung bis 30 Tage vor dem Anreisedatum werden mit 30 % des Gesamtbetrags der Reservierung berechnet.</li>
<li>Stornierungen innerhalb von 29 Tagen vor dem Anreisedatum werden mit 100 % des Gesamtbetrags der Reservierung berechnet.</li>', NULL, NULL, '2025-03-18 12:48:52', '1');


--  cancellation policy rule 1
UPDATE translation_messages
SET message='<li>Fino a 60 giorni prima dell''arrivo, l''ospite ha diritto alla cancellazione gratuita.</li>
<li>Da 59 a 29 giorni prima dell''arrivo, l''ospite ha diritto al rimborso del 50% del prezzo totale concordato per l’alloggio.</li>
<li>Da 29 a 1 giorno prima dell''arrivo, l''ospite ha diritto al rimborso del 20% del prezzo totale concordato per l’alloggio.</li>
' where translation_messages.key='cancellation policy rule 1' and language_id = 7;

--  cancellation policy rule 2
UPDATE translation_messages
SET message='<li>Fino a 30 giorni prima dell''arrivo, l''ospite ha diritto alla cancellazione gratuita.</li>
<li>Da 29 a 14 giorni prima dell''arrivo, l''ospite ha diritto al rimborso del 50% del prezzo totale concordato per l’alloggio.</li>
<li>In caso di cancellazione entro 14 giorni dalla data di arrivo, verrà addebitato il 100% del prezzo totale concordato per l’alloggio.</li>
' where translation_messages.key='cancellation policy rule 3' and language_id = 7;

--  cancellation policy rule 3
UPDATE translation_messages
SET message='<li>Fino a 14 giorni prima dell''arrivo, l''ospite ha diritto alla cancellazione gratuita.</li>
<li>Da 14 a 7 giorni prima dell''arrivo, l''ospite ha diritto al rimborso del 50% del prezzo totale concordato per l’alloggio.</li>
<li>In caso di cancellazione entro 7 giorni dalla data di arrivo, verrà addebitato il 100% del prezzo totale concordato per l’alloggio.</li>
' where translation_messages.key='cancellation policy rule 3' and language_id = 7;

--  cancellation policy rule 4
UPDATE translation_messages
SET message='<li>Fino a 7 giorni prima dell''arrivo, l''ospite ha diritto alla cancellazione gratuita.</li>
<li>In caso di cancellazione entro 7 giorni dalla data di arrivo, verrà addebitato il 100% del prezzo totale concordato per l’alloggio.</li>
' where translation_messages.key='cancellation policy rule 4' and language_id = 7;

--  cancellation policy rule 5
UPDATE translation_messages
SET message='<li>Fino a 7 giorni prima dell''arrivo, l''ospite ha diritto alla cancellazione gratuita.</li>
<li>In caso di cancellazione entro 7 giorni dalla data di arrivo, verrà addebitato il 50% del prezzo totale concordato per l’alloggio.</li>
<li> In caso di "no show", verrà addebitato il 100% del prezzo totale concordato per l’alloggio.</li>
' where translation_messages.key='cancellation policy rule 5' and language_id = 7;

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'cancellation policy rule 6', '<li>Fino a 7 giorni prima dell''arrivo, l''ospite ha diritto alla cancellazione gratuita.</li>
<li>In caso di cancellazione entro 7 giorni dalla data di arrivo, verrà addebitato l''importo della prima notte.</li>
<li>In caso di "no show", verrà addebitato il 100% del prezzo totale concordato per l’alloggio.</li>
', NULL, NULL, '2025-03-18 12:48:52', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'cancellation policy rule 7', '<li>Le cancellazioni dal giorno della prenotazione fino a 30 giorni prima della data di arrivo comportano un addebito del 30% dell''importo totale della prenotazione.</li>
<li>Le cancellazioni entro 29 giorni prima della data di arrivo comportano un addebito del 100% dell''importo totale della prenotazione.</li>', NULL, NULL, '2025-03-18 12:48:52', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'condition policy rule 1', '<li>Ukupan iznos rezervacije potrebno je uplatiti najkasnije do 29 dana prije dolaska</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'condition policy rule 2', '<li>Ukupan iznos rezervacije potrebno je uplatiti najkasnije do 35 dana prije dolaska</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'condition policy rule 3', '<li>Ukupan iznos rezervacije potrebno je uplatiti najkasnije do 30 dana prije dolaska</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'condition policy rule 4', '<li>Ukupan iznos rezervacije potrebno je uplatiti najkasnije do 30 dana prije dolaska</li>', NULL, NULL, '2025-05-08 14:30:43', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'condition policy rule 5', '<li>Ukupan iznos rezervacije potrebno je uplatiti najkasnije do 30 dana prije dolaska</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'condition policy rule 6', '<li>Ukupan iznos rezervacije potrebno je uplatiti najkasnije do 30 dana prije dolaska</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(5, 'condition policy rule 7', '<li>Ukupan iznos rezervacije moguće je uplatiti odmah ili u 2 rate:</li><li>1. rata: 30% odmah kreditnom karticom ili bankovnim transferom u roku od 2 dana od izvršene rezervacije</li><li>2. rata: 70% preostalog iznosa kreditnom karticom ili bankovnim transferom najkasnije 35 dana prije dolaska.</li>', NULL, NULL, '2025-05-08 14:30:43', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'condition policy rule 1', '<li>The total amount of the reservation must be paid no later than 29 days before arrival</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'condition policy rule 2', '<li>The total amount of the reservation must be paid no later than 35 days before arrival</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'condition policy rule 3', '<li>The total amount of the reservation must be paid no later than 30 days before arrival</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'condition policy rule 4', '<li>The total amount of the reservation must be paid no later than 30 days before arrival</li>', NULL, NULL, '2025-05-08 14:30:43', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'condition policy rule 5', '<li>The total amount of the reservation must be paid no later than 30 days before arrival</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'condition policy rule 6', '<li>The total amount of the reservation must be paid no later than 30 days before arrival</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(6, 'condition policy rule 7', '<li>The total amount of the reservation can be paid immediately or in 2 instalments:</li><li>1st instalment: 30% immediately by credit card or bank transfer within 2 days of booking</li><li>2nd instalment: 70% of the remaining amount by credit card or bank transfer no later than 35 days before arrival</li>', NULL, NULL, '2025-05-08 14:30:43', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'condition policy rule 1', '<li>L''importo totale della prenotazione deve essere pagato al più tardi 29 giorni prima dell''arrivo</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'condition policy rule 2', '<li>L''importo totale della prenotazione deve essere pagato al più tardi 35 giorni prima dell''arrivo</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'condition policy rule 3', '<li>L''importo totale della prenotazione deve essere pagato al più tardi 30 giorni prima dell''arrivo</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'condition policy rule 4', '<li>L''importo totale della prenotazione deve essere pagato al più tardi 30 giorni prima dell''arrivo</li>', NULL, NULL, '2025-05-08 14:30:43', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'condition policy rule 5', '<li>L''importo totale della prenotazione deve essere pagato al più tardi 30 giorni prima dell''arrivo</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'condition policy rule 6', '<li>L''importo totale della prenotazione deve essere pagato al più tardi 30 giorni prima dell''arrivo</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(7, 'condition policy rule 7', '<li>L''importo totale della prenotazione può essere pagato subito oppure in 2 rate:</li><li>1ª rata: 30% subito con carta di credito o bonifico bancario entro 2 giorni dalla prenotazione</li><li>2ª rata: 70% dell''importo restante con carta di credito o bonifico bancario al più tardi 35 giorni prima dell''arrivo</li>', NULL, NULL, '2025-05-08 14:30:43', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'condition policy rule 1', '<li>Der Gesamtbetrag der Reservierung muss spätestens 29 Tage vor der Anreise bezahlt werden</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'condition policy rule 2', '<li>Der Gesamtbetrag der Reservierung muss spätestens 35 Tage vor der Anreise bezahlt werden</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'condition policy rule 3', '<li>Der Gesamtbetrag der Reservierung muss spätestens 30 Tage vor der Anreise bezahlt werden</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'condition policy rule 4', '<li>Der Gesamtbetrag der Reservierung muss spätestens 30 Tage vor der Anreise bezahlt werden</li>', NULL, NULL, '2025-05-08 14:30:43', '1');


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'condition policy rule 5', '<li>Der Gesamtbetrag der Reservierung muss spätestens 30 Tage vor der Anreise bezahlt werden</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'condition policy rule 6', '<li>Der Gesamtbetrag der Reservierung muss spätestens 30 Tage vor der Anreise bezahlt werden</li>', NULL, NULL, '2025-05-08 14:30:43', '1');

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
VALUES(8, 'condition policy rule 7', '<li>Der Gesamtbetrag der Reservierung kann sofort oder in 2 Raten bezahlt werden:</li><li>Rate: 30 % sofort per Kreditkarte oder Banküberweisung innerhalb von 2 Tagen nach der Buchung</li><li>Rate: 70 % des verbleibenden Betrags per Kreditkarte oder Banküberweisung spätestens 35 Tage vor der Anreise</li>', NULL, NULL, '2025-05-08 14:30:43', '1');
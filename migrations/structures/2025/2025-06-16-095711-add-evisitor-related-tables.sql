CREATE TABLE `evisitor_api_users` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`username` varchar(255) NOT NULL,
`password` varchar(255) NOT NULL,
`api_key` varchar(255) NOT NULL,
`created` datetime DEFAULT current_timestamp(),
`user_id` int(11) NOT NULL,
PRIMARY KEY (`id`),
UNIQUE KEY `evisitor_api_users_username_IDX` (`username`) USING BTREE,
UNIQUE KEY `evisitor_api_users_api_key_IDX` (`api_key`) USING BTREE,
KEY `evisitor_api_users_FK` (`user_id`),
CONSTRAINT `evisitor_api_users_FK` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `evisitor_api_users_products` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`evisitor_api_users_id` int(11) NOT NULL,
`product_id` int(11) NOT NULL,
`created` datetime DEFAULT current_timestamp(),
`evisitor_code` varchar(20) NOT NULL,
PRIMARY KEY (`id`),
UNIQUE KEY `evisitor_api_users_products_product_id_IDX` (`product_id`) USING BTREE,
KEY `evisitor_api_users_products_FK` (`evisitor_api_users_id`),
CONSTRAINT `evisitor_api_users_products_FK` FOREIGN KEY (`evisitor_api_users_id`) REFERENCES `evisitor_api_users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
CONSTRAINT `evisitor_api_users_products_FK_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `evisitor_reservations` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`reservation_id` int(11) NOT NULL,
`evisitor_data` text DEFAULT NULL,
`created` datetime DEFAULT current_timestamp(),
`confirmed` datetime DEFAULT NULL,
`is_processed` tinyint(1) NOT NULL DEFAULT 0,
`email_sent_date` datetime DEFAULT NULL,
PRIMARY KEY (`id`),
UNIQUE KEY `evisitor_reservations_reservation_id_IDX` (`reservation_id`) USING BTREE,
CONSTRAINT `evisitor_reservations_FK` FOREIGN KEY (`reservation_id`) REFERENCES `reservations` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `evisitor_reservations_user_data` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`evisitor_reservations_id` int(11) NOT NULL,
`evisitor_user_data` text NOT NULL,
`created` datetime DEFAULT current_timestamp(),
`evisitor_sent_date` datetime DEFAULT NULL,
`error` text DEFAULT NULL,
PRIMARY KEY (`id`),
KEY `evisitor_reservations_user_data_FK` (`evisitor_reservations_id`),
CONSTRAINT `evisitor_reservations_user_data_FK` FOREIGN KEY (`evisitor_reservations_id`) REFERENCES `evisitor_reservations` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
ALTER TABLE offers MODIFY COLUMN `type` enum('privateOffer','lastMinute','specialOffer') CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT 'privateOffer' NOT NULL;

ALTER TABLE offers DROP COLUMN price_increase;
ALTER TABLE offers DROP COLUMN price_decrease;

ALTER TABLE price_rules ADD `type` enum('fee_per_night','early_bird','default') DEFAULT 'default' NOT NULL;
ALTER TABLE price_rules CHANGE `type` `type` enum('fee_per_night','early_bird','default') DEFAULT 'default' NOT NULL AFTER product_id;

ALTER TABLE price_rules ADD min_days_to_arrival_for_discount int(3) DEFAULT null NULL;
ALTER TABLE price_rules CHANGE min_days_to_arrival_for_discount min_days_to_arrival_for_discount int(3) DEFAULT null NULL AFTER max_days_to_arrival;

ALTER TABLE price_rules ADD fee_after_number_of_guests int(2) DEFAULT null NULL;
ALTER TABLE price_rules CHANGE fee_after_number_of_guests fee_after_number_of_guests int(2) DEFAULT null NULL AFTER min_days_to_arrival_for_discount;
ALTER TABLE `feature_subgroups_translations`
ADD COLUMN `villa_front_section_id` INT(11) DEFAULT NULL,
ADD COLUMN `updated` datetime DEFAULT NULL,
ADD COLUMN  `updated_by` int(11) DEFAULT NULL,
ADD COLUMN  `created` datetime DEFAULT NULL,
ADD COLUMN  `created_by` int(11) DEFAULT NULL;


ALTER TABLE `feature_subgroups_translations`
    ADD CONSTRAINT `FK__feature_subgroups_translations__villa_front_section_id`
        FOREIGN KEY (`villa_front_section_id`)
            REFERENCES `single_villa_front_sections` (`id`)
            ON DELETE NO ACTION
            ON UPDATE NO ACTION;
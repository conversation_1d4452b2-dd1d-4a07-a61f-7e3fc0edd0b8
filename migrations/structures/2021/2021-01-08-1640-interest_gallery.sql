CREATE TABLE interest_images (
    id int(11) auto_increment NOT NULL,
    page_id int(11) NOT NULL,
    image VARCHAR(250) NOT NULL,
    order_number int(2) DEFAULT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `FK_interest_images__page_id__id`
    FOREIGN KEY (`page_id`)
    REFERENCES `pages` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
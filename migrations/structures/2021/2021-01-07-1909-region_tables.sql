CREATE TABLE region_texts (
   id int(11) auto_increment NOT NULL,
   region_id int(11) NOT NULL,
   language enum('en', 'hr', 'it', 'de') NOT NULL,
   title VARCHAR(100) DEFAULT NULL,
   content TEXT DEFAULT NULL,
   slug VARCHAR(100) DEFAULT NULL,
   alt_text VARCHAR(100) DEFAULT NULL,
   `updated` datetime DEFAULT NULL,
   `updated_by` int(11) DEFAULT NULL,
   `created` datetime DEFAULT NULL,
   `created_by` int(11) DEFAULT NULL,
   PRIMARY KEY (`id`),
   CONSTRAINT `FK_region_texts__region_id__id` FOREIGN KEY (`region_id`) REFERENCES `areas_groups` (`id`)
   ON DELETE NO ACTION
   ON UPDATE NO ACTION
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;


CREATE TABLE region_images (
  id int(11) auto_increment NOT NULL,
  region_id int(11) NOT NULL,
  image VARCHAR(250) NOT NULL,
  order_number int(2) DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK_region_images__region_id__id`
      FOREIGN KEY (`region_id`)
          REFERENCES `areas_groups` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

ALTER TABLE areas_groups DROP COLUMN text;
ALTER TABLE areas_groups DROP COLUMN alt_text;
ALTER TABLE areas_groups DROP COLUMN image;



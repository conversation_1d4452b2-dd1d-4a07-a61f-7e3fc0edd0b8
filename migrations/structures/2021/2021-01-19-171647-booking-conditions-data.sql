ALTER TABLE booking_cancellation_policy ADD max_people_number INT(3) NOT NULL AFTER `min_people_number`;

INSERT INTO booking_cancellation_policy (partner_id,translation_key,days,percentage,min_people_number, max_people_number ,updated,updated_by,created,created_by) VALUES
(1,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','65',30,NULL,14, NULL,NULL,'2021-01-15 14:52:10.000',1),
(1,'<PERSON>tkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','64 - 40',55,NULL,14,'2021-01-15 15:15:23.000',1,'2021-01-15 14:59:31.000',1),
(1,'<PERSON>t<PERSON><PERSON><PERSON><PERSON> unutar %s dana prije datuma dolaska naplaćuje se %s ukupnog iznosa rezervacije','39',100,NULL,14,NULL,NULL,'2021-01-15 15:00:06.000',1),
(1,'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','80',90,14,100,NULL,NULL,'2021-01-15 16:35:06.000',1),
(1,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','100',25,14,100,NULL,NULL,'2021-01-15 17:01:37.000',1);

INSERT INTO booking_cancellation_policy (partner_id,translation_key,days,percentage,min_people_number,max_people_number,updated,updated_by,created,created_by) VALUES
(2,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','65',30,NULL,14,NULL,NULL,'2021-01-15 14:52:10.000',1),
(2,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','64 - 40',55,NULL,14,'2021-01-15 15:15:23.000',1,'2021-01-15 14:59:31.000',1),
(2,'Otkazivanje unutar %s dana prije datuma dolaska naplaćuje se %s ukupnog iznosa rezervacije','39',100,NULL,14,NULL,NULL,'2021-01-15 15:00:06.000',1),
(2,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','80',90,14,100,NULL,NULL,'2021-01-15 16:35:06.000',1),
(2,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','100',25,14,100,NULL,NULL,'2021-01-15 17:01:37.000',1);

INSERT INTO booking_cancellation_policy (partner_id,translation_key,days,percentage,min_people_number,max_people_number,updated,updated_by,created,created_by) VALUES
(3,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','65',30,NULL,14,NULL,NULL,'2021-01-15 14:52:10.000',1),
(3,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','64 - 40',55,NULL,14,'2021-01-15 15:15:23.000',1,'2021-01-15 14:59:31.000',1),
(3,'Otkazivanje unutar %s dana prije datuma dolaska naplaćuje se %s ukupnog iznosa rezervacije','39',100,NULL,14,NULL,NULL,'2021-01-15 15:00:06.000',1),
(3,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','80',90,14,100,NULL,NULL,'2021-01-15 16:35:06.000',1),
(3,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','100',25,14,100,NULL,NULL,'2021-01-15 17:01:37.000',1);

INSERT INTO booking_cancellation_policy (partner_id,translation_key,days,percentage,min_people_number,max_people_number,updated,updated_by,created,created_by) VALUES
(4,'Otkazivanje %s dana prije datuma dolaska naplaćuje se %s od ukupnog iznosa rezervacije','42',30,NULL,100,NULL,NULL,'2021-01-15 14:52:10.000',1),
(4,'Otkazivanje unutar %s dana prije datuma dolaska naplaćuje se %s ukupnog iznosa rezervacije','41- 28',50,NULL,100,'2021-01-15 15:15:23.000',1,'2021-01-15 14:59:31.000',1),
(4,'Otkazivanje unutar %s dana prije datuma dolaska naplaćuje se %s ukupnog iznosa rezervacije','27- 8',70,NULL,100,'2021-01-15 15:15:23.000',1,'2021-01-15 14:59:31.000',1),
(4,'Otkazivanje unutar %s dana prije datuma dolaska naplaćuje se %s ukupnog iznosa rezervacije','7-1',80,NULL,100,NULL,NULL,'2021-01-15 15:00:06.000',1),
(4,'Otkazivanje na dan dolaska naplaćuje se 100 posto ukupnog iznosa rezervacije','',null,NULL,100,NULL,NULL,'2021-01-15 15:00:06.000',1);

ALTER TABLE booking_payment_dates_conditions ADD first_installment_modify_days_value INT(3) NOT NULL AFTER `modify_days_value`;

INSERT INTO booking_payment_dates_conditions (partner_id,condition_type,first_date_difference,second_date_difference,modify_date,modify_days_value,first_installment_modify_days_value,updated,updated_by,created,created_by) VALUES
(1,'more_than',69,NULL,'arrival',65,3,NULL,NULL,'2021-01-18 13:30:42.000',1),
(1,'between',21,69,'today',2,3,NULL,NULL,'2021-01-18 13:30:53.000',1),
(1,'less_than',21,NULL,'today',2,3,NULL,NULL,'2021-01-18 13:31:30.000',1);

INSERT INTO booking_payment_dates_conditions (partner_id,condition_type,first_date_difference,second_date_difference,modify_date,modify_days_value,first_installment_modify_days_value,updated,updated_by,created,created_by) VALUES
(2,'more_than',60,NULL,'arrival',50,3,NULL,NULL,'2021-01-18 13:30:42.000',1),
(2,'between',21,60,'today',2,3,NULL,NULL,'2021-01-18 13:30:53.000',1),
(2,'less_than',21,NULL,'today',2,3,NULL,NULL,'2021-01-18 13:31:30.000',1);

INSERT INTO booking_payment_dates_conditions (partner_id,condition_type,first_date_difference,second_date_difference,modify_date,modify_days_value,first_installment_modify_days_value,updated,updated_by,created,created_by) VALUES
(3,'more_than',60,NULL,'arrival',50,3,NULL,NULL,'2021-01-18 13:30:42.000',1),
(3,'between',21,60,'today',2,3,NULL,NULL,'2021-01-18 13:30:53.000',1),
(3,'less_than',21,NULL,'today',2,3,NULL,NULL,'2021-01-18 13:31:30.000',1);

INSERT INTO booking_payment_dates_conditions (partner_id,condition_type,first_date_difference,second_date_difference,modify_date,modify_days_value,first_installment_modify_days_value,updated,updated_by,created,created_by) VALUES
(4,'more_than',47,NULL,'arrival',42,5,NULL,NULL,'2021-01-18 13:30:42.000',1),
(4,'less_than',48,NULL,'today',1,5,NULL,NULL,'2021-01-18 13:31:30.000',1);

INSERT INTO booking_payment_amounts_conditions (partner_id,first_installment_percentage,second_installment_percentage) VALUES (1,35,65);
INSERT INTO booking_payment_amounts_conditions (partner_id,first_installment_percentage,second_installment_percentage) VALUES (2,35,65);
INSERT INTO booking_payment_amounts_conditions (partner_id,first_installment_percentage,second_installment_percentage) VALUES (3,35,65);
INSERT INTO booking_payment_amounts_conditions (partner_id,first_installment_percentage,second_installment_percentage) VALUES (4,30,70);

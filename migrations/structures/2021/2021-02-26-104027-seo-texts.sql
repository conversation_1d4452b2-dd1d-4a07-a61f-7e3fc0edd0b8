CREATE TABLE seo_text_groups (
     id INT(11) auto_increment NOT NULL,
     group_name varchar(100) NOT NULL,
     `updated` datetime DEFAULT NULL,
     `updated_by` int(11) DEFAULT NULL,
     `created` datetime DEFAULT NULL,
     `created_by` int(11) DEFAULT NULL,
     PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;


CREATE TABLE seo_texts (
   id INT(11) auto_increment NOT NULL,
   group_id INT(11) NOT NULL,
   language VARCHAR(3) NOT NULL,
   text TEXT NOT NULL,
   `updated` datetime DEFAULT NULL,
   `updated_by` int(11) DEFAULT NULL,
   `created` datetime DEFAULT NULL,
   `created_by` int(11) DEFAULT NULL,
   PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

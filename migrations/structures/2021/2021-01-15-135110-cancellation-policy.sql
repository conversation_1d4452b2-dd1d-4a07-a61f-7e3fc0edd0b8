CREATE TABLE booking_cancellation_policy (
     id int(11) auto_increment NOT NULL,
     partner_id int(11) NOT null,
     translation_key TEXT NOT NULL,
     days int(11) DEFAULT null,
     percentage int(3) DEFAULT null,
     `updated` datetime DEFAULT NULL,
     `updated_by` int(11) DEFAULT NULL,
     `created` datetime DEFAULT NULL,
     `created_by` int(11) DEFAULT NULL,
     PRIMARY KEY (`id`),
     CONSTRAINT `FK_booking_cancellation_policy__partner_id__id` FOREIGN KEY (`partner_id`) REFERENCES `partners` (`id`)
         ON DELETE NO ACTION
         ON UPDATE NO ACTION
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'confirm your booking', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'enter your personal details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'this field is required', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'invalid email address', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'phone number', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'choose your payment details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'more information', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'secure payment', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'confirm and book', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'your booking details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'nikolina salamun - help center', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'help needed?', 1, NOW());



INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'confirm your booking', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'enter your personal details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'this field is required', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'invalid email address', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'phone number', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'choose your payment details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'more information', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'secure payment', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'confirm and book', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'your booking details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'nikolina salamun - help center', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'help needed?', 1, NOW());



INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'confirm your booking', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'enter your personal details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'this field is required', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'invalid email address', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'phone number', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'choose your payment details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'more information', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'secure payment', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'confirm and book', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'your booking details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'nikolina salamun - help center', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'help needed?', 1, NOW());


INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'confirm your booking', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'enter your personal details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'this field is required', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'invalid email address', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'phone number', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'choose your payment details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'more information', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'secure payment', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'confirm and book', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'your booking details', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'nikolina salamun - help center', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'help needed?', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (5, 'confirm your booking at %s | villas guide', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (6, 'confirm your booking at %s | villas guide', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (7, 'confirm your booking at %s | villas guide', 1, NOW());

INSERT IGNORE INTO `translation_messages` (`language_id`, `key`, `created_by`, `created`)
VALUES (8, 'confirm your booking at %s | villas guide', 1, NOW())
CREATE TABLE booking_payment_dates_conditions (
    id int(11) auto_increment NOT NULL,
    partner_id int(11) NOT NULL,
    condition_type enum('more_than', 'less_than', 'between') NOT NULL,
    first_date_difference int(3) NOT NULL,
    second_date_difference int(3) DEFAULT null,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `FK_booking_payment_dates_conditions__partner_id__id` FOREIGN KEY (`partner_id`) REFERENCES `partners` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE booking_payment_amounts_conditions (
    id int(11) auto_increment NOT NULL,
    partner_id int(11) NOT null,
    first_installment_percentage int(3) NOT NULL,
    second_installment_percentage int(3) NOT NULL,
    `updated` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `created` datetime DEFAULT NULL,
    `created_by` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `FK_booking_payment_amounts_conditions__partner_id__id` FOREIGN KEY (`partner_id`) REFERENCES `partners` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
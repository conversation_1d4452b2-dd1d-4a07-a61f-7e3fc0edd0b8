CREATE TABLE price_rules (
 id int(11) auto_increment NOT NULL,
 product_id int(11) NOT NULL,
 date_from DATE NOT NULL,
 date_to DATE NOT NULL,
 discount_percentage DECIMAL(4,2) DEFAULT NULL,
 arrival_days VARCHAR(25) DEFAULT NULL,
 min_stay_days int(2) NOT NULL,
 max_stay_days int(2) NOT NULL,
 max_days_to_arrival int(3) DEFAULT NULL,
 `updated` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `created` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 CONSTRAINT `FK_price_rules__product_id__id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
     ON DELETE NO ACTION
     ON UPDATE NO ACTION
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
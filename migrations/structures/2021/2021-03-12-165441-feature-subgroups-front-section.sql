CREATE TABLE features_subgroups_section (
     id INT(11) auto_increment NOT NULL,
     feature_id INT(11) NOT NULL,
     feature_subgroup_id INT(11) NOT NULL,
     villa_front_section_id INT(11) NOT NULL,
     `updated` datetime DEFAULT NULL,
     `updated_by` int(11) DEFAULT NULL,
     `created` datetime DEFAULT NULL,
     `created_by` int(11) DEFAULT NULL,
     PRIMARY KEY (`id`)
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

ALTER TABLE `features_subgroups_section`
    ADD CONSTRAINT `FK__features_subgroups_section__villa_front_section_id`
        FOREIGN KEY (`villa_front_section_id`)
            REFERENCES `single_villa_front_sections` (`id`)
            ON DELETE NO ACTION
            ON UPDATE NO ACTION;

ALTER TABLE `feature_subgroups_translations` DROP FOREIGN KEY `FK__feature_subgroups_translations__villa_front_section_id`;
ALTER TABLE feature_subgroups_translations DROP villa_front_section_id;
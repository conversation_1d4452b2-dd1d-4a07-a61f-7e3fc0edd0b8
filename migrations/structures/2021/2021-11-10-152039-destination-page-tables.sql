CREATE TABLE destination_texts (
  id int(11) auto_increment NOT NULL,
  destination_id int(11) NOT NULL,
  language enum('en', 'hr', 'it', 'de') NOT NULL,
  title VARCHAR(100) DEFAULT NULL,
  content TEXT DEFAULT NULL,
  slug VARCHAR(400) DEFAULT NULL,
  alt_text VARCHAR(100) DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK_destination_texts__destination_id__id` FOREIGN KEY (`destination_id`) REFERENCES `areas_translations` (`area_subgroup_id`)
      ON DELETE NO ACTION
      ON UPDATE NO ACTION
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;


CREATE TABLE destination_images (
   id int(11) auto_increment NOT NULL,
   destination_id int(11) NOT NULL,
   image VARCHAR(250) NOT NULL,
   order_number int(2) DEFAULT NULL,
   `updated` datetime DEFAULT NULL,
   `updated_by` int(11) DEFAULT NULL,
   `created` datetime DEFAULT NULL,
   `created_by` int(11) DEFAULT NULL,
   PRIMARY KEY (`id`),
   CONSTRAINT `FK_destination_images__region_id__id`
       FOREIGN KEY (`destination_id`)
           REFERENCES `areas_translations` (`area_subgroup_id`)
           ON DELETE NO ACTION
           ON UPDATE NO ACTION
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
CREATE TABLE `product_concierge_types` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `name` varchar(255) DEFAULT NULL,
                                           `updated` datetime DEFAULT NULL,
                                           `updated_by` int(11) DEFAULT NULL,
                                           `created` datetime DEFAULT NULL,
                                           `created_by` int(11) DEFAULT NULL,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `product_concierge_types_un` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;


-- villas_guide.product_concierges definition

CREATE TABLE `product_concierges` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `product_id` int(11) NOT NULL,
                                      `product_concierge_type_id` int(11) NOT NULL,
                                      `updated` datetime DEFAULT NULL,
                                      `updated_by` int(11) DEFAULT NULL,
                                      `created` datetime DEFAULT NULL,
                                      `created_by` int(11) DEFAULT NULL,
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `product_concierges_un` (`product_id`,`product_concierge_type_id`),
                                      KEY `FK_product_concierges_productId_idx` (`product_id`),
                                      KEY `FK_product_concierges_productConciergeTypeId_idx` (`product_concierge_type_id`),
                                      CONSTRAINT `FK_product_concierges_productConciergeTypeId` FOREIGN KEY (`product_concierge_type_id`) REFERENCES `product_concierge_types` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
                                      CONSTRAINT `FK_product_concierges_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;


-- villas_guide.pre_reservation_concierges definition

CREATE TABLE `pre_reservation_concierges` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `pre_reservation_id` int(11) NOT NULL,
                                              `product_concierge_type_id` int(11) NOT NULL,
                                              `updated` datetime DEFAULT NULL,
                                              `updated_by` int(11) DEFAULT NULL,
                                              `created` datetime DEFAULT NULL,
                                              `created_by` int(11) DEFAULT NULL,
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `product_concierges_un` (`pre_reservation_id`,`product_concierge_type_id`),
                                              KEY `FK_reservation_concierges_pre_reservation_idx` (`pre_reservation_id`),
                                              KEY `FK_reservation_concierges_productConciergeTypeId_idx` (`product_concierge_type_id`),
                                              CONSTRAINT `FK_reservation_concierges_pre_reservation_id` FOREIGN KEY (`pre_reservation_id`) REFERENCES `pre_reservations` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
                                              CONSTRAINT `FK_reservation_concierges_productConciergeTypeId` FOREIGN KEY (`product_concierge_type_id`) REFERENCES `product_concierge_types` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;


INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Privatni kuhar / catering', NULL, NULL, '2023-02-09 13:47:20', 1);
INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Održavanje vile', NULL, NULL, '2023-02-09 13:47:20', 1);
INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Prijevoz', NULL, NULL, '2023-02-09 13:47:20', 1);
INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Masaža', NULL, NULL, '2023-02-09 15:21:58', 1);
INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Najam bicikla', NULL, NULL, '2023-02-09 15:22:12', 1);
INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Najam čamca', NULL, NULL, '2023-02-09 15:22:12', 1);
INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Kućna dostava', NULL, NULL, '2023-02-10 13:28:57', 1);
INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Joga', NULL, NULL, '2023-02-10 13:30:44', 1);
INSERT INTO product_concierge_types
(name, updated, updated_by, created, created_by)
VALUES('Tečaj plivanja', NULL, NULL, '2023-02-10 13:30:44', 1);


ALTER TABLE product_concierges ADD not_included_in_reservation_price TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_concierges CHANGE not_included_in_reservation_price not_included_in_reservation_price TINYINT(1) DEFAULT null NULL AFTER product_concierge_type_id;
ALTER TABLE product_concierges ADD price int(6) DEFAULT null NULL;
ALTER TABLE product_concierges CHANGE price price int(6) DEFAULT null NULL AFTER not_included_in_reservation_price;
ALTER TABLE product_concierges ADD price_on_request TINYINT(1) DEFAULT null NULL;
ALTER TABLE product_concierges CHANGE price_on_request price_on_request TINYINT(1) DEFAULT null NULL AFTER price;

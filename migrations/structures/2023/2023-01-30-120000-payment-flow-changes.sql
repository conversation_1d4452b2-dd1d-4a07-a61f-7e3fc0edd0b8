ALTER TABLE `reservations`
    ADD COLUMN `partner_first_installment_payment_status` ENUM('paid', 'waiting_for_payment') NULL DEFAULT NULL AFTER `payment_second_installment_amount`;

ALTER TABLE `reservations`
    ADD COLUMN `partner_second_installment_payment_status` ENUM('paid', 'waiting_for_payment') NULL DEFAULT NULL AFTER `partner_first_installment_payment_status`;

ALTER TABLE `reservations`
    ADD COLUMN `partner_first_installment_paid_date` DATETIME NULL DEFAULT NULL AFTER `partner_second_installment_payment_status`;

ALTER TABLE `reservations`
    ADD COLUMN `partner_second_installment_paid_date` DATETIME NULL DEFAULT NULL AFTER `partner_first_installment_paid_date`;

ALTER TABLE `reservations`
    ADD COLUMN `partner_first_installment_payment_due` DATETIME NULL DEFAULT NULL AFTER `partner_second_installment_payment_status`;

ALTER TABLE `reservations`
    ADD COLUMN `partner_second_installment_payment_due` DATETIME NULL DEFAULT NULL AFTER `partner_first_installment_paid_date`;

ALTER TABLE `products`
    ADD COLUMN `private_partner_name` VARCHAR(200) NULL DEFAULT NULL AFTER `partner`;


UPDATE products
SET private_partner_name = 'Mon Perin'
WHERE name like '%porto bus%';
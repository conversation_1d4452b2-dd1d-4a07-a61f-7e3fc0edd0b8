CREATE TABLE `distribution_channels` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `name` varchar(100) DEFAULT NULL,
                                         `updated` datetime DEFAULT NULL,
                                         `updated_by` int(11) DEFAULT NULL,
                                         `created` datetime DEFAULT NULL,
                                         `created_by` int(11) DEFAULT NULL,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;

CREATE TABLE `product_distribution_channels` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `distribution_channel_id` int(11) NOT NULL,
                                                 `product_id` int(11) NOT NULL,
                                                 `percentage` decimal(6,2) NOT NULL,
                                                 `updated` datetime DEFAULT NULL,
                                                 `updated_by` int(11) DEFAULT NULL,
                                                 `created` datetime DEFAULT NULL,
                                                 `created_by` int(11) DEFAULT NULL,
                                                 PRIMARY KEY (`id`),
                                                 <PERSON><PERSON><PERSON> `FK_product_distribution_channel_id_idx` (`distribution_channel_id`),
                                                 KEY `FK_product_distribution_channels_productId_idx` (`product_id`),
                                                 CONSTRAINT `FK_product_distribution_channels_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO action,
                                                 CONSTRAINT `FK_product_distribution_channel_id_` FOREIGN KEY (`distribution_channel_id`) REFERENCES `distribution_channels` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;

ALTER TABLE reservations ADD product_distribution_channel_id int(11) DEFAULT NULL NULL;
ALTER TABLE reservations CHANGE product_distribution_channel_id product_distribution_channel_id int(11) DEFAULT NULL NULL AFTER ga_confirmation_tracked;

INSERT INTO distribution_channels
(name, updated, updated_by, created, created_by)
VALUES('Airbnb', NULL, NULL, NULL, NULL);
INSERT INTO distribution_channels
(name, updated, updated_by, created, created_by)
VALUES('Booking.com', NULL, NULL, NULL, NULL);
INSERT INTO distribution_channels
(name, updated, updated_by, created, created_by)
VALUES('Hometogo', NULL, NULL, NULL, NULL);
INSERT INTO distribution_channels
(name, updated, updated_by, created, created_by)
VALUES('Holidu', NULL, NULL, NULL, NULL);
INSERT INTO distribution_channels
(name, updated, updated_by, created, created_by)
VALUES('Ferienhausmiete', NULL, NULL, NULL, NULL);
INSERT INTO distribution_channels
(name, updated, updated_by, created, created_by)
VALUES('Traum-Ferienwohnugen', NULL, NULL, NULL, NULL);

ALTER TABLE product_concierge_types ADD `is_default` TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE product_concierge_types CHANGE `is_default` `is_default` TINYINT(1) DEFAULT 0 NOT NULL AFTER name;

CREATE TABLE `product_concierge_assigned_types` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `product_id` int(11) NOT NULL,
                                                    `product_concierge_type_id` int(11) NOT NULL,
                                                    `updated` datetime DEFAULT NULL,
                                                    `updated_by` int(11) DEFAULT NULL,
                                                    `created` datetime DEFAULT NULL,
                                                    `created_by` int(11) DEFAULT NULL,
                                                    PRIMARY KEY (`id`),
                                                    UNIQUE KEY `product_concierge_assigned_types_un` (`product_id`,`product_concierge_type_id`),
                                                    KEY `FK_product_concierge_assigned_types_productId_idx` (`product_id`),
                                                    KEY `FK_product_concierge_assigned_types_productConciergeTypeId_idx` (`product_concierge_type_id`),
                                                    CONSTRAINT `FK_product_concierge_assigned_types_productConciergeTypeId` FOREIGN KEY (`product_concierge_type_id`) REFERENCES `product_concierge_types` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
                                                    CONSTRAINT `FK_product_concierge_assigned_types_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;
-- villas_guide.product_log definition

CREATE TABLE `product_log` (
                               `id` int(11) NOT NULL AUTO_INCREMENT,
                               `product_id` int(11) NOT NULL,
                               `step_1` text DEFAULT NULL,
                               `step_1_changes` text DEFAULT NULL,
                               `step_1_approved` tinyint(1) NOT NULL DEFAULT 0,
                               `step_2` text DEFAULT NULL,
                               `step_2_changes` text DEFAULT NULL,
                               `step_2_approved` tinyint(1) NOT NULL DEFAULT 0,
                               `step_3` text DEFAULT NULL,
                               `step_3_changes` text DEFAULT NULL,
                               `step_3_approved` tinyint(1) NOT NULL DEFAULT 0,
                               `step_4` text DEFAULT NULL,
                               `step_4_changes` text DEFAULT NULL,
                               `step_4_approved` tinyint(1) NOT NULL DEFAULT 0,
                               `step_5` text DEFAULT NULL,
                               `step_5_changes` text DEFAULT NULL,
                               `step_5_approved` tinyint(1) NOT NULL DEFAULT 0,
                               `step_6` text DEFAULT NULL,
                               `step_6_changes` text DEFAULT NULL,
                               `step_6_approved` tinyint(1) NOT NULL DEFAULT 0,
                               `step_7` text DEFAULT NULL,
                               `step_7_changes` text DEFAULT NULL,
                               `step_7_approved` tinyint(1) NOT NULL DEFAULT 0,
                               `step_8` text DEFAULT NULL,
                               `step_8_changes` text DEFAULT NULL,
                               `step_8_approved` tinyint(1) NOT NULL DEFAULT 0,
                               `updated` datetime DEFAULT NULL,
                               `updated_by` int(11) DEFAULT NULL,
                               `created` datetime DEFAULT NULL,
                               `created_by` int(11) DEFAULT NULL,
                               PRIMARY KEY (`id`),
                               KEY `FK_product_log_productId_idx` (`product_id`),
                               CONSTRAINT `FK_product_log_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;
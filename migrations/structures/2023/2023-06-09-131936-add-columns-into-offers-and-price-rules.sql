ALTER TABLE price_rules ADD price decimal(6,2) DEFAULT null NULL;
ALTER TABLE price_rules CHANGE price price decimal(6,2) DEFAULT null NULL AFTER max_days_to_arrival;
ALTER TABLE price_rules ADD is_custom_rule TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE price_rules CHANGE is_custom_rule is_custom_rule TINYINT(1) DEFAULT 0 NOT NULL AFTER price;
ALTER TABLE price_rules ADD price_increase TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE price_rules CHANGE price_increase price_increase TINYINT(1) DEFAULT 0 NOT NULL AFTER is_custom_rule;
ALTER TABLE price_rules ADD price_decrease TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE price_rules CHANGE price_decrease price_decrease TINYINT(1) DEFAULT 0 NOT NULL AFTER price_increase;


ALTER TABLE offers ADD `price_increase` tinyint(1) NOT NULL DEFAULT 0;
ALTER TABLE offers ADD `price_decrease` tinyint(1) NOT NULL DEFAULT 1;
ALTER TABLE offers ADD `updated` datetime DEFAULT NULL;
ALTER TABLE offers ADD `updated_by` int(11) DEFAULT NULL;
ALTER TABLE offers ADD `created` datetime DEFAULT NULL;
ALTER TABLE offers ADD `created_by` int(11) DEFAULT NULL;
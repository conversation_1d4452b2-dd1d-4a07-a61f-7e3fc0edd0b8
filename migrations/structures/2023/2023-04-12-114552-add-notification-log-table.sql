CREATE TABLE `notification_log` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `product_id` int(11) DEFAULT NULL,
                                    `email` varchar(255) DEFAULT NULL,
                                    `updated` datetime DEFAULT NULL,
                                    `updated_by` int(11) DEFAULT NULL,
                                    `created` datetime DEFAULT NULL,
                                    `created_by` int(11) DEFAULT NULL,
                                    PRIMARY KEY (`id`),
                                    KEY `FK_notification_log_productId_idx` (`product_id`),
                                    CONSTRAINT `FK_notification_log_productId` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
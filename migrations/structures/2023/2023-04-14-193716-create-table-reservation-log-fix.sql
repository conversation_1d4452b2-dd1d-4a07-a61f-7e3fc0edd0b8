drop table reservation_log;

CREATE TABLE `reservation_log` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `reservation_id` int(11) NOT NULL,
                                   `contact_email` varchar(90) DEFAULT NULL,
                                   `contact_lastname` varchar(60) DEFAULT NULL,
                                   `contact_firstname` varchar(60) DEFAULT NULL,
                                   `arrive` datetime DEFAULT NULL,
                                   `departure` datetime DEFAULT NULL,
                                   `phone` varchar(20) DEFAULT NULL,
                                   `adults` int(2) DEFAULT NULL,
                                   `children` int(2) DEFAULT NULL,
                                   `pets` int(2) DEFAULT NULL,
                                   `updated` datetime DEFAULT NULL,
                                   `updated_by` int(11) DEFAULT NULL,
                                   `created` datetime DEFAULT NULL,
                                   `created_by` int(11) DEFAULT NULL,
                                   PRIMARY KEY (`id`),
                                   KEY `FK_reservation_log_reservationId_idx` (`reservation_id`),
                                   CONSTRAINT `FK_reservation_log_reservationId` FOREIGN KEY (`reservation_id`) REFERENCES `reservations` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
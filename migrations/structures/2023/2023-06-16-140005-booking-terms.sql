DROP table booking_cancellation_policy;

CREATE TABLE `booking_terms` (
       `id` int(11) NOT NULL AUTO_INCREMENT,
       `partner_id` int(11) NOT NULL,
       `language_id` varchar(6) DEFAULT NULL,
       `terms` text DEFAULT NULL,
       `cancellation_policy` text DEFAULT NULL,
       `updated` datetime DEFAULT NULL,
       `updated_by` int(11) DEFAULT NULL,
       `created` datetime DEFAULT NULL,
       `created_by` int(11) DEFAULT NULL,
       PRIMARY KEY (`id`),
       KEY `FK_terms_partnerId_idx` (`partner_id`),
       CONSTRAINT `FK_terms_partnerId` FOREIGN KEY (`partner_id`) REFERENCES `partners` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;


alter table booking_terms
    add constraint booking_terms_pk
        unique (partner_id, language_id);

ALTER TABLE translation_languages MODIFY COLUMN module enum('admin','front','dashboard') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL;

INSERT INTO translation_languages
(id, module, name, published, is_default, updated, updated_by, created, created_by)
VALUES(9, 'dashboard', 'Croatia', 1, 1, NULL, NULL, NULL, NULL);
INSERT INTO translation_languages
(id, module, name, published, is_default, updated, updated_by, created, created_by)
VALUES(10, 'dashboard', 'England', 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO translation_languages
(id, module, name, published, is_default, updated, updated_by, created, created_by)
VALUES(11, 'dashboard', 'Italy', 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO translation_languages
(id, module, name, published, is_default, updated, updated_by, created, created_by)
VALUES(12, 'dashboard', 'Germany', 1, 0, NULL, NULL, NULL, NULL);


INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
select 9, `key`, message, updated, updated_by, created, created_by from translation_messages tm where language_id = 5;

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
select 10, `key`, message, updated, updated_by, created, created_by from translation_messages tm where language_id = 6;

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
select 11, `key`, message, updated, updated_by, created, created_by from translation_messages tm where language_id = 7;

INSERT INTO translation_messages
(language_id, `key`, message, updated, updated_by, created, created_by)
select 12, `key`, message, updated, updated_by, created, created_by from translation_messages tm where language_id = 8;
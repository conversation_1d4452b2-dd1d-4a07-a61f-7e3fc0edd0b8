name: "build"

on:
  pull_request:
    paths-ignore:
      - "docker/**"
  push:
    branches:
      - "master"
  schedule:
    - cron: "0 8 * * 1" # At 08:00 on Monday

env:
  extensions: "json, pcov"
  cache-version: "2"
  composer-version: "v2"
  composer-install: "composer install"

jobs:
  qa:
    name: "Quality assurance"
    runs-on: "${{ matrix.operating-system }}"

    strategy:
      matrix:
        php-version: ["8.3"]
        operating-system: ["ubuntu-latest"]
      fail-fast: false

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Setup PHP cache environment"
        id: "extcache"
        uses: "shivammathur/cache-extensions@v1"
        with:
          php-version: "${{ matrix.php-version }}"
          extensions: "${{ env.extensions }}"
          key: "${{ env.cache-version }}"

      - name: "Cache PHP extensions"
        uses: "actions/cache@v3"
        with:
          path: "${{ steps.extcache.outputs.dir }}"
          key: "${{ steps.extcache.outputs.key }}"
          restore-keys: "${{ steps.extcache.outputs.key }}"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php-version }}"
          extensions: "${{ env.extensions }}"
          tools: "composer:${{ env.composer-version }} "

      - name: "Setup problem matchers for PHP"
        run: 'echo "::add-matcher::${{ runner.tool_cache }}/php.json"'

      - name: "Get Composer cache directory"
        id: "composercache"
        run: 'echo "::set-output name=dir::$(composer config cache-files-dir)"'

      - name: "Cache PHP dependencies"
        uses: "actions/cache@v3"
        with:
          path: "${{ steps.composercache.outputs.dir }}"
          key: "${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}"
          restore-keys: "${{ runner.os }}-composer-"

      - name: "Install dependencies"
        run: "${{ env.composer-install }}"

      - name: "Coding Standard"
        run: "composer qa"

  static-analysis:
    name: "Static analysis"
    runs-on: "${{ matrix.operating-system }}"

    strategy:
      matrix:
        php-version: ["8.3"]
        operating-system: ["ubuntu-latest"]
      fail-fast: false

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Setup PHP cache environment"
        id: "extcache"
        uses: "shivammathur/cache-extensions@v1"
        with:
          php-version: "${{ matrix.php-version }}"
          extensions: "${{ env.extensions }}"
          key: "${{ env.cache-version }}"

      - name: "Cache PHP extensions"
        uses: "actions/cache@v3"
        with:
          path: "${{ steps.extcache.outputs.dir }}"
          key: "${{ steps.extcache.outputs.key }}"
          restore-keys: "${{ steps.extcache.outputs.key }}"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php-version }}"
          extensions: "${{ env.extensions }}"
          tools: "composer:${{ env.composer-version }} "

      - name: "Setup problem matchers for PHP"
        run: 'echo "::add-matcher::${{ runner.tool_cache }}/php.json"'

      - name: "Get Composer cache directory"
        id: "composercache"
        run: 'echo "::set-output name=dir::$(composer config cache-files-dir)"'

      - name: "Cache PHP dependencies"
        uses: "actions/cache@v3"
        with:
          path: "${{ steps.composercache.outputs.dir }}"
          key: "${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}"
          restore-keys: "${{ runner.os }}-composer-"

      - name: "Install dependencies"
        run: "${{ env.composer-install }}"

      - name: "PHPStan"
        run: "vendor/bin/phpstan analyse -l 1 -c phpstan.neon app --memory-limit 3072M"

  tests:
    name: "Tests"
    runs-on: "${{ matrix.operating-system }}"

    strategy:
      matrix:
        php-version: ["8.3"]
        operating-system: ["ubuntu-latest"]
        composer-args: [ "" ]
      fail-fast: false

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Setup PHP cache environment"
        id: "extcache"
        uses: "shivammathur/cache-extensions@v1"
        with:
          php-version: "${{ matrix.php-version }}"
          extensions: "${{ env.extensions }}"
          key: "${{ env.cache-version }}"

      - name: "Cache PHP extensions"
        uses: "actions/cache@v3"
        with:
          path: "${{ steps.extcache.outputs.dir }}"
          key: "${{ steps.extcache.outputs.key }}"
          restore-keys: "${{ steps.extcache.outputs.key }}"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php-version }}"
          extensions: "${{ env.extensions }}"
          tools: "composer:${{ env.composer-version }} "

      - name: "Setup problem matchers for PHP"
        run: 'echo "::add-matcher::${{ runner.tool_cache }}/php.json"'

      - name: "Get Composer cache directory"
        id: "composercache"
        run: 'echo "::set-output name=dir::$(composer config cache-files-dir)"'

      - name: "Cache PHP dependencies"
        uses: "actions/cache@v3"
        with:
          path: "${{ steps.composercache.outputs.dir }}"
          key: "${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}"
          restore-keys: "${{ runner.os }}-composer-"

      - name: "Install dependencies"
        run: "${{ env.composer-install }} ${{ matrix.composer-args }}"

      - name: "Setup problem matchers for PHPUnit"
        run: 'echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"'

      - name: "Tests"
        run: "vendor/bin/phpunit --coverage-clover=coverage.xml &&  bash <(curl -s https://codecov.io/bash) -t 5839e825-5bdc-4f4a-bd58-058decead1c8"
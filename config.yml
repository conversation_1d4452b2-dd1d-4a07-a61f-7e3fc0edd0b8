# config.yml
# Linux Users: PHP Namespaces use back slash \ rather than a forward slash /
# So for destinations_php, the namespace would be TestNs\MyApp

xsd2php:
  namespaces:
    'http://www.novasol.com': 'App\xsd2class'
  destinations_php:
    'App\xsd2class': app/xsd2class/src
    #'TestNs\MyApp': soap/src  #  on Windows
  destinations_jms:
    'App\xsd2class': app/xsd2class/metadata
    #'TestNs\MyApp': soap/metadata  #  on Windows
  naming_strategy: long # optional and default
  path_generator: psr4 # optional and default



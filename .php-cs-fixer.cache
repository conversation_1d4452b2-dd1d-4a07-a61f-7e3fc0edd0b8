{"php": "7.4.26", "version": "3.6.0:v3.6.0#1975e4453eb2726d1f50da0ce7fa91295029a4fa", "indent": "    ", "lineEnding": "\n", "rules": {"blank_line_after_namespace": true, "braces": true, "class_definition": true, "constant_case": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"on_multiline": "ensure_fully_multiline"}, "no_break_comment": true, "no_closing_tag": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_spaces_inside_parenthesis": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": {"elements": ["property"]}, "single_import_per_statement": true, "single_line_after_imports": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "visibility_required": {"elements": ["method", "property"]}, "encoding": true, "full_opening_tag": true}, "hashes": []}
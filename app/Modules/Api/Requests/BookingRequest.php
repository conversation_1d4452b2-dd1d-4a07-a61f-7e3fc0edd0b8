<?php declare (strict_types = 1);

namespace App\Modules\Api\Requests;

use Wedo\Api\Attributes\Control;
use Wedo\Api\Attributes\RequiredRule;

class BookingRequest extends BaseRequest
{

	#[Control(Control::TEXT)]
	#[RequiredRule]
	public string $firstname;

	#[Control(Control::TEXT)]
	#[RequiredRule]
	public string $lastname;

	#[Control(Control::TEXT)]
	public ?string $phone;

	/**
	 * options: [en, de, it, nl, hr]
	 */
	#[Control(Control::TEXT)]
	#[RequiredRule]
	public string $language;

	/**
	 * additional security so price is not different when doing booking
	 */
	#[Control(Control::TEXT)]
	#[RequiredRule]
	public int $price_eur;

	#[Control(Control::TEXT)]
	#[RequiredRule]
	public float $amount;

	#[Control(Control::CHECKBOX)]
	public bool $two_installments;

	/**
	 * options: [cc, bt, mo]
	 */
	#[Control(Control::TEXT)]
	public ?string $payment_type_for_first_installment;

	/**
	 * options: [cc, bt, mo]
	 */
	#[Control(Control::TEXT)]
	public ?string $payment_type_for_second_installment;

	#[Control(Control::TEXT)]
	#[RequiredRule]
	public string $property_id;

	#[Control(Control::TEXT)]
	#[RequiredRule]
	public string $email;

	#[Control(Control::TEXT)]
	public ?string $distribution_channel_name;

	/**
	 * format Ymd (20200608)
	 */
	#[Control(Control::TEXT)]
	#[RequiredRule]
	public string $arrival;

	/**
	 * format Ymd (20200608)
	 */
	#[Control(Control::TEXT)]
	#[RequiredRule]
	public string $departure;

	/**
	 * format Ymd (20200608)
	 */
	#[Control(Control::TEXT)]
	public ?string $booked_date;

	/**
	 * format Ymd (20200608141618)
	 */
	#[Control(Control::TEXT)]
	public ?string $creation_date;

	#[Control(Control::INTEGER)]
	#[RequiredRule]
	public int $adults;

	#[Control(Control::INTEGER)]
	public int $children = 0;

	#[Control(Control::INTEGER)]
	public int $pets = 0;

	#[Control(Control::CHECKBOX)]
	public bool $test = false;

	#[Control(Control::TEXT)]
	public ?string $comments;

	#[Control(Control::TEXT)]
	public ?string $product_link;

	#[Control(Control::TEXT)]
	public ?string $reference;

}

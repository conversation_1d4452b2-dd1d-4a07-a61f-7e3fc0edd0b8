<?php declare (strict_types = 1);

namespace App\Modules\Api\Logic;

use App\Common\AvailabilityStatusChangedEvent;
use App\Common\Enums\AvailabilityStatusEnum;
use App\Common\Enums\PartnersEnum;
use App\Common\Enums\PaymentTypeEnum;
use App\Common\Enums\ProductAgencyProvisionEnum;
use App\Common\Enums\TwoLetterLanguageEnum;
use app\Common\Helpers\Strings;
use App\Common\Logic\ReservationValidatorLogic;
use App\Common\PaymentConfirmation\ConfirmationTypes;
use App\Common\ProductInfo\ProductPriceResolver;
use App\Common\Queue\Parameters\AvailabilityParams;
use App\Common\Queue\Parameters\PaymentConfirmationInvoiceParams;
use App\Common\Reservation\BookingConditionsFinder;
use App\Common\Reservation\Helpers\CustomProductReservationInfoHelper;
use App\Common\Reservation\Helpers\DistributionChannelHelper;
use App\Common\Reservation\PartnerPaymentDatesResolver;
use App\Models\Entities\custom\PriceParameterEntity;
use App\Models\Entities\custom\PricesEntity;
use App\Models\Entities\custom\ReservationEventEntity;
use App\Models\Entities\PartnerEntity;
use App\Models\Entities\PreReservationEntity;
use App\Models\Entities\ProductEntity;
use App\Models\Entities\ReservationEntity;
use App\Modules\Api\Requests\BookingRequest;
use App\Modules\Front\Dependencies\BookingCompleteEventDependencies;
use App\Repositories\PreReservationRepository;
use App\Repositories\ProductRepository;
use App\Repositories\ReservationsRepository;
use Dibi\DateTime;
use Psr\EventDispatcher\EventDispatcherInterface;
use Wedo\Api\Exceptions\ResponseException;

class BookingLogic extends BaseLogic
{

	private ReservationsRepository $repository;

	private ProductPriceResolver $priceResolver;

	private ProductRepository $productRepository;

	private PreReservationRepository $preReservationRepository;

	private CustomProductReservationInfoHelper $customProductReservationInfoHelper;

	private DistributionChannelHelper $distributionChannelHelper;

	private EventDispatcherInterface $dispatcher;

	private BookingCompleteEventDependencies $dependencies;

	private ReservationValidatorLogic $reservationValidatorLogic;

	private BookingConditionsFinder $bookingConditionsFinder;

	public function __construct(
		ReservationsRepository $repository,
		ProductPriceResolver $priceResolver,
		ProductRepository $productRepository,
		PreReservationRepository $preReservationRepository,
		CustomProductReservationInfoHelper $customProductReservationInfoHelper,
		DistributionChannelHelper $distributionChannelHelper,
		EventDispatcherInterface $dispatcher,
		BookingCompleteEventDependencies $dependencies,
		ReservationValidatorLogic $reservationValidatorLogic,
		BookingConditionsFinder $bookingConditionsFinder
	)
	{
		$this->repository = $repository;
		$this->priceResolver = $priceResolver;
		$this->productRepository = $productRepository;
		$this->preReservationRepository = $preReservationRepository;
		$this->customProductReservationInfoHelper = $customProductReservationInfoHelper;
		$this->distributionChannelHelper = $distributionChannelHelper;
		$this->dispatcher = $dispatcher;
		$this->dependencies = $dependencies;
		$this->reservationValidatorLogic = $reservationValidatorLogic;
		$this->bookingConditionsFinder = $bookingConditionsFinder;
	}

	public function create(BookingRequest $bookingRequest): int
	{
		$test = 'true';

		if (!$bookingRequest->test) {
			$test = 'false';
		}

		/** @var ProductEntity $productEntity */
		$productEntity = $this->productRepository->getBy([ProductEntity::ID => (int) $bookingRequest->property_id]);

		$arrival = DateTime::createFromFormat('Ymd', $bookingRequest->arrival)->setTime(0, 0, 0);
		$departure = DateTime::createFromFormat('Ymd', $bookingRequest->departure)->setTime(0, 0, 0);

		$priceParameterEntity = new PriceParameterEntity();
		$priceParameterEntity->productId = $productEntity->id;
		$priceParameterEntity->arrival = $arrival;
		$priceParameterEntity->departure = $departure;
		$priceParameterEntity->adultsCount = $bookingRequest->adults;
		$priceParameterEntity->childrenCount = $bookingRequest->children;
		$priceParameterEntity->test = $test;
		$priceParameterEntity->salesmarket = '280';
		$priceInfo = $this->priceResolver->resolve((int) $bookingRequest->property_id, $priceParameterEntity);

		if (empty($priceInfo->price)) {
			throw new ResponseException('Failed: Check availabilities for this period and peoples because is not possible to get price', 400);
		}

		$entity = new PreReservationEntity();
		$entity->product_id = $productEntity->id;
		$entity->request_from_guests = $bookingRequest->comments;

		$reservationCode = $this->preReservationRepository->insert($entity);
		$this->preReservationRepository->update($reservationCode, [PreReservationEntity::PARTNER_RESERVATION_CODE => $reservationCode]);
		$reservationEntity = $this->getReservationEntity($reservationCode, $productEntity, $bookingRequest, $priceInfo);
		$this->closeAvailabilities($reservationEntity);
		$dealId = $stageId = null;

		if (!$bookingRequest->test) {
			$contactVid = $this->dependencies->hubspot->getContactVidByEmail($reservationEntity->contact_email);

			if (empty($contactVid)) {
				$contactVid = $this->dependencies->bookingHelper
					->createHsContact($reservationEntity, $reservationEntity->contact_firstname, $reservationEntity->contact_lastname);
			} else {
				$this->dependencies->bookingHelper
					->updateHsContact($reservationEntity, $reservationEntity->contact_firstname, $reservationEntity->contact_lastname);
			}

			$hs = $this->createDeal($reservationEntity, $contactVid);
			$dealId = (int) $hs['dealId'];
			$stageId = $hs['stageId'];
		}

		$reservationEntity->first_installment_payment_status = $this->distributionChannelHelper->getStatusForFirstInstallment(
			$reservationEntity->product_distribution_channel_id,
			$dealId
		);
		$reservationEntity->second_installment_payment_status = $this->distributionChannelHelper->getStatusForSecondInstallment(
			$reservationEntity->payment_type_for_second_installment,
			$reservationEntity->product_distribution_channel_id,
			$dealId
		);

		$reservationEntity->deal_id = $dealId;
		$reservationEntity->source = 'Avantio';
		$reservationEntity->id = $this->repository->insert($reservationEntity);

		if (!$bookingRequest->test) {
			$params = new AvailabilityParams((int) $entity->product_id);
			$this->dependencies->availabilityProducer->publish($params);

			$reservationInfo = $this->dependencies->reservationInfo->getReservationInfo($dealId, $bookingRequest->test, $bookingRequest->email);
			$this->dependencies->hubspot->updateToClosedWonOrOptionInHubspot($dealId, $stageId, $reservationInfo, '#RES', '/' . $bookingRequest->reference);
			$this->sendEmails($reservationEntity, $bookingRequest);
			$this->createInvoice($reservationEntity);
		}

		return $reservationEntity->id;
	}

	private function getReservationEntity(
		int $reservationCode,
		ProductEntity $productEntity,
		BookingRequest $bookingRequest,
		PricesEntity $priceInfo
	): ReservationEntity
	{
		$productDistributionChannelId = $this->distributionChannelHelper->getDistributionChannelIdByName(
			$productEntity->id,
			$bookingRequest->distribution_channel_name
		);

		if (empty($productDistributionChannelId) && !empty($bookingRequest->distribution_channel_name)) {
			throw new ResponseException('Unknown distribution channel!', 400);
		}

		$entity = new ReservationEntity();
		$entity->product_id = $productEntity->id;
		$entity->product_distribution_channel_id = $productDistributionChannelId;
		$entity->reservation_code = (string) $reservationCode;
		$entity->amount = (float) $priceInfo->price;
		$entity->price_eur = $priceInfo->price;
		$entity->price_without_discount = $priceInfo->priceWithoutDiscount;
		$entity->contact_email = $bookingRequest->email;
		$entity->contact_lastname = $bookingRequest->lastname;
		$entity->contact_firstname = $bookingRequest->firstname;
		$entity->phone = $bookingRequest->phone;
		$entity->arrive = DateTime::createFromFormat('Ymd', $bookingRequest->arrival)->setTime(0, 0);
		$entity->departure = DateTime::createFromFormat('Ymd', $bookingRequest->departure)->setTime(0, 0);
		$entity->adults = $bookingRequest->adults;
		$entity->children = $bookingRequest->children;
		$entity->payment_type = $bookingRequest->payment_type_for_first_installment;
		$entity->payment_type_for_second_installment = $bookingRequest->payment_type_for_second_installment;
		$entity->two_installments = $bookingRequest->two_installments;
		$entity->pets = $bookingRequest->pets;
		$entity->contact_country_id = null;
		$entity->website_lang = $bookingRequest->language;
		$entity->booked_date = DateTime::createFromFormat('Ymd', $bookingRequest->booked_date)->setTime(0, 0);
		$entity->created = DateTime::createFromFormat('YmdHis', $bookingRequest->creation_date);
		$entity->booked_time = DateTime::createFromFormat('YmdHis', $bookingRequest->creation_date)->format('His');
		$partnerPaymentDueDates = $this->dependencies->partnerPaymentDates->getDates($entity);

		if (isset($partnerPaymentDueDates[PartnerPaymentDatesResolver::FIRST_INSTALLMENT_DUE])) {
			$entity->partner_first_installment_payment_due = $partnerPaymentDueDates[PartnerPaymentDatesResolver::FIRST_INSTALLMENT_DUE];
		}

		if (isset($partnerPaymentDueDates[PartnerPaymentDatesResolver::SECOND_INSTALLMENT_DUE])) {
			$entity->partner_second_installment_payment_due = $partnerPaymentDueDates[PartnerPaymentDatesResolver::SECOND_INSTALLMENT_DUE];
		}

		$entity->price_hrk = $entity->price_eur;
		$entity->price_currency = 'EUR';

		$insurancePrice = 0;
		$today = new DateTime();
		$today->setTime(0, 0);
		$paymentDateService = $this->bookingConditionsFinder->getPaymentDate($productEntity->id, $entity->arrive, $today);
		$entity->payment_first_installment_date = $paymentDateService->getPaymentDate();
		$entity->total_price_eur = $bookingRequest->amount;
		$entity->distribution_price = empty($entity->product_distribution_channel_id) ? null : $entity->total_price_eur - $insurancePrice;

		if (!$bookingRequest->two_installments) {
			$entity->payment_first_installment_amount = $entity->total_price_eur;
			$entity->payment_second_installment_amount = 0;
			$entity->payment_second_installment_date = null;
		} else {
			$paymentDateServiceSecondInstallment = $this->bookingConditionsFinder->getPaymentDate($productEntity->id, $entity->arrive, $today, null, true);
			$entity->payment_second_installment_date = $paymentDateServiceSecondInstallment->getPaymentDate();

			if ($this->distributionChannelHelper->isSetDistributionChannel('Airbnb', $entity->product_distribution_channel_id)) {
				$entity->payment_second_installment_date = $entity->payment_first_installment_date;
			}

			$entity->payment_first_installment_amount = $this->distributionChannelHelper->getFirstInstallmentAmount(
				$productEntity->id,
				null,
				$entity->distribution_price,
				$insurancePrice
			);
			$entity->payment_second_installment_amount = $this->distributionChannelHelper->getSecondInstallmentAmount(
				$productEntity->id,
				null,
				$entity->distribution_price
			);
		}

		$entity->test = $bookingRequest->test;

		return $entity;
	}

	protected function createDeal(ReservationEntity $entity, ?int $contactVid)
	{
		$pipelineId = $this->dependencies->hubspot->getPipelineId($entity->arrive, 'Villas - Online Deals');
		$stageName = 'CLOSE WON';
		$stageId = $this->dependencies->hubspot->getDealStageIdByName($stageName, $pipelineId);

		return $this->dependencies->bookingHelper->createCloseWonOrOptionDeal(
			$entity->product_id,
			$pipelineId,
			$stageId,
			$stageName,
			$entity->price_eur,
			$entity->contact_firstname,
			$entity->contact_lastname,
			$contactVid
		);
	}

	public function sendEmails(ReservationEntity $reservationEntity, BookingRequest $bookingRequest): void
	{
		$mailParams = $this->customProductReservationInfoHelper->setMailParametersWithReservationInfoEntity($reservationEntity);
		$mailParams['website_lang'] = $mailParams['language'] = $reservationEntity->website_lang;
		$mailParams['second_payment_type'] = $reservationEntity->payment_type_for_second_installment;
		$mailParams['credit_card'] = $reservationEntity->payment_type === PaymentTypeEnum::CREDIT_CARD;
		$mailParams['company_phone_number'] = $this->dependencies->companyPhoneNumber->get((string) $reservationEntity->website_lang);

		/** @var ?PartnerEntity $partner */
		$partner = $this->dependencies->partnersRepository->getBy([PartnerEntity::INTERNAL_NAME => PartnersEnum::PRIVATE]);
		$mailParams['partner_data'] = $partner;
		$mailParams['booking_terms'] = $this->dependencies->bookingConditionsFinder
			->getBookingConditions($partner->id, $reservationEntity->website_lang, $reservationEntity->product_id);
		$mailParams['productLink'] = $bookingRequest->product_link;
		$mailParams['distribution_channel_name'] = $bookingRequest->distribution_channel_name;

		$shouldSendToCustomer = !Strings::contains($reservationEntity->contact_email ?? '', 'villas-guide.com');
		$this->dependencies->bookingHelper->sendMail($reservationEntity->contact_email, $mailParams, $shouldSendToCustomer);

		$productOwner = $this->dependencies->bookingHelper->getReservationsRepository()->getProductOwner($reservationEntity->product_id);

		if ($productOwner !== null) {
			$params = [
				'id' => $reservationEntity->id,
				'language' => TwoLetterLanguageEnum::CROATIAN,
				'conciergesInPrice' => $mailParams['conciergesInPrice'] ?? [],
				'conciergesNotInPrice' => $mailParams['conciergesNotInPrice'] ?? [],
			];
			$emails = explode(',', $productOwner->reservation_notify_mail);
			$this->dependencies->ownerConfirmationMailFactory->send($emails, $params);
			$params['language'] = TwoLetterLanguageEnum::ENGLISH;
			$this->dependencies->ownerConfirmationMailFactory->send($emails, $params);
		}
	}

	private function createInvoice(ReservationEntity $reservationEntity): void
	{
		if ($reservationEntity->payment_type === PaymentTypeEnum::CREDIT_CARD) {
			$this->dependencies->invoice->calculateProvisionAndCreateInvoice($reservationEntity->id, ProductAgencyProvisionEnum::DISTRIBUTION);

			$paymentConfirmationParams = new PaymentConfirmationInvoiceParams();
			$paymentConfirmationParams->reservationId = $reservationEntity->id;
			$paymentConfirmationParams->language = $reservationEntity->website_lang;
			$confirmationType = $reservationEntity->two_installments ? ConfirmationTypes::ADVANCE : ConfirmationTypes::FULL;
			$paymentConfirmationParams->confirmationType = $confirmationType;
			$this->dependencies->paymentConfirmationInvoiceProducer->publish($paymentConfirmationParams);

			return;
		}

		$this->dependencies->invoice->resolveAgencyProvision($reservationEntity->id, ProductAgencyProvisionEnum::DISTRIBUTION);
	}

	public function validateRequest(BookingRequest $bookingRequest): void
	{
		/** @var ProductEntity $productEntity */
		$productEntity = $this->productRepository->getBy([ProductEntity::ID => (int) $bookingRequest->property_id]);

		if ($productEntity === null) {
			throw new ResponseException('Wrong product!', 400);
		}

		$productDistributionChannelId = $this->distributionChannelHelper->getDistributionChannelIdByName(
			$productEntity->id,
			$bookingRequest->distribution_channel_name
		);

		if (empty($productDistributionChannelId)) {
			throw new ResponseException('Unknown distribution channel!', 400);
		}

		$arrival = DateTime::createFromFormat('Ymd', $bookingRequest->arrival)->setTime(0, 0, 0);
		$departure = DateTime::createFromFormat('Ymd', $bookingRequest->departure)->setTime(0, 0, 0);

		$activeReservation = $this->reservationValidatorLogic->getActiveReservationForDates(
			$productEntity->id,
			$arrival->format('Y-m-d'),
			$departure->format('Y-m-d')
		);

		if ($activeReservation !== null) {
			throw new ResponseException('Reservation for this period exists in DB!', 400);
		}

		$this->openAvailabilities($productEntity->id, $arrival, $departure);

		$priceParameterEntity = new PriceParameterEntity();
		$priceParameterEntity->productId = $productEntity->id;
		$priceParameterEntity->arrival = $arrival;
		$priceParameterEntity->departure = $departure;
		$priceParameterEntity->adultsCount = $bookingRequest->adults;
		$priceParameterEntity->test = $bookingRequest->test;
		$priceParameterEntity->salesmarket = '280';
		$priceInfo = $this->priceResolver->resolve((int) $bookingRequest->property_id, $priceParameterEntity);

		if (empty($priceInfo->price)) {
			throw new ResponseException('There is not available dates!', 400);
		}
	}

	public function closeAvailabilities(ReservationEntity $reservationEntity): void
	{
		$reservationEventEntity = new ReservationEventEntity($reservationEntity->toArray());
		$reservationEventEntity->status = AvailabilityStatusEnum::X;
		$this->dispatcher->dispatch(new AvailabilityStatusChangedEvent($reservationEventEntity));
	}

	public function openAvailabilities(int $productId, \DateTimeImmutable $arrive, \DateTimeImmutable $departure): void
	{
		$reservationRule = new ReservationEventEntity();
		$reservationRule->status = AvailabilityStatusEnum::A;
		$reservationRule->product_id = $productId;
		$reservationRule->arrive = $arrive;
		$reservationRule->departure = $departure;
		$this->dispatcher->dispatch(new AvailabilityStatusChangedEvent($reservationRule));
	}

}

<?php declare(strict_types = 1);

namespace App\Modules\Front\Presenters;

use App\Common\Company\CompanyAddressData;
use App\Common\Enums\Front\SingleVillaPage\PriceIncludeColumnsEnum;
use App\Common\Enums\PartnersEnum;
use App\Common\ProductInfo\ProductOwnerServices;
use App\Common\ProductInfo\ProductPriceIncludedFeaturesResolver;
use App\Common\Reservation\ReservationCode;
use App\Common\SingleVillaHelper;
use App\Models\Entities\ProductBasicInfoEntity;
use App\Models\Entities\ProductEntity;
use App\Models\Entities\ReservationEntity;
use App\Models\Entities\TransactionEntity;
use App\Modules\Front\Controls\CancellationPolicyControl;
use App\Modules\Front\Factories\CancellationPolicyControlFactory;
use App\Modules\Front\Logic\BookingConfirmationLogic;
use App\Modules\Front\Logic\ReservationDealLogic;
use App\Repositories\ProductRepository;
use App\Repositories\ReservationsRepository;
use App\Repositories\TransactionsRepository;

class BookingConfirmationPresenter extends BasePresenter
{

	private SingleVillaHelper $singleVillaHelper;

	private ReservationsRepository $reservationsRepository;

	private BookingConfirmationLogic $bookingLogic;

	private ProductRepository $productRepository;

	private CancellationPolicyControlFactory $cancellationPolicyControlFactory;

	protected ProductBasicInfoEntity $product;

	private ProductPriceIncludedFeaturesResolver $priceIncludedFeaturesResolver;

	private TransactionsRepository $transactionsRepository;

	protected int $arrivalYear;

	private ProductOwnerServices $productOwnerServices;

	private ReservationDealLogic $reservationDealLogic;

	private CompanyAddressData $companyAddressData;

	private ReservationCode $reservationCode;

	public function __construct(
		SingleVillaHelper $singleVillaHelper,
		ReservationsRepository $reservationsRepository,
		BookingConfirmationLogic $booking,
		ProductRepository $productRepository,
		CancellationPolicyControlFactory $cancellationPolicyControlFactory,
		ProductPriceIncludedFeaturesResolver $priceIncludedFeaturesResolver,
		TransactionsRepository $transactionsRepository,
		ProductOwnerServices $productOwnerServices,
		ReservationDealLogic $reservationDealLogic,
		CompanyAddressData $companyAddressData,
		ReservationCode $reservationCode
	)
	{
		$this->singleVillaHelper = $singleVillaHelper;
		$this->reservationsRepository = $reservationsRepository;
		$this->bookingLogic = $booking;
		$this->productRepository = $productRepository;
		$this->cancellationPolicyControlFactory = $cancellationPolicyControlFactory;
		$this->priceIncludedFeaturesResolver = $priceIncludedFeaturesResolver;
		$this->transactionsRepository = $transactionsRepository;
		$this->productOwnerServices = $productOwnerServices;
		$this->reservationDealLogic = $reservationDealLogic;
		$this->companyAddressData = $companyAddressData;
		$this->reservationCode = $reservationCode;

		parent::__construct();
	}

	public function renderDefault(int $reservationId, int $productId, int $priceEur, ?int $countryId, ?array $values = NULL, ?string $language = NULL)
	{
		/** @var ProductEntity $product */
		$product = $this->productRepository->getBy([ProductEntity::ID => $productId]);

		$reservationCode = $this->reservationCode->get($reservationId);
		$reservation = $this->reservationsRepository->getBy([ReservationEntity::RESERVATION_CODE => (string) $reservationCode]);

		if (empty($reservation)) {
			$dealId = $this->reservationDealLogic->getDealId($productId);
			$test = $this->getTestParameter();

			if (empty($values)) {
				$values = $this->setContactValuesForFinishBooking();
			}
			$productLink = $this->link(':Front:SingleVilla:default', ['productId' => $productId]);
			$language = $this->getParameter('language');
			$values['price_eur'] = $priceEur;
			$values['custcountry_id'] = $countryId;

			$success = $this->bookingLogic->finishBookingProcess($reservationId, $productId, $productLink, $language, $test, $values, $dealId, $this->getParameters());

			if (!$success) {
				$this->redirect('somethingWentWrong', $productId);
			}
		}

		if (empty($language)) {
			$language = $this->getParameter('language');
		}
		$this->template->bookingMessageTemplate = TRUE;
		$this->template->bookingComplete = TRUE;
		$this->template->bookingTemplate = TRUE;
		$templateInfo = $this->singleVillaHelper->getSinglePageTemplateVariables($language, $productId, $this->translator->getLanguageId());
		//call getReservationCode again because it can be 0 if reservation is not in database yet
		$insuranceList = $this->bookingLogic->findReservationInsurancePolicies((string) $this->reservationCode->get($reservationId));
		$this->template->insuranceList = $insuranceList;
		$insurancePrice = 0;

		foreach ($insuranceList as $insuranceEntity) {
			$insurancePrice += $insuranceEntity->price;
		}
		$this->template->insurancePrice = $insurancePrice;

		foreach ($templateInfo as $key => $value) {
			$this->template->$key = $value;
		}

		$this->setBookingConfirmationVariables($reservationId, $product);
	}

	public function renderSomethingWentWrong(int $productId)
	{
		$this->template->bookingMessageTemplate = TRUE;
		$this->template->bookingTemplate = TRUE;
		$this->template->bookingComplete = FALSE;
		$templateInfo = $this->singleVillaHelper->getSinglePageTemplateVariables($this->getParameter('language'), $productId, $this->translator->getLanguageId());

		foreach ($templateInfo as $key => $value) {
			$this->template->$key = $value;
		}
		$this->setSomethingWentWrongTemplateVariables();
		$this->setView('default');
	}


	private function setContactValuesForFinishBooking(): array
	{
		$values = [];
		$values['firstname'] = $this->getParameter('CustomerFirstname');
		$values['lastname'] = $this->getParameter('CustomerSurname');
		$values['email'] = $this->getParameter('CustomerEmail');
		$values['phone'] = $this->getParameter('CustomerPhone');

		return $values;
	}


	private function setBookingConfirmationVariables(int $reservationCode, ProductEntity $product)
	{
		if ($product->partner !== PartnersEnum::NOVASOL) {
			$reservationCode = (int) $this->bookingLogic->getPreReservation($reservationCode)->partner_reservation_code;
		}

		/** @var ReservationEntity $reservation */
		$reservation = $this->reservationsRepository->getBy([ReservationEntity::RESERVATION_CODE => $reservationCode]);
		$this->template->gaTracked = $reservation->ga_confirmation_tracked;
		$reservation->ga_confirmation_tracked = true;
		$this->reservationsRepository->update($reservation->id, $reservation);

		$this->arrivalYear = (int) $reservation->arrive->format('Y');

		/** @var TransactionEntity $transaction */
		$transaction = $this->transactionsRepository->getBy([TransactionEntity::RESERVATION_ID => $reservation->id]);
		$this->template->cc_payment = !empty($transaction->id);
		$this->template->bookingId = $reservationCode;
		$this->template->email = (string) $reservation->contact_email;
		$this->template->contact_lastname = (string) $reservation->contact_lastname;
		$this->template->contact_firstname = (string) $reservation->contact_firstname;
		$this->template->adults = (int) $reservation->adults;
		$this->template->children = (int) $reservation->children;
		$this->template->pets = (int) $reservation->pets;
		$this->template->phone = $reservation->phone;
		$this->template->arrival = $reservation->arrive->format('d.m.Y');
		$this->template->departure = $reservation->departure->format('d.m.Y');
		$this->template->bookedDate = $reservation->booked_date;
		$this->template->price_eur = (int) $reservation->price_eur;
		$this->template->total_price_eur = (float) $reservation->total_price_eur;
		$this->template->installmentsDate = [];
		$this->template->installmentsDate[1] = $reservation->payment_first_installment_date->format('d.m.Y');
		$this->template->second_payment_type = $reservation->payment_type_for_second_installment;

		if ($reservation->two_installments && $reservation->payment_second_installment_date !== null) {
			$this->template->installmentsDate[2] = $reservation->payment_second_installment_date->format('d.m.Y');
		}

		$created = $reservation->created->setTime(0, 0);
		$paymentDateService = $this->singleVillaHelper->getBookingConditionsFinder()->getPaymentDate(
			$product->id,
			$reservation->arrive,
			$created
		);

		if ($paymentDateService->getImmediatelyPayment()) {
			$this->template->immediatelyPayment = TRUE;
		}

		/** @var ProductBasicInfoEntity $basicInfo */
		$basicInfo = $this->productRepository->getProductMainInfo($product->id);
		$this->template->partnerData = $this->companyAddressData->getPartnerData($product->partner);
		$this->product = $basicInfo;
		$ownerServices = $this->productOwnerServices->getExtraCostsServicesWithInsurances($product->id, (string) $reservationCode);
		$priceIncludeFeaturesFromProductSearch = $this->singleVillaHelper->getArrayWithKeysFromOtherArray($basicInfo->toArray(), PriceIncludeColumnsEnum::toArray());

		//todo -> move in resolver
		if (!$basicInfo->free_parking) {
			unset($priceIncludeFeaturesFromProductSearch[strtoupper(ProductBasicInfoEntity::PARKING)]);
		}

		$this->template->ownerServices = $ownerServices;
		$this->template->priceIncludeFeatures = $this->priceIncludedFeaturesResolver->resolve($priceIncludeFeaturesFromProductSearch, $ownerServices);
		$this->template->basicInfo = $basicInfo;
		$this->template->first_installment_price = $reservation->total_price_eur - $reservation->payment_second_installment_amount;
		$this->template->second_installment_price = $reservation->payment_second_installment_amount;
		$this->template->daysForFirstInstallment = $reservation->payment_first_installment_date->diff($created)->days;
		[$this->template->conciergesInPrice, $this->template->conciergesNotInPrice] = $this->singleVillaHelper->findProductConciergesWithPriceWrapper($product->id);

		if (!empty($reservation->product_distribution_channel_id)) {
			$this->template->distribution_price = $reservation->distribution_price;
		}
	}

	private function setSomethingWentWrongTemplateVariables(): void
	{
		$this->template->email = '';
		$this->template->contact_lastname = '';
		$this->template->contact_firstname = '';
		$this->template->arrival = '';
		$this->template->departure = '';
		$this->template->adults = '';
		$this->template->children = '';
		$this->template->pets = '';
		$this->template->price = '';
	}

	protected function createComponentCancellationPolicyControl(): CancellationPolicyControl
	{
		$partnerId = (int) $this->singleVillaHelper->getPartnerId($this->product->partner);
		$productId = $this->product->id;

		return $this->cancellationPolicyControlFactory
			->create($partnerId, $this->getParameter('language'), (int) $this->product->people_number, $productId);
	}

}

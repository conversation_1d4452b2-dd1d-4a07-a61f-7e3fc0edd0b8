<?php declare(strict_types = 1);

namespace App\Modules\Front\Presenters;

use App\Common\Enums\FaqContentTypeEnum;
use App\Common\Enums\PartnersEnum;
use App\Common\Enums\RoleEnum;
use App\Common\Enums\TrackingGoalsEnum;
use App\Common\ProductInfo\ProductOffer;
use App\Common\SingleVillaHelper;
use App\Modules\CallCenter\Factories\ICalculatePriceFormFactory;
use App\Modules\CallCenter\Factories\IReviewControlFactory;
use App\Modules\CallCenter\Factories\ISingleVillaBathroomControlFactory;
use App\Modules\CallCenter\Factories\ISingleVillaBedroomControlFactory;
use App\Modules\CallCenter\Factories\ISingleVillaSurroundingControlFactory;
use App\Modules\Front\Controls\FaqControl;
use App\Modules\Front\Controls\PrivateVilla\PrivateVillaControl;
use App\Modules\Front\Controls\PrivateVilla\PrivateVillaMainContentControl;
use App\Modules\Front\Controls\ReviewControl;
use App\Modules\Front\Controls\SimilarVillas;
use App\Modules\Front\Controls\SingleVillaBathroomControl;
use App\Modules\Front\Controls\SingleVillaBedroomControl;
use App\Modules\Front\Controls\SingleVillaGeneralInfoControl;
use App\Modules\Front\Controls\SingleVillaSurroundingControl;
use App\Modules\Front\Factories\IFaqControlFactory;
use App\Modules\Front\Factories\IPrivateVillaControlFactory;
use App\Modules\Front\Factories\IPrivateVillaMainControlFactory;
use App\Modules\Front\Factories\ISendInquiryFormFactory;
use App\Modules\Front\Factories\ISingleVillaGeneralInfoControlFactory;
use App\Modules\Front\Forms\Booking\CalculatePriceForm;
use App\Modules\Front\Forms\SendInquiryForm;
use App\Repositories\ProductLocationRepository;
use Nette\Utils\Strings;

class SingleVillaPresenter extends BasePresenter
{

	public int $productId;

	public int $countryId;

	private SingleVillaHelper $singleVillaHelper;

	private ISendInquiryFormFactory $sendInquiryFormFactory;

	private ICalculatePriceFormFactory $calculatePriceFormFactory;

	private SimilarVillas $similarVillas;

	private ISingleVillaBathroomControlFactory $singleVillaBathroomControlFactory;

	private ISingleVillaSurroundingControlFactory $surroundingControlFactory;

	private ISingleVillaGeneralInfoControlFactory $generalInfoControlFactory;

	private ISingleVillaBedroomControlFactory $singleVillaBedroomControlFactory;

	private ProductLocationRepository $productLocationRepository;

	private array $countriesMenu;

	private IPrivateVillaControlFactory $privateVillaControlFactory;

	private IPrivateVillaMainControlFactory $privateVillaMainContentControl;

	private IFaqControlFactory $faqControlFactory;

	private ProductOffer $productOffer;

	private IReviewControlFactory $reviewControlFactory;

	public function __construct(
		array $countriesMenu,
		SingleVillaHelper $singleVillaHelper,
		ICalculatePriceFormFactory $calculatePriceFormFactory,
		ISendInquiryFormFactory $sendInquiryFormFactory,
		SimilarVillas $similarVillas,
		ISingleVillaBathroomControlFactory $singleVillaBathroomControlFactory,
		ISingleVillaSurroundingControlFactory $surroundingControlFactory,
		ISingleVillaGeneralInfoControlFactory $generalInfoControlFactory,
		ISingleVillaBedroomControlFactory $singleVillaBedroomControlFactory,
		ProductLocationRepository $productLocationRepository,
		IPrivateVillaControlFactory $privateVillaControlFactory,
		IPrivateVillaMainControlFactory $privateVillaMainContentControl,
		ProductOffer $productOffer,
		IFaqControlFactory $faqControlFactory,
		IReviewControlFactory $reviewControlFactory
	)
	{
		$this->singleVillaHelper = $singleVillaHelper;
		$this->sendInquiryFormFactory = $sendInquiryFormFactory;
		$this->calculatePriceFormFactory = $calculatePriceFormFactory;
		$this->similarVillas = $similarVillas;
		$this->singleVillaBathroomControlFactory = $singleVillaBathroomControlFactory;
		$this->surroundingControlFactory = $surroundingControlFactory;
		$this->generalInfoControlFactory = $generalInfoControlFactory;
		$this->singleVillaBedroomControlFactory = $singleVillaBedroomControlFactory;
		$this->productLocationRepository = $productLocationRepository;
		$this->countriesMenu = $countriesMenu;
		$this->privateVillaControlFactory = $privateVillaControlFactory;
		$this->privateVillaMainContentControl = $privateVillaMainContentControl;
		$this->productOffer = $productOffer;
		$this->faqControlFactory = $faqControlFactory;
		$this->reviewControlFactory = $reviewControlFactory;

		parent::__construct();
	}


	public function startup(): void
	{
		$this->productId = $this->getProductId();
		$this->countryId = $this->productLocationRepository
			->getProductLocationCountryId($this->productId, $this->countriesMenu['novasol_code']);

		//Redirect if villa is from any country that is not in main menu (Croatia, Italy)
		if ($this->countryId === 0 ||
			!$this->singleVillaHelper->isHavingBookingIstriaHome($this->productId) ||
			$this->singleVillaHelper->getPartnerById($this->productId) === PartnersEnum::NOVASOL
		) {
			$this->error('Content deleted', 410);
		}

		//test object (wspay testing)
		if ($this->productId === 20105) {
			if (!$this->user->loggedIn) {
				$this->redirect('Homepage:default');
			}
		}

		parent::startup();
	}


	public function renderDefault(
		$language,
		$productId,
		$firstname = NULL,
		$lastname = NULL,
		$email = NULL,
		$arrive = NULL,
		$departure = NULL,
		$price = NULL,
		$adults_count = NULL,
		$children_count = NULL,
		$pets = NULL,
		$offerId = NULL
	)
	{
		$houseInfo = $this->singleVillaHelper->getProductMainInfo($this->productId);

		if (!$houseInfo->is_published && !$this->getUser()->isInRole(RoleEnum::ADMIN)) {
			$this->error('Content not found');
		}

		if (!isset($this->template->bookingMessageTemplate)) {
			$this->template->bookingMessageTemplate = FALSE;
		}

		if (!isset($this->template->bookingTemplate)) {
			$this->template->bookingTemplate = FALSE;
		}
		$this->setView('default');

		$this->template->numberOfGuests = !empty($adults_count) ? (int) $adults_count + (int) $children_count : 2;
		$templateInfo = $this->singleVillaHelper->getSinglePageTemplateVariables($language, $this->productId, $this->translator->getLanguageId());

		foreach ($templateInfo as $key => $value) {
			$this->template->$key = $value;
		}

		$houseInfo = $this->singleVillaHelper->getMainInfo();
		$arrive = $this->getParameter('arrive');
		$departure = $this->getParameter('departure');
		$this->template->price = $this->template->priceWithDiscount = 0;

		if (!empty($arrive)) {
			$arrive = \DateTime::createFromFormat('d.m.Y', $arrive)->setTime(0, 0, 0);
		}

		if (!empty($departure)) {
			$departure = \DateTime::createFromFormat('d.m.Y', $departure)->setTime(0, 0, 0);
		}

		if ($arrive && $departure) {
			$productPriceParameters = $this->singleVillaHelper->getProductPriceResolver()
				->setParameters($this->productId, $arrive, $departure, (int) $adults_count, (int) $children_count, $this->getTestParameter());
			$prices = $this->singleVillaHelper->getProductPriceResolver()->resolve($this->productId, $productPriceParameters);
			$this->template->priceWithDiscount = $prices->price ?? 0;
			$this->template->price = $prices->priceWithoutDiscount ?? 0;
		}

		$this->template->pets = $pets !== null ? (int) $pets : 0;
		$this->template->title = !empty($templateInfo['h1_text']) ?
			$templateInfo['h1_text'] : $this->singleVillaHelper->getTitle($templateInfo['basicInfo'], $this->translator, $templateInfo['productName'], $language);
		$this->setMetaTags($this->template->title, $templateInfo['text_heading'], $templateInfo['firstPicture']);
		[$this->template->conciergesInPrice, $this->template->conciergesNotInPrice] = $this->singleVillaHelper->findProductConciergesWithPriceWrapper($this->productId);
		$this->template->offers = $this->productOffer->getOfferDates($this->productId);
		$activeOffers = $this->productOffer->findActiveOffers($this->productId);
		$this->template->offersByRange = $activeOffers;
		$this->template->priceDiscountPercentage = $this->productOffer->getMaxDiscount($activeOffers);
		$this->redrawContent();

		$this->getComponent('similarVillasControl')
			->prepare(
				$this->productId,
				$arrive,
				$departure,
				$houseInfo->people_number,
				$houseInfo->allowed_pet_count,
				$houseInfo->area_name
			);
	}

	private function getProductId(): int
	{
		if (!empty($this->getParameter('productId'))) {
			return (int) $this->getParameter('productId');
		}

		if (!empty($this->productId)) {
			return (int) $this->productId;
		}

		$path = $this->getHttpRequest()->getUrl()->path;
		$pathParts = explode('/', $path);
		$string = Strings::after(end($pathParts), '-', -1);
		$productId = explode('?', $string)[0];

		return (int) $productId;
	}


	public function actionGetPrice(int $productId, $arrive, $departure, $adults, $children)
	{
		if (empty($arrive) || empty($departure)) {
			return;
		}

		$arriveDate = \DateTime::createFromFormat('d.m.Y', $arrive)->setTime(0, 0);
		$departureDate = \DateTime::createFromFormat('d.m.Y', $departure)->setTime(0, 0);

		$productPriceParameters = $this->singleVillaHelper->getProductPriceResolver()
			->setParameters($productId, $arriveDate, $departureDate, (int) $adults, (int) $children, $this->getTestParameter());
		$prices = $this->singleVillaHelper->getProductPriceResolver()->resolve($productId, $productPriceParameters);
		$this->payload->price = $prices->priceWithoutDiscount;
		$this->payload->priceWithDiscount = $prices->price;

		$this->setView('default');
	}


	public function handleBackToDetails()
	{
		$this->template->bookingTemplate = FALSE;
		$this->redrawControl('contentArea');
		$this->redrawControl('js');
	}

	protected function createComponentReviewControl(): ReviewControl
	{
		$control = $this->reviewControlFactory->create([$this->getProductId()]);
		$this->template->reviewItem = $control->getReviewItem($this->getProductId());

		return $control;
	}

	protected function createComponentCalculatePriceForm(): CalculatePriceForm
	{
		$form = $this->calculatePriceFormFactory->create($this->getProductId(), $this->getTestParameter());

		$form->onRedirect[] = function () {
			$params = $this->getParameters();

			if (!empty($params['arrive']) && preg_match('/^\d{4}-\d{2}-\d{2}$/', $params['arrive'])) {
				$dateTime = \DateTime::createFromFormat('Y-m-d', $params['arrive']);

				if ($dateTime) {
					$params['arrive'] = $dateTime->format('d.m.Y');
				}
			}

			if (!empty($params['departure']) && preg_match('/^\d{4}-\d{2}-\d{2}$/', $params['departure'])) {
				$dateTime = \DateTime::createFromFormat('Y-m-d', $params['departure']);

				if ($dateTime) {
					$params['departure'] = $dateTime->format('d.m.Y');
				}
			}

			unset($params['do']);
			$this->payload->forceRedirect = true;
			$this->redirect('Booking:default', $params);
		};

		return $form;
	}


	protected function createComponentSendInquiryForm(): SendInquiryForm
	{
		$form = $this->sendInquiryFormFactory->create($this->hubspotContactData, $this->productId);
		$form->onFormSuccess[] = function () {
			$this->session->getSection('hubspotContactData')['req_send'] = FALSE;
			$this->template->trackingGoal = TRUE;
			$this->template->goal = TrackingGoalsEnum::SINGLE_VILLA_INQUIRY;
			$this->redrawControl('trackingGoal');
		};
		$form->onSend[] = function () {
			$this->template->singleVillaInquirySuccess = TRUE;
			$this->redrawControl('wrapper');
			$this->redrawControl('singlevilla-inquiry-form-response');
			$this->redrawControl('thankYou');
		};

		return $form;
	}


	protected function createComponentSimilarVillasControl(): SimilarVillas
	{
		return $this->similarVillas;
	}

	protected function createComponentBathroomControl(): SingleVillaBathroomControl
	{
		return $this->singleVillaBathroomControlFactory->create($this->productId);
	}


	protected function createComponentSurroundingControl(): SingleVillaSurroundingControl
	{
		return $this->surroundingControlFactory->create($this->productId, $this->singleVillaHelper->getSurroundingByFeatures());
	}


	protected function createComponentGeneralInfoControl(): SingleVillaGeneralInfoControl
	{
		return $this->generalInfoControlFactory->create(
			$this->singleVillaHelper->getMainInfo(),
			$this->singleVillaHelper->getGeneralInfoByFeatures(),
			$this->singleVillaHelper->getPleaseNoticeTexts()
		);
	}

	protected function createComponentBedroomControl(): SingleVillaBedroomControl
	{
		return $this->singleVillaBedroomControlFactory->create($this->singleVillaHelper->getMainInfo());
	}

	protected function createComponentPrivateVillaControl(): PrivateVillaControl
	{
		return $this->privateVillaControlFactory->create($this->productId);
	}

	protected function createComponentPrivateVillaMainContentControl(): PrivateVillaMainContentControl
	{
		return $this->privateVillaMainContentControl->create($this->productId);
	}

	protected function createComponentFaqControl(): FaqControl
	{
		return $this->faqControlFactory->create(
			$this->productId,
			FaqContentTypeEnum::SINGLE_VILLA,
			$this->language
		);
	}

}

{layout '../@evisitorLayout.latte'}
{block content}
    <div class="evisitor-modal-overlay evisitor-modal-close-js"></div>
    <div class="evisitor-navbar">
        <div class="container">
            <div class="evisitor-navbar-inner">
                <h2 class="evisitor-navbar-headline">{_'evisitor.title'}</h2>
                {embeddedSvg 'assets/img/svg/e-visitor.svg', class => 'e-visitor-logo'}
            </div>
        </div>
    </div>
    <div class="container evisitor-container-wrapper">
        <div class="evisitor-spinner-wrapper">
            {embeddedSvg 'assets/img/svg/spinner.svg',
            class => 'evisitor-spinner',
            fill => '#121212',
            height => 60,
            width => 60}
        </div>
        <div class="evisitor-grid-wrapper">
            <div class="evisitor-guest-list">
                <div class="evisitor-guest-list-item" data-initialized="false">
                    <div class="evisitor-guest-list-img-wrapper">
                        {embeddedSvg 'assets/img/svg/user.svg',
                            class => 'adult-svg',
                            fill => '#121212',
                            height => 28,
                            width => 28}
                        {embeddedSvg 'assets/img/svg/kids.svg',
                            class => 'kids-svg',
                            fill => '#121212',
                            height => 36,
                            width => 36}
                        {embeddedSvg 'assets/img/svg/checkmark-green-white.svg',
                            class => 'evisitor-checkmark-svg',
                            height => 22,
                            width => 22}
                    </div>
                    <div>
                        <p class="evisitor-guest-list-item-title">
                            <span class="evisitor-guest-list-item-title-number">1.</span> {_'Guest'|firstUpper} <span class="evisitor-guest-list-item-title-number--mobile">1.</span>
                        </p>
                        <p class="evisitor-guest-list-item-subtitle" data-child="{_'minor person'|firstUpper}">{_'adult person'|firstUpper}</p>
                    </div>
                </div>
                <p class="evisitor-complete-info" style="display: none;">
                    {embeddedSvg 'assets/img/svg/check-circle.svg',
                        class => 'evisitor-complete-check-icon',
                        height => 18,
                        width => 18}
                    <span>{_'Registration complete'|firstUpper}</span>
                </p>
            </div>
            <div class="evisitor-thankyou" style="display: none;">
                <div class="evisitor-thankyou-icon-wrapper">
                    {embeddedSvg 'assets/img/svg/checklist.svg',
                        class => '',
                        height => 80,
                        width => 55}
                    {embeddedSvg 'assets/img/svg/check-green.svg',
                        class => 'evisitor-thankyou-icon-check',
                        height => 26,
                        width => 26}
                </div>
                <p class="evisitor-thankyou-headline">{_'evisitor.success.title'|firstUpper}</p>
                <p class="evisitor-thankyou-subheadline">{_'evisitor.success.message'|firstUpper}</p>
                <br>
                <p class="evisitor-thankyou-text-last">{_'evisitor.success.info'|firstUpper}</p>
            </div>
            <div class="evisitor-content">
                <div class="forms">
                    <div class="form-wrapper" data-initialized="false">
                        <p class="form-headline" data-child="{_'minor person'|firstUpper}"><span class="form-headline-num">1.</span> <span class="form-headline-label">{_'adult person'|firstUpper}</span></p>
                        <p class="form-subtitle"> {_'Enter the required information.'|firstUpper}</p>
                        <div class="evisitor-modal-headline evisitor-modal-trigger evisitor-modal-trigger-in-form">
                            {embeddedSvg 'assets/img/svg/info.svg',
                                class => 'evisitor-modal-headline-icon',
                                fill => '#444050;',
                                height => 17,
                                width => 17}
                            <span class="evisitor-modal-label">{_'Application procedure'|firstUpper}</span>
                        </div>
                        <p class="evisitor-label">{_'Gender'|firstUpper}</p>
                        <div class="gender-wrapper">
                            <input type="radio" id="man" name="gender" value="muški" checked />
                            <label for="man">{_'Man'|firstUpper}</label>

                            <input type="radio" id="woman" name="gender" value="ženski" />
                            <label for="woman">{_'Woman'|firstUpper}</label>
                        </div>
                        <div class="inputs-wrapper">
                            <div>
                                <label class="evisitor-label" for="name">{_'Name'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <input type="text" id="name" name="name" placeholder="{_'Name'|firstUpper}" autocomplete="false" class="form-control-visitor" />
                            </div>

                            <div>
                                <label class="evisitor-label" for="surname">{_'Lastname'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <input type="text" id="surname" name="surname" placeholder="{_'Lastname'|firstUpper}" class="form-control-visitor" />
                            </div>

                            <div>
                                <label class="evisitor-label" for="country_of_residence">{_'Country of residence'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <select  name="country_of_residence" id="country_of_residence" class="form-control-visitor" required>
                                    <option value="" disabled selected hidden>{_'Choose a country'|firstUpper}</option>
                                </select>
                                <span class="city-error-msg" style="display: none;">{_'First choose country of residence'|firstUpper}</span>
                            </div>

                            <div>
                                <label class="evisitor-label" for="city_of_residence">{_'City of residence'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <input readonly data-bs-toggle="tooltip" title="{_'First choose country of residence'|firstUpper}" type="text" id="city_of_residence" name="city_of_residence" data-placeholder="{_'Enter the city'|firstUpper}" placeholder="{_'First choose country of residence'|firstUpper}" class="form-control-visitor"/>
                                <div class="select2-wrapper-evisitor"></div>
                            </div>

                            <div>
                                <label class="evisitor-label" for="citizenship">{_'Citizenship'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <select  name="citizenship" id="citizenship" class="form-control-visitor" required>
                                    <option value="" disabled selected hidden>{_'Choose a country'|firstUpper}</option>
                                </select>
                            </div>

                            <div>
                                <label class="evisitor-label" for="country_of_birth">{_'Country of birth'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <select data-common="{_'Common countries'|firstUpper}" data-other="{_'Other countries'|firstUpper}"  name="country_of_birth" id="country_of_birth" class="form-control-visitor" required>
                                    <option value="" disabled selected hidden>{_'Choose a country'|firstUpper}</option>
                                </select>
                            </div>

                            <div>
                                <label class="evisitor-label" for="date_of_birth">{_'Date of birth'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <input id="date_of_birth" type="text" class="form-control-visitor" placeholder="{_'Date of birth'|firstUpper}"/>
                                <span class="date-error-msg" style="display: none;" data-child="{_'Children must be younger than 18 years'|firstUpper}">{_'Adult person must be older than 18 years'|firstUpper}</span>
                            </div>

                            <div>
                                <label class="evisitor-label" for="identity_document">{_'Identity document'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <select name="identity_document" id="identity_document" class="form-control-visitor" required>
                                    <option value="" disabled selected hidden>{_'Select a document'|firstUpper}</option>
                                </select>
                            </div>

                            <div>
                                <label class="evisitor-label" for="document_number">{_'Document number'|firstUpper}<span class="label-required-asterix"> *</span></label>
                                <input type="text" id="document_number" name="document_number" placeholder="{_'Enter the document number'|firstUpper}" class="form-control-visitor" />
                            </div>
                            <div class="tax-type-wrapper">
                                <p class="tax-type-label">{_'Choose one of the options offered.'|firstUpper}</p>
                                <div class="tax-type-item-wrapper">
                                    <input type="radio" id="companion" name="tax_type" value="8" />
                                    <label for="companion">{_'accompanist of a person with a physical disability of 70% or more'|firstUpper}</label>
                                </div>
                                <div class="tax-type-item-wrapper">
                                    <input type="radio" id="disabled" name="tax_type" value="7" />
                                    <label for="disabled">{_'people with physical disabilities 70% and above'|firstUpper}</label>
                                </div>
                                <div class="tax-type-item-wrapper">
                                    <input type="radio" id="nothing" name="tax_type" value="xx" checked />
                                    <label for="nothing">{_'none of the above'|firstUpper}</label>
                                </div>
                            </div>
                            <div class="evisitor-button-submit-wrapper">
                                <button class="evisitor-button-submit" data-last="{_'Finish entry'|firstUpper}">
                                    <span>{_'Save and enter the next person'|firstUpper}</span>
                                    {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
                                    class => '',
                                    fill => '#ffffff',
                                    height => 15,
                                    width => 25}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-details">
                    <div classs="evisitor-modal">
                        <div class="evisitor-modal-headline evisitor-modal-trigger">
                            {embeddedSvg 'assets/img/svg/info.svg',
                                class => 'evisitor-modal-headline-icon',
                                fill => '#444050;',
                                height => 18,
                                width => 18}
                            <span class="evisitor-modal-label">{_'Application procedure'|firstUpper}</span>
                        </div>
                        <ul class="evisitor-modal-list">
                            <li>
                                <strong>{_'evisitor.step1.title'|firstUpper}</strong><br>
                                <small>{_'evisitor.step1.info'}</small>
                            </li>
                            <li>
                                <strong>{_'evisitor.step2.title'|firstUpper}</strong><br>
                                <small>{_'evisitor.step2.info'}</small>
                            </li>
                            <li>
                                <strong>{_'evisitor.step3.title'|firstUpper}</strong><br>
                                <small>{_'evisitor.step3.info'}</small>
                            </li>
                        </ul>

                        <p class="evisitor-modal-help-label">{_'You need help?'|firstUpper}</p>
                        <a data-label="{_'Can you help me with E-visitor, my reservation number is'|firstUpper}" href="" target="_blank" class="evisitor-modal-help-cta">
                            {embeddedSvg 'assets/img/svg/whatsapp.svg',
                                class => '',
                                fill => '#000000',
                                height => 25,
                                width => 25}
                            <span class="evisitor-modal-help-cta-label">{_'Send us a message'|firstUpper}</span>
                        </a>
                        <button class="evisitor-modal-close-btn evisitor-modal-close-js">
                            {embeddedSvg 'assets/img/svg/close.svg',
                                class => '',
                                fill => '#444050;',
                                height => 18,
                                width => 18}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <select id="settlement_template" data-initialized="false" data-placeholder="{_'Choose a city'|firstUpper}" style="width:100%; display:none;">
    <!-- Options will be inserted dynamically -->
    </select>
{/block}

{block js}
    <script>
        let hash = {$hash};
        let errorMessage = {$error_message};

        {if $evisitor_data !== null}
            let evisitorData = {json_encode($evisitor_data)|noescape};
        {else}
            let evisitorData = null;
        {/if}
    </script>
{/block}

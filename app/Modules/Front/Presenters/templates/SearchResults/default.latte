{layout '../@layout.latte'}
{var $header_class = "header-container header-container--search-results fullwidth"}
{var $header_sticky = " not-sticky"}
{var $navigation_position = "header__right-side"}
{var $templateVariables = $presenter->getTemplate()->min_max_filter_values}

{block title}{_'Rezultati pretrage'} |{/block}
{block meta_description}{_'Rezultati pretrage meta_description'}{/block}

{block head}
    {control searchResultsControl-relNextPrev}
{/block}

{block content}
    <div class="fs-container">
        <div class="fs-inner-container content">
            <div class="fs-content">
                <section class="search search--sticky search-result-search-section--sticky">
                    {control searchResultsControl-filterFormControl}
                </section>
                <section class="listings-container listing-container--search-results background-white">
                    {snippet resultsExists}
                        {if $presenter->getTemplate()->resultCount !== 0}
                            <div class="row fs-switcher" n:snippet="resultCount">
                                <div class="hide-on-mobile">
                                  {control searchResultsControl-resultCountControl}
                                </div>
                                <div class="show-on-mobile">
                                  <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6">
                                    <div class="filtering-button-wrapper">
                                      <a
                                          href="#filtering"
                                          onclick="setStartEndValuesForCapacitySlider({$control['searchResultsControl']['filterFormControl']->min_people_number_start_left}, {$control['searchResultsControl']['filterFormControl']->max_people_number_start_right})"
                                          class="search__anchor-filter popup-with-zoom-anim button border filtering-button">
                                          {embeddedSvg 'assets/img/svg/filter.svg',
                                          class => 'search-listing-block__icon',
                                          fill => '#252525',
                                          height => 20,
                                          width => 20}
                                          <span class="search__anchor-filter-text">{_'Filters'}</span>
                                          <span class="search-notice"></span>
                                      </a>
                                      <a
                                        href="#"
                                        class="show-filters-map button border show-filters-map-button">
                                        {embeddedSvg 'assets/img/svg/fullmap-pin.svg',
                                        class => 'map-icon-show-map',
                                        fill => '#252525',
                                        height => 20,
                                        width => 20}
                                        {_'Show on map'}
                                      </a>
                                    </div>
                                  </div>
                                </div>
                                  <div class="show-on-mobile">
                                    {control searchResultsControl-resultCountControl}
                                  </div>
                                <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6">
                                  <div class="search-result-sort-filter">
                                    <div class="hide-on-mobile">
                                      <a
                                        href="#filtering"
                                        onclick="setStartEndValuesForCapacitySlider({$control['searchResultsControl']['filterFormControl']->min_people_number_start_left}, {$control['searchResultsControl']['filterFormControl']->max_people_number_start_right})"
                                        class="search__anchor-filter popup-with-zoom-anim button border filtering-button">
                                        {embeddedSvg 'assets/img/svg/filter.svg',
                                        class => 'search-listing-block__icon',
                                        fill => '#252525',
                                        height => 20,
                                        width => 20}
                                        <span class="search__anchor-filter-text">{_'Filters'}</span>
                                        <span class="search-notice"></span>
                                      </a>
                                    </div>
                                    <div class="sort-by">
                                        <div class="sort-by-select">
                                            <select data-placeholder="Default order" id="search-order" name="sort_type" aria-label="{_'Sort'}" class="chosen-select-no-single ajax" title="Default order" onchange="submitForm(this.value)">
                                                <option value="default">{_'standard_sort'}</option>
                                                <option value="DESC">{_'Descending by price'}</option>
                                                <option value="ASC">{_'Ascending by price'}</option>
                                                <option value="DESC_BY_REVIEW">{_'Descending by review'}</option>
                                                <option value="ASC_BY_REVIEW">{_'Ascending by review'}</option>
                                            </select>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        {/if}
                    {/snippet}

                    <div>
                        {snippet searchResultsControl}
                            {control searchResultsControl}
                        {/snippet}
                        {snippet paginatorControl}
                            {control searchResultsControl-paginatorControl}
                        {/snippet}
                        {control searchResultsControl-mapControl}
                    </div>
                    <div class="map-scroll scroll-stopper"></div>
                </section>
            </div>
        </div>

        <div class="fs-inner-container main-map map-fixed">
            <div id="map-container">
                <div id="map" data-map-zoom="12" data-map-scroll="true"></div>
                <div class="custom-infobox-container">
                    <div class="custom-infobox"></div>
                </div>
                <div class="map-action-header">
                  <span class="button border map-container__close-button">
                    {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
                        class => 'map-container__close-icon',
                        fill => '#252525',
                        height => 18,
                        width => 18}
                        {_'Back to list'}
                  </span>
                  <a
                    href="#filtering"
                    onclick="setStartEndValuesForCapacitySlider({$control['searchResultsControl']['filterFormControl']->min_people_number_start_left}, {$control['searchResultsControl']['filterFormControl']->max_people_number_start_right})"
                    class="search__anchor-filter popup-with-zoom-anim button border filtering-button">
                    {embeddedSvg 'assets/img/svg/filter.svg',
                    class => 'search-listing-block__icon',
                    fill => '#252525',
                    height => 20,
                    width => 20}
                    <span class="search__anchor-filter-text">{_'Filters'}</span>
                    <span class="search-notice"></span>
                  </a>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <div class="page-loader page-loader--filtering-results">
        <div>
            <div class="load-circle">{_'Loading'}...</div>
            {_'Filtering results'}
        </div>
    </div>
{/block}

{block js}
<script>
  var autocompleteLink = {plink Homepage:autoComplete},
    searchMessage = {_'Searching...'};
  let resultCount = {$resultCount};
  let label = document.querySelector('.panel-apply').dataset.availableLabel;
  label = label.replace(/(\d+)/, resultCount);

  if (resultCount <= 0) {
      label = document.querySelector('.panel-apply').dataset.unavailableLabel;
  }
  document.querySelector('.panel-apply').textContent = label;
</script>
{include '../js-bundle.latte', files => ['search-result'], is_static => false}
<script>
  function setStartEndValuesForCapacitySlider($start_left = null, $start_right = null) {
    var div = document.getElementById('check-capacity'),
      adults_count = document.getElementById('adults_num').value,
      children_count = document.getElementById('children_num').value;

    if (div !== 'undefined' && div !== null) {
      var data_start_from = div.getAttribute("data-start-left"),
        data_start_to = div.getAttribute("data-start-right"),
        people_num = parseInt(adults_count) + parseInt(children_count);

      if ((people_num > parseInt(data_start_from) || people_num < parseInt(data_start_from)) && people_num > 3) {
        var left = parseInt(people_num);

        $('.nouislider').each(function(index, item) {
          item.noUiSlider.destroy();
        });

        $('.nouislider').each(function(index, item) {
          noUiRangeSlider(item);
        });

        if ($start_left !== null && $start_right !== null) {
          div.setAttribute("data-start-left", $start_left);
          div.setAttribute("data-start-right", $start_right);
          document.getElementById("people_min_value").innerHTML = $start_left;
          document.getElementById("people_max_value").innerHTML = $start_right;
          document.getElementById("people_number_min").value = $start_left;
          document.getElementById("people_number_max").value = $start_right;
        } else {
          div.setAttribute("data-start-left", left);
          div.setAttribute("data-start-right", {$templateVariables['max_people_number']});
          document.getElementById("people_min_value").innerHTML = left;
          document.getElementById("people_max_value").innerHTML = {$templateVariables['max_people_number']};
          document.getElementById("people_number_min").value = left;
          document.getElementById("people_number_max").value = {$templateVariables['max_people_number']};
        }
      }
    }
  }
</script>

<script n:syntax="off">
var pagination_success = false;

  function onSetPageExtension() {
    naja.addEventListener('success', function (params) {
      keenCarousel();

      pagination_success = false;

      if (params.response.snippets.hasOwnProperty('snippet--searchResultsControl')) {
        pagination_success = true;
        showMainListingLoader(pagination_success);
      }

      showLoadMoreSpinner();
      showHideFiltersMobile();
      showHideFiltersMapMobile();
      showPetsNumber();
      lightGalleryInit();
      magnificPopupInit();
      positionSearchMapOnScroll();
      hide_show_pets_number_field();
      formSubmitLoader();
      setSelectedSortOption();
      showFilteringLoader();
      if ($('.reevoo-badge--inject-css').length > 0 ) {
        reevooInjectCSS();
      }
    }.bind(this));
    return this;
  }
  naja.registerExtension(onSetPageExtension);
</script>

{include '../suggestedItems.latte'}
{/block}

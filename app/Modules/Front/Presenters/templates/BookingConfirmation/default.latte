{layout '../@bookingLayout.latte'}

{block head}
<style>
.thank-you-container {
    background: #fff;
    border: 1px solid #cacaca;
    padding: 15px 0;
    border-radius: 4px;
}

.thank-you-container h4,
.bank-informations h4 {
    font-family: 'Brandon Grotesque',sans-serif;
    margin: 0 15px;
    padding-bottom: 6px;
    font-size: 1.44rem;
}

.bank-informations h4 {
    margin-left: 0;
    margin-right: 0;
}

.thank-you-container table {
    padding: 10px;
    width: 100%;
    line-height: 1.6;
}

.thank-you-container--fixed-height {
    min-height: 230px;
}

.thank-you-container table td {
    padding: 0 15px;
}

.thank-you-container table {
    margin-top: 10px;
}

.thank-you-container table td:first-child {
    width: 210px;
}

.thank-you-container table.payment-information-rates {
    margin-top: 0;
}

.thank-you-container table.payment-information-rates td:first-child {
    width: 360px;
}

.thank-you-container .payment-information strong,
.thank-you-container .payment-information-rates strong {
    color: #252525;
}

.thank-you-container .company-information {
    display: flex;
    padding: 0 15px;
    justify-content: space-around;
    align-items: center;
}

.thank-you-container .company-information p {
    margin-bottom: 0;
    line-height: 1.4;
    font-size: 1rem;
}

.thank-you-container .company-information h4 {
    margin-left: 0;
    border: 0;
    padding-bottom: 5px;
}

.bank-informations {
    background: #fff;
    padding: 15px;
    border: 1px solid #cacaca;
    border-radius: 4px;
}

.bank-informations .bank-informations-block {
    width: 50%;
    float: left;
    line-height: 1.6;
}

.bank-informations .cancellation-block {
    padding-left: 15px;
    border-top: 0;
    border-left: 1px solid #efefef;
    margin-top: 20px;
    margin-left: 50px;
}

.bank-informations .cancellation-block .title {
    margin-top: 0;
}

.bank-informations .bank-informations-block .cancellation li {
    line-height: 1.4;
    padding-left: 15px;
    margin-bottom: 6px;
}

.bank-informations .bank-informations-block .cancellation li:before {
    content: '';
    display: inline-block;
    width: 7px;
    height: 7px;
    min-width: 7px;
    line-height: 16px;
    background-color: #cdd0dc;
    border-radius: 2px;
    vertical-align: middle;
    margin-right: 8px;
    position: relative;
    top: 9px;
}

.thank-you-container p.title,
.bank-informations-block p.title {
    margin-bottom: 0;
    margin-top: 14px;
    font-weight: 600;
    color: #252525;
}

.cancellation > .booking-policy-list-item > .booking-policy-list-item-circle {
    display: none;
}

.bank-informations ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.bank-informations p {
    margin-bottom: 0;
}

.bank-informations .bank-note {
    font-size: 1rem;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #cacaca;
    line-height: 1.4;
    display: flex;
    align-items: center;
}

.bank-note__icon {
    margin-right: 10px;
    min-width: 18px;
}

@media (max-width: 767px) {

	.thank-you-container table td {
		border-bottom: none;
		font-size: 15px;
		text-align: left;
	}

	.thank-you-container--fixed-height {
		min-height: 0;
	}

    .bank-informations .bank-note {
        align-items: flex-start;
    }

    .bank-note__icon {
        margin-top: 2px;
    }

	.bank-informations .bank-informations-block {
		width: auto;
		float: none;
	}

	.bank-informations .cancellation-block {
		margin-left: 0;
		border-left: 0;
		padding-left: 0;
	}

	.thank-you-container .company-information {
		flex-direction: column;
		justify-content: initial;
		align-items: initial;
	}

	.thank-you-container .company-information li {
		margin-bottom: 15px;
	}
}
</style>
{/block}

{block content}

    {varType App\Models\Entities\ProductBasicInfoEntity $basicInfo}
    {varType App\Models\Entities\PartnerEntity $partnerData}

    {if $bookingComplete}
        <div class="booking-component">
            <div class="container">

                <div class="text-xs-center">
                    <h2>{_'Hvala na rezervaciji!'}</h2>
                    <p><strong>{$translator->translate('Na vaš je email poslana potvrda rezervacije!')|noescape}</strong></p>

                    <p class="margin-bottom-0">
                        {_'Hvala što ste odabrali VillasGuide i s nama rezervirali željenu kuću za odmor!<br />Potvrdu rezervacije vam dostavljamo u nastavku, a detaljnije informacije o smještaju i organizaciji vašeg putovanja poslati ćemo Vam nakon izvršene uplate.'|noescape}
                    </p>
                </div>

                <div class="row">
                    <div class="col-md-6 padding-top-20">
                        <div class="thank-you-container thank-you-container--fixed-height">
                            <h4>{_'Informacije o rezervaciji'}:</h4>
                            <table class="reservation-information">
                                <tr>
                                    <td>{_'Rezervirani objekt'}:</td>
                                    <td>{if empty($h1_text)}
                                            {$productName}
                                         {else}
                                            {$h1_text}
                                        {/if}
                                    {for $stars_count = 0; $stars_count < $basicInfo->quality; $stars_count++}*{/for}
                                    </td>
                                </tr>
                                <tr>
                                    <td>{_'Broj rezervacije'}:</td>
                                    <td>{$bookingId}</td>
                                </tr>
                                <tr>
                                    <td>{_'Datum'}:</td>
                                    <td>{$bookedDate|date:'d.m.Y'}</td>
                                </tr>
                                <tr>
                                    <td>{_'Šifra agencije'}:</td>
                                    <td>2051883</td>
                                </tr>
                                <tr>
                                    <td>{_'Period najma'}:</td>
                                    <td>{$arrival|date:'d.m.Y'} - {$departure|date:'d.m.Y'}</td>
                                </tr>
                                <tr>
                                    <td>{_'Broj gostiju'}:</td>
                                    <td>{$adults} {_'odraslih'}, {$children} {_'djece'}, {$pets} {_'kućnih ljubimaca'} </td>
                                </tr>

                            </table>
                        </div>
                    </div>

                    <div class="col-md-6 padding-top-20">
                        <div class="thank-you-container thank-you-container--fixed-height">
                            <h4>{_'Informacije o gostu'}:</h4>
                            <table class="customer-information">
                                <tr>
                                    <td>{_'Ime gosta'}:</td>
                                    <td>{$contact_lastname|firstUpper} {$contact_firstname|firstUpper}</td>
                                </tr>
                                <tr>
                                    <td>{_'Telefonski broj gosta'}:</td>
                                    <td>{$phone}</td>
                                </tr>
                                <tr>
                                    <td>{_'Email gosta'}:</td>
                                    <td>{$email}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-12 padding-top-20">
                        <div class="thank-you-container">
                            <h4>{_'Informacije o plaćanju'}:</h4>
                            <table class="payment-information">
                                <tr>
                                    <td>{_'Cijena najma'}:</td>
                                    <td><strong>{$total_price_eur|number:2,',','.'} €</strong></td>
                                </tr>
                                {if !empty($insuranceList)}
                                    <tr>
                                        <td>{_'Accommodation'}:</td>
                                        {if !empty($distribution_price)}
                                            <td><strong>{$distribution_price|number:2,',','.'} €</strong></td>
                                        {else}
                                            <td><strong>{$price_eur|number:2,',','.'} €</strong></td>
                                        {/if}
                                    </tr>
                                    {foreach $insuranceList as $policy}
                                        <tr>
                                            <td>{$translator->translate($policy->type)}:</td>
                                            <td><strong>{$policy->price|number:2,',','.'} €</strong></td>
                                        </tr>
                                    {/foreach}
                                {/if}
                            </table>

                            {if !empty($immediatelyPayment)}
                                <table>
                                    <tr>
                                        <td>{_'Ukupan iznos rezervacije potrebno je uplatiti odmah isključivo putem kreditne kartice.'}</td>
                                    </tr>
                                </table>
                            {else}
                                {if empty($installmentsDate[2])}
                                    <table>
                                        <tr>
                                            <td>{_'Ukupan iznos rezervacije moguće je uplatiti odmah ili najkasnije %s dana od izvršenja rezervacije (najkasnije do <strong>%s</strong>)', $daysForFirstInstallment, $installmentsDate[1]|noescape}</td>
                                        </tr>
                                    </table>
                                {/if}
                                {if !empty($installmentsDate[2])}
                                    <table>
                                        <tr>
                                            <td>{_'Ukupan iznos rezervacije moguće je uplatiti odmah ili u 2 rate'}:</td>
                                        </tr>
                                    </table>
                                    <table class="payment-information-rates">
                                        <tr>
                                            <td>{_'1. rata'}: {_'Najkasnije do'} <strong>{$installmentsDate[1]}</strong></td>
                                            <td><strong>{$first_installment_price|number:2,',','.'} €</strong></td>
                                        </tr>
                                        <tr>
                                            <td>{_'2. rata'}:
                                                {if $second_payment_type === App\Common\Enums\PaymentTypeEnum::MONEY}
                                                    {_'Rest pay at location'}
                                                {else}
                                                    {_'Najkasnije do'} <strong>{$installmentsDate[2]}</strong>
                                                {/if}
                                            </td>
                                            <td><strong>{$second_installment_price|number:2,',','.'} €</strong></td>
                                        </tr>
                                    </table>
                                    {if $cc_payment && $second_payment_type === App\Common\Enums\PaymentTypeEnum::CREDIT_CARD}
                                        <p class="margin-left-15">{_'Nekoliko dana prije datuma dospijeća dostavit ćemo vam putem maila poziv za plaćanje 2. rate, putem kojeg možete izvršiti plaćanje kreditnom karticom.'}</p>
                                    {/if}
                                {/if}
                            {/if}

                            <p class="title margin-top-20 margin-left-15">{_'Cijena uključuje'}:</p>
                            <div class="price-includes-information margin-top-0">
                                <ul class="no-style margin-left-15 margin-bottom-0">
                                    {foreach $priceIncludeFeatures as $key => $value}
                                        {if !empty($value)}
                                            <li>{$translator->translate($key)}</li>
                                        {/if}
                                    {/foreach}
                                    {foreach $conciergesInPrice as $concierge}
                                        <li>{_$concierge->name}</li>
                                    {/foreach}
                                </ul>
                            </div>
                            <p class="title margin-top-20 margin-left-15">{_'Dodatni troškovi'}:</p>
                            <div class="price-not-included-information margin-top-0">
                                <ul class="no-style margin-left-15 margin-bottom-0">
                                    {var $extraCosts = 0}
                                    {foreach $ownerServices as $service}
                                        {if !empty($service->price)}
                                            <li>
                                                {$translator->translate($service->name)}: {$service->price}{$translator->translate($service->currency)} {_(str_replace('_', ' ',$service->price_type))}
                                                {if $service->service_type}
                                                    <strong>({_'Mandatory'})</strong>
                                                {else}
                                                    <strong>({_'Optional'})</strong>
                                                {/if}
                                            </li>
                                            {var $extraCosts = 1}
                                        {/if}
                                    {/foreach}
                                    {foreach $conciergesNotInPrice as $concierge}
                                        <li>
                                            {_$concierge->name}:
                                            {if $concierge->price}
                                                <strong>{$concierge->price|number:0} €</strong>
                                            {else}
                                                <strong>{_'Price on request'}</strong>
                                            {/if}
                                        </li>
                                    {/foreach}
                                    {if empty($extraCosts) && empty($conciergesNotInPrice)}
                                        <li>{_'No extra costs'}</li>
                                    {/if}
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 padding-top-20">
                        <div class="bank-informations clearfix">
                            <div class="clearfix">
                                {if !$cc_payment}
                                    <h4>{_'Bankovni podaci'}:</h4>
                                    <div class="bank-informations-block">
                                        <p class="margin-top-10">{_'Molimo vas da uplatu izvršite na sljedeći račun'}:</p>

                                        <p class="title">{_'Ime agencije i adresa'}:</p>
                                        <ul class="no-style">
                                            <li>{$partnerData->company_name}</li>
                                            <li>{$partnerData->company_street}, {$partnerData->company_zip} {$partnerData->city} - {$partnerData->company_country}</li>
                                            <li>VAT ID (OIB): <strong>{$partnerData->company_oib}</strong></li>
                                        </ul>

                                        <p class="title">{_'Ime banke i adresa'}:</p>
                                        <ul class="no-style">
                                            <li>{$partnerData->bank_name}</li>
                                            <li>{$partnerData->bank_street}</li>
                                            <li>IBAN: <strong>{$partnerData->bank_iban}</strong></li>
                                            <li>SWIFT: <strong>{$partnerData->bank_swift}</strong></li>
                                        </ul>

                                    </div>
                                {/if}
                                <div class="bank-informations-block">
                                    <div class="{if !$cc_payment}cancellation-block{/if}">
                                        <p class="title">{_'Mogućnost otkazivanja'}:</p>
                                        <ul class="cancellation">
                                            {control cancellationPolicyControl}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="clearfix"></div>
                            {if !$cc_payment}
                                <p class="bank-note">
                                    {embeddedSvg 'assets/img/svg/info.svg',
                                    class => 'bank-note__icon',
                                    fill => '#252525',
                                    height => 18,
                                    width => 18}

                                    <span>
                                            {_'U opisu uplate molimo obavezno navesti broj rezervacije!'}<br />
                                            {_'Kopiju uplate dostaviti na sljedeću mail adresu:'} <strong><EMAIL></strong>
                                        </span>
                                </p>
                            {/if}
                        </div>
                    </div>

                    <div class="col-md-12 margin-top-20">
                        <div class="thank-you-container">
                            <ul class="no-style company-information margin-bottom-0">
                                <li>
                                    <img n:srcv="/assets/img/logo-black.png" alt="Villas Guide" width="200" />
                                </li>
                                <li>
                                    <h4>{_'Trebate li pomoć?'}</h4>
                                    <p>{_'Nazovite nas:'} <strong>{$company_phone_number}</strong></p>
                                </li>
                                <li>
                                    <h4>{_'Služba za korisnike'}</h4>
                                    <p>{_'Ponedjeljak - Petak - 8:00 - 16:00 <br />Subotom i nedjeljom zatvoreno'|noescape}</p>
                                </li>
                                <li>
                                    <p>{_'villas-guide.com'}</p>
                                    <p>{$companyAddressInfo['companyAddress']}</p>
                                    <p>{$companyAddressInfo['companyPostalcode']} {$companyAddressInfo['CompanyCity']}</p>
                                    <p>{_'Hrvatska'}</p>
                                </li>
                            </ul>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    {else}
        <div class="booking-component">
            <div class="container">
                <div class="jumbotron text-xs-center">
                    <h1 class="display-3">{_'Something went wrong!'}</h1>
                    <p>{$translator->translate('<strong>Holiday home is probably not available for reservation.</strong></p>')|noescape}</p>
                    <hr>
                    <p>
                        {_'Having trouble?'} <a n:href="ContactUs:default">{_'Contact us'}</a>
                    </p>
                </div>
            </div>
        </div>
    {/if}

    {if $bookingComplete && !$gaTracked}
        <script>
          jQuery(window).load(function($) {

            window.dataLayer = window.dataLayer || [];

            // Push Confirm booking event
            dataLayer.push(
              {'event': 'confirmed_booking', 'eventAction': 'confirm_booking'});

            // Clear previous ecommerce object
            // Clearing the object will prevent multiple ecommerce events on a page from affecting each other.
            dataLayer.push({ ecommerce: null });

            // Push purchase ecommerce event
            dataLayer.push({
              event: "purchase",
              ecommerce: {
                transaction_id: {(string) $bookingId}, // Transaction ID, (string) 7342183
                affiliation: {$productName}, // Affiliation, (string) "Holiday home - Barban-Prhati"
                value: {$total_price_eur}, // Value, (integer) 949
                currency: "EUR",
                items: [{
                    item_id: {(string) $basicInfo->id}, // ID of the holiday home
                    item_name: {$productName},
                    affiliation: {$productName},
                    index: 0,
                    price: {$total_price_eur}, // Value, (integer) 949
                    quantity: 1
                }]
              }
            });
          });
        </script>
    {/if}

{/block}

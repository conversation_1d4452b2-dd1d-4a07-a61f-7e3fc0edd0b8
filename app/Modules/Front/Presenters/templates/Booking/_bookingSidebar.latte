{if $language === 'de'}
    {var $guests_translation = $translator->translate('guests')}
    {var $adults_translation = $translator->translate('adults')}
    {var $pets_translation = $translator->translate('pets')}
    {var $children_translation = $translator->translate('children')}
    {var $nights_translation = $translator->translate('nights')}
{else}
    {var $guests_translation = lcfirst($translator->translate('guests'))}
    {var $adults_translation = lcfirst($translator->translate('adults'))}
    {var $pets_translation = lcfirst($translator->translate('pets'))}
    {var $children_translation = lcfirst($translator->translate('children'))}
    {var $nights_translation = lcfirst($translator->translate('nights'))}
{/if}

<div class="booking-component__sidebar booking-component__sidebar--sticky booking-component__sidebar--as-modal">
    <div class="sidebar-modal-close-btn-wrapper">
        <button class="button sidebar-modal-close-btn">
            {embeddedSvg 'assets/img/svg/close.svg',
                height => 14,
                width => 14
            }
        </button>
    </div>
    <div class="booking-component__sidebar-section booking-component__sidebar-section--summary">
        <div class="booking-component__sidebar-gallery listing-lazy-images mfp-gallery-container keen-slider">
            {foreach $pictures as $picture}
                {if $useBrandedGallery}
                    {var $image_source = str_replace("1024/", "branded/1024/", $picture)}
                {else}
                    {if $isTablet}
                        {var $image_source = $picture}
                    {else}
                        {var $image_source = str_replace("1024/", "1024/", $picture)}
                    {/if}
                {/if}
                <div class="booking-component__gallery-item keen-slider__slide--fade" data-slide="{$iterator->counter}">
                    <a
                        {if $useBrandedGallery}
                            href="{str_replace("1024/", "branded/1024/", $picture)}"
                        {else}
                            href="{$picture}"
                        {/if}
                        {if in_array($picture, $representativeImages)}
                            data-sub-html="{_'Fotografija je informativnog karaktera'}"
                        {/if}
                        data-exthumbimage="{str_replace("1024/", "400/", $picture)}"
                        class="mfp-gallery">
                        <picture class="booking-component__gallery-picture">
                            <img
                                src="{$image_source}"
                                class="booking-component__gallery-picture-source"
                                alt="{$productName}" />
                        </picture>
                    </a>
                </div>
            {/foreach}
            <div class="keen-prev keen-arrow"></div>
            <div class="keen-next keen-arrow"></div>
        </div>

        <div class="booking-component__sidebar-content">
            <p class="booking-component__sidebar-object-title">
                {if empty($h1_text)}
                    {$productName}
                {else}
                    {$h1_text}
                {/if}
            </p>
            <ul class="booking-component__sidebar-object-features">
                <li class="booking-component__sidebar-object-feature">
                    {$basicInfo->adult_count} {_'people'}
                </li>
                <li class="booking-component__sidebar-object-feature">
                    <span class="booking-component__sidebar-object-dot"></span>
                    {$basicInfo->bedrooms_count} {_'bedrooms'}
                </li>
                <li class="booking-component__sidebar-object-feature">
                    <span class="booking-component__sidebar-object-dot"></span>
                    {$basicInfo->bathrooms_count} {_'bathrooms'}
                </li>
                {if $basicInfo->outdoor_swimming_pool || $basicInfo->indoor_swimming_pool}
                    <li class="booking-component__sidebar-object-feature">
                        <span class="booking-component__sidebar-object-dot"></span>
                        {if $basicInfo->outdoor_swimming_pool}
                            {_'outdoor_swimming_pool'}
                        {/if}
                        {if $basicInfo->outdoor_swimming_pool && $basicInfo->indoor_swimming_pool}
                            +
                        {/if}
                        {if $basicInfo->indoor_swimming_pool}
                            {_'indoor_swimming_pool'}
                        {/if}
                        {if !empty($basicInfo->pool_size)}
                            {$basicInfo->pool_size}m<sup>2</sup>
                        {/if}
                    </li>
                {/if}
            </ul>
        </div>
    </div>
    
    <div class="booking-component__sidebar-section">
        <div class="booking-component__sidebar-details">
            <div class="booking-component__sidebar-guests">
                {embeddedSvg 'assets/img/svg/people-outline.svg',
                    class => 'booking-component__sidebar-guests-icon',
                    height => 20,
                    width => 20}
                <span class="booking-component__sidebar-guests-item"><span class="booking-sidebar-adults-count">{$adultsCount}</span> {$adults_translation}</span>
                <span class="booking-component__sidebar-guests-item"><div class="sidebar-guests-dot"></div><span class="booking-sidebar-children-count">{$childrenCount}</span> {$children_translation}</span> 
                <span class="booking-component__sidebar-guests-item"><div class="sidebar-guests-dot"></div><span class="booking-sidebar-pets-count">{$petsCount}<span> {$pets_translation}</span> 

            </div>
            <div class="booking-component__sidebar-dates">
                <div class="booking-component__sidebar-arrival-departure-wrapper">
                    <span class="booking-component__sidebar-dates-label">{_'Check-in'}</span>
                    <span class="booking-component__sidebar-arrival">{$arrival}</span>
                </div>

                <div class="booking-component__sidebar-dates-middle">
                    <div class="booking-component__sidebar-ornament">
                        <div class="booking-component__outline-circle"></div>
                        <div class="booking-component__dash-line"></div>
                    </div>

                    <span class="booking-component__sidebar-nights">{$nights} {$nights_translation}</span>

                    <div class="booking-component__sidebar-ornament">
                        <div class="booking-component__dash-line"></div>
                        <div class="booking-component__outline-circle"></div>
                    </div>
                </div>

                <div class="booking-component__sidebar-arrival-departure-wrapper">
                    <span class="booking-component__sidebar-dates-label">{_'Check-out'}</span>
                    <span class="booking-component__sidebar-departure">{$departure}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="booking-component__sidebar-section booking-component__sidebar-section-for-overlay">
        {snippet prices2}
            <div class="booking-component__sidebar-prices booking-component__sidebar-prices--total-price">
                <p class="booking-component__sidebar-prices-text">

                    {if empty($extraCosts) && !$notAvailable && empty($presenter->getTemplate()->propertyDamageInsurancePrice) && empty($presenter->getTemplate()->cancellationInsurancePrice)}
                        <span class="booking-component__sidebar-prices-additional">
                            {_'No additional costs!'}
                        </span>
                    {/if}
                    <span class="booking-component__sidebar-prices-additional booking-component__sidebar-prices-additional__base_price booking-component__sidebar-prices-additional--flex">{_'Accommodation'}:
                        <span class="booking-component__sidebar-prices-amount booking-component__sidebar-prices-amount--flex"><span n:if="$priceWithoutDiscount > $basePrice" class="booking-component__price-discount"><del><span>{$priceWithoutDiscount|number:2,',','.'} €</span></del></span> {$basePrice|number:2,',','.'} €</span>
                    </span>
                    <span class="booking-component__sidebar-prices-additional booking-component__sidebar-prices-additional__property_damage_insurance booking-component__sidebar-prices-additional--flex">
                        <span class="booking-component__sidebar-prices--property-damage-insurance-label">{_'Property Damage Insurance'}:</span>
                        <span class="booking-component__sidebar-prices-amount">{$presenter->getTemplate()->propertyDamageInsurancePrice|number:2,',','.'} €</span>
                    </span>
                    {if isset ($presenter->getTemplate()->cancellationInsurancePrice)}
                        <span class="booking-component__sidebar-prices-additional booking-component__sidebar-prices-additional__cancellation_insurance">
                            {_'Cancellation Insurance'}:
                            <span class="booking-component__sidebar-prices-amount">{$presenter->getTemplate()->cancellationInsurancePrice|number:2,',','.'} €</span>
                        </span>
                    {/if}
                    <span class="booking-component__sidebar-prices-total">
                        {_'Total price'}
                        <span class="booking-component__sidebar-price">
                            {$priceWithDiscount|number:2,',','.'} €
                        </span>
                    </span>
                    <span class="booking-component__sidebar-prices-additional booking-component__sidebar-prices-additional__save-price booking-component__sidebar-prices-additional--flex">
                        <span class=" booking-component__sidebar-prices-amount--flex"> <span n:if="$priceWithoutDiscount > $basePrice"> <span class="you-save">{_'You save'}: {$priceWithoutDiscount - $basePrice|number:2,',','.'} €</span></span></span>
                    </span>
                </p>
            </div>
        {/snippet}
        {if $paymentInTwoInstallments}
            <div id="payment_details" class="booking-component__sidebar-installments{if $basicInfo->partner !== 'istria_home'} hidden{/if}">
                <p class="payment-two-installments-title">{_'Instalment payment'}</p>
                {if isset($firstInstallmentPrice)}
                    <div class="booking-component__sidebar-prices booking-component__sidebar-prices--less-margin">
                        <div class="booking-final-price booking-component__sidebar-prices-text booking-component__sidebar-prices-text--smaller">
                            <span class="installment-label">{_'First payment due'}</span> 
                            <div class="installment-right-side">
                                <span class="booking-component__sidebar-prices-additional two-installments-date">{$firstInstallmentDate}</span>
                                <div class="booking-component__dash-line-minimal"></div>
                                <span class="booking-component__sidebar-price booking-component__sidebar-price-first-installment booking-component__sidebar-price--smaller">{$firstInstallmentPrice|number:2,',','.'} €</span>
                            </div>
                        </div>
                    </div>
                    <div class="booking-component__sidebar-prices booking-component__sidebar-prices--less-margin">
                        <div class="booking-final-price booking-component__sidebar-prices-text booking-component__sidebar-prices-text--smaller">
                            <span class="installment-label">{_'Second payment due'}</span> 
                            <div class="installment-right-side">
                                <span class="booking-component__sidebar-prices-additional two-installments-date due-date-for-payment-method">{$secondInstallmentDate}</span>
                                <div class="booking-component__dash-line-minimal"></div>
                                <span class="booking-component__sidebar-price booking-component__sidebar-price--smaller">{$secondInstallmentPrice|number:2,',','.'} €</span>
                            </div>
                        </div>
                    </div>
                {/if}
                {if isset($installmentPrice)}
                    <div class="booking-component__sidebar-prices">
                        <div class="booking-final-price booking-component__sidebar-prices-text">
                            {_'Payment deadline'}: {$installmentDate})
                        </div>
                        <span class="booking-component__sidebar-price">{$installmentPrice} €</span>
                    </div>
                {/if}
            </div>
        {/if}
    </div>

    <div class="booking-component__sidebar-section booking-component__sidebar-section--help">
        <div class="booking-component__content-help">
            <picture>
                <source
                    data-srcset="/assets/img/agent-nikolina.jpg.webp"
                    type="image/webp" />
                <img
                    class="booking-component__sidebar-help-image lazyload"
                    src="{$presenter->getTemplate()->image_placeholder|nocheck}"
                    data-src="/assets/img/agent-nikolina.jpg"
                    alt="{_'Nikolina Salamun - Help Center'}"
                    width="60"
                    height="60" />
            </picture>
            <p class="booking-component__sidebar-help-text">
                {_'Need help?'}
                <a href="tel:{$company_phone_number}" class="booking-component__sidebar-help-number booking-component__sidebar-help-number-call">{$company_phone_number}</a>
            </p>
        </div>
        <div class="booking-component__content-customer-care">
            <!-- TrustBox widget - Starter -->
            <div class="trustpilot-widget trustpilot-widget--booking-sidebar trustpilot-widget--lazy-load" data-locale="{$language}-{$language}" data-template-id="5419b6ffb0d04a076446a9af" data-businessunit-id="62fb9e2516432cccacd09c8a" data-style-height="50px" data-style-width="100%" data-theme="light" style="width: 100%">
                <a href="https://{$language}.trustpilot.com/review/gaveia.com" target="_blank" rel="noopener">Trustpilot</a>
            </div>
            <!-- End TrustBox widget -->
        </div>
    </div>
</div>

<div class="booking-component__section booking-component__section--personal-details booking-form-payment">
    <h1 class="booking-component__title">{_'Complete your booking in just 2 minutes'}</h1>

    <div class="booking-component__block--first-step">
        <div class="booking-component__form-row booking-component__form-row--row-mobile">
            <div class="booking-component__form-col booking-component__form-col--field-title">
                <div class="booking-component-black-radio booking-component-black-radio--active">
                    <input type="radio" id="form-personal-details-title-mr" class="booking-component__form-radio--title" n:name="title" checked/>
                    <label for="form-personal-details-title-mr" class="booking-component__form-label booking-component__form-label--title booking-component__black-radio-label--title">{_'Mr.'}</label>
                </div>
                <div class="booking-component-black-radio">
                    <input type="radio" id="form-personal-details-title-mrs" class="booking-component__form-radio--title" n:name="title"/>
                    <label for="form-personal-details-title-mrs" class="booking-component__form-label booking-component__form-label--title booking-component__black-radio-label--title">{_'Mrs'}</label>
                </div>
            </div>
        </div>
        <div class="booking-component__form-row booking-component__form-row--row-mobile">
            <div class="booking-component__form-col">
                <label for="form-personal-details-firstname" class="booking-component__form-label">{_'First name'} *</label>
                <input
                    data-clarity-unmask="true"
                    type="text"
                    class="booking-component__form-input booking-component__form-input--first-name disable-validation"
                    n:name="firstname"
                    id="form-personal-details-firstname"
                    placeholder="{_'First name'}"
                    data-validate-error="{_'This field is required'}" />
            </div>
            <div class="booking-component__form-col">
                <label for="form-personal-details-lastname" class="booking-component__form-label">{_'Last name'} *</label>
                <input
                    data-clarity-unmask="true"
                    type="text"
                    class="booking-component__form-input disable-validation"
                    n:name="lastname"
                    id="form-personal-details-lastname"
                    placeholder="{_'Last name'}"
                    data-validate-error="{_'This field is required'}" />
            </div>
        </div>

        <div class="booking-component__form-row">
            <div class="booking-component__form-col">
                <label for="form-personal-details-email" class="booking-component__form-label">{_'Email'} *</label>
                <input
                    data-clarity-unmask="true"
                    type="text"
                    class="booking-component__form-input disable-validation"
                    n:name="email"
                    id="form-personal-details-email"
                    placeholder="{_'Email'}"
                    autocomplete="on"
                    data-error-email="{_'Invalid email address'}"
                    data-validate-error="{_'This field is required'}" />
            </div>
            <div class="booking-component__form-col">
                <label for="form-personal-details-verify-email" class="booking-component__form-label">{_'Verify email'} *</label>
                <input
                    data-clarity-unmask="true"
                    type="text"
                    class="booking-component__form-input disable-validation"
                    n:name="email_verify"
                    id="form-personal-details-verify-email"
                    placeholder="{_'Verify email'}"
                    data-error-email="{_'Invalid email address'}"
                    data-validate-error="{_'This field is required'}" />
            </div>
        </div>

        <div class="booking-component__form-row">
            <div class="booking-component__form-col">
                <label for="form-personal-details-phone" class="booking-component__form-label">{_'Phone number'} *</label>
                <input
                    data-clarity-unmask="true"
                    type="text"
                    class="booking-component__form-input disable-validation"
                    n:name="phone"
                    id="form-personal-details-phone"
                    placeholder="{_'Phone number'}"
                    pattern="[0-9]*"
                    autocomplete="on"
                    data-validate-error="{_'This field is required'}" />
            </div>
            <div class="booking-component__form-col">
                <label for="booking-country" class="booking-component__form-label">{_'Country'} *</label>
                <select n:name="custcountry" id="booking-country" class="country_selectpicker booking-component__form-select booking-component__form-select--country"></select>
            </div>
        </div>

        {if $form->getComponent('street', false) !== null}
            <div class="booking-component__novasol-inputs">
                <div class="booking-component__novasol-input-group">
                    <div class="booking-component__novasol-input-group--big">
                        <label for="form-personal-street" class="booking-component__form-label">{_'Street name'} *</label>
                        <input
                            data-clarity-unmask="true"
                            type="text"
                            class="booking-component__form-input disable-validation booking-component__form-input--novasol"
                            n:name="street"
                            id="form-personal-street"
                            data-validate-error="{_'This field is required'}" />
                    </div>
                    <div class="booking-component__novasol-input-group--small">
                        <label for="form-personal-street-number" class="booking-component__form-label">{_'Street number'} *</label>
                        <input
                            data-clarity-unmask="true"
                            type="text"
                            class="booking-component__form-input disable-validation booking-component__form-input--novasol"
                            n:name="street_number"
                            id="form-personal-street-number"
                            data-validate-error="{_'This field is required'}" />
                    </div>
                </div>
                <div class="booking-component__novasol-input-group">
                    <div class="booking-component__novasol-input-group--big">
                        <label for="form-personal-city" class="booking-component__form-label">{_'City'} *</label>
                        <input
                            data-clarity-unmask="true"
                            type="text"
                            class="booking-component__form-input disable-validation booking-component__form-input--novasol"
                            n:name="city"
                            id="form-personal-city"
                            data-validate-error="{_'This field is required'}" />
                    </div>
                    <div class="booking-component__novasol-input-group--small">
                        <label for="form-personal-zip" class="booking-component__form-label">{_'zip code'} *</label>
                        <input
                            data-clarity-unmask="true"
                            type="text"
                            class="booking-component__form-input disable-validation booking-component__form-input--novasol"
                            n:name="zip"
                            id="form-personal-zip"
                            data-validate-error="{_'This field is required'}" />
                    </div>
                </div>
            </div>
        {/if}

        <input
            id="form-personal-details-is-human"
            type="text"
            name="h_password"
            tabindex="-1"
            autocomplete="off" />

        {if $user->isLoggedIn() && $user->isInRole('admin')}
            <div class="booking-component__form-row">
                <div class="booking-component__form-col checkboxes">
                    <input type="checkbox" class="disable-validation" n:name="option_booking" id="option_booking"/>
                    <label class="margin-top-8" for="option_booking">{_'Create a reservation as an option'}</label>
                </div>
            </div>
            <div class="booking-component__form-row">
                <div class="booking-component__form-col">
                    <label class="margin-top-8" for="distribution_channel">{_'Distribution channels'}</label>
                    {input product_distribution_channel_id, 'class' => 'booking-component__form-select'}
                </div>
                <div class="booking-component__form-col">
                </div>
            </div>
        {/if}
        <p class="booking-component__action-note">
            <img
                n:srcv="/assets/img/svg/secure.svg"
                class="booking-component__action-note-icon"
                width="18"
                height="18"
                alt="{_'Secure payment'}" />
            {_'Your personal details will be encrypted and kept confidential.'}
        </p>
        <div class="booking-page-step__next-btn--wrapper mobile-d-none">
            <button class="booking-page-step__next-btn button button--smaller button--color-anchor button--color-anchor--arrow-right" data-step="1" disabled>
                {_'Continue to additional services'}
                {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
                    class => 'gold-btn-right-arrow',
                    fill => '#252525',
                    height => 16,
                    width => 16
                }
            </button>
        </div>
    </div>
</div>

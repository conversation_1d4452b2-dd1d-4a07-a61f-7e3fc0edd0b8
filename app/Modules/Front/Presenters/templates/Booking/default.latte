{varType App\Models\Entities\ProductBasicInfoEntity $basicInfo}
{layout '../@bookingLayout.latte'}

{block head}
    <link rel="alternate" hreflang="en-gb" n:href="//Booking:default, language=>'en', adults => $adultsCount, children => $childrenCount, pets => $pets, arrive => $arrival, departure => $departure" />
    <link rel="alternate" hreflang="hr" n:href="//Booking:default, language=>'hr', adults => $adultsCount, children => $childrenCount, pets => $pets, arrive => $arrival, departure => $departure "/>
    <link rel="alternate" hreflang="de-de" n:href="//Booking:default, language=>'de', adults => $adultsCount, children => $childrenCount, pets => $pets, arrive => $arrival, departure => $departure "/>
    <link rel="alternate" hreflang="it-it" n:href="//Booking:default, language=>'it', adults => $adultsCount, children => $childrenCount, pets => $pets, arrive => $arrival, departure => $departure "/>
{/block}

{block content}

    {form bookingForm, autocomplete => 'off'}
        {if $sandbox}
            <div class="sandbox-notification-block">{ucfirst($translator->translate('You are currently in SANDBOX environment.'))} Agency: {strtoupper($partner)}</div>
        {/if}
        <div class="booking-component">

            {if $notAvailable}
                <div class="booking-component__error">
                    <div class="booking-component__error-content">
                        <div class="booking-component__error-image">
                            <div class="booking-component__error-icon-wrapper">
                                <img
                                    src="/assets/img/svg/warning-sign.svg"
                                    class="booking-component__error-icon"
                                    width="32"
                                    height="32"
                                    alt="{_'Warning'}">
                            </div>
                        </div>
                        <p class="booking-component__error-title">
                            {_'We are sorry, the villa is not available anymore.'}
                        </p>
                        <p class="booking-component__error-subtitle">
                            {_'In the meantime the villa has been booked. Please, choose another villa or contact us for help.'}
                        </p>
                        <div class="booking-component__error-actions">
                            <a href="tel:{$company_phone_number}" class="booking-component__error-anchor button button--smaller border">{_'Contact us'}</a>
                        </div>
                    </div>
                </div>
            {/if}

            <div class="container">
                {include './_bookingSteps.latte'}
                <div class="row row-flex row-flex--mobile row-flex--mobile-column-reverse row-flex--tablet row-flex--tablet-column-reverse booking-page-steps-wrapper">
                    <div class="col-md-8">
                        <div class="booking-page-step booking-component__page-step--active" data-step="1">
                            {include './_bookingStep1.latte', form => $form}
                        </div>
                        <div class="booking-page-step" data-step="2">
                            {include './_bookingStep2.latte', form => $form}
                        </div>
                        <div class="booking-page-step" data-step="3">
                            {include './_bookingStep3.latte'}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    {snippetArea sidebar}
                        {include './_bookingSidebar.latte'}
                    {/snippetArea}
                </div>
                <div class="booking-component-modal-bottom-bar-wrapper"></div>
                    <div class="booking-component__sidebar-details booking-component__sidebar-details--sticky">
                        <div class="booking-component__sidebar-details--sticky-content">
                            <div class="booking-component__sidebar-dates booking-component__sidebar-dates--sticky">
                                <div class="sidebar-details-dates-sticky">
                                    <span>{$arrival}</span>
                                    <span class="sidebar-details-dates-sticky__departure">{$departure}</span>
                                    <div class="sidebar-details-dates-sticky__circle"></div>
                                    <span class="booking-component__sidebar-guest"><span class="sidebar-details-dates-sticky__persons">{$numberOfGuests}</span> {$guests_translation}</span>
                                </div>
                            </div>
                            {snippet prices}
                                <div class="booking-component__sidebar-prices booking-component__sidebar-prices--total-price">
                                    <p class="booking-component__sidebar-prices-text booking-component__sidebar-prices-text--bottom-bar">
                                        <span class="booking-component__sidebar-prices-additional booking-component__sidebar-prices-additional__base_price booking-component__sidebar-prices-additional__base_price--sticky booking-component__sidebar-prices-additional__base_price--bottom-bar {if $priceWithoutDiscount > $basePrice} booking-component__sidebar-prices-additional__base_price--sticky-show {/if}">
                                            <span class="booking-component__sidebar-prices-amount"><span n:if="$priceWithoutDiscount > $basePrice" class="booking-component__price-discount"><del><span>{$priceWithoutDiscount|number:2,',','.'} €</span></del></span></span>
                                        </span>

                                        <span class="booking-component__sidebar-price booking-component__sidebar-price--sticky booking-component__sidebar-price--bottom-bar">
                                            {$priceWithDiscount|number:2,',','.'} €
                                        </span> 

                                        <span class="booking-component__sidebar-prices-additional booking-component__sidebar-prices-additional__save-price booking-component__sidebar-prices-additional--flex booking-component__sidebar-prices-additional__base_price--sticky">
                                        </span>
                            
                                    </p>
                                </div>
                            {/snippet}
                            <div class="booking-component-bottom-bar__details">
                                <button class="button border button-show-sidebar">{_'Vidi detalje'}</button>
                                {if $paymentInTwoInstallments}
                                    <p class="two-installment-badge-sticky-bottom-bar {if $basicInfo->partner !== 'istria_home'} hidden{/if}">{_'Installment Payment'}</p>
                                {/if}
                            </div>
                        </div>
                        <div class="sidebar-sticky-cta-wrapper">
                            <button class="booking-page-step__next-btn button button--smaller button--color-anchor button--color-anchor--arrow-right button-sticky-next-step" data-step="1" disabled>
                                {_'Next'}
                                {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
                                    class => 'gold-btn-right-arrow gold-btn-right-arrow__booking',
                                    fill => '#252525',
                                    height => 16,
                                    width => 16
                                }
                            </button>
                            <button class="booking-page-step__next-btn button button--smaller button--color-anchor button--color-anchor--arrow-right button-sticky-next-step d-none" data-step="2">
                                {_'Next'}
                                {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
                                    class => 'gold-btn-right-arrow gold-btn-right-arrow__booking',
                                    fill => '#252525',
                                    height => 16,
                                    width => 16
                                }
                            </button>
                                <button
                                type="submit"
                                id="submit_booking"
                                data-gaaction="submit"
                                data-step="3"
                                data-gacategory="inquiry"
                                data-gaevent="booking_proces__book_now"
                                class="button--booking-loader booking-component__submit button button--color-action ga-event button-sticky-next-step submit-booking-btn button--disabled d-none">{_'Confirm'}
                            </button>
                        </div>
                    </div>

            </div>
        </div>
    {/form}

    <div class="loading-booking loading-booking--confirmation">
        <div>
            <div class="load-circle">{_'Loading'}...</div>
            {_'Loading booking confirmation'}
        </div>
    </div>

{/block}

{block js}
    <!-- TrustBox script -->
    <script type="text/javascript" src="//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js" async></script>
    <!-- End TrustBox script -->

    <script type="text/javascript" n:syntax="off">

        function formatNumber(number)
        {
          var opts = {maximumFractionDigits: 2, minimumFractionDigits: 2};
          return number.toLocaleString(number, opts);
        }

    </script>
    <script>
      var credit_card_payment_enabled = {$creditCardPaymentStatus},
          bookingTemplate = 1,
          secondInstallmentDate = {$secondInstallmentDate};
          arrivalDate = {$arrival};

      addEventListener("error", (event) => {
          console.log(event);
      });

      jQuery(document).ready(function ($) {
          bookingOptionsCheck();
          enableBookNowButton();
          confirmCheckboxCheck();
          bookingOptionCheckboxCheck();
          bookingFormComponents();
          setBookingPaymentTypeBlock();
          trustpilotShowWidget();
          checkboxesColors();
          checkboxesColorsGender();
          toggleTextArea();
          bookingSteps();
          handleSidebarModal();
          personsDropdown();
          deleteInputError();
          toggleBookNowButtonInMobile();
          toggleConciergeServices();
          paymentCheckboxOnMobile();
          deleteAllErrorsOnPayment();

          $('.button-sticky-next-step[data-step="1"]').prop('disabled', false);
          $('.booking-page-step__next-btn[data-step="1"]').prop('disabled', false);

          if (jQuery('[id="property_damage_insurance"]').is(':checked')) {
              redrawBlock();
          }
      });

      function sendToHS () {
        const firstName = document.getElementById('form-personal-details-firstname')?.value ?? '';
        const lastName = document.getElementById('form-personal-details-lastname')?.value ?? '';
        const email = document.getElementById('form-personal-details-email')?.value ?? '';
        const sel = document.getElementById('booking-country');
        const country = sel.options[sel.selectedIndex]?.text ?? '';
        const arrive = {$presenter->getParameter('arrive')};
        const departure = {$presenter->getParameter('departure')};
        const adults = document.getElementsByName('adults')[0]?.value ?? 0;
        const children = document.getElementById('children_num')?.value ?? 0;
        const pets = document.getElementById('pets_num')?.value ?? 0;
        const phoneNumber = document.getElementById('form-personal-details-phone')?.value ?? '';
        const contactLanguage = {$language};

         naja.makeRequest(
        'POST',
        {plink 'Booking:sendToHs'},
        {
            'first_name': firstName,
            'last_name': lastName,
            'email': email,
            'country': country,
            'contact_language': contactLanguage,
            'date_of_arrival': arrive,
            'date_of_departure': departure,
            'number_of_persons': parseInt(adults),
            'number_of_children': parseInt(children),
            'pets': parseInt(pets),
            'phone': phoneNumber,
            'context': {$contextUrl},
            'amount': {$priceWithDiscount},
        },
        {'history': false}
        )
      }

      jQuery('#property_damage_insurance').on('change', function() {
          redrawBlock();
      })

      jQuery('#deposit').on('change', function() {
          redrawBlock();
      })

      jQuery('#cancellation_insurance').on('change', function() {
          redrawBlock();
      })

      jQuery('#qtyButton-guests').on('click', function() {
          redrawBlock();
      })

      function redrawBlock() {
        var arrive = {$presenter->getParameter('arrive')},
          departure = {$presenter->getParameter('departure')},
          adults = document.getElementsByName('adults')[0]?.value ?? 0,
          children = document.getElementById('children_num')?.value ?? 0,
          propertyDamageInsurancePrice = {$presenter->getTemplate()->propertyDamageInsurancePrice},
          cancellationInsurancePrice = {if isset($presenter->getTemplate()->cancellationInsurancePrice)}{$presenter->getTemplate()->cancellationInsurancePrice}{else}0{/if};


        if (!jQuery('[id="property_damage_insurance"]').is(':checked')) {
          propertyDamageInsurancePrice = 0;
        } else  {
          propertyDamageInsurancePrice = {$presenter->getTemplate()->propertyDamageInsurancePrice};
        }

        if (jQuery('[id="cancellation_insurance"]:checked').val() !== 'on') {
          cancellationInsurancePrice = 0;
        } else {
          cancellationInsurancePrice = {if isset($presenter->getTemplate()->cancellationInsurancePrice)}{$presenter->getTemplate()->cancellationInsurancePrice}{else}0{/if};
        }

        sidebarBlockUpdateOverlay('show');
        $('.disable-when-loading').css('pointer-events', 'none')

        naja.makeRequest(
          'POST',
          {plink 'Booking:changePrice'},
          {
            'arrive': arrive,
            'departure': departure,
            'adults': parseInt(adults),
            'children': parseInt(children),
            'propertyDamageInsurancePrice': propertyDamageInsurancePrice,
            'cancellationInsurancePrice': cancellationInsurancePrice,
          },
          {'history': false}
        ).then((payload) => {
            const dashLine = jQuery('<div class="booking-component__dash-line-minimal"></div>');
            const elem = jQuery('.booking-component__sidebar-prices-amount');
            const totalPrice = jQuery('.booking-component__sidebar-prices-total');

            if(jQuery('[id="property_damage_insurance"]').is(':checked') || jQuery('[id="cancellation_insurance"]').is(':checked') ) {
                dashLine.insertBefore(elem);
                totalPrice.addClass('booking-component__sidebar-prices-total--border-top');
            }else {
                totalPrice.removeClass('booking-component__sidebar-prices-total--border-top');
            }

            if (!jQuery('[id="property_damage_insurance"]').is(':checked') && jQuery('[id="cancellation_insurance"]:checked').val() !== 'on') {
              jQuery('.booking-component__sidebar-prices-additional__base_price').hide();
              jQuery('.booking-component__sidebar-price-first-installment').html({$firstInstallmentPrice|number:2,',','.'} + ' €');
            } else {
              var firstInstallmentWithExtras = propertyDamageInsurancePrice + cancellationInsurancePrice + {$firstInstallmentPrice};
              jQuery('.booking-component__sidebar-prices-additional__base_price').css('display', 'flex');
              jQuery('.booking-component__sidebar-price-first-installment').html(formatNumber(firstInstallmentWithExtras) + ' €');
            }

            if (!jQuery('[id="property_damage_insurance"]').is(':checked')) {
              jQuery('.booking-component__sidebar-prices-additional__property_damage_insurance').hide();
              
            } else  {
              jQuery('.booking-component__sidebar-prices-additional__property_damage_insurance').css('display', 'flex');

            }

            if (jQuery('[id="cancellation_insurance"]:checked').val() !== 'on') {
              jQuery('.booking-component__sidebar-prices-additional__cancellation_insurance').hide();
              
            } else {
              jQuery('.booking-component__sidebar-prices-additional__cancellation_insurance').css('display', 'flex');

            }

        sidebarBlockUpdateOverlay('hide');
        $('.disable-when-loading').css('pointer-events', 'auto')
        });
      }

    </script>
{/block}




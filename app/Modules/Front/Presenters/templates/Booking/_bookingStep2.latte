<div class="booking-component__section">
    <div class="booking-component__block ">
        <h3 class="booking-component__block-title">{_'Check your dates and guests list'}</h3>
        <div class="main-search-input-item main-search-input-item--persons persons panel-dropdown panel-dropdown--input-open panel-dropdown--input--booking">
            <input type="text" class="cursor-pointer booking-component-input-guests" readonly="" />
            <div class="input-persons-placeholder input-persons-placeholder--main-search input-persons-placeholder--main-search--booking" data-max-persons="{$max_guests}">
                {embeddedSvg 'assets/img/svg/people-outline.svg',
                    class => 'booking-component__dropdown-guests-icon',
                    height => 20,
                    width => 18}
                <span class="booking-input-placeholder-guests">{$adultsCount}</span> {$adults_translation},
                <span class="booking-input-placeholder-children">{$childrenCount}</span> {$children_translation},
                <span class="booking-input-placeholder-pets">{$petsCount}</span> {$pets_translation}
            <img
                n:srcv="/assets/img/svg/down-arrow.svg"
                class="booking-component__action-dropdown-icon"
                width="10"
                height="10"
                alt="{_'arrow'}" />
            </div>

            <div class="panel-dropdown-content panel-dropdown-content--booking">
                <div class="qtyButtons">
                    <div class="qtyTitle">{_'Adults'}<span class="panel-dropdown-content__subtitle">{_'Age 18+'}</span></div>
                    <div class="qtyDec qtyDec--adults qtyDec--persons qtyDec--persons__booking" data-min="1"></div>
                    <input n:name="adults" type="text" id="adults_num" class="qtyInput" title="adults" value="{$adultsCount}">
                    <div class="qtyInc qtyInc--adults qtyInc--persons qtyInc--persons__booking" data-max="{$basicInfo->adult_count}"></div>
                </div>
                <div class="qtyButtons children">
                    <div class="qtyTitle">{_'Children'}<span class="panel-dropdown-content__subtitle">{_'Age under 17'}</span></div>
                    <div class="qtyDec qtyDec--children qtyDec--children__booking" data-min="0"></div>
                    <input n:name="children" type="text" id="children_num" class="qtyInput" title="children" value="{$childrenCount}">
                    <div class="qtyInc qtyInc--children qtyInc--children__booking" data-max="{$basicInfo->adult_count}"></div>
                </div>

                <div class="qtyButtons pets" n:if="$basicInfo->pets_allowed">
                    <div class="qtyTitle">{_'Pets'}<span class="panel-dropdown-content__subtitle">{_'Any pets'}</span></div>
                    <div class="qtyDec qtyDec--pets qtyDec--pets__booking" data-min="0"></div>
                    <input n:name="pets" type="text" id="pets_num" class="qtyInput" title="pets" value="{$petsCount}" data-label="{$pets_translation}">
                    <div class="qtyInc qtyInc--pets qtyInc--pets__booking" data-max="{$basicInfo->allowed_pet_count}"></div>
                </div>

                <a href="#" id="qtyButton-guests" class="button button--color-main button--apply float-right booking-dropdown-persons-close-btn">{_'Apply'}</a>
            </div>
        </div>
        {if $rule_fee_per_night !== null}
            {foreach $ownerServices as $service}
                {continueIf $service->owner_service_id !== App\Common\Enums\OwnerExtraServicesEnum::PER_PERSON}
                <div class="additional-surcharge-for-person-in-guest-list-block">
                    <p class="booking-component__action-note booking-component__action-note--additional-surcharge-for-person">
                        <img
                                n:srcv="/assets/img/svg/info-filled.svg"
                                class="booking-component__action-note-icon"
                                width="18"
                                height="18"
                                alt="{_'Secure payment'}" />
                        <span>{_'Additional fee per person applies from the %s guest', $rule_fee_per_night->fee_after_number_of_guests}: <strong>{$service->price} {$translator->translate($service->currency)} {$translator->translate(str_replace('_', ' ', $service->price_type))}</strong></span>
                    </p>
                </div>
            {/foreach}
        {/if}
    </div>
</div>

<div class="booking-component__section">
    <div class="booking-component__block booking-component__block--no-pb">
        <h3 class="booking-component__block-title">{_'Included in the price'}</h3>
        <p class="booking-component__block-subtitle booking-component__block-subtitle--no-mobile">{_'Additional services are included in price'}</p>

        <ul class="booking-component__pricing-list listing-features checkboxes--green">
            {foreach $ownerServices as $service}
                {if empty($service->price)}
                    <li>{$translator->translate($service->name)}</li>
                {/if}
            {/foreach}
            <li>{_'Rezijski troskovi'}</li>
            {foreach $priceIncludeFeatures as $key => $value}
                {if !empty($value)}
                    <li>{$translator->translate($key)}</li>
                {/if}
            {/foreach}
            {if !empty($basicInfo->price_include_notes)}
                <li>{$translator->translate($basicInfo->price_include_notes)}</li>
            {/if}
            {foreach $conciergesInPrice as $concierge}
                <li>{_$concierge->name}</li>
            {/foreach}
        </ul>
    </div>
</div>


{var $showMandatoryCharges = false}
{foreach $ownerServices as $service}
    {if !empty($service->price) && $service->service_type && $service->owner_service_id !== App\Common\Enums\OwnerExtraServicesEnum::PER_PERSON}
        {var $showMandatoryCharges = true}
    {/if}
{/foreach}

<div class="booking-component__block--ghost" n:if="!empty($extraCosts) && $showMandatoryCharges">
    <h3 class="booking-component__block-title">
        {_'Mandatory Surcharges'}
    </h3>
    <p class="booking-component__block-subtitle booking-component__block-subtitle--no-mobile">{_'Charged on the spot'}</p>
    <div class="booking-component__checkbox-list">
        {var $extraCost = 0}
        {foreach $ownerServices as $service}
            {continueIf $service->owner_service_id === App\Common\Enums\OwnerExtraServicesEnum::PER_PERSON}
            {if !empty($service->price) && $service->service_type}
            <div class="booking-component__checkbox-item checkboxes checkbox-as-box checkbox-as-box--active disabled-checkboxes">
                <div class="checkbox-top-part">
                    <input type="checkbox" id="{$translator->translate($service->name)}" class="disable-validation selected" checked disabled/>
                    <label for="{$translator->translate($service->name)}" class="booking-component__action-label booking-component__action-label--normal-height checkbox-as-box__label">
                        {$translator->translate($service->name)}
                    </label>
                </div>
                <div class="checkbox-bottom-part">
                    <span class="checkbox-bottom-label">
                        {$service->price} {$translator->translate($service->currency)}
                        {$translator->translate(str_replace('_', ' ', $service->price_type))}
                        {if $service->name === 'Pets'}
                            / {_'per pets'}
                        {/if}
                    </span>
                </div>
            </div>
            {php $extraCost = 1}
            {/if}
        {/foreach}
    </div>
</div>


{var $showOptionalCharges = false}
{var $isOnlyDepositInOwnerServices = false}
{var $isThereOptionalSurcharge = false}

{foreach $ownerServices as $service}
    {continueIf $service->owner_service_id === App\Common\Enums\OwnerExtraServicesEnum::PER_PERSON}
    {if !empty($service->price) && !$service->service_type}
        {var $showOptionalCharges = true}
    {/if}

    {if count($ownerServices) <= 1 && $service->name === 'Deposit'}
        {var $isOnlyDepositInOwnerServices = true}
    {/if}

    {if ($service->service_type === 0 || $service->service_type === null) && $service->name !== 'Deposit'}
        {var $isThereOptionalSurcharge = true}
    {/if}
{/foreach}

<div class="booking-component__block--ghost" n:if="!empty($extraCosts) && $showOptionalCharges && !$isOnlyDepositInOwnerServices && $isThereOptionalSurcharge">
    <h3 class="booking-component__block-title">
        {_'Optional Surcharges'}
    </h3>
    <p class="booking-component__block-subtitle booking-component__block-subtitle--no-mobile">{_'Charged on the spot'}</p>
    <div class="booking-component__checkbox-list">
    {var $extraCost = 0}
    {foreach $ownerServices as $service}
        {continueIf $service->owner_service_id === App\Common\Enums\OwnerExtraServicesEnum::PER_PERSON}
        {if !empty($service->price) && !$service->service_type && $service->name !== 'Deposit'}
        <div class="booking-component__checkbox-item checkboxes checkbox-as-box">
            <div class="checkbox-top-part">
                <input id="{$translator->translate($service->name)}" type="checkbox" class="disable-validation selected" />
                <label for="{$translator->translate($service->name)}" class="booking-component__action-label booking-component__action-label--normal-height checkbox-as-box__label">
                        {$translator->translate($service->name)}
                </label>
            </div>
            <div class="checkbox-bottom-part">
                <span class="checkbox-bottom-label">
                    {$service->price} {$translator->translate($service->currency)}
                    {$translator->translate(str_replace('_', ' ', $service->price_type))}
                </span>
            </div>
        </div>
        {php $extraCost = 1}
        {/if}
    {/foreach}
    </div>
</div>

<div class="booking-component__block--ghost booking-component__block--concierge" n:if="$basicInfo->partner === App\Common\Enums\PartnersEnum::PRIVATE && !empty($concierges) && $conciergesNotInPrice">
    <h3 class="booking-component__block-title">
        {_'Concierge services'}
    </h3>
    <p class="booking-component__block-subtitle booking-component__block-subtitle--no-mobile">{_'Charged on the spot'}</p>

    <div class="booking-component__checkbox-list">
        <div class="booking-component__checkbox-item checkboxes checkbox-as-box checkbox-as-box--concierge" n:foreach="$conciergesNotInPrice as $concierge">
            <div class="checkbox-top-part">
                <input type="checkbox" n:name="$concierge->product_concierge_type_id . App\Models\Entities\ProductConciergeEntity::PRODUCT_CONCIERGE_TYPE_ID" class="disable-validation selected" />
                <label n:name="$concierge->product_concierge_type_id . App\Models\Entities\ProductConciergeEntity::PRODUCT_CONCIERGE_TYPE_ID" class="booking-component__action-label booking-component__action-label--normal-height checkbox-as-box__label">
                        {_$concierge->name}
                </label>

                {var $svgPath = WWW_DIR . "/assets/img/svg/concierge/{$concierge->product_concierge_type_id}.svg"}
                {if file_exists($svgPath)}
                <img
                    src="/assets/img/svg/concierge/{$concierge->product_concierge_type_id}.svg"
                    class="booking-component__concierge-icon"
                    alt="{_$concierge->name}"
                    width="20"
                    height="20" />
                {/if}
            </div>
            <div class="checkbox-bottom-part">
                <span class="checkbox-bottom-label">
                    {if $concierge->price}
                        {$concierge->price|number:0} €
                    {else}
                        {_'Price on request/per day'}
                    {/if}
                </span>
            </div>
        </div>
    </div>
</div>
<div class="concierge-services-toggle-wrapper">
    <button class="concierge-services-toggle d-none">
        {_'Show all concierge'}
        {embeddedSvg 'assets/img/svg/double-down.svg',
            class => 'booking-component__show-all-concierge-icon',
            height => 16,
            width => 16}
    </button>
</div>

{if (isset($form[property_damage]) && count($form[property_damage]->items) >= 1 && array_key_exists('property_damage_insurance', $form[property_damage]->items)) || isset($presenter->getTemplate()->cancellationInsurancePrice) }
    <h2 class="booking-component__section-title booking-component__block-subtitle--no-mobile">
        {_'Select insurance options'}
    </h2>
{/if}

{if isset($form[property_damage]) && count($form[property_damage]->items) >= 1 && array_key_exists('property_damage_insurance', $form[property_damage]->items)}
    <div class="booking-component__section">
        <div class="booking-component__block booking-component__block--travel-insurance">
            <div class="insurance-left-side">
                <img src="/assets/img/allianz/allianz-damage-insurance.png" class="cancellation-travel-insurance-image"/>
                {embeddedSvg 'assets/img/allianz/allianz-white.svg',
                    class => 'booking-component__block-insurance-logo',
                    height => 28,
                    width => 110}
            </div>
            <div class="insurance-right-side">
                <h3 class="booking-component__block-title booking-component__block-title--insurance">{_'Add Allianz Property damage insurance for %s €', number_format( $presenter->getTemplate()->propertyDamageInsurancePrice, 2, ',', '.')}</h3>

                <div class="insurance-list-wrapper">
                    <div class="insurance-list-item">
                        {embeddedSvg 'assets/img/svg/check-outline.svg',
                        class => 'booking-component__block-insurance-list-icon',
                        height => 14,
                        width => 14}
                        {_'Even during vacation, accident happens'}
                    </div>
                    <div class="insurance-list-item">
                        {embeddedSvg 'assets/img/svg/check-outline.svg',
                        class => 'booking-component__block-insurance-list-icon',
                        height => 14,
                        width => 14}
                        {_'Say goodbye to cash security deposits on-site'}
                    </div>
                    <div class="insurance-list-item">
                        {embeddedSvg 'assets/img/svg/check-outline.svg',
                        class => 'booking-component__block-insurance-list-icon',
                        height => 14,
                        width => 14}
                        {_'Enjoy coverage for damages, up to a maximum of 1.250,00 €'}
                    </div>
                </div>

                <div class="booking-component__options booking-component__options--travel-insurance booking-component__form-col--field-title" data-error-option="{_'Please select one of the available options'}">
                    {foreach $form[property_damage]->items as $key => $label}
                        <div class="booking-component-black-radio booking-component-black-radio--insurance disable-when-loading {if $key === 'property_damage_insurance'} booking-component-black-radio--damage-insurance{else} booking-component-black-radio--deposit {/if}">
                            <input type="radio" name="damage-insurance" class="booking-form--radio insurance-input booking-component__form-radio--title damage-insurance-input" id="{$key}-1"/>
                            <label class="insurance-label booking-component__black-radio-label--title" for="{$key}-1">{$label}</label>
                        </div>
                    {/foreach}
                </div>

                <div class="booking-component__options booking-component__options--property-damage d-none">
                    {foreach $form[property_damage]->items as $key => $label}
                        <label n:name="property_damage:$key" class="booking-payment__option" for="{$key}">
                            <input class="booking-form--radio booking-form--radio-damage-insurance" id="{$key}" n:name="property_damage:$key">{$label}
                        </label>
                    {/foreach}
                </div>
                <div class="booking-component-insurance-footer">
                    <p class="booking-component-deposit-info">* {_'Deposits are refundable'}</p>
                    {if $language === 'de'}
                        {var $document_allianz = '/documents/allianz/Private_Haftpflichtversicherung_für_Sachschäden.pdf'}
                    {else}
                        {var $document_allianz = '/documents/allianz/Property_damage.pdf'}
                    {/if}

                    <a href="{$document_allianz}"
                        title="{_'Allianz - damage insurance'}"
                        target="_blank"
                        class="booking-component__anchor-allianz"
                        rel="noopener">
                        {_'Insurance conditions'}
                    </a>
                </div>
            </div>
        </div>
    </div>
{/if}

{if isset($presenter->getTemplate()->cancellationInsurancePrice)}
    <div class="booking-component__section">
        <div class="booking-component__block booking-component__block--cancellation-insurance">
            <div class="insurance-left-side">
                <img src="/assets/img/allianz/allianz-travel.jpg" class="cancellation-travel-insurance-image"/>
                {embeddedSvg 'assets/img/allianz/allianz-white.svg',
                    class => 'booking-component__block-insurance-logo',
                    height => 28,
                    width => 110}
            </div>
            <div class="insurance-right-side">
                <h3 class="booking-component__block-title booking-component__block-title--insurance">{_'Add Allianz Travel Cancellation Insurance for %s €', number_format($presenter->getTemplate()->cancellationInsurancePrice, 2, ',', '.')}</h3>

                <div class="insurance-list-wrapper">
                    <div class="insurance-list-item">
                        {embeddedSvg 'assets/img/svg/check-outline.svg',
                        class => 'booking-component__block-insurance-list-icon',
                        height => 14,
                        width => 14}
                        {_'At just 4.5% of your booking cost'}
                    </div>
                    <div class="insurance-list-item">
                        {embeddedSvg 'assets/img/svg/check-outline.svg',
                        class => 'booking-component__block-insurance-list-icon',
                        height => 14,
                        width => 14}
                        {_'Secure a 90% refund on the total amount paid'}
                    </div>
                    <div class="insurance-list-item">
                        {embeddedSvg 'assets/img/svg/check-outline.svg',
                        class => 'booking-component__block-insurance-list-icon',
                        height => 14,
                        width => 14}
                        {_'92% choose travel cancellation insurance - you should too'}
                    </div>
                </div>

                <div class="booking-component__options booking-component__options--cancellation-insurance booking-component__form-col--field-title">
                    <div class="booking-component-black-radio booking-component-black-radio--insurance booking-component-black-radio--insurance-yes disable-when-loading">
                        <input type="radio" class="insurance-input booking-component__form-radio--title"  id="cancellation_insurance_yes"/>
                        <label class="insurance-label booking-component__black-radio-label--title" for="cancellation_insurance_yes">{_'Add Travel Cancelation Insurance'}</label>
                    </div>

                    <div class="booking-component-black-radio booking-component-black-radio--insurance booking-component-black-radio--insurance-no disable-when-loading">
                        <input type="radio" class="insurance-input booking-component__form-radio--title" id="cancellation_insurance_no" />
                        <label class="insurance-label booking-payment__option--clear-insurance booking-component__black-radio-label--title" for="cancellation_insurance_no">{_"No Thanks"}</label>
                    </div>
                </div>


            <div class="booking-component__options booking-component__options--cancellation-insurance d-none">
                <label for="cancellation_insurance" class="booking-payment__option">
                    <input type="checkbox" class="disable-validation" n:name="cancellation_insurance" id="cancellation_insurance"/>
                </label>

                <label for="booking-payment__option--clear-insurance" class="booking-payment__option booking-payment__option--clear-insurance">
                    <input id="booking-payment__option--clear-insurance" type="hidden" class="disable-validation" id="cancellation_insurance_none" />
                </label>
            </div>



                {if $language === 'de'}
                    {var $document_allianz_cancellation = '/documents/allianz/Reiserücktrittsversicherung.pdf'}
                {else}
                    {var $document_allianz_cancellation = '/documents/allianz/Travel_cancellation.pdf'}
                {/if}

                <a href="{$document_allianz_cancellation}"
                    title="{_'Allianz - travel cancellation insurance'}"
                    class="booking-component__anchor-allianz booking-component__anchor-allianz--cancellation-insurance"
                    target="_blank"
                    rel="noopener">
                    {_'Insurance conditions'}
                </a>
            </div>
        </div>
    </div>
{/if}

<p class="booking-component__action-note">
    <img
        n:srcv="/assets/img/svg/secure.svg"
        class="booking-component__action-note-icon"
        width="18"
        height="18"
        alt="{_'Secure payment'}" />
    {_'Your personal details will be encrypted and kept confidential.'}
</p>

<div class="booking-page-step__next-btn--wrapper mobile-d-none">
    <button class="booking-page-step__next-btn button button--smaller button--color-anchor button--color-anchor--arrow-right" data-step="2">
        {_'Proceed to payment'}
        {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
            class => 'gold-btn-right-arrow',
            fill => '#252525',
            height => 16,
            width => 16
        }
    </button>
</div>
    
{var $schemaHouseName = $productName}
{var $schemaDescription = $text_heading}
{var $schemaNumberOfRooms = $basicInfo->bedrooms_count}
{var $schemaLongitude = $basicInfo->longitude}
{var $schemaLatitude = $basicInfo->latitude}
{var $schemaBathroomCount = $basicInfo->bathrooms_count}
{var $schemaPetsAllowed = $basicInfo->pets_allowed}
{var $schemaURL = $presenter->template->canonicalRelUrl}
{var $schemaCity = $basicInfo->subarea_name}
{var $schemaCountry = $basicInfo->country_code}
{var $review = $presenter->template->reviewItem}
{var $avgRating = isset($review->averageRating) ? $review->averageRating !== null ? $review->averageRating : 0 : 0}
{var $hasMap = "https://maps.google.com/maps?q={$schemaLatitude},{$schemaLongitude}&z=8&hl={$language}"}

<script type="application/ld+json">
    {
        "@context": "http://schema.org/",
        "@type": {$avgRating > 0 ? ["Product","Accommodation"] : "Accommodation"},
        "name": {$schemaHouseName},
        "description": {$schemaDescription},
        "image": {$pictures|first},
        "brand": {
            "@type": "Brand",
            "name": "VillasGuide"
        },
        {if $avgRating > 0}
        "aggregateRating": {
            "@type": "AggregateRating",
            "bestRating": "5",
            "worstRating": "1",
            "ratingValue": {$review->averageRating},
            "reviewCount": {$review->totalReviews}
        },
        {if $review->reviews}
        "review": [
            {var $validReviewCount = 0}
            {foreach $review->reviews as $reviewItem}
                {if $reviewItem->averageRating}
                    {var $validReviewCount = $validReviewCount + 1}
                {/if}
            {/foreach}
            {var $currentIndex = 0}
            {foreach $review->reviews as $reviewItem}
                {if $reviewItem->averageRating}
                    {var $currentIndex = $currentIndex + 1}
                    {
                        "@type": "Review",
                        "reviewRating": {
                            "@type": "Rating",
                            "ratingValue": {$reviewItem->averageRating},
                            "bestRating": "5",
                            "worstRating": "1"
                        },
                        "author": {
                            "@type": "Person",
                            "name": {$reviewItem->reviewerName}
                        },
                        {if $reviewItem->reviewDate}
                        "datePublished": {$reviewItem->reviewDate->format('Y-m-d')},
                        {/if}
                        "reviewBody": {$reviewItem->comment->commentEn ? $reviewItem->comment->commentEn : $reviewItem->comment->originalComment}
                    }{if $currentIndex < $validReviewCount},{/if}
                {/if}
            {/foreach}
        ],
        {/if}
        {/if}
        "numberOfRooms": {$schemaNumberOfRooms},
        "numberOfBathroomsTotal": {$schemaBathroomCount},
        "numberOfBedrooms": {$schemaNumberOfRooms},
        "petsAllowed": {$schemaPetsAllowed},
        "url": {$schemaURL},
        {if $schemaLatitude}
            "hasMap": {=json_encode($hasMap, JSON_UNESCAPED_SLASHES)|noescape},
        {/if}
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": {$schemaLatitude},
            "longitude": {$schemaLongitude}
        },
        "address": {
            "@type": "PostalAddress",
            "addressLocality": {$schemaCity},
            "addressCountry": {$schemaCountry}
        }{if !$avgRating > 0},{/if}
        {if !$avgRating > 0}
        "telephone": "+43 720 11 6564"
        {/if}
    }
</script>

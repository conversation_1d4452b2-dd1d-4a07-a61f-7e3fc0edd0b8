<?php declare(strict_types = 1);

namespace App\Modules\Front\Presenters;

use App\Modules\CallCenter\Factories\IFeaturedObjectsHomepageControlFactory;
use App\Modules\CallCenter\Factories\IFrontSearchFormFactory;
use App\Modules\CallCenter\Factories\IOfferedObjectsHomepageControlFactory;
use App\Modules\CallCenter\Factories\ITestimonialControlFactory;
use App\Modules\CallCenter\Factories\IVpiHomepageControlFactory;
use App\Modules\CallCenter\Factories\IVpiWizardControlFactory;
use App\Modules\Front\Controls\FeaturedObjectsHomepageControl;
use App\Modules\Front\Controls\OfferedObjectsHomepageControl;
use App\Modules\Front\Controls\TestimonialControl;
use App\Modules\Front\Controls\VpiHomepageControl;
use App\Modules\Front\Controls\VpiWizardFormControl;
use App\Modules\Front\Controls\VprHomepageControl;
use App\Modules\Front\Factories\IVprHomepageControlFactory;
use App\Modules\Front\Forms\FrontSearchForm;
use App\Modules\Front\Services\FrontSearchAutocompleteService;
use App\Repositories\ProductRepository;
use App\Repositories\VpiImagesRepository;

class HomepagePresenter extends BasePresenter
{

	private VpiImagesRepository $vpiImagesRepository;

	private FrontSearchAutocompleteService $frontSearchAutocompleteService;

	private IVpiWizardControlFactory $vpiWizardControlFactory;

	private IVpiHomepageControlFactory $vpiHomepageControlFactory;

	private IFeaturedObjectsHomepageControlFactory $featuredObjectsHomepageControlFactory;

	private ITestimonialControlFactory $testimonialControlFactory;

	private IFrontSearchFormFactory $frontSearchFormFactory;

	private IVprHomepageControlFactory $vprHomepageControlFactory;

	private array $countriesMenu;

	private ProductRepository $productRepository;

	private IOfferedObjectsHomepageControlFactory $offeredObjectsHomepageControlFactory;

	public function __construct(
		array $countriesMenu,
		IFrontSearchFormFactory $frontSearchFormFactory,
		IVpiWizardControlFactory $vpiWizardControlFactory,
		VpiImagesRepository $vpiImagesRepository,
		FrontSearchAutocompleteService $frontSearchAutocompleteService,
		IVpiHomepageControlFactory $vpiHomepageControlFactory,
		IFeaturedObjectsHomepageControlFactory $featuredObjectsHomepageControlFactory,
		ITestimonialControlFactory $testimonialControlFactory,
		IVprHomepageControlFactory $vprHomepageControlFactory,
		ProductRepository $productRepository,
		IOfferedObjectsHomepageControlFactory $objectsHomepageControlFactory,
	)
	{
		$this->vpiImagesRepository = $vpiImagesRepository;
		$this->frontSearchAutocompleteService = $frontSearchAutocompleteService;
		$this->vpiWizardControlFactory = $vpiWizardControlFactory;
		$this->vpiHomepageControlFactory = $vpiHomepageControlFactory;
		$this->featuredObjectsHomepageControlFactory = $featuredObjectsHomepageControlFactory;
		$this->testimonialControlFactory = $testimonialControlFactory;
		$this->frontSearchFormFactory = $frontSearchFormFactory;
		$this->vprHomepageControlFactory = $vprHomepageControlFactory;
		$this->countriesMenu = $countriesMenu;
		$this->productRepository = $productRepository;
		$this->offeredObjectsHomepageControlFactory = $objectsHomepageControlFactory;
	}


	public function renderDefault(?string $language = null)
	{
		if ($language !== null) {
			$this->language = $language;
		}

		$this->setView('default');
		unset($this->getPresenter()->hubspotContactData->wizard_interes_region_checkbox);

		$this->template->max_guests = $this->productRepository
			->getMaxPeopleCountInPublishedSupportedVillas($this->countriesMenu['novasol_code']);
		$this->template->images = $this->vpiImagesRepository->getImagesCategoryIdLinkPairs();

		$this->setMetaTags(
			$this->translator->translate('Pronađite vašu vilu u Hrvatskoj!'),
			$this->translator->translate('Pronađite vašu vilu u Hrvatskoj! - Više od 4.000 vila i kuća za odmor na jednom mjestu'),
			$this->getHttpRequest()->getUrl()->getHostUrl() . '/assets/img/home-hero.jpg'
		);
	}


	protected function createComponentHomepageSearchForm(): FrontSearchForm
	{
		$form = $this->frontSearchFormFactory->create();
		$form->onSearch[] = function ($values) {
			$this->redirect('SearchResults:Results', $values);
		};

		return $form;
	}

	protected function createComponentVpiWizardControl(): VpiWizardFormControl
	{
		$control = $this->vpiWizardControlFactory->create();
		$control->onSuccess[] = function () {
			$this->template->vpiWizardSuccess = TRUE;
			$this->redrawControl('vpiwizard-form-response');
			$this->template->trackingGoal = TRUE;
			$this->template->category = 'inquiry';
			$this->template->action = 'action';
			$this->redrawControl('trackingGoal');
		};

		return $control;
	}

	protected function createComponentVpiHomepageControl(): VpiHomepageControl
	{
		return $this->vpiHomepageControlFactory->create();
	}

	protected function createComponentVprHomepageControl(): VprHomepageControl
	{
		return $this->vprHomepageControlFactory->create();
	}


	public function actionAutoComplete(?string $q)
	{
		$this->sendJson($this->frontSearchAutocompleteService->autoComplete($q));
	}


	protected function createComponentFeaturedObjectsHomepageControl(): FeaturedObjectsHomepageControl
	{
		return $this->featuredObjectsHomepageControlFactory->create();
	}

	protected function createComponentOfferedObjectsHomepageControl(): OfferedObjectsHomepageControl
	{
		return $this->offeredObjectsHomepageControlFactory->create('offered');
	}

	protected function createComponentTestimonialControl(): TestimonialControl
	{
		return $this->testimonialControlFactory->create($this->translator, 'front');
	}

}

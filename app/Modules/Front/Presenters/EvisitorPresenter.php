<?php declare(strict_types = 1);

namespace App\Modules\Front\Presenters;

use App\Common\EVisitor\EVisitorService;
use App\Common\EVisitor\SyncEVisitorDataService;
use Wedo\Api\Exceptions\ResponseException;

class EvisitorPresenter extends BasePresenter
{

	private EVisitorService $eVisitorService;

	private SyncEVisitorDataService $syncEVisitorDataService;

	public function __construct(
		EVisitorService $eVisitorService,
		SyncEVisitorDataService $syncEVisitorDataService
	)
	{
		parent::__construct();
		$this->eVisitorService = $eVisitorService;
		$this->syncEVisitorDataService = $syncEVisitorDataService;
	}

	public function actionDefault(?string $hash = null)
	{
		if (!$hash) {
			$this->redirect('Homepage:default', ['language' => $this->language]);
		}

		$this->template->hash = $hash;

		try {
			$this->eVisitorService->setTranslator($this->translator);
			$this->template->evisitor_data = $this->eVisitorService->getEvisitorReservationData($hash);
			$this->template->error_message = null;
		} catch (ResponseException | \Throwable $exception) {
			$errorMessage = $this->translator->translate(
				$exception instanceof ResponseException
					? $exception->getMessage()
					: $this->translator->translate('evisitor_reservation_error')
			);
			$this->logger->error($exception->getMessage());
			$this->template->error_message = $errorMessage;
			$this->template->evisitor_data = null;
		}
	}

	public function actionConfirmEvisitorData()
	{
		$success = false;
		$errorMessage = null;
		$isConfirmed = false;

		if ($this->getHttpRequest()->getMethod() !== 'POST') {
			$this->sendJson([
				'success' => false,
				'error_message' => 'wrong http method',
				'is_confirmed' => false,
			]);
		}

		$postData = json_decode($this->getHttpRequest()->getRawBody());

		try {
			$isConfirmed = $this->eVisitorService->confirmEvisitorData($postData->hash, json_encode($postData->data));
			$success = true;
		} catch (ResponseException | \Throwable $exception) {
			$errorMessage = $this->translator->translate(
				$exception instanceof ResponseException
					? $exception->getMessage()
					: $this->translator->translate('evisitor_confirm_reservation_error')
			);
			$this->logger->error($exception->getMessage());
		}

		if ($isConfirmed) {
			$this->eVisitorService->updateEvisitorInfoFilledOut($postData->hash);
			$this->syncEVisitorDataService->sendConfirmationMail($postData->hash);
		}

		$this->sendJson([
			'success' => $success,
			'error_message' => $errorMessage,
			'is_confirmed' => $isConfirmed,
		]);
	}

}

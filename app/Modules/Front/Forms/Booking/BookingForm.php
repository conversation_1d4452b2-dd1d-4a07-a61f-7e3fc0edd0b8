<?php declare(strict_types = 1);

namespace App\Modules\Front\Forms\Booking;

use App\Common\Enums\PartnersEnum;
use App\Common\Enums\PaymentOptionEnum;
use App\Common\Enums\PaymentTypeEnum;
use App\Common\Enums\PropertyInsuranceOptionsEnum;
use App\Common\EnvironmentDetector;
use App\Common\Forms\BaseForm;
use App\Common\Payment;
use App\Models\Entities\ProductConciergeEntity;
use App\Models\Entities\ProductDetailEntity;
use App\Modules\Front\Logic\BookingFormLogic;
use App\Modules\Front\Logic\BookingLogic;
use App\Repositories\ProductDistributionChannelRepository;
use Nette\ComponentModel\IComponent;
use Nette\Forms\Form;
use Psr\Log\LoggerInterface;

class BookingForm extends BaseForm
{

	/**
	 * @var callable
	 */
	public $onBookingFailed;

	/**
	 * @var callable
	 */
	public $onRedirect;

	/**
	 * @var callable
	 */
	public $onBookingStepOneCompleted;

	private Payment $payment;

	private BookingLogic $logic;

	private BookingFormLogic $bookingFormLogic;

	public const CANCELLATION_INSURANCE_PRICE = 'cancellation_insurance_price';

	public const PROPERTY_DAMAGE_INSURANCE_PRICE = 'property_damage_insurance_price';

	private LoggerInterface $logger;

	private EnvironmentDetector $environmentDetector;

	private ProductDistributionChannelRepository $distributionChannelRepository;

	public function __construct(
		Payment $payment,
		BookingLogic $logic,
		BookingFormLogic $bookingFormLogic,
		LoggerInterface $logger,
		EnvironmentDetector $environmentDetector,
		ProductDistributionChannelRepository $distributionChannelRepository,
		private ?float $price = 0.0,
		private ?float $priceWithoutDiscount = 0.0
	)
	{
		parent::__construct();
		$this->payment = $payment;
		$this->logic = $logic;
		$this->bookingFormLogic = $bookingFormLogic;
		$this->logger = $logger;
		$this->environmentDetector = $environmentDetector;
		$this->distributionChannelRepository = $distributionChannelRepository;
	}


	public function attached(IComponent $presenter): void
	{
		parent::attached($presenter);
		unset($this['submit']);
	}


	public function prepare(): void
	{
		$titles = ['Mr.', 'Mrs'];
		$parameters = $this->getPresenter()->getParameters();
		$productId = (int) $parameters['productId'];
		$action = $this->getPresenter()->presenter->action;
		$language = $this->getPresenter()->getParameter('language');
		$countries = $this->bookingFormLogic->getAllCountriesByLanguage($language);
		$product = $this->logic->getProduct($productId);
		$this->setAction($this->getPresenter()->presenter->link($action, $parameters));
		$this->addSelect('title', 'Title', $titles);
		$this->addText('firstname')->setMaxLength(60);
		$this->addText('lastname')->setMaxLength(60)->setRequired();
		$this->addEmail('email')->setMaxLength(90)->setRequired();
		$this->addEmail('email_verify')
			->addRule(Form::EQUAL, 'Wrong e-mail!', $this['email'])
			->setRequired();
		$this->addText('phone')->setMaxLength(90)->setRequired();
		$this->addSelect('custcountry', 'Country', $countries)->setTranslator(NULL)->setRequired();
		$this->addCheckbox('option_booking', 'Create a reservation as an option');

		$propertyDamageBlockOptions = [];

		$premiumPrice = $this->bookingFormLogic->getInsurance()->calculateDamageInsuranceManually();
		$this->getPresenter()->template->propertyDamageInsurancePrice = $premiumPrice;

		$propertyDamageSelectedType = null;

		if ($product->partner === PartnersEnum::PRIVATE) {
			$productDetail = $this->bookingFormLogic->getProductDetailRepository()->getBy([ProductDetailEntity::PRODUCT_ID => $product->id]);

			if ($productDetail instanceof ProductDetailEntity) {
				$propertyDamageSelectedType = $productDetail->property_insurance_options;
			}

			if ($propertyDamageSelectedType !== PropertyInsuranceOptionsEnum::DEPOSIT) {
				$propertyDamageInsuranceLabelOption = $this->getTranslator()->translate('Add property damage insurance');
				$propertyDamageBlockOptions['property_damage_insurance'] = $propertyDamageInsuranceLabelOption;
			}
		}

		if ($propertyDamageSelectedType !== PropertyInsuranceOptionsEnum::ALLIANZ
			&& $propertyDamageSelectedType !== PropertyInsuranceOptionsEnum::ALLIANZ_DEPOSIT_MANDATORY) {
			$deposit = $this->bookingFormLogic->getProductDeposit($productId);

			if ($deposit !== null) {
				$propertyDamageBlockOptions['deposit'] = $this->getTranslator()->translate('Deposit %s€ on arrival', $deposit->price);
				$this->getPresenter()->template->propertyDepositPrice = $deposit->price;
			}
		}

		if (!empty($propertyDamageBlockOptions)) {
			$this->addRadioList('property_damage', '', $propertyDamageBlockOptions);
			$this->addHidden(self::PROPERTY_DAMAGE_INSURANCE_PRICE, $premiumPrice);
		}

		$isObjectFromPartner = $this->distributionChannelRepository->isObjectFromPartner($productId, PartnersEnum::GECO);

		if ($product->partner !== PartnersEnum::NOVASOL && !$isObjectFromPartner) {
			$cancellationPremiumPrice = $this->bookingFormLogic->getInsurance()->calculateCancellationInsuranceManually($this->price);
			$this->getPresenter()->template->cancellationInsurancePrice = $cancellationPremiumPrice;
			$this->addHidden(self::CANCELLATION_INSURANCE_PRICE, $cancellationPremiumPrice);
			$this->addCheckbox('cancellation_insurance', 'Travel cancellation insurance');
		}

		$this->addRadioList('first_payment_from_two_installments', '', [
			PaymentTypeEnum::BANK_TRANSFER => 'Bank Transfer',
			PaymentTypeEnum::CREDIT_CARD => 'Credit card',
		]);

		$this->addRadioList('second_payment_from_two_installments', '', [
			PaymentTypeEnum::BANK_TRANSFER => 'Bank Transfer',
			PaymentTypeEnum::CREDIT_CARD => 'Credit card',
			PaymentTypeEnum::MONEY => 'Money',
		]);

		$this->addRadioList('full_payment', '', [
			PaymentTypeEnum::BANK_TRANSFER => 'Bank Transfer',
			PaymentTypeEnum::CREDIT_CARD => 'Credit card',
		]);

		$this->addRadioList('payment_option', '', [
			PaymentOptionEnum::PAYMENT_TWO_INSTALLMENTS => 'Option 1',
			PaymentOptionEnum::FULL_PAYMENT => 'Option 2',
		]);

		$this->addHidden('credit_card_ota', 0);
		$this->setDefaults($parameters);

		$this->addHidden('offerId', isset($parameters['offerId']) ? (int) $parameters['offerId'] : 0);
		$this->addText('adults', $parameters['adults']);

		if (isset($parameters['children'])) {
			$children = $parameters['children'];
		} elseif (isset($parameters['_children'])) {
			$children = $parameters['_children'];
		} else {
			$children = '0';
		}

		$this->addText('children', $children);
		$this->addText('pets', !empty($parameters['pets']) ? (string) $parameters['pets'] : '0');
		$this->addHidden('arrive', $parameters['arrive']);
		$this->addHidden('departure', $parameters['departure']);

		if ($product->partner === PartnersEnum::PRIVATE) {
			$this->addTextArea('request_from_guests', 'Request from guest')
				->setHtmlAttribute('placeholder', 'Enter your comment and wishes')
				->setHtmlAttribute('maxlength', 255);

			$concierges = $this->logic->findProductConcierges($productId);

			foreach ($concierges as $concierge) {
				$this->addCheckbox($concierge->id . ProductConciergeEntity::PRODUCT_CONCIERGE_TYPE_ID, $concierge->name);
			}
		}

		if ($product->partner === PartnersEnum::NOVASOL) {
			$this->addText('street');
			$this->addText('street_number');
			$this->addText('city');
			$this->addText('zip');
		}

		$distributionChannels = $this->distributionChannelRepository->findByProductId($productId);
		$this->addSelect('product_distribution_channel_id', 'Distribution channels', $distributionChannels)
			->setPrompt('Select...')
			->setTranslator(NULL);

		$this->addSubmit('confirm', 'Confirm')->onClick[] = [$this, 'confirm'];
		$this->onSubmit[] = [$this, 'confirm'];
	}


	public function confirm(): void
	{
		$this->logger->info('Booking form submitted');
		$productId = (int) $this->getPresenter()->getParameter('productId');
		$values = $this->getValues(TRUE);

		if (empty($values['payment_option'])) {
			$this->logger->info('Booking failed, no payment option selected');
			$this->onBookingFailed($productId);
		}

		$host = $this->getPresenter()->getHttpRequest()->getUrl()->host;
		$test = $this->environmentDetector->getTestParameter();
		$values = $this->logic->setValues($productId, $values, $test);

		if ($values === null) {
			$this->logger->info('Booking failed, no values');
			$this->onBookingFailed($productId);
		}

		$firstRequestParameters = $this->logic->setFirstBookingRequestEntity($productId, $values);

		if ($values['option_booking']) {
			$firstRequestParameters->type = 'option';
		}

		$reservationId = $this->logic->getBookingResolver()->resolveFirstRequest($productId, $firstRequestParameters);

		if (empty($reservationId)) {
			$this->logger->info('Booking failed, no reservation ID from first booking request');
			$this->onBookingFailed($productId);
		}

		$lang = strtoupper($this->getPresenter()->getParameter('language'));

		if (!empty($values['credit_card'])) {
			$returnUrls = $this->setReturnUrls($host, $reservationId, $productId, $values);

			if ($productId === 20105) {
				$test = 'false';
			}
			$this->payment->makepayment($values, $reservationId, $returnUrls, $lang, $test);
			$this->logger->info('Payment gateway redirect');
			$this->onRedirect();
		}

		$this->logger->info('Booking step one completed');
		$values['price_without_discount'] = (int) $this->priceWithoutDiscount;
		$this->onBookingStepOneCompleted($reservationId, (int) $productId, (int) $values['price_eur'], $values['custcountry_id'], $values);
	}


	private function setReturnUrls(string $host, int $reservationId, int $productId, array $values): array
	{
		$returnUrls = [];
		$scheme = $this->getPresenter()->getHttpRequest()->getUrl()->getScheme();
		$countryId = (int) $values['custcountry_id'] > 0 ? (int) $values['custcountry_id'] : null;
		//http headers
		$returnUrls['returnUrl'] = $scheme . '://' . $host .
			$this->getPresenter()->link(
				'BookingConfirmation:default',
				$reservationId,
				$productId,
				(int) $values['price_eur'],
				$countryId,
				$values
			);
		$returnUrls['returnErrorURL'] = $scheme . '://' . $host
			. $this->getPresenter()->link('BookingConfirmation:somethingWentWrong', $productId);
		$lang = $this->getPresenter()->getParameter('language');
		$returnUrls['cancelURL']
			= $scheme . '://' . $host
			. $this->getPresenter()->link(
				'Booking:default',
				$lang,
				$values['firstname'],
				$values['lastname'],
				$values['email'],
				$values['arrive_dmY'],
				$values['departure_dmY'],
				(int) $values['adults'],
				(int) $values['children'],
				(int) $values['pets'],
				$values['offerId'],
				$productId,
			);

		return $returnUrls;
	}

}

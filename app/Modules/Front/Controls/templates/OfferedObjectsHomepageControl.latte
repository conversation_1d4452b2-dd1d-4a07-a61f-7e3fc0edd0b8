{varType App\Models\Entities\FeaturedBlockObjectEntity $villa}
{if (!empty($objects))}
    <section class="block-featured background-white">
        <div class="container container--carousel container--carousel--spo">
            <div class="row">
                <div class="col-md-12 col-xl-4">
                    <h3 class="headline smaller">{_'Special offers on very special villas'|firstUpper}
                        <span>{_'Find your ideal villa at a great price in our special offer collection.'|firstUpper}</span>
                    </h3>
                </div>
                <div class="col-md-12 col-xl-8">
                    <div class="grid-thumbnail grid-thumbnail--regions keen-block--disable-mobile keen-block--enable-dots-line">
                        <div class="listing-items-similar listing-items-grid keen-slider keen-slider--disable-mobile listing-items-carousel--spo">
                            {foreach $objects as $villa}

                                {breakIf $iterator->counter > 10}
                                {var $url = $presenter->link(':Front:SingleVilla:default', ['productId' => $villa->id])}
                                <div class="listing-item-container list-layout keen-slider__slide">
                                    <div class="listing-item">
                                        <div class="listing-item-image listing-item-image--grid">
                                            {if $villa->discount_percentage > 0}
                                            <div class="special-offer-label special-offer-label--left-position">
                                                <div class="special-offer-left">
                                                    {embeddedSvg 'assets/img/svg/discount.svg',
                                                    class => 'special-offer--left__icon',
                                                    height => 16,
                                                    width => 16}
                                                    <div class="special-offer-left__label">
                                                        <span>{_'Special'}</span>
                                                        <span>{_'Offer'}</span>
                                                    </div>
                                                </div>
                                                <div class="special-offer-right">
                                                    <span>{_'Up to'}</span>
                                                    <span class="special-offer--discount">{$villa->discount_percentage}%</span>
                                                </div>
                                            </div>
                                            {/if}
                                            <a target="_blank" class="listing-item-content__anchor" href="{$url}" aria-label="{_'View villa details'}">
                                                <img
                                                    src="{$presenter->getTemplate()->image_placeholder|nocheck}"
                                                    class="lazyload"
                                                    data-src="{str_replace("1024/", "400/", $pictures[$villa->id][0])}"
                                                    alt="{$translator->translate($villa->object_type)} {$villa->location}"/>
                                            </a>
                                        </div>
                                        <div class="listing-item-content {if $villa->new_object_on_vg} new-object{/if}">
                                            <a target="_blank" class="listing-item-content__anchor" href="{$url}" aria-label="{_'View villa details'}"></a>
                                            <div class="listing-item-inner">
                                                <div class="new-object-label--so">
                                                    <div class="new-object-label" n:if="$villa->new_object_on_vg">
                                                        <span>{_'New object on portal'|firstUpper}</span>
                                                    </div>
                                                </div>
                                                <div class="listing-item-inner-top listing-item-inner-top--so">

                                                    <p class="listing-item-title listing-item-title--so">
                                                        {if empty($villa->heading_in_listing)}
                                                            {$villa->name}
                                                        {else}
                                                            {$villa->heading_in_listing}
                                                        {/if}
                                                    </p>
                                                    <span class="listing-stars listing-start--so">
                                                        <i n:for="$stars_count = 0; $stars_count < $villa->quality; $stars_count++" class="listing-stars__icon--star"></i>
                                                    </span>
                                                    <span class="listing-item-location">
                                                        {ucfirst($translator->translate($villa->subarea))}, {ucfirst($translator->translate($villa->area))} ({ucfirst($translator->translate($villa->country))})
                                                    </span>
                                                     <div class="own-review-badge-simple--so">
                                                        {control reviewControl:review, productId: $villa->id, showComments: false, showLabel: false, showLabelInside: true, position: 1, shortVersion: true}
                                                    </div>
                                                    <p class="listing-item-price-info">
                                                        {_'from'} {number_format($villa->min_price, 0, ',', '.')} € / {_'week'}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {if $iterator->counter > 9 || $iterator->last}
                                    <div class="listing-item-container list-layout keen-slider__slide">
                                        <div class="listing-item">
                                            <div class="listing-item__spo-last">
                                                <p class="listing-item__spo-label">{_'our best prices just for you'}</p>
                                                <a href="{$presenter->link('Page:Internal', 'Special offer villas in Croatia', $presenter->getParameter('language'))}" class="button button--smaller button--color-anchor button--color-anchor--arrow-right listing-item__spo-btn">
                                                    {_'Show all special offers'}
                                                    {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
                                                        class => 'gold-btn-right-arrow',
                                                        fill => '#252525',
                                                        height => 16,
                                                        width => 16
                                                    }
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {/if}

                            {/foreach}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="listing-item__spo-btn--mobile-wrapper">
            <a href="{$presenter->link('Page:Internal', 'Special offer villas in Croatia', $presenter->getParameter('language'))}" class="button button--smaller button--color-anchor button--color-anchor--arrow-right listing-item__spo-btn listing-item__spo-btn--mobile">
                {_'Show all Special Offers'}
                {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
                    class => 'gold-btn-right-arrow',
                    fill => '#252525',
                    height => 16,
                    width => 16
                }
            </a>
        </div>
    </section>
{/if}

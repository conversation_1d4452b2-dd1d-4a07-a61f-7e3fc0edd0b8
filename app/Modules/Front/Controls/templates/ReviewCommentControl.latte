{varType App\Common\PropertyHub\Reviews\Entities\ReviewMessageItemEntity $review}

<div class="own-review-comment-wrapper">
    <div class="own-review-comment-header">
        <span class="own-review-comment">{$review->reviewerName}</span>
        {if isset($review->averageRating) && $review->averageRating}
            <div class="own-review-comment-rating-wrapper">
                {embeddedSvg 'assets/img/svg/review-star.svg',
                class => '',
                fill => '#000',
                height => 18,
                width => 18}
                <span class="own-review-comment-score">{$review->averageRating|number:2}</span>
            </div>
        {/if}
    </div>
    {if isset($review->reviewerCountry) && $review->reviewerCountry}
        <div class="own-review-comment-location">
            <img src="/assets/img/svg/flags/{$review->reviewerCountry|lower}.svg"
                 class="own-review-comment-location-country-flag"
                 alt="{$review->reviewerCountry}">
            {if isset($review->reviewerCountryTranslations) && !empty($review->reviewerCountryTranslations[$presenter->getParameter('language')])}
                {var $reviewerCountry = $review->reviewerCountryTranslations[$presenter->getParameter('language')]}
            {else}
                {var $reviewerCountry = $review->reviewerCountry}
            {/if}

            <span class="own-review-comment-location-country-name">{$reviewerCountry|firstUpper}</span>
        </div>
    {/if}

    {var $language = 'comment' . ucfirst($presenter->language)}
    <p class="own-review-comment-text"
       data-isoriginal="false"
       data-original="{$review->comment->originalComment}"
       data-translated="{$review->comment->$language}"
       data-comment="{$review->comment->$language}">
        {$review->comment->$language ?? $review->comment->originalComment}
    </p>

    <button
            data-read-more="{_'Read more'}"
            data-read-less="{_'Read less'}"
            class="own-review-comment-read-more-btn"
            aria-label="{_'Read whole comment'}">
        {_'Read more'}
    </button>

    <div class="own-review-comment__translated-label{if $review->comment->$language} d-block{else} d-none{/if}">
        <span>{_'Translated with'|firstUpper}</span>
        {embeddedSvg 'assets/img/svg/google-translate.svg',
        height => 13,
        width => 13}
    </div>
    {if $review->comment->$language}
    <a role="button"
       href="#"
       aria-label="{_'Show original'|firstUpper}"
       data-show-translation="{_'Show translation'|firstUpper}"
       data-show-original="{_'Show original'|firstUpper}"
       class="own-review-comment__show-translation {if 'isModal' == 'isModal'} ph-review-comment__show-translation--modal{/if}"
       href="#">
        {_'Show original'|firstUpper}
    </a>
    {/if}

    <span class="own-review-comment-date">{$review->reviewDate|date:'d.m.Y'}</span>
</div>
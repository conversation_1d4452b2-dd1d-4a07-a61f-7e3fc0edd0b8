{var $lightboxNum = 1}
{varType App\Models\Entities\custom\OfferItemEntity $product}
{varType App\Models\Entities\GuestInfoEntity $guest}
{var $products_count = count($products)}

<div class="listing-list">
    <h2 class="listing-list__title">
        {if $products_count == 1}
            {_'Imamo <strong>%s</strong> primamljivu ponudu za vas', $products_count|noescape}
        {elseif $products_count > 1 && $products_count < 5}
            {_'Imamo <strong>%s</strong> primamljive ponude za vas', $products_count|noescape}
        {else}
            {_'Imamo <strong>%s</strong> primamljivih ponuda za vas', $products_count|noescape}
        {/if}
    </h2>
    {foreach $products as $product}
        {if !$uniqueTermin}
            <h3 class="listing-list__title-dates">{_'%s - %s',$product->arrival, $product->departure}</h3>
        {/if}
        <div class="listing-item-container list-layout">
            <div class="listing-item">
                {if !empty($product->thumbnail_picture)}
                <div class="listing-item-image">
                    <div class="listing-lazy-images mfp-gallery-container keen-slider">
                        {foreach $product->pictures as $picture}
                            <div class="keen-slider__slider keen-slider__slide--fade"
                                 data-slide="0">
                                <a
                                    {if $product->is_branded_gallery}
                                        href="{str_replace("1024/", "branded/1024/", $picture)}"
                                    {else}
                                        href="{$picture}"
                                    {/if}
                                   data-exthumbimage="{str_replace("1024/", "400/", $picture)}"
                                   class="mfp-gallery" rel="lightbox{$lightboxNum}">
                                    <img
                                            src="{$presenter->getTemplate()->image_placeholder|nocheck}"
                                            data-src="{str_replace("1024/", "400/", $picture)}"
                                            class="lazyload"
                                            alt=""/>
                                </a>
                            </div>
                        {/foreach}
                        <div class="keen-prev keen-arrow"></div>
                        <div class="keen-next keen-arrow"></div>
                    </div>
                    {*<a href="#" class="listing-lazy-images__anchor">{_'Gallery'}</a>*}
                </div>
                {/if}
                <div class="listing-item-content listing-item__info">
                    <div class="listing-location-container">
                        <span class="listing-item__location">{ucfirst($product->areas->destination)}, {ucfirst($translator->translate($product->areas->region))} ({ucfirst($translator->translate($product->areas->country))})</span>
                    </div>
                    <div class="listing-title-container">
                        <a target="_blank" href="{$presenter->link(':Front:SingleVilla:default',
                        $presenter->language,
                        $product->info->product_id,
                        $guest->contact_firstname,
                        $guest->contact_lastname,
                        $guest->contact_email,
                        $product->arrival,
                        $product->departure,
                        $product->prices->price !== null ? round($product->prices->price) : null,
                        $guest->adults_count,
                        $guest->children_count,
                        $guest->pets_count,
                        $guest->hubspot_deal_id
                        )}"
                        class="listing-item__title">{if !empty($product->info->name)}{$product->info->name}{else}{_'in'} {$product->areas->destination}{/if}
                        </a>
                        <div class="listing-item-stars">
                                {for $stars_count = 0; $stars_count < $product->info->quality; $stars_count++}
                                    <i class="listing-item-stars__icon--star"></i>
                                {/for}
                            </div>
                    </div>

                    <ul class="main-features">
                        {if !empty($product->info->house_unit_size)}
                            <li class="listing-surface">
                                {embeddedSvg 'admin_assets/images/svg/home.svg',
                                class => 'listing-item-features__icon listing-item-features__icon--home',
                                fill => '#252525',
                                height => 20,
                                width => 20}
                                <strong>{$product->info->house_unit_size}m<sup>2</sup></strong>
                            </li>
                        {/if}

                        {if !empty($product->info->bedrooms_count)}
                            <li class="listing-rooms">
                                {embeddedSvg 'admin_assets/images/svg/bed.svg',
                                class => 'listing-item-features__icon listing-item-features__icon--bed',
                                fill => '#252525',
                                height => 20,
                                width => 20}
                                <strong>{$product->info->bedrooms_count}</strong> {_'bedrooms'}
                            </li>
                        {/if}

                        {if !empty($product->info->people_number)}
                            <li class="listing-people">
                                {embeddedSvg 'admin_assets/images/svg/group-2.svg',
                                class => 'listing-item-features__icon listing-item-features__icon--group',
                                fill => '#252525',
                                height => 20,
                                width => 20}
                                <strong>{$product->info->people_number}</strong> {_'persons'}
                            </li>
                        {/if}

                        {if !empty($product->info->pets_allowed)}
                            <li class="listing-pets">
                                {embeddedSvg 'admin_assets/images/svg/pets-allowed.svg',
                                class => 'listing-item-features__icon listing-item-features__icon--pets-allowed',
                                fill => '#252525',
                                height => 20,
                                width => 20}
                                {_'Pets allowed'} <strong>({$product->info->allowed_pet_count})</strong>
                            </li>
                        {/if}

                        {if !empty($product->swimming_pools)}
                            {foreach $product->swimming_pools as $swimming_pool}
                                <li class="listing-pool">
                                    {embeddedSvg 'admin_assets/images/svg/pool.svg',
                                    class => 'listing-item-features__icon listing-item-features__icon--pool',
                                    fill => '#252525',
                                    height => 20,
                                    width => 20}
                                    {$presenter->translator->translate($swimming_pool->name)}
                                    {if count($product->swimming_pools) === 1 && !empty($product->info->pool_size)}
                                        <strong>({$product->info->pool_size}m<sup>2</sup>)</strong>
                                    {/if}
                                </li>
                            {/foreach}
                        {/if}

                        {if !empty($product->info->bathrooms_count)}
                            <li class="listing-bathroom">
                                {embeddedSvg 'admin_assets/images/svg/bathroom.svg',
                                class => 'listing-item-features__icon listing-item-features__icon--bathroom',
                                fill => '#252525',
                                height => 20,
                                width => 20}
                                <strong>{$product->info->bathrooms_count}</strong> {_'bathrooms'}
                            </li>
                        {/if}
                    </ul>

                    <ul class="action-features">
                        <li>
                            <a href="#main-features-{$lightboxNum}" class="popup-with-zoom-anim features-modal__anchor">{_'more details'}</a>
                        </li>
                        <li>
                            <a data-iframe="true" data-src="https://www.google.com/maps/embed/v1/place?q={$product->coordinates->latitude},{$product->coordinates->longitude}&amp;language={$presenter->language}&amp;zoom=11&amp;key={$presenter->getTemplate()->gmaps_api_key->value}" class="action-features__map">
                                {_'Location'}</a>
                        </li>
                        {if !empty($product->floor_plan_image)}
                            {include 'OfferFloorplanLegendControl.latte' product => $product}
                        {/if}
                    </ul>

                    <div id="main-features-{$lightboxNum}"
                         class="features-modal zoom-anim-dialog mfp-hide">
                        <div class="features-modal-container">
                            <h3 class="features-modal__title">{_'Distances'}</h3>
                            <ul class="features-modal__list">
                                {if !empty($product->info->parcel_unit_size)}
                                    <li>{_'Parcel size'}: <strong>{$product->info->parcel_unit_size} m<sup>2</sup></strong></li>
                                {/if}
                                {if !empty($product->info->panorama)}
                                    <li>{_'Panoramic sea view'}</li>
                                {/if}
                                {if !empty($product->info->modernized_year)}
                                    <li>{_'Modernized year'}: <strong>{$product->info->modernized_year}</strong></li>
                                {else}
                                    {if !empty($product->info->build_year)}<li>{_'Build year'}: <strong>{$product->info->build_year}</strong></li>{/if}
                                {/if}
                                {if !empty($product->info->neighbour_distance)}
                                    <li>{_'Neighbours distance'}: <strong>{$product->info->neighbour_distance} m</strong></li>
                                {/if}
                                {if !empty($product->info->town_distance)}
                                    <li>{_'Town distance'}: <strong>{$product->info->town_distance} m</strong></li>
                                {/if}
                                {if !empty($product->info->sea_distance)}
                                    <li>{_'Beach distance'}: <strong>{$product->info->sea_distance} m</strong></li>
                                {/if}
                            </ul>
                            <h3 class="features-modal__title features-modal__title--price">{_'Included in the price'}</h3>
                            <ul class="features-modal__price-included">
                                {foreach $product->features as $feature}
                                    <li>{$translator->translate($feature)}</li>
                                {/foreach}
                                {foreach $product->features_count as $feature => $value}
                                    <li>{$translator->translate($feature)}: {$value}</li>
                                {/foreach}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="listing-item-actions">
                    <div class="listing-item-price">
                        {if !empty($product->prices->price)}
                            {if !empty($product->prices->price_withour_discount) && $product->prices->price_withour_discount !== $product->prices->price}
                                <span class="old-price">{$product->prices->price_withour_discount|number:2,',','.'} €</span>
                            {/if}
                            <span class="real-price">{$product->prices->price|number:2,',','.'} €</span>

                            {if !empty($product->extra_costs)}

                                {if count($product->extra_costs) > 2}
                                <a href="#additional-costs-{$lightboxNum}" class="popup-with-zoom-anim additional-costs__anchor">{_'Additional costs'}</a>
                                <div id="additional-costs-{$lightboxNum}"
                                     class="features-modal zoom-anim-dialog mfp-hide">
                                    <div class="features-modal-container">
                                        <h3 class="features-modal__title">{_'Additional costs'}</h3>
                                        <ul class="features-modal__list">
                                        {foreach $product->extra_costs as $extra_cost}
                                            <li>
                                                {$translator->translate($extra_cost->name)}: <strong>{$extra_cost->price}{$translator->translate($extra_cost->currency)}</strong> {$translator->translate(str_replace('_', ' ', $extra_cost->price_type))}
                                                {if $extra_cost->service_type}
                                                    <strong>({_'Mandatory'})</strong>
                                                {else}
                                                    <strong>({_'Optional'})</strong>
                                                {/if}
                                            </li>
                                        {/foreach}
                                        </ul>
                                    </span>
                                    </div>
                                </div>
                                {else}
                                <div class="additional-costs-container">
                                {foreach $product->extra_costs as $extra_cost}
                                    <span class="additional-costs">
                                        {$translator->translate($extra_cost->name)}: <strong>{$extra_cost->price}{$translator->translate($extra_cost->currency)}</strong> {$translator->translate(str_replace('_', ' ', $extra_cost->price_type))}
                                    </span>
                                {/foreach}
                                </div>
                                {/if}
                            {else}
                            <div class="additional-costs-container">
                                <span class="additional-costs">
                                    {_'No additional costs!'}
                                </span>
                            </div>
                            {/if}
                            <div class="terms-conditions">
                                <a href="#terms-conditions-{$lightboxNum}" class="popup-with-zoom-anim terms-conditions__anchor">{_'Uvjeti rezervacije'}</a>

                                <div id="terms-conditions-{$lightboxNum}"
                                     class="terms-conditions-popup inquiry-modal zoom-anim-dialog mfp-hide">
                                    <div class="small-dialog-header">
                                        <h3>{_'Uvjeti rezervacije'}</h3>
                                    </div>
                                    <div class="small-dialog-form">
                                        {foreach $product->info->booking_terms as $term}
                                            {$term|noescape}<br/>
                                        {/foreach}
                                        <p class="terms-conditions__title">{_'Uvjeti otkazivanja rezervacije'}</p>
                                        <ul class="terms-conditions__list">
                                            <li>{_'Rezervacija se može otkazati isključivo pisanim oblikom.'}</li>
                                            {foreach $product->info->cancellation_policies as $policy}
                                                <li>{$policy|noescape}.</li>
                                            {/foreach}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        {else}
                            <span class="no-availability">{_'Too late, sold out!'}</span>
                        {/if}
                    </div>
                    <div class="listing-item-inner-bottom">
                        <a href="{$presenter->link(':Front:SingleVilla:default',
                        $presenter->language,
                        $product->info->product_id,
                        $guest->contact_firstname,
                        $guest->contact_lastname,
                        $guest->contact_email,
                        $product->arrival,
                        $product->departure,
                        $product->prices->price !== null ? round($product->prices->price) : null,
                        $guest->adults_count,
                        $guest->children_count,
                        $guest->pets_count,
                        $guest->hubspot_deal_id
                        )}" class="button button--proceed border button--right-spacing" target="_blank">
                            {_'View villa'}
                        </a>
                        <a href=
                           "{$presenter->link(':Front:Booking:default',
                           $presenter->language,
                           $guest->contact_firstname,
                           $guest->contact_lastname,
                           $guest->contact_email,
                           $product->arrival,
                           $product->departure,
                           $guest->adults_count,
                           $guest->children_count,
                           $guest->pets_count,
                           $guest->hubspot_deal_id,
                           $product->info->product_id
                           )}"
                           class="btn-book-now{if empty($product->prices->price)} btn-book-now--disabled{/if}"
                           data-gaevent="offers__book_now"
                           data-gacategory="book"
                           data-gaaction="submit"
                           target="_blank">
                            {_'Book now'}
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="external-content">
            <span class="terms trigger">{_'Booking and cancellation policy'}</span>
            <ul class="terms__booking-terms">
                <li>
                    {foreach $product->info->booking_terms as $term}
                        {$term|noescape}<br/>
                    {/foreach}
                </li>
            </ul>
            <ul class="terms__cancellation-terms">
                <li>{_'Rezervacija se može otkazati isključivo pisanim oblikom.'}</li>
                {foreach $product->info->cancellation_policies as $policy}
                    <li>{$policy|noescape}.</li>
                {/foreach}
            </ul>
            <a href="#" class="terms__show-more" data-show-less="{_'Show less conditions'}" data-show-more="{_'Show more conditions'}">
                {_'Show more conditions'}
            </a>
        </div>
        {var $lightboxNum = $lightboxNum + 1}
    {/foreach}
</div>


<div class="listing-item">
    {var $productLink = $presenter->link(':Front:SingleVilla:default', $linkParams + ['productId' => $result->product_id] + (!empty($result->arrival) && !empty($result->departure) ? ['arrive' => $result->arrival->format('d.m.Y'), 'departure' => $result->departure->format('d.m.Y')] : []))}
    {var $additional_name = ''}

    {if $result->partner === 'novasol'}
        {var $additional_name = 'additional_novasol_in_word'}
    {/if}

    <div class="listing-item-image skeleton-element">
        <div class="listing-lazy-images keen-slider">
            <a aria-label="{if empty($result->heading_in_listing)}{$result->name}{else}{$result->heading_in_listing}{/if}" class="villa-link-on-image" href="{$productLink}" target="_blank">
                <div class="special-offer-label special-offer-label--right-position" n:if="(!empty($result->discount_percentage) && $result->discount_percentage >= 5) || (!empty($result->discount_percentage_in_range) && $result->discount_percentage_in_range >= 5)">
                    <div class="special-offer-left">
                        {embeddedSvg 'assets/img/svg/discount.svg',
                        class => 'special-offer--left__icon',
                        height => 16,
                        width => 16}
                        <div class="special-offer-left__label">
                            <span>{_'Special'}</span>
                            <span>{_'Offer'}</span>
                        </div>
                    </div>
                    <div class="special-offer-right">
                        {if !empty($result->discount_percentage_in_range)}
                            <span class="special-offer--discount special-offer--discount-with-range">{$result->discount_percentage_in_range}%</span>
                        {else}
                            <span>{_'Up to'}</span>
                            <span class="special-offer--discount">{$result->discount_percentage}%</span>
                        {/if}
                    </div>
                </div>
                {if isset($result->pictures)}
                    {foreach $result->pictures as $picture}
                        {breakIf $iterator->counter > 20}
                            <div class="keen-slider__slide--fade" data-slide="0">
                                <div class="listing-lazy-images__image">
                                    <img
                                        src="{$presenter->getTemplate()->image_placeholder|nocheck}"
                                        class="lazyload"
                                        data-src="{str_replace("1024/", "500/", $picture)}"
                                        alt="{$result->name}" />
                                </div>
                            </div>
                    {/foreach}
                    <div class="gallery-object keen-slider__slide keen-slider__slide--fade" data-slide="0">
                        <div class="gallery-trigger-object">
                            <span>{_'Za čitavu galeriju, pregledajte'}</span>
                            <a href="{$productLink}">{_'stranicu objekta'}</a>
                        </div>
                    </div>
                {/if}
            </a>
            <div class="keen-prev keen-arrow"></div>
            <div class="keen-next keen-arrow"></div>
        </div>
        <span class="listing-item-title show-on-mobile show-on-tablet">
            <a target="_blank" href="{$productLink}">
                {if empty($result->heading_in_listing)}
                    {$result->name}
                {else}
                    {$result->heading_in_listing}
                {/if}
            </a>
        </span>
    </div>
    <div class="listing-item-content">
        <div class="listing-item-inner">
            <div class="listing-item-inner-top">
                <div class="new-object-label skeleton-element" n:if="$result->new_object_on_vg">
                    <span>{_'New object on portal'|firstUpper}</span>
                </div>

                <span class="listing-item-title listing-item-title--search-result">
                    <div class="listing-item-title-elipsis {if $searchResultPage}listing-item-title-wrapper{/if} skeleton-element">
                    <a target="_blank" href="{$productLink}" data-toggle="tooltip" title="
                        {if $searchResultPage}
                            {if empty($result->heading_in_listing)}
                                {$result->name}
                            {else}
                                {$result->heading_in_listing}
                            {/if}
                        {/if}">
                        {if empty($result->heading_in_listing)}
                            {$result->name}
                        {else}
                            {$result->heading_in_listing}
                        {/if}
                    </a>
                    </div>
                    <span class="listing-stars listing-stars--grid-view skeleton-element">
                        <i n:for="$stars_count = 0; $stars_count < $result->quality; $stars_count++" class="listing-stars__icon--star"></i>
                    </span>
                    <span class="listing-stars listing-stars--list-view skeleton-element">
                        <i n:for="$stars_count = 0; $stars_count < $result->quality; $stars_count++" class="listing-stars__icon--star"></i>
                    </span>

                </span>
                <span class="listing-item-location skeleton-element">
                    {ucfirst($translator->translate($result->subregion))},
                    {ucfirst($translator->translate($result->region))}
                    ({ucfirst($translator->translate($result->country))})
                </span>
                <div class="review-badge-listing skeleton-element">
                    {control reviewControl:review, productId: $result->product_id, showComments: false, showLabel: false, showLabelInside: true, position: 1, shortVersion: $isMobile}
                </div>
                {if $result->propertyID && !$control->getComponent('reviewControl')->hasReviews($result->product_id)}
                    <div class="listing-item__reviews">
                        {* Reevoo badge *}
                        {include '../../Presenters/templates/reevoo-widget.latte', variant => 'rect', injectCSS => true, product => $result->propertyID}
                    </div>
                {/if}
                {include 'ListingItemPrice.latte', grid_view => false}
            </div>
            <div class="listing-item-inner-middle {if $searchResultPage}listing-item-inner-middle--search-result{/if}">
                <ul class="main-features">
                    <li class="listing-capacity skeleton-element">
                        <i class="listing-capacity__icon listing-capacity__icon--group"></i> <strong class="main-features__strong">{$result->people_number}</strong> <span class="feature-text">{_'people'}</span>
                    </li>
                    <li n:if="!empty($result->bedroom)" class="listing-bedrooms skeleton-element">
                        <i class="listing-capacity__icon listing-capacity__icon--bed"></i> <strong class="main-features__strong">{$result->bedroom}</strong> <span class="feature-text">{_'bedrooms'}</span>
                    </li>
                    <li n:if="!empty($result->bathroom) && (empty($result->swimming_pools) || empty($result->concierge_names))" class="listing-bathrooms skeleton-element">
                        <i class="listing-capacity__icon listing-capacity__icon--bathroom"></i> <strong class="main-features__strong">{$result->bathroom}</strong> <span class="feature-text">{_'bathrooms'}</span>
                    </li>
                    <li n:if="!empty($result->swimming_pools)" class="listing-outdoor-pool listing-outdoor-pool--elipsis skeleton-element skeleton-element--shorter">
                        <i class="listing-capacity__icon listing-capacity__icon--pool"></i> <span class="feature-text">
                            {foreach $result->swimming_pools as $key => $swimming_pool}
                                {if (int) $key > 0} + {/if}
                                {$presenter->translator->translate($swimming_pool->name)}
                            {/foreach}
                        {if !(count($result->swimming_pools) > 1) && !empty($result->pool_size)}
                                <strong  class="main-features__strong">{$result->pool_size} m<sup>2</sup></strong>{/if}
                        </span>
                    </li>
                    <li class="listing-square-meters skeleton-element skeleton-element--shorter">
                        <i class="listing-capacity__icon listing-capacity__icon--home"></i> <span class="feature-text">{$translator->translate($result->object_type)}</span> <strong class="main-features__strong">{$result->house_unit_size}m<sup>2</sup></strong>
                    </li>
                    <li class="listing-pets skeleton-element skeleton-element--shorter">
                        {if !empty($result->pets_allowed)}
                            <i class="listing-capacity__icon listing-capacity__icon--pets-allowed"></i>
                            <span class="feature-text">{_'Pets allowed'}</span>
                        {elseif $result->pets_allowed_request}
                            <i class="listing-capacity__icon listing-capacity__icon--pets-allowed"></i>
                            <span class="feature-text">{_'pets (on request)'}</span>
                        {else}
                            <i class="listing-capacity__icon listing-capacity__icon--pets-not-allowed"></i>
                            <span class="feature-text">{_'Pets not allowed'}</span>
                        {/if}
                    </li>
                    <li class="listing-concierge skeleton-element skeleton-element--shorter" n:ifset="$result->concierge_names">
                        <i class="listing-capacity__icon listing-capacity__icon--concierge"></i>
                        <span class="feature-text">{_'Concierge services'}</span>
                    </li>
                </ul>
                {if !empty($result->date_tolerance)}
                <div class="flexible-date-label-wrapper mobile skeleton-element">
                    <span class="flexible-date-label">{$result->arrival|date:'d.m'} - {$result->departure|date:'d.m'}</span>
                </div>
                {/if}
                {include 'ListingItemPrice.latte', grid_view => true}
            </div>
            {var $discount_price_classes_for_flexi = ''}
            {if !empty($result->total_price)}
                {if (int) $result->price_with_discount !== (int) $result->total_price}
                    {var $discount_price_classes_for_flexi = 'flexible-date-label-wrapper--with-discount'}
                {/if}
            {/if}
            {if !empty($result->date_tolerance)}
                <div class="flexible-date-label-wrapper desktop {$discount_price_classes_for_flexi} skeleton-element">
                    <span class="flexible-date-label">{$result->arrival|date:'d.m'} - {$result->departure|date:'d.m'}</span>
                </div>
            {/if}
            <div class="listing-item-inner-bottom skeleton-element">
                <a
                    href="#small-dialog-{$resultNumber}"
                    class="button flat flat--smaller popup-with-zoom-anim">
                    <i class="button_icon--email"></i>
                    {_'Send inquiry'}
                </a>
                <a
                        href="{$productLink}"
                        aria-label="{$result->name}"
                        target="_blank"
                        class="button button--smaller button--color-anchor button--color-anchor--arrow-right">
                    {_'More info'}
                    {embeddedSvg 'assets/img/svg/arrow-right-button.svg',
                        class => 'gold-btn-right-arrow',
                        fill => '#252525',
                        height => 16,
                        width => 16
                    }
                </a>
            </div>
        </div>

        <div id="small-dialog-{$resultNumber}" class="inquiry-modal zoom-anim-dialog mfp-hide">
            <div class="small-dialog-header">
                <h3>{_'Inquiry for'} {$result->name}</h3>
            </div>
            <div class="small-dialog-form">
                <div class="inquiry-form hubspot-form" n:snippet="singlevilla-inquiry-form-response">
                    <div class="row">
                        {if empty($singleVillaInquirySuccess)}
                            {form sendInquiryForm-{$result->product_id}}
                                {include '../../sendInquiryForm.latte', pets => $result->pets_allowed, isSingleVilla => false, result_number => $resultNumber}
                                <input
                                    type="hidden"
                                    n:name="villa_interest"
                                    value="{$result->name}" />
                            {/form}
                        {/if}
                        {snippet thankYou}
                            <div n:class="empty($singleVillaInquirySuccess) ? hidden">
                                {include $presenter->template->frontModulePath . '/Presenters/templates/Forms/thankyou-message.latte'}
                            </div>
                        {if !empty($singleVillaInquirySuccess)}
                            <script>
                              $(document).ready(function() {
                                scrollToThankYou();
                              });
                            </script>
                        {/if}
                        {/snippet}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

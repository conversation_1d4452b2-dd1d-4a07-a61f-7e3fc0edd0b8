<?php declare(strict_types = 1);

namespace App\Modules\Property\Presenters;

use App\Common\Avantio\BookingManager;
use App\Common\Avantio\Enums\ServiceTypeEnum;
use App\Common\Enums\ActionLogDbTableEnum;
use App\Common\Enums\FlashTypeEnum;
use App\Common\EVisitor\EVisitorHelperService;
use App\Models\Entities\ReservationEntity;
use App\Modules\Api\Logic\BookingLogic;
use App\Modules\Property\Components\BaseForm;
use App\Modules\Property\FormFactories\ForeignReservationFormFactory;
use App\Modules\Property\GridFactories\ActionLogGridFactory;
use App\Modules\Property\GridFactories\ForeignReservationGridFactory;
use App\Modules\Property\Logic\ForeignReservationLogic;
use App\Modules\Property\Logic\ReservationLogic;
use App\Repositories\EVisitorReservationsRepository;
use Psr\Log\LoggerInterface;
use Ublaboo\DataGrid\DataGrid;
use Wedo\Api\Exceptions\ResponseException;

class ForeignReservationPresenter extends BasePresenter
{

	use CrudPresenterTrait;

	private ForeignReservationLogic $foreignReservationLogic;

	private BookingLogic $bookingLogic;

	private ForeignReservationGridFactory $foreignReservationGridFactory;

	private LoggerInterface $logger;

	private BookingManager $bookingManager;

	private ReservationLogic $reservationLogic;

	private ActionLogGridFactory $actionLogGridFactory;

	private EVisitorReservationsRepository $eVisitorReservationsRepository;

	private EVisitorHelperService $eVisitorHelperService;

	public function __construct(
		ForeignReservationLogic $foreignReservationLogic,
		BookingLogic $bookingLogic,
		ForeignReservationGridFactory $foreignReservationGridFactory,
		LoggerInterface $logger,
		BookingManager $bookingManager,
		ReservationLogic $reservationLogic,
		ActionLogGridFactory $actionLogGridFactory,
		EVisitorReservationsRepository $eVisitorReservationsRepository,
		EVisitorHelperService $eVisitorHelperService
	)
	{
		$this->foreignReservationLogic = $foreignReservationLogic;
		$this->bookingLogic = $bookingLogic;
		$this->foreignReservationGridFactory = $foreignReservationGridFactory;
		$this->logger = $logger;
		$this->bookingManager = $bookingManager;
		$this->reservationLogic = $reservationLogic;
		$this->actionLogGridFactory = $actionLogGridFactory;
		$this->eVisitorReservationsRepository = $eVisitorReservationsRepository;
		$this->eVisitorHelperService = $eVisitorHelperService;
		parent::__construct();
	}

	public function renderDetails(int $id): void
	{
		$this->template->id = $id;
		$reservationDetails = $this->foreignReservationLogic->getReservationDetails($id);
		$reservationFullDetails = $this->reservationLogic->getRepository()->getForeignReservationsFullDetails($id);

		if ($reservationDetails === null) {
			$this->translatedFlashMessage('Reservation does not exist!', FlashTypeEnum::ERROR);
			$this->redirect('default');
		}

		$this->template->reservationDetails = $reservationDetails;
		$this->template->reservationFullDetails = $reservationFullDetails;
		$this->template->reservationPayments = $this->foreignReservationLogic->getReservationPayments($id);
		$reservationServices = $this->foreignReservationLogic->getReservationServices($id);
		$this->template->reservationServices = $reservationServices;
		$this->template->numberOfPets = $this->foreignReservationLogic->getNumberOfPets($reservationServices);
		$this->template->product = $this->foreignReservationLogic->getProduct($reservationDetails->product_id);
		$this->template->services = ServiceTypeEnum::toValueKeyArray();
		$this->template->numberOfChildren = $this->foreignReservationLogic->getNumberOfChildren($reservationDetails);

		$this->action_log_db_table = ActionLogDbTableEnum::FOREIGN_RESERVATIONS;
		$this->action_log_db_table_id = $id;

		$eVisitorReservationData = $this->eVisitorReservationsRepository->getReservationData($id);
		$this->template->eVisitorReservationData = $eVisitorReservationData;

		if ($eVisitorReservationData !== null) {
			$hash = $this->eVisitorHelperService->encryptKey((string) $id);
			$this->template->eVisitorLink = sprintf('https://villas-guide.com/en/evisitor/%s', $hash);
		}

		$this->redrawContent();
	}

	public function actionEdit(int $id): void
	{
		$this->template->id = $id;
		$reservationDetails = $this->foreignReservationLogic->getReservationDetails($id);

		if ($reservationDetails === null) {
			$this->translatedFlashMessage('Reservation does not exist!', FlashTypeEnum::ERROR);
			$this->redirect('default');
		}

		$this->template->reservationDetails = $reservationDetails;
		$this->template->product = $this->foreignReservationLogic->getProduct($reservationDetails->product_id);
		$this->redrawContent();
	}

	public function actionApprove(int $id): void
	{
		try {
			$reservationId = $this->foreignReservationLogic->getReservationId($id);

			if ($reservationId === null) {
				$this->bookingManager->createReservation($id, false);
				$reservationId = $this->foreignReservationLogic->getReservationId($id);
			}

			if ($reservationId === null) {
				throw new \RuntimeException('Reservation could not be created.');
			}

			$bookingRequest = $this->foreignReservationLogic->getBookingRequest($id);
			$bookingRequest->test = !$this->environmentDetector->isProduction();
			$reservationId = $this->foreignReservationLogic->getReservationId($id);
			$reservation = $this->reservationLogic->getRepository()->getBy([ReservationEntity::ID => $reservationId]);
			$this->bookingLogic->sendEmails($reservation, $bookingRequest);
			$this->bookingLogic->createInvoice($reservation);
			$this->foreignReservationLogic->markAsApproved($reservationId, $id);
			$this->actionLog->log('reservation_approved', ActionLogDbTableEnum::RESERVATIONS, $reservationId, 'Reservation approved');

			$this->flashMessage('Successfully', FlashTypeEnum::SUCCESS);
		} catch (ResponseException $exception) {
			$this->flashMessage($exception->getMessage(), FlashTypeEnum::ERROR);
			$this->logger->error($exception->getMessage(), ['exception' => $exception]);
			$this->actionLog->log(
				'reservation_approved_error',
				ActionLogDbTableEnum::FOREIGN_RESERVATIONS,
				$id,
				'Error while approving reservation: ' . $exception->getMessage(),
				'error'
			);
		}

		$this->redirect('Reservation:Default');
	}

	public function actionDecline(int $id): void
	{
		try {
			$response = $this->bookingManager->cancelBooking($id);

			if ($response->getSucceed()) {
				$reservationId = $this->foreignReservationLogic->getReservationId($id);

				if ($reservationId !== null) {
					$this->reservationLogic->delete($reservationId);
				} else {
					$this->bookingManager->openAvailabilities($id);
				}

				$this->foreignReservationLogic->markAsDeclined($id);
				$this->flashMessage('Successfully', FlashTypeEnum::SUCCESS);
				$this->actionLog->log('foreign_reservation_declined', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $id, 'Reservation declined');

				$this->redirect('Reservation:Default');
			}

			$errors = $response->getErrorList()->getError();

			$this->flashMessage('Something went wrong', FlashTypeEnum::ERROR);
			$this->actionLog->log(
				'foreign_reservation_declined_error',
				ActionLogDbTableEnum::FOREIGN_RESERVATIONS,
				$id,
				'Error while declining reservation: ' . implode(', ', $errors)
			);
			$this->logger->error('Error while declining reservation', ['exception' => $errors]);

		} catch (ResponseException $exception) {
			$this->flashMessage('Something went wrong', FlashTypeEnum::ERROR);
			$this->actionLog->log(
				'foreign_reservation_declined_error',
				ActionLogDbTableEnum::FOREIGN_RESERVATIONS,
				$id,
				'Error while declining reservation: ' . $exception->getMessage()
			);
			$this->logger->error($exception->getMessage(), ['exception' => $exception]);
		}

		$this->redirect('Reservation:Default');
	}

	public function createComponentForeignReservationsGridControl(): DataGrid
	{
		$this->foreignReservationGridFactory->setPresenter($this);

		return $this->foreignReservationGridFactory->create();
	}

	public function createComponentForeignReservationForm(): BaseForm
	{
		return $this->createForm(ForeignReservationFormFactory::class);
	}

	public function createComponentActionLogGridControl(): DataGrid
	{
		$this->actionLogGridFactory->setPresenter($this);

		return $this->actionLogGridFactory->create();
	}

}

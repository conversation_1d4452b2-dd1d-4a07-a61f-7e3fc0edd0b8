<?php declare(strict_types = 1);

namespace App\Modules\Property\Presenters;

use App\Common\Avantio\BookingManager;
use App\Common\Avantio\Enums\ServiceTypeEnum;
use App\Common\Enums\ActionLogDbTableEnum;
use App\Common\Enums\FlashTypeEnum;
use App\Common\EnvironmentDetector;
use App\Modules\Api\Logic\BookingLogic;
use App\Modules\Property\Components\BaseForm;
use App\Modules\Property\FormFactories\ForeignReservationFormFactory;
use App\Modules\Property\GridFactories\ActionLogGridFactory;
use App\Modules\Property\GridFactories\ForeignReservationGridFactory;
use App\Modules\Property\Logic\ForeignReservationLogic;
use App\Modules\Property\Logic\ReservationLogic;
use Psr\Log\LoggerInterface;
use Ublaboo\DataGrid\DataGrid;
use Wedo\Api\Exceptions\ResponseException;

class ForeignReservationPresenter extends BasePresenter
{

	use CrudPresenterTrait;

	private ForeignReservationLogic $foreignReservationLogic;

	private BookingLogic $bookingLogic;

	private ForeignReservationGridFactory $foreignReservationGridFactory;

	private LoggerInterface $logger;

	private BookingManager $bookingManager;

	private ReservationLogic $reservationLogic;

	private EnvironmentDetector $environmentDetector;

	private ActionLogGridFactory $actionLogGridFactory;

	public function __construct(
		ForeignReservationLogic $foreignReservationLogic,
		BookingLogic $bookingLogic,
		ForeignReservationGridFactory $foreignReservationGridFactory,
		LoggerInterface $logger,
		BookingManager $bookingManager,
		ReservationLogic $reservationLogic,
		EnvironmentDetector $environmentDetector,
		ActionLogGridFactory $actionLogGridFactory
	)
	{
		$this->foreignReservationLogic = $foreignReservationLogic;
		$this->bookingLogic = $bookingLogic;
		$this->foreignReservationGridFactory = $foreignReservationGridFactory;
		$this->logger = $logger;
		$this->bookingManager = $bookingManager;
		$this->reservationLogic = $reservationLogic;
		$this->environmentDetector = $environmentDetector;
		$this->actionLogGridFactory = $actionLogGridFactory;
		parent::__construct();
	}

	public function renderDetails(int $id): void
	{
		$this->template->id = $id;
		$reservationDetails = $this->foreignReservationLogic->getReservationDetails($id);
		$reservationFullDetails = $this->reservationLogic->getRepository()->getForeignReservationsFullDetails($id);

		if ($reservationDetails === null) {
			$this->translatedFlashMessage('Reservation does not exist!', FlashTypeEnum::ERROR);
			$this->redirect('default');
		}

		$this->template->reservationDetails = $reservationDetails;
		$this->template->reservationFullDetails = $reservationFullDetails;
		$this->template->reservationPayments = $this->foreignReservationLogic->getReservationPayments($id);
		$reservationServices = $this->foreignReservationLogic->getReservationServices($id);
		$this->template->reservationServices = $reservationServices;
		$this->template->numberOfPets = $this->foreignReservationLogic->getNumberOfPets($reservationServices);
		$this->template->product = $this->foreignReservationLogic->getProduct($reservationDetails->product_id);
		$this->template->services = ServiceTypeEnum::toValueKeyArray();
		$this->template->numberOfChildren = $this->foreignReservationLogic->getNumberOfChildren($reservationDetails);

		$this->action_log_db_table = ActionLogDbTableEnum::FOREIGN_RESERVATIONS;
		$this->action_log_db_table_id = $id;

		$this->redrawContent();
	}

	public function actionEdit(int $id): void
	{
		$this->template->id = $id;
		$reservationDetails = $this->foreignReservationLogic->getReservationDetails($id);

		if ($reservationDetails === null) {
			$this->translatedFlashMessage('Reservation does not exist!', FlashTypeEnum::ERROR);
			$this->redirect('default');
		}

		$this->template->reservationDetails = $reservationDetails;
		$this->template->product = $this->foreignReservationLogic->getProduct($reservationDetails->product_id);
		$this->redrawContent();
	}

	public function actionApprove(int $id): void
	{
		try {
			$bookingRequest = $this->foreignReservationLogic->getBookingRequest($id);
			$bookingRequest->test = !$this->environmentDetector->isProduction();
			$this->bookingLogic->validateRequest($bookingRequest);
			$this->bookingManager->confirmBooking($id);
			$bookingRequest->product_link = 'https://villas-guide.com/' .
				$this->link(':Front:SingleVilla:default', ['productId' => (int) $bookingRequest->property_id]);
			$reservationId = $this->bookingLogic->create($bookingRequest);
			$this->foreignReservationLogic->markAsApproved($reservationId, $id);
			$this->actionLog->log('reservation_approved', ActionLogDbTableEnum::RESERVATIONS, $reservationId, 'Reservation approved');

			$this->flashMessage('Successfully', FlashTypeEnum::SUCCESS);
		} catch (ResponseException $exception) {
			$this->flashMessage($exception->getMessage(), FlashTypeEnum::ERROR);
			$this->logger->error($exception->getMessage(), ['exception' => $exception]);
			$this->actionLog->log('reservation_approved_error', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $id, 'Error while approving reservation: ' . $exception->getMessage());
		}

		$this->redirect('Reservation:Default');
	}

	public function actionDecline(int $id): void
	{
		try {
			$status = $this->bookingManager->cancelBooking($id);

			if ($status) {
				$reservationId = $this->foreignReservationLogic->getReservationId($id);

				if ($reservationId !== null) {
					$this->reservationLogic->delete($reservationId);
				} else {
					$this->bookingManager->openAvailabilities($id);
				}

				$this->foreignReservationLogic->markAsDeclined($id);
				$this->actionLog->log('foreign_reservation_declined', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $id, 'Reservation declined');
			}

			$this->flashMessage('Successfully', FlashTypeEnum::SUCCESS);
		} catch (ResponseException $exception) {
			$this->flashMessage('Something went wrong', FlashTypeEnum::ERROR);
			$this->actionLog->log('foreign_reservation_declined_error', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $id, 'Error while declining reservation: ' . $exception->getMessage());
			$this->logger->error($exception->getMessage(), ['exception' => $exception]);
		}

		$this->redirect('Reservation:Default');
	}

	public function createComponentForeignReservationsGridControl(): DataGrid
	{
		$this->foreignReservationGridFactory->setPresenter($this);

		return $this->foreignReservationGridFactory->create();
	}

	public function createComponentForeignReservationForm(): BaseForm
	{
		return $this->createForm(ForeignReservationFormFactory::class);
	}

	public function createComponentActionLogGridControl(): DataGrid
	{
		$this->actionLogGridFactory->setPresenter($this);

		return $this->actionLogGridFactory->create();
	}

}

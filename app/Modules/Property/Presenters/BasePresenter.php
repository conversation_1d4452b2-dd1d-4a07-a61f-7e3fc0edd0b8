<?php declare(strict_types = 1);

namespace App\Modules\Property\Presenters;

use App\Common\BrowserHelper;
use App\Common\Enums\RoleEnum;
use App\Common\Exceptions\FlashMessageException;
use App\Common\GtmCredentials;
use App\Common\Logger\ActionLog;
use App\Common\Translator;
use App\Models\Entities\UserProductEntity;
use App\Modules\Property\Components\BaseForm;
use App\Modules\Property\FormFactories\BaseFormFactory;
use App\Modules\Property\GridFactories\BaseGridFactory;
use App\Repositories\UserProductRepository;
use Contributte\MenuControl\UI\IMenuComponentFactory;
use Contributte\MenuControl\UI\MenuComponent;
use Nette\Application\Helpers;
use Nette\Application\UI\Presenter;
use Nette\DI\Attributes\Inject;
use Nette\DI\Container;
use Ublaboo\DataGrid\DataGrid;

class BasePresenter extends Presenter
{

	public Translator $translator;

	public BrowserHelper $browserHelper;

	public UserProductRepository $userProductRepository;

	private IMenuComponentFactory $menuFactory;

	#[Inject]
	public Container $container;

	public string $presenterName;

	/**
	 * @persistent
	 */
	public string $language;

	#[Inject]
	public GtmCredentials $gtmCredentials;

	#[Inject]
	public ActionLog $actionLog;

	public string $action_log_db_table = '';

	public int $action_log_db_table_id = 0;

	protected function startup(): void
	{
		$this->template->gtm_id = $this->gtmCredentials->id;
		$this->template->gtm_auth = $this->gtmCredentials->auth;
		$this->template->gtm_env = $this->gtmCredentials->env;
		$language = $this->getParameter('language') ?? 'en';
		$this->template->setTranslator($this->translator);
		$this->translator->setDashboardLanguage($language);
		$this->template->language = $language;
		$pieces = explode(':', $this->name);
		$this->presenterName = end($pieces);
		$this->template->productListView = $this->getHttpRequest()->getCookie('productListView') ?? 'false';
		$this->template->reservationListView = $this->getHttpRequest()->getCookie('reservationListView') ?? 'false';
		parent::startup();

		if ($this->presenterName === 'Login') {
			return;
		}

		if (!$this->getUser()->isLoggedIn() || (!$this->getUser()->isInRole(RoleEnum::ADMIN) && !$this->getUser()->isInRole(RoleEnum::PROPERTY))) {
			if ($this->isAjax()) {
				$this->sendJson(
					[
						'hard_redirect' => $this->link(
							'Login:Default',
							['returnto' => $this->link('//this', ['language' => $language]), 'language' => $language]
						),
					]
				);
			}
			$this->redirect(
				'Login:Default',
				['returnto' => $this->link('//this', ['language' => $language]), 'language' => $language]
			);
		}

		$this->template->shouldUserHaveFullAccessToDasboard = false;
		$this->template->lastDraftProduct = null;

		if ($this->getUser()->isInRole(RoleEnum::ADMIN)) {
			$this->template->shouldUserHaveFullAccessToDasboard = true;
		} else {
			$hasUserAnyFinishedProducts = $this->userProductRepository->doesUserHasAnyFinishedProducts($this->getUser()->getId());

			if ($hasUserAnyFinishedProducts) {
				$this->template->shouldUserHaveFullAccessToDasboard = true;
			} else {
				/** @var UserProductEntity|null $lastDraftProduct */
				$lastDraftProduct = $this->userProductRepository->getDraftProduct($this->getUser()->getId());
				$this->template->lastDraftProduct = $lastDraftProduct;
			}
		}
	}

	public function afterRender()
	{
		if ($this->isAjax()) {
			$this->redrawContent();
		}

		parent::afterRender();
	}

	public function injectMenuComponentFactory(IMenuComponentFactory $menuFactory): void
	{
		$this->menuFactory = $menuFactory;
	}

	protected function createComponentMenu(): MenuComponent
	{
		return $this->menuFactory->create('property');
	}

	public function injectTranslator(Translator $translator): void
	{
		$this->translator = $translator;
	}

	public function injectBrowserHelper(BrowserHelper $browserHelper): void
	{
		$this->browserHelper = $browserHelper;
	}

	public function injectUserProductRepository(UserProductRepository $userProductRepository): void
	{
		$this->userProductRepository = $userProductRepository;
	}

	public function actionPing()
	{
		echo 'ping';
		die;
	}

	protected function redrawContent(): void
	{
		if (!$this->isAjax() || $this->getParameter('do', 'form-submit') !== 'form-submit') {
			return;
		}

		$this->redrawControl('stylesheet');
		$this->redrawControl('content');
		$this->redrawControl('js');
		$this->redrawControl('flash');
	}


	/**
	 * @param mixed[] $params
	 * @phpcsSuppress SlevomatCodingStandard.TypeHints.TypeHintDeclaration.MissingParameterTypeHint
	 */
	protected function tryCall(string $method, array $params): bool
	{
		try {
			return parent::tryCall($method, $params);
		} catch (FlashMessageException $exception) {
			$this->flashMessage($exception->getMessage(), 'error');
			$this->redrawContent();

			return true;
		}
	}

	public function processSignal(): void
	{
		try {
			parent::processSignal();
		} catch (FlashMessageException $exception) {
			$this->flashMessage($exception->getMessage(), 'error');
			$this->redrawContent();
		}
	}

	public function flashMessage($message, string $type = 'info'): \stdClass
	{
		if ($this->isAjax()) {
			$this->redrawControl('flash');
		}

		return parent::flashMessage($message, $type);
	}

	public function translatedFlashMessage($message, string $type = 'info'): \stdClass
	{
		if ($this->isAjax()) {
			$this->redrawControl('flash');
		}

		return parent::flashMessage($this->translator->translate($message), $type);
	}

	/**
	 * Formats layout template file names.
	 *
	 * @return array
	 */
	public function formatTemplateFiles(): array
	{
		[, $presenter] = Helpers::splitName($this->getName());

		$dir = dirname($this->getReflection()->getFileName());
		$dir = is_dir($dir . '/templates')
			? $dir
			: dirname($dir);
		$defaultOne = __DIR__ . '/templates/_' . $this->view . '.latte';

		return [
			$dir . '/templates/' . $presenter . '/' . $this->view . '.latte',
			$dir . '/templates/_' . $this->view . '.latte',
			$defaultOne,
		];
	}

	protected function createForm(string $formFactoryType, array $params = [], bool $isNewAction = false): BaseForm
	{
		/** @var BaseFormFactory $formFactory */
		$formFactory = $this->container->getByType($formFactoryType);
		$formFactory->setPresenter($this);
		$formFactory->setTranslator($this->translator);

		$id = $isNewAction ? null : $this->getPresenter()->getParameter('id');
		$form = $formFactory->create(is_numeric($id) ? (int) $id : NULL);

		foreach ($params as $key => $value) {
			$form->addHidden($key)->setDefaultValue($value);
		}

		return $form;
	}

	protected function createGrid(string $gridFactoryType): DataGrid
	{
		/** @var BaseGridFactory $gridFactory */
		$gridFactory = $this->container->getByType($gridFactoryType);
		$gridFactory->setPresenter($this);

		return $gridFactory->create();
	}

}

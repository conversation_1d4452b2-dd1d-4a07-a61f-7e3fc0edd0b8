<?php declare(strict_types = 1);

namespace App\Modules\Property\Presenters;

use App\Common\Enums\ActionLogDbTableEnum;
use App\Common\Enums\FlashTypeEnum;
use App\Common\Enums\InstallmentsPaymentSecondStatusEnum;
use App\Common\Enums\InvoiceFixedPartNumberEnum;
use App\Common\Enums\ReservationGridTypeEnum;
use App\Common\Enums\RoleEnum;
use App\Common\EVisitor\EVisitorHelperService;
use App\Common\Logic\PreReservationLogic;
use App\Common\Reservation\Helpers\DistributionChannelHelper;
use App\Models\Entities\custom\ReservationFullDetailsEntity;
use App\Models\Entities\ReservationDetailsEntity;
use App\Models\Entities\ReservationEntity;
use App\Models\Entities\ReservationLogEntity;
use App\Modules\Property\Components\AllianzControl;
use App\Modules\Property\Components\BaseForm;
use App\Modules\Property\Components\IAllianzControlFactory;
use App\Modules\Property\Components\InvoiceControl;
use App\Modules\Property\Components\IReservationGuestControlFactory;
use App\Modules\Property\Components\IRestAmountToBePaidControlFactory;
use App\Modules\Property\Components\ReservationGuestControl;
use App\Modules\Property\Components\RestAmountToBePaidControl;
use App\Modules\Property\FormFactories\AgencyProvisionFormFactory;
use App\Modules\Property\FormFactories\ReservationDetailsFormFactory;
use App\Modules\Property\FormFactories\ReservationFormFactory;
use App\Modules\Property\GridFactories\ActionLogGridFactory;
use App\Modules\Property\GridFactories\ReservationGridFactory;
use App\Modules\Property\Logic\InvoiceDataLogic;
use App\Modules\Property\Logic\InvoiceLogic;
use App\Modules\Property\Logic\ProductLogic;
use App\Modules\Property\Logic\ProductSeasonLogic;
use App\Modules\Property\Logic\ReservationLogic;
use App\Repositories\EVisitorReservationsRepository;
use Dibi\DateTime;
use Ublaboo\DataGrid\DataGrid;

class ReservationPresenter extends BasePresenter
{

	use CrudPresenterTrait;

	private ReservationLogic $reservationLogic;

	private ReservationGridFactory $reservationGridFactory;

	private ReservationFormFactory $reservationFormFactory;

	private IRestAmountToBePaidControlFactory $restAmountToBePaidControlFactory;

	private InvoiceLogic $invoiceLogic;

	private InvoiceDataLogic $invoiceDataLogic;

	private IAllianzControlFactory $allianzControlFactory;

	private InvoiceControl $invoiceControl;

	private PreReservationLogic $preReservationLogic;

	private DistributionChannelHelper $distributionChannelHelper;

	private ProductLogic $productLogic;

	private ProductSeasonLogic $productSeasonLogic;

	private ?ReservationFullDetailsEntity $reservationFullDetails = null;

	private ActionLogGridFactory $actionLogGridFactory;

	private EVisitorReservationsRepository $eVisitorReservationsRepository;

	private EVisitorHelperService $eVisitorHelperService;

	private IReservationGuestControlFactory $reservationGuestControlFactory;

	private ReservationDetailsEntity $reservationDetails;

	private ?ReservationLogEntity $reservationLog = null;

	public function __construct(
		ReservationLogic $reservationLogic,
		ReservationGridFactory $reservationGridFactory,
		ReservationFormFactory $reservationFormFactory,
		IRestAmountToBePaidControlFactory $restAmountToBePaidControlFactory,
		InvoiceLogic $invoiceLogic,
		InvoiceDataLogic $invoiceDataLogic,
		IAllianzControlFactory $allianzControlFactory,
		InvoiceControl $invoiceControl,
		PreReservationLogic $preReservationLogic,
		DistributionChannelHelper $distributionChannelHelper,
		ProductLogic $productLogic,
		ProductSeasonLogic $productSeasonLogic,
		ActionLogGridFactory $actionLogGridFactory,
		EVisitorReservationsRepository $eVisitorReservationsRepository,
		EVisitorHelperService $eVisitorHelperService,
		IReservationGuestControlFactory $reservationGuestControlFactory
	)
	{
		$this->reservationLogic = $reservationLogic;
		$this->reservationGridFactory = $reservationGridFactory;
		$this->reservationFormFactory = $reservationFormFactory;
		$this->restAmountToBePaidControlFactory = $restAmountToBePaidControlFactory;
		$this->invoiceLogic = $invoiceLogic;
		$this->invoiceDataLogic = $invoiceDataLogic;
		$this->allianzControlFactory = $allianzControlFactory;
		$this->invoiceControl = $invoiceControl;
		$this->preReservationLogic = $preReservationLogic;
		$this->distributionChannelHelper = $distributionChannelHelper;
		$this->productLogic = $productLogic;
		$this->productSeasonLogic = $productSeasonLogic;
		$this->actionLogGridFactory = $actionLogGridFactory;
		$this->eVisitorReservationsRepository = $eVisitorReservationsRepository;
		$this->eVisitorHelperService = $eVisitorHelperService;

		parent::__construct();
		$this->reservationGuestControlFactory = $reservationGuestControlFactory;
	}

	public function beforeRender(): void
	{
		if ($this->template->lastDraftProduct !== null) {
			$this->template->lastStep = $this->productLogic->getLastDataStep($this->template->lastDraftProduct->id);
		}

		$publishedUserProducts = $this->reservationLogic->getProductRepository()->getUserProducts($this->getUser()->getId(), true);
		$this->template->allowReservationButton = $this->getUser()->isInRole(RoleEnum::ADMIN) || $publishedUserProducts;

		parent::beforeRender();
	}

	public function actionDefault()
	{
		if ($this->getSession()->hasSection('location')) {
			$this->getSession()->getSection('location')->remove('name');
		}

		$this->template->showProductLink = true;
	}

	public function actionNew(?int $product_id = null): void
	{
		$this->template->product_id = $product_id;

		if ($product_id !== null) {
			$this->template->product = $this->reservationLogic->getProductRepository()->getById($product_id);
		}
		$this->redrawContent();
		$this->setView('edit');
	}

	public function actionEdit(int $id): void
	{
		$reservationDetails = $this->reservationLogic->getRepository()->getDetails($id);

		if (!$this->productLogic->isProductOwner($reservationDetails->product_id, $this->getUser()->getId())) {
			$this->translatedFlashMessage('You dont have access ont this product!');
			$this->redirect('default');
		}

		$reservation = $this->reservationLogic->getRepository()->getById($id);
		$this->template->isReservationSecondInstallmentDirectlyToRenter
			= $reservation->second_installment_payment_status === InstallmentsPaymentSecondStatusEnum::THE_GUEST_PAYS_DIRECTLY_TO_THE_RENTER;
		$this->template->product = $this->reservationLogic->getProductRepository()->getById($reservationDetails->product_id);
		$this->template->recruiterName = $this->reservationLogic->getProductRepository()->getProductRecruiter($reservationDetails->product_id);
		$this->template->reservationDetails = $reservationDetails;
		$this->template->id = $id;
		$this->template->reservation_code = $reservationDetails->reservation_code ?? $id;
		$insuranceAmount = $this->invoiceLogic->getInsurancePrice($reservationDetails->reservation_code ?? '');
		$this->template->distributionChannelName = $this->distributionChannelHelper->getSource(
			$reservationDetails->product_distribution_channel_id
		);
		$this->template->distributionPrice = $reservationDetails->total_price_eur - $insuranceAmount;
		$this->template->insuranceAmount = $insuranceAmount;

		if ($reservationDetails->product_distribution_channel_id !== null) {
			$insuranceAmount = $reservationDetails->reservation_code !== null
				? $this->invoiceLogic->getInsurancePrice($reservationDetails->reservation_code)
				: 0;
			$markupPrice = $reservationDetails->distribution_price !== null && $reservationDetails->amount !== null
				? $reservationDetails->distribution_price - $reservationDetails->amount - $insuranceAmount
				: null;
			$this->template->markupPrice = ($markupPrice !== null && $markupPrice > 5.0) ? $markupPrice : null;
		} else {
			$this->template->markupPrice = null;
		}

		$this->redrawContent();
	}

	public function renderDetails(int $id): void
	{
		$this->template->id = $id;
		$reservationDetails = $this->reservationLogic->getRepository()->getDetails($id);
		$this->reservationFullDetails = $this->reservationLogic->getRepository()->getVgReservationsFullDetails($id);
		$this->template->reservation_code = $reservationDetails->reservation_code ?? $id;

		if ($reservationDetails === null) {
			$this->translatedFlashMessage('Reservation does not exist!', FlashTypeEnum::ERROR);
			$this->redirect('default');
		}

		$this->template->concierges = [];

		if ($reservationDetails->pre_reservation_id !== null) {
			$concierges = $this->preReservationLogic->findPreReservationsConcierges($reservationDetails->pre_reservation_id);
			$this->template->conciergesInPrice = $this->preReservationLogic->filterPreReservationsConcierges($concierges, false);
			$this->template->conciergesNotInPrice = $this->preReservationLogic->filterPreReservationsConcierges($concierges, true);
			$this->template->concierges = $concierges;
		}

		$this->template->reservationDetails = $reservationDetails;
		$this->template->product = $this->reservationLogic->getProductRepository()->getById($reservationDetails->product_id);

		$this->template->invoices = $this->invoiceLogic->getInvoices($id);
		$this->template->lastInvoice = $this->invoiceLogic->getInvoice($id);
		$this->template->cancelledInvoices = $this->invoiceLogic->getInvoices($id, InvoiceFixedPartNumberEnum::CANCELLED_INVOICE_FIXED_PART);
		$this->template->markupInvoices = $this->invoiceLogic->getInvoices($id, InvoiceFixedPartNumberEnum::MARKUP_INVOICE_FIXED_PART);
		$this->template->cancelledMarkupInvoices = $this->invoiceLogic->getInvoices($id, InvoiceFixedPartNumberEnum::MARKUP_CANCELLED_INVOICE_FIXED_PART);

		$this->template->reservationLog = $this->reservationLogic->getLastReservationLog($id);
		$this->template->reservationFromProductPage = $this->getSession()->hasSection('location');

		if ($reservationDetails->product_distribution_channel_id !== null) {
			$insuranceAmount = $reservationDetails->reservation_code !== null
				? $this->invoiceLogic->getInsurancePrice($reservationDetails->reservation_code)
				: 0;
			$markupPrice = $reservationDetails->distribution_price !== null && $reservationDetails->amount !== null
				? $reservationDetails->distribution_price - $reservationDetails->amount - $insuranceAmount
				: null;
			$this->template->isMarkupSupported = $markupPrice !== null && $markupPrice > 5.0;
		} else {
			$this->template->isMarkupSupported = false;
		}

		$agencyProvisionAmount = $this->invoiceLogic->getAgencyProvisionAmount($id);
		$this->template->agencyProvisionAmount = $agencyProvisionAmount * 1.25;
		$this->template->reservationFullDetails = $this->reservationFullDetails;

		$this->action_log_db_table = ActionLogDbTableEnum::RESERVATIONS;
		$this->action_log_db_table_id = $id;

		$eVisitorReservationData = $this->eVisitorReservationsRepository->getReservationData($id);
		$this->template->eVisitorReservationData = $eVisitorReservationData;

		if ($eVisitorReservationData !== null) {
			$hash = $this->eVisitorHelperService->encryptKey((string) $id);
			$this->template->eVisitorLink = sprintf('https://villas-guide.com/en/evisitor/%s', $hash);
		}

		$this->reservationDetails = $reservationDetails;
		$this->reservationLog = $this->template->reservationLog;

		$this->redrawContent();
	}

	private function deleteReservation(int $id): bool
	{
		$productId = $this->reservationLogic->delete($id);

		if ($productId !== null) {
			$this->productSeasonLogic->deleteSeason($id);

			return true;
		}

		return false;
	}

	public function actionDelete(int $id, ?int $product_id = null): void
	{
		$isDeletedSuccessfully = $this->deleteReservation($id);

		if ($isDeletedSuccessfully) {
			$this->translatedFlashMessage('Reservation cancelled successfully!', FlashTypeEnum::SUCCESS);

			if ($product_id !== null) {
				$this->redirect('Product:details', ['id' => $product_id]);
			} else {
				$this->redirect('default');
			}
		}

		$this->translatedFlashMessage('Error when cancelling reservation!', FlashTypeEnum::DANGER);

		if ($product_id !== null) {
			$this->redirect('Product:details', ['id' => $product_id]);
		} else {
			$this->redirect('default');
		}
	}

	public function actionRemove(int $id, ?int $product_id = null): void
	{
		$isReservationCancelled = $this->reservationLogic->isReservationCancelled($id);

		if ($isReservationCancelled === null) {
			$this->translatedFlashMessage('Error when cancelling reservation!', FlashTypeEnum::DANGER);

			if ($product_id !== null) {
				$this->redirect('Product:details', ['id' => $product_id]);
			} else {
				$this->redirect('default');
			}
		}

		if (!$isReservationCancelled) {
			$isCancelledSuccessfully = $this->deleteReservation($id);

			if (!$isCancelledSuccessfully) {
				$this->translatedFlashMessage('Error when cancelling reservation!', FlashTypeEnum::DANGER);

				if ($product_id !== null) {
					$this->redirect('Product:details', ['id' => $product_id]);
				} else {
					$this->redirect('default');
				}
			}
		}

		$rowsUpdated = $this->reservationLogic->getRepository()->update($id, [ReservationEntity::DELETED => new DateTime()]);

		if ($rowsUpdated > 0) {
			$this->translatedFlashMessage('Reservation deleted successfully!', FlashTypeEnum::SUCCESS);

			if ($product_id !== null) {
				$this->redirect('Product:details', ['id' => $product_id]);
			} else {
				$this->redirect('default');
			}
		}

		$this->translatedFlashMessage('Error when deleting reservation!', FlashTypeEnum::DANGER);

		if ($product_id !== null) {
			$this->redirect('Product:details', ['id' => $product_id]);
		} else {
			$this->redirect('default');
		}
	}

	public function actionCreateInvoice(int $id): void
	{
		$this->setView('details');
		$this->redrawContent();

		if (!$this->user->isInRole('finance')) {
			$this->translatedFlashMessage('Only finance user can create the invoice!', FlashTypeEnum::DANGER);

			return;
		}

		$invoiceId = $this->invoiceLogic->calculateProvisionAndCreateInvoice($id);

		if ($invoiceId) {
			$this->invoiceDataLogic->generateInvoiceData($invoiceId);
			$this->translatedFlashMessage('Invoice created successfully!', FlashTypeEnum::SUCCESS);
		} else {
			$this->translatedFlashMessage('You should first cancel invoice!', FlashTypeEnum::WARNING);
		}
	}

	public function actionCancelInvoice(int $id, int $invoiceId): void
	{
		$this->setView('details');
		$this->redrawContent();

		if (!$this->user->isInRole('finance')) {
			$this->translatedFlashMessage('Only finance user can create the invoice!', FlashTypeEnum::DANGER);

			return;
		}

		$this->invoiceLogic->cancelInvoice($invoiceId, InvoiceFixedPartNumberEnum::CANCELLED_INVOICE_FIXED_PART);
		$this->translatedFlashMessage('Invoice cancelled successfully!', FlashTypeEnum::SUCCESS);
	}

	public function actionCreateMarkupInvoice(int $id): void
	{
		$this->setView('details');
		$this->redrawContent();

		if (!$this->user->isInRole('finance')) {
			$this->translatedFlashMessage('Only finance user can create markup invoice!', FlashTypeEnum::DANGER);

			return;
		}

		$invoiceId = $this->invoiceLogic->createInvoice($id, InvoiceFixedPartNumberEnum::MARKUP_INVOICE_FIXED_PART);

		if ($invoiceId) {
			$this->invoiceDataLogic->getMarkupInvoiceData($id, false, false, $invoiceId);
			$this->translatedFlashMessage('Invoice created successfully!', FlashTypeEnum::SUCCESS);
		} else {
			$this->translatedFlashMessage('You should first cancel invoice!', FlashTypeEnum::WARNING);
		}
	}

	public function actionCancelMarkupInvoice(int $id, int $invoiceId): void
	{
		$this->setView('details');
		$this->redrawContent();

		if (!$this->user->isInRole('finance')) {
			$this->translatedFlashMessage('Only finance user can create the invoice!', FlashTypeEnum::DANGER);

			return;
		}

		$this->invoiceLogic->cancelInvoice($invoiceId, InvoiceFixedPartNumberEnum::MARKUP_CANCELLED_INVOICE_FIXED_PART);
		$this->translatedFlashMessage('Invoice cancelled successfully!', FlashTypeEnum::SUCCESS);
	}

	public function createComponentReservationsGridControl(): DataGrid
	{
		$this->reservationGridFactory->setPresenter($this);

		return $this->reservationGridFactory->create();
	}

	public function createComponentUpcomingReservationsGridControl(): DataGrid
	{
		$this->reservationGridFactory->setPresenter($this);

		return $this->reservationGridFactory->createByType(ReservationGridTypeEnum::UPCOMING);
	}

	public function createComponentCancelledReservationsGridControl(): DataGrid
	{
		$this->reservationGridFactory->setPresenter($this);

		return $this->reservationGridFactory->createByType(ReservationGridTypeEnum::CANCELLED);
	}

	public function createComponentReservationForm(): BaseForm
	{
		return $this->createForm(ReservationFormFactory::class);
	}

	public function createComponentAgencyProvisionForm(): BaseForm
	{
		return $this->createForm(AgencyProvisionFormFactory::class);
	}

	public function createComponentReservationDetailsForm(): BaseForm
	{
		return $this->createForm(ReservationDetailsFormFactory::class);
	}

	public function createComponentRestAmountToBePaidControl(): RestAmountToBePaidControl
	{
		return $this->restAmountToBePaidControlFactory->create($this->reservationFullDetails);
	}

	public function createComponentAllianzControl(): AllianzControl
	{
		return $this->allianzControlFactory->create((int) $this->getParameter('id'));
	}

	protected function createComponentInvoiceControl(): InvoiceControl
	{
		return $this->invoiceControl;
	}

	public function handleSend(int $reservationId): void
	{
		$this->invoiceControl->send($reservationId);
		$this->translatedFlashMessage('Invoice is sent!', FlashTypeEnum::SUCCESS);
		$this->redrawControl('stylesheet');
		$this->redrawControl('content');
		$this->redrawControl('js');
		$this->redrawControl('flash');
	}

	public function createComponentActionLogGridControl(): DataGrid
	{
		$this->actionLogGridFactory->setPresenter($this);

		return $this->actionLogGridFactory->create();
	}

	public function createComponentReservationGuestControl(): ReservationGuestControl
	{
		return $this->reservationGuestControlFactory->create($this->reservationDetails, $this->reservationLog);
	}

}

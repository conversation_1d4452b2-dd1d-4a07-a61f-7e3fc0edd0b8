{varType App\Models\Entities\ReservationDetailsEntity $reservationDetails}
{varType App\Models\Entities\custom\ReservationFullDetailsEntity $reservationFullDetails}

{block content}
    <div class="d-flex justify-content-end justify-content-md-start mt-0">
        {if $reservationFromProductPage}
            <a n:href="Product:details, $reservationDetails->product_id" class="ajax btn btn-helper-sidebar btn-helper-sidebar-white waves-effect hide-for-print px-1 py-50">
                {embeddedSvg 'property-assets/app-assets/images/svg/left-arrow.svg',
                class => 'me-25',
                fill => '#333',
                height => 16,
                width => 16}
            {_'back to all reservations'}</a>
        {else}
            <a n:href="Default" class="ajax btn btn-helper-sidebar btn-helper-sidebar-white waves-effect hide-for-print px-1 py-50">
                {embeddedSvg 'property-assets/app-assets/images/svg/left-arrow.svg',
                class => 'me-25',
                fill => '#333',
                height => 16,
                width => 16}
            {_'back to all reservations'}</a>
        {/if}
    </div>
    <div class="row mt-0 mt-lg-2">
        <div class="col-lg-3 gx-lg-0 ps-1 hide-for-print">
            <div class="hide-for-print">
                <div class="d-flex flex-row flex-lg-column my-2 mt-3 my-lg-0 mb-lg-0 invoice-sidebar-helper-wrapper">
                    <a n:href="Edit, $id" n:if="!$reservationDetails->cancelled && (empty($reservationDetails->reservation_code) || $user->isInRole('admin'))"  class="btn btn-helper-sidebar round waves-effect">
                        {embeddedSvg 'property-assets/app-assets/images/svg/edit.svg',
                        class => 'me-25',
                        fill => '#121926',
                        height => 15,
                        width => 15}
                        {_'Edit'}
                    </a>
                    <a href="javascript:window.print();" class="btn btn-helper-sidebar rounded-lg waves-effect">
                        {embeddedSvg 'property-assets/app-assets/images/svg/printer.svg',
                        class => 'me-25',
                        fill => '#121926',
                        height => 16,
                        width => 16}
                        {_'Print'}
                    </a>

                    <a href="#" class="btn btn-helper-sidebar waves-effect round btn-download-reservation">
                        {embeddedSvg 'property-assets/app-assets/images/svg/download.svg',
                        class => 'me-25',
                        fill => '#121926',
                        height => 16,
                        width => 16}
                        {_'Download reservation'}
                    </a>

                    <a n:if="!$reservationDetails->cancelled && (empty($reservationDetails->reservation_code) || $user->isInRole('admin'))" n:href="Delete, $id" class="btn-reservation-delete btn btn-helper-sidebar btn-helper-sidebar-red waves-effect">
                        {embeddedSvg 'property-assets/app-assets/images/svg/trash.svg',
                        class => 'me-25',
                        height => 15,
                        width => 15}
                        {_'Delete'}
                    </a>
                    {if $reservationDetails->partner_first_installment_payment_status === \App\Common\Enums\InstallmentsPaymentStatusEnum::PAID}
                        {if !$lastInvoice->sent}
                            <a class="ajax btn btn-helper-sidebar btn-confirmation rounded-lg waves-effect w-100"
                               data-confirm="{_'Are you sure?'}"
                               data-naja-history="off"
                                    n:href="send!, $id">
                                {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                class => 'me-25',
                                fill => '#121926',
                                height => 18,
                                width => 18}
                                    {_'Send invoice'}
                            </a>
                        {else}
                            <a href="#" class="btn btn-helper-sidebar waves-effect round">
                                {_'The invoice has been sent to the owner'}
                            </a>
                        {/if}
                    {/if}
                    <div class="mt-lg-2 pt-lg-2 sidebar-helper-divider w-100"></div>
                        {if $user->isInRole(\App\Common\Enums\RoleEnum::FINANCE)}
                            <a class="btn btn-helper-sidebar btn-confirmation rounded-lg waves-effect w-100 ajax" data-confirm="{_'Are you sure?'}" data-naja-history="off"
                                href="{plink 'Reservation:createInvoice' $id}">
                                {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                class => 'me-25',
                                fill => '#333',
                                height => 16,
                                width => 16}
                                <span class="font-weight-bold">{_'Create invoice'}</span>
                            </a>
                        {/if}
                        {foreach $invoices as $invoice}
                            {continueIf $invoice->cancellation_id !== null}
                            {breakIf $iterator->counter === 2}
                            <a class="btn btn-helper-sidebar rounded-lg waves-effect w-100"
                                                        target="_blank"
                                                        href="{plink 'Invoice:default' $id, true, false, $invoice->id}">
                                {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                class => 'me-25',
                                fill => '#121926',
                                height => 18,
                                width => 18}
                                    {_'Download invoice'} - {$invoice->number_numeric_part}
                            </a>
                        {/foreach}
                        {if $user->isInRole(\App\Common\Enums\RoleEnum::FINANCE)}
                            {foreach $invoices as $invoice}
                                {continueIf $invoice->cancellation_id !== null}
                                <a class="btn btn-helper-sidebar btn-confirmation rounded-lg waves-effect w-100 ajax" data-naja-history="off"
                                   data-confirm="{_'Are you sure?'}"
                                   href="{plink 'Reservation:cancelInvoice' $id, $invoice->id}">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                    class => 'me-25',
                                    fill => '#333',
                                    height => 16,
                                    width => 16}
                                    <span class="font-weight-bold">{_'Cancell invoice'} - {$invoice->number_numeric_part}</span>
                                </a>
                            {/foreach}
                        {/if}
                        {if $isMarkupSupported}
                            <div class="mt-lg-2 pt-lg-2 sidebar-helper-divider w-100"></div>
                            {if $user->isInRole(\App\Common\Enums\RoleEnum::FINANCE)}
                                <a class="btn btn-helper-sidebar btn-confirmation rounded-lg waves-effect w-100 ajax" data-confirm="{_'Are you sure?'}" data-naja-history="off"
                                   href="{plink 'Reservation:createMarkupInvoice' $id}">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                    class => 'me-25',
                                    fill => '#333',
                                    height => 16,
                                    width => 16}
                                    <span class="font-weight-bold">{_'Create markup invoice'}</span>
                                </a>
                            {/if}
                            {foreach $markupInvoices as $markupInvoice}
                                {continueIf $markupInvoice->cancellation_id !== null}
                                {breakIf $iterator->counter === 2}
                                <a class="btn btn-helper-sidebar rounded-lg waves-effect w-100"
                                   target="_blank"
                                   href="{plink 'Invoice:markup' $id, true, false, $markupInvoice->id}">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                    class => 'me-25',
                                    fill => '#121926',
                                    height => 18,
                                    width => 18}
                                    {_'Download markup invoice'} - {$markupInvoice->number_numeric_part}
                                </a>
                            {/foreach}
                            {if $user->isInRole(\App\Common\Enums\RoleEnum::FINANCE)}
                                {foreach $markupInvoices as $markupInvoice}
                                    {continueIf $markupInvoice->cancellation_id !== null}
                                    <a class="btn btn-helper-sidebar btn-confirmation rounded-lg waves-effect w-100 ajax" data-naja-history="off"
                                       data-confirm="{_'Are you sure?'}"
                                       href="{plink 'Reservation:cancelMarkupInvoice' $id, $markupInvoice->id}">
                                        {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                        class => 'me-25',
                                        fill => '#333',
                                        height => 16,
                                        width => 16}
                                        <span class="font-weight-bold">{_'Cancel markup invoice'} - {$markupInvoice->number_numeric_part}</span>
                                    </a>
                                {/foreach}
                            {/if}
                        {/if}
                        {if $cancelledInvoices || $cancelledMarkupInvoices}
                        <div class="mt-lg-2 pt-lg-2 sidebar-helper-divider w-100"></div>
                            <strong>{_'Archived documents'}</strong>
                            {foreach $invoices as $invoice}
                                {continueIf $invoice->cancellation_id === null}
                                <a class="btn btn-helper-sidebar rounded-lg waves-effect w-100"
                                   target="_blank"
                                   href="{plink 'Invoice:default' $id, true, false, $invoice->id}">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                    class => 'me-25',
                                    fill => '#121926',
                                    height => 18,
                                    width => 18}
                                    {_'Download invoice'} - {$invoice->number_numeric_part}
                                </a>
                            {/foreach}
                            {foreach $cancelledInvoices as $cancelledInvoice}
                                <a class="btn btn-helper-sidebar rounded-lg waves-effect w-100"
                                   target="_blank"
                                   href="{plink 'Invoice:default' $id, true, true, $cancelledInvoice->id}">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                    class => 'me-25',
                                    fill => '#121926',
                                    height => 18,
                                    width => 18}
                                    {_'Download cancelled invoice'} - {$cancelledInvoice->number_numeric_part}
                                </a>
                            {/foreach}
                            {foreach $markupInvoices as $markupInvoice}
                                {continueIf $markupInvoice->cancellation_id === null}
                                <a class="btn btn-helper-sidebar rounded-lg waves-effect w-100"
                                   target="_blank"
                                   href="{plink 'Invoice:markup' $id, true, false, $markupInvoice->id}">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                    class => 'me-25',
                                    fill => '#121926',
                                    height => 18,
                                    width => 18}
                                    {_'Download markup invoice'} - {$markupInvoice->number_numeric_part}
                                </a>
                            {/foreach}
                            {foreach $cancelledMarkupInvoices as $cancelledMarkupInvoice}
                                <a class="btn btn-helper-sidebar rounded-lg waves-effect w-100"
                                   target="_blank"
                                   href="{plink 'Invoice:markup' $id, true, true, $cancelledMarkupInvoice->id}">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/invoice-3.svg',
                                    class => 'me-25',
                                    fill => '#121926',
                                    height => 18,
                                    width => 18}
                                    {_'Download cancelled markup invoice'} - {$cancelledMarkupInvoice->number_numeric_part}
                                </a>
                            {/foreach}
                        {/if}
                </div>
            </div>
        </div>
        <div class="col-lg-9">
            <div class="card">
                <div class="card-body" id="reservation">
                    <div class="justify-content-between d-flex">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2 class="mb-0 me-1 fw-bolder first-letter-capital">{_'Reservation'} #{$reservation_code}{if $reservationDetails->deal_stage === App\Common\Enums\DealStagesEnum::STORNO} - {_'STORNO'|firstUpper}{/if}</h2>
                        </div>
                    </div>
                    
                    <div class="info-container" n:if="$reservationDetails">
                        <div class="pt-2 pb-1 border-bottom">
                            <a n:href="Product:details, $reservationDetails->product_id" class="ajax">
                                <h4 class="mb-1 fw-bolder first-letter-capital">{$reservationDetails->product_name}</h4>
                            </a>
                            {control reservationGuestControl}
                        </div>
                        <div class="pt-2 pb-1 border-bottom">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Guest Information'}</h4>
                            <ul class="list-unstyled">
                                <li class="mb-75">{$reservationDetails->contact_firstname} {$reservationDetails->contact_lastname}</li>
                                {if $reservationLog !== null && $reservationLog->phone !== $reservationDetails->phone}
                                    <li class="mb-75"><del>{$reservationLog->phone}</del></li>
                                {/if}
                                <li class="mb-75">{$reservationDetails->phone}</li>
                            </ul>
                        </div>
                        <div class="pt-2 pb-1 border-bottom" n:if="!empty($concierges)">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Concierge'}</h4>
                            <h5 n:if="$conciergesInPrice" class="mb-1 fw-bolder">{_'Included in reservation price'}</h5>
                            <ul n:if="$conciergesInPrice" class="list-unstyled">
                                <li n:foreach="$conciergesInPrice as $concierge" class="mb-75">
                                    {$concierge->name}
                                </li>
                            </ul>
                            <h5 n:if="$conciergesNotInPrice" class="mb-1 fw-bolder">{_'Not included in reservation price'}</h5>
                            <ul n:if="$conciergesNotInPrice" class="list-unstyled">
                                <li n:foreach="$conciergesNotInPrice as $concierge" class="mb-75">
                                    {$concierge->name} -
                                    {if $concierge->price}
                                        <span>{$concierge->price|number,2} €</span>
                                    {else}
                                        <span>{_'Price on request'}</span>
                                    {/if}
                                </li>
                            </ul>
                        </div>
                        <div class="pt-2 pb-1 border-bottom">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Period'}</h4>
                            <ul class="list-unstyled">
                                {if $reservationLog !== null && ($reservationLog->arrive->format('d.m.Y.') !== $reservationDetails->arrive->format('d.m.Y.') ||
                                $reservationLog->departure->format('d.m.Y.') !== $reservationDetails->departure->format('d.m.Y.'))}
                                    <li class="mb-75 fw-bolder"><del>{$reservationLog->arrive->format('d.m.Y.')} → {$reservationLog->departure->format('d.m.Y.')}</del></li>
                                {/if}
                                <li class="mb-75 fw-bolder">{$reservationDetails->arrive->format('d.m.Y.')} → {$reservationDetails->departure->format('d.m.Y.')}</li>
                                {if $reservationLog !== null && $reservationDetails->departure->diff($reservationDetails->arrive)->format('%a') !== $reservationLog->departure->diff($reservationLog->arrive)->format('%a')}
                                    <li class="mb-75"><del>{_'Length of stay'|firstUpper}: <span class="fw-bolder">{$reservationLog->departure->diff($reservationLog->arrive)->format('%a')} {_'nights'}</span></del></li>
                                {/if}
                                <li class="mb-75">{_'Length of stay'|firstUpper}: <span class="fw-bolder">{$reservationDetails->departure->diff($reservationDetails->arrive)->format('%a')} {_'nights'}</span></li>
                                {if $reservationFullDetails->arrive_in_days > 0}
                                    <li class="mb-75">
                                        <span>{_'Arriving in'|firstUpper}:</span> <span class="fw-bolder">{$reservationFullDetails->arrive_in_days} {_'days'}</span>
                                    </li>
                                {/if}
                                            
                            </ul>
                        </div>
                        <div class="pt-2 pb-1 border-bottom">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Invoice'}</h4>
                            {var $initialAmount = $reservationFullDetails->total_price_without_discount}
                            {var $bruttoAmount = $reservationFullDetails->total_price}
                            {var $discountAmount = $initialAmount - $bruttoAmount}
                            {var $agencyProvisionAmount = $bruttoAmount * ($reservationFullDetails->agency_provision_percentage / 100) * 1.25}
                            {var $nettoAmount = $bruttoAmount - $agencyProvisionAmount}

                            <div class="row">
                                <div class="col-12 col-lg-12">
                                    <ul class="list-unstyled">
                                    
                                        {if $discountAmount > 0}
                                            <li class="mb-75">
                                                <span class="ps-0"><span>{_'Reservation price without discount'|firstUpper}:</span></span>
                                                <span class="ps-0"><span class="fw-bolder">{$initialAmount|number,2,'.'} €</span></span>
                                            </li>
                                            <li class="mb-75">
                                                <span class="ps-0"><span>{_'Discount amount'|firstUpper}:</span></span>
                                                <span class="ps-0"><span class="fw-bolder">{$discountAmount|number,2,'.'} €</span></span>
                                            </li>
                                        {/if}
                                        <li class="mb-75">
                                            <span class="ps-0"><span>{_'Reservation Amount BRUTO'|firstUpper}:</span></span>
                                            <span class="ps-0"><span class="fw-bolder">{$bruttoAmount|number,2,'.'} €</span></span>
                                        </li>
                                        {if $reservationFullDetails->agency_provision_percentage > 0}
                                            <li class="mb-75">
                                                <span class="ps-0"><span>{_'Agency provision amount'|firstUpper}:</span></span>
                                                <span class="ps-0"><span class="fw-bolder">{$agencyProvisionAmount|number,2,'.'} €</span></span>
                                            </li>
                                        {/if}
                                        <li class="mb-75">
                                            <span class="ps-0"><span>{_'Reservation Amount NETO'|firstUpper}:</span></span>
                                            <span class="ps-0"><span class="fw-bolder">{$nettoAmount|number,2,'.'} €</span></span>
                                        </li>
                                        

                                    </ul>
                                </div>
                            </div>
                        </div>

                        {control restAmountToBePaidControl}
                        {control allianzControl}
                        <div class="pt-2 pb-1 border-bottom">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Cancellation Policy'}</h4>
                            <div class="row">
                                <div class="col-12 col-lg-12">
                                    <ul class="list-unstyled">
                                        <li>
                                            <span class="ps-0">{_'Cancellation Policy'|firstUpper}:</span>
                                            <span class="ps-0">
                                                <span class="fw-bolder">
                                                    {if $reservationFullDetails->cancellation_policy_rule_id !== null}
                                                        {_'cancellation policy rule '. $reservationFullDetails->cancellation_policy_rule_id|noescape}
                                                    {else}
                                                        -
                                                    {/if}
                                                </span>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="pt-2 pb-1" n:if="empty($reservationDetails->created_by)">
                            {if $user->isInRole('admin')}
                                {form reservationDetailsForm, class => 'ajax form form-vertical'}
                                    <h4 class="mb-1 fw-bolder first-letter-capital">{_'Request from guests'|firstUpper}</h4>
                                    {input request_from_guests,  class => 'form-control'}
                                    <div class="form-check mt-1 hide-for-print">
                                        <input class="form-check-input" type="checkbox" n:name="request_from_guests_approved">
                                        <label class="form-check-label" n:name="request_from_guests_approved">
                                            {_'Approve changes'}
                                        </label>
                                    </div>
                                    {input submit, class=>"btn btn-dark me-1 mt-1 waves-effect waves-float waves-light hide-for-print"}
                                {/form}
                            {elseif $reservationDetails->request_from_guests_approved}
                                <h4 class="mb-1 fw-bolder first-letter-capital">{_'Request from guests'}</h4>
                                <p>
                                    {$reservationDetails->request_from_guests}
                                </p>
                            {/if}
                        </div>

                        <div class="pt-2 pb-1" n:ifset="$eVisitorReservationData->id">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'evisitor.info.filled_out'}</h4>
                            <p>
                                {$eVisitorLink}
                            </p>
                            <p>
                                {if $eVisitorReservationData->confirmed}
                                    ✅ {_'Yes'} – <span class="fw-bold">{$eVisitorReservationData->confirmed|date:'d.m.Y H:i'}</span>
                                {else}
                                    ❌ {_'No'}
                                {/if}
                            </p>
                        </div>

                        <div class="row">
                        </div>
                    </div>
                </div>
            </div>
            <div class="card" n:if="$user->isInRole('admin')">
                <div class="card-body" id="reservation">
                    <div class="justify-content-between d-flex">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2 class="mb-0 me-1 fw-bolder first-letter-capital">{_'Change agency provision'}</h2>
                        </div>
                    </div>
                    <div class="info-container">
                        <div class="pt-2 pb-1 border-bottom">
                            {control agencyProvisionForm}
                        </div>
                    </div>
                </div>
            </div>


            {if $user->isInRole('admin')}
                <div class="card">
                    <div class="card-text">
                        <div class="datagrid-box table-responsive">
                            {control actionLogGridControl}
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    </div>
{/block}
{block js}
    <script n:srcv="/property-assets/assets/vendors/js/html2pdf.bundle.min.js"></script>
    <script>
        $(document).ready(function () {
            $('.btn-reservation-delete').click(function (e) {
                let status = confirm('{_'You sure you want to delete reservation?'}');

                if (!status) {
                    e.stopPropagation();
                    e.preventDefault();

                    return false;
                }
            });

            $('.btn-download-reservation').click(function (e) {

                if (typeof window.html2pdf === 'undefined') {
                    return;
                }

                let element = document.getElementById('reservation');
                let opt = {
                    filename:     'reservation.pdf',
                    image:        { type: 'jpeg', quality: 0.98 },
                    html2canvas:  { scale: 2 },
                    jsPDF:        { unit: 'in', format: 'letter', orientation: 'landscape' }
                };

                // New Promise-based usage:
                html2pdf().set(opt).from(element).save();
            });
        });
    </script>
{/block}

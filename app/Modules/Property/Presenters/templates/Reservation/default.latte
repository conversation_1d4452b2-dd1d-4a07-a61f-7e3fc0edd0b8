{block content}
    <div class="row mx-md-2">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h1 class="h1 text-capitalize mb-0">{_'Reservations'}</h1>
                <div class="show-on-tablet">
                    <div class="action-buttons">
                        <div class="d-flex justify-content-center justify-content-xxl-start">
                            <ul class="nav nav-tabs mb-0" role="tablist" n:if="$allowReservationButton">
                                <li class="nav-item">
                                    <a class="btn btn-dark btn-sm waves-effect btn-36" n:href="New">
                                        <span class="d-flex align-items-center h6 mb-0 gap-25 text-white">
                                            {_'Add new reservation'}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#ffffff" stroke="#ffffff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus font-small-4">
                                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                                <line x1="5" y1="12" x2="19" y2="12"></line>
                                            </svg>
                                        </span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <a n:if="$allowReservationButton" n:href="New" class="btn btn-icon new-btn-floating rounded-circle shadow-md" style="display: none;" title="{_'Add new reservation'}"><i data-feather="plus"></i></a>
            <div class="card-text">
                <div class="datagrid-box">
                    {control reservationsGridControl}
                </div>
            </div>
        </div>
    </div>
    <div class="notification-overlay"></div>
    <div class="notification-wrapper notification-pwa">
        <div class="d-flex justify-content-end align-items-start">
            <button class="notification-close notification-close--pwa p-0">
                    {embeddedSvg 'assets/img/svg/close.svg',
                        class => 'notification-close-icon',
                        fill => '#fff',
                        height => 12,
                        width => 12}
            </button>
        </div>
        <div class="d-flex justify-content-center mb-1">
            <div class="notification-logo-wrapper">
            {embeddedSvg 'assets/img/svg/logo.svg',
                class => 'notification-logo-icon',
                fill => '#fff',
                height => 30,
                width => 30}
            </div>
        </div>
        <p class="notification-heading mb-2">{_'Get the Owner Dashboard app'|firstUpper}</p>
        <p class="notification-subheading text-center mb-50">{_'Access your villa dashboard in a single tap?'|firstUpper}</p>
        <p class="notification-text text-center">{_'Add this as an app to your home screen for quick and easy management of your villa on the go.'|firstUpper}</p>
        <div class="d-flex justify-content-center notification-part-android mt-2">
            <button class="btn btn-dark btn-sm notification-btn--pwa add-to-home px-2 py-1">{_'Add app to home screen'|firstUpper}</button>
        </div>
        <div class="text-center pt-75 notification-part-ios notification-cta-ios mt-2">
            <span>{_'Tap'|firstUpper}</span>
            <span class="notification-icon-ios">
                {embeddedSvg 'assets/img/svg/ios-share.svg',
                    class => 'notification-icon-ios',
                    fill => '#007AFF',
                    height => 20,
                    width => 20}
            </span>
            <span> {_'and then'}</span>
            <span>"{_'Add to home screen'|firstUpper}"</span>
            <div class="triangle"></div>
        </div>
    </div>
{/block}
{block js}
    {include ../_js/Grid/helpers.latte}
    {include ../_js/Grid/dateTimePicker.latte}
    <script>
        var LOCAL_STORAGE_KEY = 'reservationListView';
        naja.addEventListener('success', function () {
            registerGridEvents();
        });
        registerGridEvents();
    </script>
<script n:syntax="off">
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/service-worker.js')
        });
    }

    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();

        deferredPrompt = e;
        const addBtns = document.getElementsByClassName('add-to-home');
        for (let i = 0; i < addBtns.length; i++) {
            addBtns[i].addEventListener('click', () => {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    deferredPrompt = null;
                });
            });
        }
    });
</script>
<script>
    $(document).ready(function () {
        function getMobileOperatingSystem() {
            var userAgent = navigator.userAgent || navigator.vendor || window.opera;

            if (/iPhone/.test(userAgent)) {
                return 'iPhone';
            }

            // Check for iPad (iOS 12 and below)
            if (/iPad/.test(userAgent)) {
                return 'iPad';
            }

            // Check for iPad on iOS 13+ (iPads running iOS 13+ return 'MacIntel' as the platform)
            if (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) {
                return 'iPad';
            }

            // Check for Android
            if (/android/i.test(userAgent)) {
                return 'Android';
            }

            return 'unknown';
            }

        const device = getMobileOperatingSystem();

        if (localStorage.getItem('pwa-notification-reacted') === null) {
            localStorage.setItem('pwa-notification-reacted', 'false');
        }

        if (window.navigator.standalone || window.matchMedia('(display-mode: standalone)').matches) {
            localStorage.setItem('pwa-notification-reacted', 'true');
            $('.nav-item-pwa').addClass('d-none');
        }

        if(localStorage.getItem('pwa-notification-reacted') === 'false' && $(window).width() < 991) {
            $('.notification-close').on('click', function () {
                $(this).closest('.notification-wrapper').addClass('notification-closing');
                $('.notification-overlay').fadeOut();
                setTimeout(function () {
                    $(this).closest('.notification-wrapper').removeClass('notification-opening');
                }, 301);
            });

            function initialOpening() {
                setTimeout(function () {
                    if (device === 'iPhone') {
                        $('.notification-part-android').addClass('d-none');
                        $('.notification-pwa').addClass('notification-ios');
                    } else if (device === 'iPad') {
                        $('.notification-part-android').addClass('d-none');
                        $('.notification-pwa').addClass('notification-ios');
                        $('.notification-pwa').addClass('notification-ipad');
                    } else if (device === 'Android') {
                        $('.notification-part-ios').addClass('d-none');
                        $('.notification-pwa').addClass('notification-android');
                    } else {
                        $('.notification-part-ios').addClass('d-none');
                        $('.notification-pwa').addClass('notification-android');
                    }

                    $('.notification-pwa').addClass('notification-opening');
                    $('.notification-overlay').fadeIn();
                }, 200);
            }
            initialOpening();

            $('.notification-close--pwa, .notification-btn--pwa, .notification-overlay').on('click', function () {
                localStorage.setItem('pwa-notification-reacted', 'true');
                $('.notification-wrapper').addClass('notification-closing');
                $('.notification-overlay').fadeOut();
                setTimeout(function () {
                    $('.notification-wrapper').removeClass('notification-opening');
                }, 301);
            });
        }
    });
</script>
<script>
    $(document).ready(function () {
        $('.reservation-details--js').on('mousedown', function(e) {
            // 0 = Left click, 1 = Middle click
            if (e.button === 0 || e.button === 1) {
                const href = $(this).data('href');
                window.open(href, '_blank');
            }
        });

        $('#hideCancelled').on('change', function() {
            sendRequest();
        });

        naja.addEventListener('success', function () {
            $('.loader-grid-table').addClass('d-none');

            $('#hideCancelled').on('change', function() {
                sendRequest();
            });

            $('.reservation-details--js').on('mousedown', function(e) {
                // 0 = Left click, 1 = Middle click
                if (e.button === 0 || e.button === 1) {
                    const href = $(this).data('href');
                    window.open(href, '_blank');
                }
            });
        });

        naja.addEventListener('start', function (event) {
            $('.loader-grid-table').removeClass('d-none');
        });
        
    });
</script>
{/block}

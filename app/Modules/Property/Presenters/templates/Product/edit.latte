{block stylesheet}
    <link rel="stylesheet" type="text/css" n:hrefv="/property-assets/assets/vendors/css/dropzone.min.css">
{/block}
{block content}
    <section class="modern-vertical-wizard" n:wizard="formWizard">
        <div class="row mx-md-2">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-2 mt-sm-1">
                    {if $product->name}
                    <div class="d-flex flex-column">
                        <h1 class="h1 text-capitalize mb-0">{$product->name}</h1>
                        <p class=" mb-0 ms-25">{_'Edit property'|firstUpper}</p>
                    </div>
                    {else}
                        <h1 class="h1 text-capitalize mb-0">{_'New property'}</h1>
                    {/if}
                    <a n:href="details, $presenter->getParameter('id')" class="btn btn-flat-dark waves-effect pe-0 pe-md-2">
                        {embeddedSvg 'property-assets/app-assets/images/svg/left-arrow.svg',
                        class => 'me-25',
                        fill => '#333',
                        height => 16,
                        width => 16}
            {_'Back'}
                    </a>
                </div>
            </div>
        </div>
        <div class="row mx-md-2">
            <div class="col-md-12">
                <div class="bs-stepper vertical wizard-modern modern-vertical-wizard-example wizard-modern-sticky mt-1">
                    <div class="bs-stepper-header" role="tablist">
                        {if !$wizard->isSuccess()}
                            {foreach $wizard->steps as $step}
                                {continueIf $user->isInRole('partner') && $step === 1}
                                {php $stepChangesColumn = 'step_' . $step . '_changes'}
                                {php $stepApprovedColumn = 'step_' . $step . '_approved'}
                                <div n:class="step,
                                $wizard->isDisabled($step) ? disabled,
                                $wizard->isActive($step) ? active,
                                $notApprovedData !== null && !empty($notApprovedData->$stepChangesColumn) && !$notApprovedData->$stepApprovedColumn ? step-property-changes" data-target="#account-details" role="tab">
                                    <a class="ajax tab-link" n:tag-if="$wizard->useLink($step)" n:href="changeStep! $step">
                                        <button type="button" class="step-trigger" aria-selected="true">
                                            <span class="bs-stepper-box">
                                                <img
                                                    src="/property-assets/app-assets/images/svg/{$wizard->getStepData($step)['icon']}.svg"
                                                    class="bs-stepper-icon"
                                                    alt="{$wizard->getStepData($step)['icon']}"
                                                    width="18"
                                                    height="18" />

                                            </span>
                                            <span class="bs-stepper-label">
                                                {var $slideName = $wizard->getStepData($step)['name']}
                                                {if !Nette\Utils\Strings::contains($product->product_type, 'pool') && $wizard->getStepData($step)['name'] === 'Pool & Spa'}
                                                    {php $slideName = 'Spa'}
                                                {elseif $step === 2}
                                                    {php $slideName .= ' ' . $product->product_type}
                                                {/if}
                                                <span class="bs-stepper-title">{_$slideName}</span>
                                            </span>
                                        </button>
                                    </a>
                                </div>
                                <div class="line"></div>
                            {/foreach}
                        {/if}
                    </div>
                    <div class="container-fluid g-0 g-lg-1 product-form-wizard">
                        <div id="account-details" class="content active dstepper-block" role="tabpanel">
                        {step 1}
                            {include 'form.latte', form => $form, step => 1}
                        {/step}
                        {step 2}
                            {include 'form.latte', form => $form, step => 2}
                        {/step}
                        {step 3}
                            {include 'form.latte', form => $form, step => 3}
                        {/step}
                        {step 4}
                            {include 'form.latte', form => $form, step => 4}
                        {/step}
                        {step 5}
                            {include 'form.latte', form => $form, step => 5}
                        {/step}
                        {step 6}
                            {include 'form.latte', form => $form, step => 6}
                        {/step}
                        {step 7}
                            {include 'form.latte', form => $form, step => 7}
                        {/step}
                        {step 8}
                            {include 'form.latte', form => $form, step => 8}
                        {/step}
                        {step 9}
                            {include 'form.latte', form => $form, step => 9}
                        {/step}
                        {step 10}
                            {include 'form.latte', form => $form, step => 10}
                        {/step}
                        {step 11}
                            {include 'form.latte', form => $form, step => 11}
                        {/step}
                        {step 12}
                            {control gallery}
                            {include 'form.latte', form => $form, step => 12}
                        {/step}
                        {step 13}
                            {include 'form.latte', form => $form, step => 13}
                        {/step}
                        {step success}
                            {_'Registration was successful'}
                        {/step}
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{/block}
{block js}
    {include ../_js/Product/cards.latte}
    {include ../_js/Product/distances.latte}
    <script>
        document.addEventListener('keydown', function (event) {
            if (event.key === 'Enter') {
                event.preventDefault(); // Prevent form submission and page reload
            }
        });

        $(function () {
            {if $presenter->getUser()->isInRole('admin') && $currentStep === 2}
                if (typeof tinymce !== 'undefined') {
                    tinymce.remove('.wysiwyg > textarea');
                    tinymce.init({
                        selector: '.wysiwyg > textarea',
                        setup: (editor) => {
                            editor.on("blur", function (e) {
                                tinymce.triggerSave();
                                console.log("Trigger save working");
                            });
                        },
                    });
                }
            {/if}

            $('#frm-formWizard-step2-region').select2Remote(
                    {plink Product:regionAutoComplete, $presenter->getParameter('id')}, {
                    dropdownParent: $('#frm-formWizard-step2-region').parent(),
                    placeholder: {_'Select value'},
                    language: {
                       inputTooShort: function () {
                            return {_'Please enter 1 or more characters'};
                        },
                        noResults: function () {
                            return {_'No results found'};
                        }
                    }
                }
            );
            $('#frm-formWizard-step2-destination').select2Remote({plink Product:destinationAutoComplete, $presenter->getParameter('id')}, {
                dropdownParent: $('#frm-formWizard-step2-destination').parent(),
                placeholder: {_'Select value'},
                tags: true,
                language: {
                    inputTooShort: function () {
                        return {_'Please enter 1 or more characters'};
                    },
                    noResults: function () {
                        return {_'No results found'};
                    }
                }
            });

            $('#frm-formWizard-step2-destination').on('change', function (e) {
                let destinationId = $('#frm-formWizard-step2-destination').val();

                if($('#frm-formWizard-step2-region').val() === '' && !isNaN(destinationId) && !isNaN(parseFloat(destinationId))) {
                    $.ajax({
                        url: {plink Product:getRegion, $presenter->getParameter('id')},
                        type: 'GET',
                        data: {
                            'destinationId': destinationId,
                        },
                        success: function (response) {
                            if (typeof response[0] === 'undefined') {
                                return;
                            }
                            let newOption = new Option(response[0].text, response[0].id, true, true);
                            $('#frm-formWizard-step2-region').append(newOption).trigger('change');
                        }
                    });
                }

            });

            const originalFormData = getFormData($('form[id^="frm-formWizard"]'));

            $('.tab-link').on('click', function(event) {
                event.preventDefault(); // Prevent default anchor behavior

                const currentFormData = getFormData($('form[id^="frm-formWizard"]'));

                if (JSON.stringify(originalFormData) === JSON.stringify(currentFormData)) {
                    console.log('No changes detected, skipping AJAX call.');
                    return; // Exit if no changes
                }

                const form = $('form[id^="frm-formWizard"]')[0];
                const formData = new FormData(form);

                // Example of sending the form data using AJAX
                $.ajax({
                    url: form.action, // Use the form's action attribute
                    method: form.method, // Use the form's method attribute
                    data: formData,
                    processData: false, // Prevent jQuery from processing the data
                    contentType: false, // Prevent jQuery from setting content type
                    success: function(response) {
                        console.log('Form submitted successfully:', response);
                    },
                    error: function(error) {
                        console.error('Error submitting form:', error);
                    }
                });
            });

            $('.beach-types').select2();

            $(document).on('select2:open', () => {
                document.querySelector('.select2-search__field').focus();
            });

            // Download property documents
            $('button[name="download_document_solution"], button[name="download_user_permission"]').on('click', function () {
                // Get the file URL from the data-url attribute
                const fileUrl = $(this).data('url');

                if (fileUrl) {
                    const a = $('<a>')
                        .attr('href', fileUrl)
                        .attr('target', '_blank')
                        .attr('download', '');
                    $('body').append(a);
                    a[0].click();
                    a.remove();
                }
            });
        });

        naja.addEventListener('complete', function(event) {
            if (typeof event.response.skipScrollToTop !== 'undefined') {
                return;
            }
            document.body.scrollTop = document.documentElement.scrollTop = 0;

            if (typeof Waves !== 'undefined') {
                Waves.attach('.next-button,.back-button');
            }
        });

        // Function to get form data as an object
        function getFormData($form) {
            const formData = {};
            $form.serializeArray().forEach(({ name, value }) => {
                formData[name] = value;
            });
            return formData;
        }

        function renderOwner(element) {
            if (element.value === 'new' || element.value === '') {
                return;
            }

            let url = window.location.href;
            if (url.indexOf('?') > -1){
                url += '&owner_id=' + parseInt(element.value);
            }else{
                url += '?owner_id=' + parseInt(element.value);
            }
            window.location.href = url;
        }

        function showAlert(element) {

            if ($('input[data-welcome-book=1]').length > 0 &&
                !Nette.validateControl($('input[data-welcome-book=1]'))) {
                return;
            }

            if (typeof Swal !== 'undefined') {
                Swal.fire(
                    element.getAttribute('data-message-header'),
                    element.getAttribute('data-message-body'),
                    'success'
                );
            }
        }
    </script>
    {include ../_js/Product/maps.latte}
    <script defer n:srcv="/admin_assets/js/admin-gallery.js"></script>
{/block}

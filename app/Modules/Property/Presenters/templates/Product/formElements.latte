{foreach $group->getControls() as $field}
    {var $cssClass = ''}
    {if isset($field->getControlPart()->attrs['class']) && $field->getOptions()['type'] !== 'button'}
        {if is_array($field->getControlPart()->attrs['class'])}
            {foreach $field->getControlPart()->attrs['class'] as $class}
                {continueIf $class === 'form-control'}
                {php $cssClass .= ' ' . $class}
            {/foreach}
        {else}
            {php $cssClass = $field->getControlPart()->attrs['class']}
        {/if}
    {/if}
    <div n:class="$field instanceof Contributte\FormsBootstrap\Inputs\CheckboxInput ? 'form-check form-check-dark form-check-inline',
    $field->getOptions()['type'] === 'button' && $field->getName() === 'prev' ? 'float-start',
    $field->getOptions()['type'] === 'button' && $field->getName() === 'next' ? 'float-end',
    $field->getOptions()['type'] === 'button' && $field->getName() === 'finish' ? 'float-end',
    $field->getOptions()['type'] !== 'button' ? 'mb-1',
    $cssClass"
    {if isset($field->getControlPart()->attrs['data-depending'])} data-depending="{$field->getControlPart()->attrs['data-depending']}" {/if}
    {if isset($field->getControlPart()->attrs['data-depending-values'])} data-depending-values="{$field->getControlPart()->attrs['data-depending-values']}" {/if}
    {if isset($field->getControlPart()->attrs['data-unique-values'])} data-unique-values="{$field->getControlPart()->attrs['data-unique-values']}" {/if}
    >
        {if $field->getName() === 'map'}
            <div id="map" style="width: 100%; height: 400px"></div>
        {/if}
        {var $errorCss = $field->getError() === NULL ? '' : ' is-invalid'}
        {if str_contains($cssClass, 'input-group')}
            <div class="input-group-prepend">
                <div class="input-group-text">{_$field->getCaption(), $product->product_type}</div>
            </div>
        {elseif $field->getOptions()['type'] !== 'checkbox' && $field->getOptions()['type'] !== 'button'}
            <label for="{$field->getName()}" n:class="form-label">{_$field->getCaption(), $product->product_type}</label>
        {else}
            {label $field->getName(), class => 'form-label'/}
        {/if}
        {if $field->getOptions()['type'] === 'checkbox'}
            {if $field instanceof Contributte\FormsBootstrap\Inputs\CheckboxInput}
                <input n:name="$field->getName()" n:class="form-check-input, $errorCss">
                <label n:name="$field->getName()" n:class="form-check-label">{_$field->getCaption(), $product->product_type}</label>
            {else}
                {input $field->getName()}
            {/if}
        {elseif $field->getOptions()['type'] === 'button'}
            {input $field->getName()}
        {else}
            {if $field instanceof Contributte\FormsBootstrap\Inputs\RadioInput && isset($field->getControlPart()->attrs['custom-rendering'])}
                {include 'radioListElements.latte', name => $field->getName(), values => $field->getItems(), defaultValue => $field->getSelectedItem()}
            {else}
                {input $field->getName(), class => 'form-control' . $errorCss}
            {/if}
        {/if}
        {if $field->getError() !== NULL}
            <div class="invalid-feedback">{inputError $field->getName()}</div>
        {/if}
    </div>
{/foreach}
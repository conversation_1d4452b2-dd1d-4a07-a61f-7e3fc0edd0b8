<div class="col-12">
    <div class="form-card p-2 mb-2" n:multiplier="distanceMultiplier">
        <div class="card-header p-0 text-capitalize">
            <h4 class="card-title">{_'Other location distances'}</h4>
        </div>
        <div class="card-body p-0">
            <input type="hidden" class="form-control" n:name="id">
            <div class="row pb-3 pb-lg-2 align-items-end">
                <div class="col-lg-3 col-12">
                    <label>{_'Service name'}</label>
                    <select class="form-control" n:name="product_distance_type_id"></select>
                </div>
                <div class="col-lg-3 col-12">
                    <label>{_'Object name'}</label>
                    <input class="form-control" n:name="name">
                </div>
                <div class="col-lg-3 col-12">
                    <label>{_'Distance'}</label>
                    <fieldset>
                        <div class="input-group">
                            <input class="form-control" n:name="distance">
                            <button type="button" class="btn btn-outline-dark dropdown-toggle waves-effect disable-validation metric-button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="metric-text">{_'Kilometers'}</span>
                            </button>
                            <div class="dropdown-menu metric-dropdown-menu">
                                <a class="dropdown-item metric-option" data-value="Kilometara" href="#">{_'Kilometers'}</a>
                                <a class="dropdown-item metric-option" data-value="Metara" href="#">{_'Meters'}</a>
                                {input metric_type}
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-md-1 col-12">
                    {btnRemove class => 'btn btn-outline-danger text-nowrap px-1 waves-effect'}
                </div>
        </div>
    </div>
</div>
{btnCreate distanceMultiplier class => 'btn btn-icon btn-primary add-new-button waves-effect waves-float waves-light mb-2'}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize metric dropdowns for existing elements
    initializeMetricDropdowns();

    // Re-initialize when new elements are added via multiplier
    document.addEventListener('click', function(e) {
        if (e.target.closest('.add-new-button')) {
            setTimeout(function() {
                initializeMetricDropdowns();
            }, 100);
        }
    });

    function initializeMetricDropdowns() {
        document.querySelectorAll('.metric-dropdown-menu').forEach(function(dropdown) {
            const button = dropdown.previousElementSibling;
            const hiddenInput = dropdown.querySelector('input[name*="metric_type"]');
            const metricText = button.querySelector('.metric-text');

            if (!hiddenInput) return;

            // Set initial button text based on current value
            updateButtonText(button, hiddenInput.value);

            // Handle dropdown item clicks
            dropdown.querySelectorAll('.metric-option').forEach(function(item) {
                item.addEventListener('click', function(e) {
                    e.preventDefault();

                    const value = this.getAttribute('data-value');
                    const text = this.textContent.trim();

                    // Update hidden input
                    hiddenInput.value = value;

                    // Update button text
                    metricText.textContent = text;

                    // Close dropdown
                    const bsDropdown = bootstrap.Dropdown.getInstance(button);
                    if (bsDropdown) {
                        bsDropdown.hide();
                    }
                });
            });
        });
    }

    function updateButtonText(button, value) {
        const metricText = button.querySelector('.metric-text');
        if (!metricText) return;

        switch(value) {
            case 'Metara':
                metricText.textContent = '{_"Meters"}';
                break;
            case 'Kilometara':
            default:
                metricText.textContent = '{_"Kilometers"}';
                break;
        }
    }
});
</script>
<div class="col-12">
    <div class="form-card p-2 mb-2" n:multiplier="distanceMultiplier">
        <div class="card-header p-0 text-capitalize">
            <h4 class="card-title">{_'Other location distances'}</h4>
        </div>
        <div class="card-body p-0">
            <input type="hidden" class="form-control" n:name="id">
            <div class="row pb-3 pb-lg-2 align-items-end">
                <div class="col-lg-3 col-12">
                    <label>{_'Service name'}</label>
                    <select class="form-control" n:name="product_distance_type_id"></select>
                </div>
                <div class="col-lg-3 col-12">
                    <label>{_'Object name'}</label>
                    <input class="form-control" n:name="name">
                </div>
                <div class="col-lg-3 col-12">
                    <label>{_'Distance'}</label>
                    <fieldset>
                        <div class="input-group">
                            <input class="form-control" n:name="distance">
                            <button id="metric-button'" type="button" class="btn btn-outline-dark dropdown-toggle waves-effect disable-validation" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                {_'Kilometers'}
                            </button>
                            <div id="metric-items" class="dropdown-menu metric-dropdown-menu">
                                <a class="dropdown-item" data-value="Kilometara" href="#">{_'Kilometers'}</a>
                                <a class="dropdown-item" data-value="Metara" href="#">{_'Meters'}</a>
                                {input metric_type}
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-md-1 col-12">
                    {btnRemove class => 'btn btn-outline-danger text-nowrap px-1 waves-effect'}
                </div>
        </div>
    </div>
</div>
{btnCreate distanceMultiplier class => 'btn btn-icon btn-primary add-new-button waves-effect waves-float waves-light mb-2'}
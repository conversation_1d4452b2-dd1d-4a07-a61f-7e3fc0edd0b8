<script>
    flatPickCalendar = function() {
        let _divName;
        let _language;
        let _startMonth;
        let _picker;
        let _hasDefaultDates;
        let _notAvailableDates;
        let _arrivals;
        let _departures;
        let _ownReservations;
        let _agencyReservations;
        let _offers;
        let _statusDates;
        let _monthsToShow;
        let _defaultDates;
        let _calendarStateUnavailable = {_'Unavailable'};
        let _calendarStateOwnReservation = {_'Own Reservation'};
        let _calendarStateAgencyReservation = {_'Agency Reservation'};
        let _calendarContentAfter =
            "<div class='date-legend-container'>" +
            "<span class='date-legend date-legend--unavailable'></span><span class='date-legend-content'>" + _calendarStateUnavailable + "</span>" +
            "<span class='date-legend date-legend--own-reservation'></span><span class='date-legend-content'>" + _calendarStateOwnReservation + "</span>" +
            "<span class='date-legend date-legend--agency-reservation'></span><span class='date-legend-content'>" + _calendarStateAgencyReservation + "</span>" +
            "</div>";

        function init(divName, language) {
            _divName = divName;
            _language = language;
        }

        function setBasicDates(notAvailableDates, defaultDates, statusDates) {
            _notAvailableDates = notAvailableDates ?? [];
            _defaultDates = defaultDates ?? [];
            _statusDates = statusDates;
        }

        function load(monthsToShow = 3) {
            _hasDefaultDates = _defaultDates.length > 0;
            _monthsToShow = monthsToShow;

            if (_hasDefaultDates) {
                _startMonth = _defaultDates[0];
            }

            if ($(window).width() < 768) {
                _monthsToShow = 1;
            }

            if ($(window).width() > 768 && $(window).width() < 992) {
                _monthsToShow = 2;
            }

            let properties = {
                altInputClass: 'date-rangepicker',
                inline: true,
                disable: [
                    function (date) {
                        if (date < new Date().setHours(0, 0, 0, 0)) {
                            return true;
                        }
                        if (_hasDefaultDates && (new Date()) > moment(_defaultDates[1], 'DD.MM.YYYY').toDate()) {
                            return true;
                        }

                        if (_hasDefaultDates &&
                            moment(date, 'DD.MM.YYYY').toDate() >= moment(_defaultDates[0], 'DD.MM.YYYY').toDate() &&
                            moment(date, 'DD.MM.YYYY').toDate() <= moment(_defaultDates[1], 'DD.MM.YYYY').toDate()) {
                            return;
                        }

                        const key = flatpickr.formatDate(date, 'Y-m-d');

                        if (typeof _arrivals !== 'undefined' && _arrivals.indexOf(key) >= 0) {
                            return;
                        }

                        if (typeof _departures !== 'undefined' && _departures.indexOf(key) >= 0) {
                            return;
                        }

                        if (typeof _statusDates !== 'undefined' && _statusDates[key] === 'X') {
                            return true;
                        }

                        return typeof _notAvailableDates !== 'undefined' && _notAvailableDates.indexOf(key) >= 0;
                    }],
                showMonths: _monthsToShow,
                mode: 'range',
                minDate: _startMonth,
                altFormat: 'd.m.Y',
                dateFormat: 'd.m.Y',
                disableMobile: true,
                locale: _language,
                altInput: true,
                allowInput: false,
                onReady: function(selectedDates, dateStr, instance) {
                    $(instance.calendarContainer).append(_calendarContentAfter);
                    const inputYear = instance._input.parentNode.getElementsByClassName("cur-year")[0];
                    inputYear.disabled = true;
                },
                onChange: function (selectedDates, dateStr, instance) {
                    
                    if (selectedDates.length === 1) {
                        $('.reservation-mobile-bottom-bar-title').hide();
                        $('.reservation-mobile-bottom-bar-info').removeClass('d-none');
                    }

                    if (selectedDates.length === 2) {
                        const dateStart = flatpickr.formatDate(selectedDates[0], 'Y-m-d');
                        const dateEnd = flatpickr.formatDate(selectedDates[1], 'Y-m-d');

                        $('#arrive_date').val(dateStart);
                        $('#departure_date').val(dateEnd);
                        $('#date_from').val(dateStart);
                        $('#date_to').val(dateEnd);
                        $('.date-start').html(flatpickr.formatDate(selectedDates[0], 'd.m.Y')).removeClass('date-default-state');
                        $('.date-end').html(flatpickr.formatDate(selectedDates[1], 'd.m.Y')).removeClass('date-default-state');
                        setDefaultValues(flatpickr.formatDate(selectedDates[0], 'd.m.Y'), flatpickr.formatDate(selectedDates[1], 'd.m.Y'));
                        $('.total-nights-wrapper').removeClass('opacity-0');
                        const totalNights = (selectedDates[1] - selectedDates[0]) / (1000 * 60 * 60 * 24); // Calculate total nights
                        $('.total-nights-number').html(totalNights);
                        $('.under-button-label').removeClass('opacity-0');
                        $('.dates-wrapper-placeholder').removeClass('new-reservation-button-block--shaker');
                        if($(window).width() && $('.reservation-btn').length)
                        $('html, body').animate({
                            scrollTop: $('.reservation-btn').offset().top
                        }, 200);
                    }else {
                        $('.new-reservation-button-block').attr('title', $('.new-reservation-button-block').data('tooltip-label'));
                        $('.reservation-btn').attr('disabled', true);
                        $('.date-start').html(flatpickr.formatDate(selectedDates[0], 'd.m.Y')).removeClass('date-default-state');
                        $('.date-end').html($('.date-end').data('default')).addClass('date-default-state');
                        $('.total-nights-wrapper').addClass('opacity-0');
                        $('.under-button-label').removeClass('opacity-0');
                        $('.dates-wrapper-placeholder').addClass('new-reservation-button-block--shaker');
                    }
                },
                onDayCreate: function(dObj, dStr, fp, dayElem) {
                    if (dayElem.classList.contains('prevMonthDay') || dayElem.classList.contains('nextMonthDay')) {
                        dayElem.classList.add('disabled');
                        dayElem.classList.add('no-action');
                        return;
                    }

                    let date = flatpickr.formatDate(dayElem.dateObj, 'Y-m-d');

                    if (typeof _ownReservations !== 'undefined' && _ownReservations.indexOf(date) >= 0) {
                        dayElem.classList.add('ownReservation');
                    }

                    if (typeof _agencyReservations !== 'undefined' && _agencyReservations.indexOf(date) >= 0) {
                        dayElem.classList.add('agencyReservation');
                    }

                    if (typeof _offers !== 'undefined' && _offers.indexOf(date) >= 0) {
                        dayElem.classList.add('specialOffer');
                    }

                    if (typeof _statusDates !== 'undefined') {
                        if (_statusDates[date] === 'A') {
                            dayElem.classList.add('availableDate');
                        } else if (_statusDates[date] === 'X') {
                            if (_arrivals.indexOf(date) >= 0 || _departures.indexOf(date) >= 0 || _notAvailableDates.indexOf(date) >= 0) {
                                dayElem.classList.add('bookedDate');
                            } else {
                                dayElem.classList.add('notAvailableDate');
                            }
                        }
                    }

                    if (typeof _arrivals === 'undefined' || typeof _departures === 'undefined') {
                        return;
                    }

                    if (_arrivals.indexOf(date) < 0 && _departures.indexOf(date) < 0) {
                        return;
                    }

                    if (_arrivals.indexOf(date) >= 0) {
                        dayElem.classList.add('startDate');
                    }
                    if (_departures.indexOf(date) >= 0) {
                        dayElem.classList.add('endDate');
                    }


                    if (typeof _agencyReservations !== 'undefined' && _agencyReservations.indexOf(date) >= 0 && _arrivals.indexOf(date) >= 0 && !isPrevDayInArray(date, _agencyReservations)) {
                        dayElem.classList.add('agencyReservationStartDate');
                    }
                    
                    if (typeof _ownReservations !== 'undefined' && _ownReservations.indexOf(date) >= 0 && _arrivals.indexOf(date) >= 0 && !isPrevDayInArray(date, _ownReservations)) {
                        dayElem.classList.add('ownReservationStartDate');
                    }
                    
                    if (typeof _ownReservations !== 'undefined' && _ownReservations.indexOf(date) >= 0 && _departures.indexOf(date) >= 0 && isPrevDayInArray(date, _ownReservations)) {
                        dayElem.classList.add('ownReservationEndDate');
                    }

                    if (typeof _agencyReservations !== 'undefined' && _agencyReservations.indexOf(date) >= 0 && _departures.indexOf(date) >= 0 && isPrevDayInArray(date, _agencyReservations)) {
                        dayElem.classList.add('agencyReservationEndDate');
                    }

                    if(isPrevDayInArray(date, _ownReservations) && isPrevDayInArray(date, _agencyReservations)) {
                        if(isPrevDayInArray(date, _ownReservations)) {
                            dayElem.classList.add('blue-yellow');
                        }
                    }
                }
            };

            if (_defaultDates.length > 0) {
                properties.defaultDate = _defaultDates;
            }

            _picker = flatpickr(_divName, properties);

            if (_hasDefaultDates) {
                setDefaultValues(_defaultDates[0], _defaultDates[1]);
            }
        }

        function isPrevDayInArray(date, array) {
            const prevDay = moment(date, 'YYYY-MM-DD').subtract(1, 'days').format('YYYY-MM-DD');
            return array.indexOf(prevDay) >= 0;
        }

        function setArrivals(dates) {
            _arrivals = dates ?? [];
        }

        function setDepartures(dates) {
            _departures = dates ?? [];
        }

        function setOwnReservations(dates) {
            _ownReservations = dates ?? [];
        }

        function setAgencyReservations(dates) {
            _agencyReservations = dates ?? [];
        }

        function setOffers(dates) {
            _offers = dates ?? [];
        }

        function formatDate(dateString) {
            const dateTime = new Date(dateString);
            let date, month, year;

            date = dateTime.getDate();
            month = dateTime.getMonth() + 1;
            year = dateTime.getFullYear();
            date = date.toString().padStart(2, '0');
            month = month.toString().padStart(2, '0');

            return date + '.' + month + '.' + year;
        }

        function clearValues() {
            $('#arrive_date').val('');
            $('#departure_date').val('');
            $('.daterange-picker-value > span').html('');
            $('.daterange-picker-info').css('opacity', '0');
            $('.reservation-btn').attr('disabled', true);
            $('.new-reservation-button-block').attr('title', $('.new-reservation-button-block').data('tooltip-label'));
            $('.select-dates-empty-dates-info').show();
            $('.calendar-wrapper-for-error').removeClass('form-card-error-gold');
            $('.date-error-msg').addClass('d-none');
        }

        function setDefaultValues(dateStart, dateEnd) {
            $('.daterange-picker-value > span').html(dateStart + ' → ' + dateEnd);
            $('.new-reservation-button-block').show();
            $('.daterange-picker-info').css('opacity', '1');
            $('.reservation-btn').attr('disabled', false);
            $('.new-reservation-button-block').attr('title', '');
            $('.select-dates-empty-dates-info').hide();
            $('.calendar-wrapper-for-error').removeClass('form-card-error-gold');
            $('.date-error-msg').addClass('d-none');
        }

        return {
            init: init,
            setBasicDates: setBasicDates,
            load: load,
            clearValues: clearValues,
            formatDate: formatDate,
            setArrivals: setArrivals,
            setDepartures: setDepartures,
            setOwnReservations: setOwnReservations,
            setAgencyReservations: setAgencyReservations,
            setOffers: setOffers
        }
    }();
</script>

<script>
    if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
        // Google Maps API script has not been loaded, load it now
        var script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyDQklcagQPbT5fvxJLgIjQWdbxZe0ej4YE&v=weekly&libraries=places,marker';
        script.defer = true;
        script.async = true;
        script.onload = function () {
            initMap();
        };
        document.head.appendChild(script);
    } else {
        initMap();
    }

    function initMap() {
        if (!document.getElementById('frm-formWizard-step2-address_latitude')) {
            return;
        }

        // Initialize Autocomplete
        const input = document.getElementById('frm-formWizard-step2-address_street');
        const autocomplete = new google.maps.places.Autocomplete(input, {
            types: ['geocode'], // Can change to ["(cities)"] for cities only
            componentRestrictions: { country: 'hr' } // Remove this for all countries
        });

        // When user selects an address
        autocomplete.addListener("place_changed", function () {
            const place = autocomplete.getPlace();
            let street = '', houseNumber = '', city = '', state = '', country = '', postalCode = '';

            if (!place.geometry) {
                alert("No details available for input: '" + input.value + "'");
                return;
            }

            // Extract City, State, and Country
            if (place.address_components) {
                place.address_components.forEach(component => {
                    if (component.types.includes("street_number")) {
                        houseNumber = component.long_name; // House Number
                    }

                    if (component.types.includes("route")) {
                        street = component.long_name; // Street Name
                    }

                    if (component.types.includes("locality")) {
                        city = component.long_name;
                    }

                    if (component.types.includes("administrative_area_level_1")) {
                        state = component.long_name;
                    }

                    if (component.types.includes("country")) {
                        country = component.long_name;
                    }

                    if (component.types.includes("postal_code")) {
                        postalCode = component.long_name; // Postal Code (ZIP)
                    }
                });
            }

            let fullAddress = street + (houseNumber ? " " + houseNumber : "");
            console.log('Address: ' + fullAddress);
            console.log('City: ' + city);
            console.log('State: ' + state);
            console.log('Country: ' + country);
            console.log('PostalCode: ' + postalCode);

            document.getElementById("frm-formWizard-step2-address_city").value = city;
            document.getElementById("frm-formWizard-step2-address_street").value = street;
            document.getElementById("frm-formWizard-step2-address_street_number").value = houseNumber;
            document.getElementById("frm-formWizard-step2-address_zip").value = postalCode;

            let isVisibleRegionSelector = $('#frm-formWizard-step2-region').length > 0;

            if (isVisibleRegionSelector) {
                $.ajax({
                    url: {plink Product:regionAutoComplete, $presenter->getParameter('id')},
                    type: 'GET',
                    data: {
                        'q': state,
                    },
                    success: function (response) {
                        if (typeof response[0] === 'undefined') {
                            return;
                        }
                        let newOption = new Option(response[0].text, response[0].id, true, true);
                        $('#frm-formWizard-step2-region').append(newOption).trigger('change');
                    }
                });
            }

            // Move Map to Selected Location
            if (place.geometry.location) {
                map.setCenter(place.geometry.location);
                map.setZoom(15); // Zoom into location

                // Place Marker at the Selected Location
                marker.position = place.geometry.location;
            }
        });

        let lat = 45.25666564;
        if (document.getElementById('frm-formWizard-step2-address_latitude').value !== '') {
            lat = parseFloat(document.getElementById('frm-formWizard-step2-address_latitude').value);
        }
        let lng = 13.902663056;
        if (document.getElementById('frm-formWizard-step2-address_longitude').value !== '') {
            lng = parseFloat(document.getElementById('frm-formWizard-step2-address_longitude').value);
        }
        const croatia = { lat: lat, lng: lng };
        const map = new google.maps.Map(document.getElementById('map'), {
            zoom: 10,
            center: croatia,
            mapTypeId: 'satellite',
            mapId: 'AIzaSyDQklcagQPbT5fvxJLgIjQWdbxZe0ej4YE',
            zoomControl: true
        });

        // Create Advanced Marker
        const marker = new google.maps.marker.AdvancedMarkerElement({
            position: croatia,
            map: map, // Attach the marker to the map
            draggable: false
        });

        google.maps.event.addListener(map, 'click', function (event) {
            const clickedLocation = event.latLng;
            // Update the marker's position
            marker.position = clickedLocation;

            // Update the latitude and longitude inputs
            document.getElementById('frm-formWizard-step2-address_latitude').value = clickedLocation.lat();
            document.getElementById('frm-formWizard-step2-address_longitude').value = clickedLocation.lng();
        });
    }
</script>
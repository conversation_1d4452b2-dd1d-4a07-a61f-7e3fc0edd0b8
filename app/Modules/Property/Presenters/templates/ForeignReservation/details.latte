{varType App\Models\Entities\ForeignReservationEntity $reservationDetails}
{varType App\Models\Entities\ProductEntity $product}
{block content}
    <div class="d-flex justify-content-end justify-content-md-start mt-0">
        <a n:href="Reservation:Default" class="ajax btn btn-helper-sidebar btn-helper-sidebar-white waves-effect hide-for-print px-1 py-50">
            {embeddedSvg 'property-assets/app-assets/images/svg/left-arrow.svg',
            class => 'me-25',
            fill => '#333',
            height => 16,
            width => 16}
            {_'back to all reservations'}</a>
    </div>
    <div class="row mt-0 mt-lg-2">
        <div n:if="$user->isInRole(App\Common\Enums\RoleEnum::ADMIN)" class="col-lg-3 gx-lg-0 ps-1 hide-for-print">
            <div class="hide-for-print">
                <div class="d-flex flex-row flex-lg-column my-2 mt-3 my-lg-0 mb-lg-0 invoice-sidebar-helper-wrapper">
                    <a n:href="approve, $id" class="btn btn-helper-sidebar round waves-effect" n:if="!$reservationDetails->approved">
                        {embeddedSvg 'property-assets/app-assets/images/svg/edit.svg',
                        class => 'me-25',
                        fill => '#121926',
                        height => 15,
                        width => 15}
                        {_'Approve'}
                    </a>
                </div>
                <div class="d-flex flex-row flex-lg-column my-2 mt-3 my-lg-0 mb-lg-0 invoice-sidebar-helper-wrapper">
                    <a n:href="edit, $id" class="btn btn-helper-sidebar round waves-effect">
                        {embeddedSvg 'property-assets/app-assets/images/svg/edit.svg',
                        class => 'me-25',
                        fill => '#121926',
                        height => 15,
                        width => 15}
                        {_'Edit'}
                    </a>
                </div>
            </div>
        </div>
        <div class="col-lg-9">
            <div class="card">
                <div class="card-body" id="reservation">
                    <div class="justify-content-between d-flex">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2 class="mb-0 me-1 fw-bolder first-letter-capital">{_'Reservation'} #{$reservationDetails->booking_code}</h2>
                        </div>
                    </div>
                    <div class="info-container" n:if="$reservationDetails">
                        <div class="pt-2 pb-1 border-bottom">
                            <a n:href="Product:details, $reservationDetails->product_id" class="ajax">
                                <h4 class="mb-1 fw-bolder first-letter-capital">{$product->name}</h4>
                            </a>
                            <ul class="list-unstyled d-inline-flex">
                                <li class="d-flex align-items-center" n:if="$reservationDetails->adults_number">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/user.svg',
                                    class => 'nav-link-icon',
                                    fill => '#252525',
                                    height => 18,
                                    width => 18}
                                    <span class="ms-50">{$reservationDetails->adults_number + $numberOfChildren} {_'Guests'} ({$reservationDetails->adults_number} {_'adults'}, {$numberOfChildren} {_'children'})</span>
                                </li>
                                <li class="me-1 d-flex align-items-center" n:if="$numberOfPets">
                                    <span class="ms-1">{$numberOfPets} {_'pets'}</span>
                                </li>
                            </ul>
                        </div>
                        <div class="pt-2 pb-1 border-bottom">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Guest Information'}</h4>
                            <ul class="list-unstyled">
                                <li class="mb-75">{$reservationDetails->name} {$reservationDetails->surname}</li>
                                <li class="mb-75">{$reservationDetails->telephone}</li>
                            </ul>
                        </div>
                        <div class="pt-2 pb-1 border-bottom">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Period'}</h4>
                            <ul class="list-unstyled">
                                <li class="mb-75 fw-bolder">{$reservationDetails->arrive->format('d.m.Y.')} → {$reservationDetails->departure->format('d.m.Y.')}</li>
                                <li class="mb-75">{_'Length of stay'|firstUpper}: <span class="fw-bolder">{$reservationDetails->departure->diff($reservationDetails->arrive)->format('%a')} {_'nights'}</span></li>
                                <li class="mb-75">{_'Rental price'|firstUpper}: <span class="fw-bolder">{number_format($reservationDetails->rental_price, 2, ',', '.')} {$reservationDetails->currency}</span></li>
                            </ul>
                        </div>
                        <div class="pt-2 pb-1 border-bottom" n:if="$reservationPayments">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Dinamika isplate'|firstUpper}</h4>
                            <div class="row">
                                <div class="col-sm-6">
                                    <ul class="list-unstyled">
                                        {foreach $reservationPayments as $payment}
                                            {continueIf $payment->security_deposit || $iterator->counter > 1}
                                            <li class="mb-75">{number_format($reservationDetails->rental_price, 2, ',', '.')} {$reservationDetails->currency} - {_$payment->payment_status}</li>
                                        {/foreach}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="pt-2 pb-1 border-bottom" n:if="$reservationServices">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Services'|firstUpper}</h4>
                            <div class="row">
                                <div class="col-sm-6">
                                    <ul class="list-unstyled">
                                        {foreach $reservationServices as $reservationService}
                                            <li class="mb-75">{$services[$reservationService->code]} - {number_format($reservationService->amount, 2, ',', '.')} X {number_format($reservationService->price, 2, ',', '.')} €</li>
                                        {/foreach}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="pt-2 pb-1">
                            <h4 class="mb-1 fw-bolder first-letter-capital">{_'Request from guests'}</h4>
                            <p>{$reservationDetails->comments}</p>
                        </div>
                        <div class="row">
                        </div>
                    </div>

                    <div class="pt-2 pb-1" n:ifset="$eVisitorReservationData->id">
                        <h4 class="mb-1 fw-bolder first-letter-capital">{_'evisitor.info.filled_out'}</h4>
                        <p>
                            {$eVisitorLink}
                        </p>
                        <p>
                            {if $eVisitorReservationData->confirmed}
                                ✅ {_'Yes'} – <span class="fw-bold">{$eVisitorReservationData->confirmed|date:'d.m.Y H:i'}</span>
                            {else}
                                ❌ {_'No'}
                            {/if}
                        </p>
                    </div>
                </div>
            </div>

            {if $user->isInRole('admin')}
                <div class="card">
                    <div class="card-text">
                        <div class="datagrid-box table-responsive">
                            {control actionLogGridControl}
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    </div>
{/block}
{block js}
    <script n:srcv="/property-assets/assets/vendors/js/html2pdf.bundle.min.js"></script>
{/block}

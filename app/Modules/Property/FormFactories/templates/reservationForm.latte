<div n:class="empty($id) ? reservation-form-block" {if empty($id)} style="display: none" {/if}>
    <div class="col-lg-12">
        <div class="row">
            <div class="col-md-12 col-12">
                <div class="form-card">
                    <h4 class="form-card-label">{_'Guest details'}</h4>
                    <div class="">
                        <div class="row">
                            <div class="col-md-6 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'First name'}</label>
                                    {input contact_firstname, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Last name'}</label>
                                    {input contact_lastname, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Email'}</label>
                                    {input contact_email, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Phone'}</label>
                                    {input phone, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Street'}</label>
                                    {input guest_street, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-4 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Postcode'}</label>
                                    {input postcode, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-4 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'City'}</label>
                                    {input guest_city, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-4 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Country'}</label>
                                    {input contact_country_id, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-4 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Adults'}</label>
                                    {input adults, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-4 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Children'}</label>
                                    {input children, class=>'form-control'}
                                </div>
                            </div>
                            <div class="col-md-4 col-12">
                                <div class="mb-1">
                                    <label class="form-label">{_'Pets'}</label>
                                    {input pets, class=>'form-control'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" n:if="!empty($id)">
            <div class="d-flex justify-content-end mb-2">
                <button type="submit" n:name="submit_customer_data" class="submit-button-with-loader btn btn-dark mt-1 waves-effect waves-float waves-light">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                    <span class="ms-25 align-middle">{_'Save customer data'}</span>
                </button>
            </div>
        </div>
        {if $user->isInRole('admin')}
            <div class="row">
                <div class="col-md-12 col-12">
                    <div class="form-card">
                        <h4 class="form-card-label">{_'Unavailable'}</h4>
                        <div class="">
                            <div class="row">
                                <div class="col-md-6 col-12">
                                    <div class="mb-1">
                                        {input is_hidden, class=>'form-control'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-12">
                    <div class="form-card">
                        <h4 class="form-card-label">{_'Podaci o plaćanju'}</h4>
                        <div class="">
                            <div class="row">
                                <div class="row">
                                    <div class="col-md-6 col-12" n:if="!empty($reservationDetails->product_distribution_channel_id)">
                                        <label class="form-label">{_'Distribution price'}</label>
                                        <div class="mb-1 input-group">
                                            {input distribution_price, class=>'form-control'}
                                            <span class="input-group-text">€</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12" n:if="!empty($reservationDetails->product_distribution_channel_id)">
                                        <div class="mb-1">
                                            {_'Distribution channel name'}: <strong>{$distributionChannelName}</strong>
                                        </div>
                                        <div class="mb-1" n:if="$markupPrice !== null">
                                            {_'Markup price'}: <strong>{number_format($markupPrice, 2, ',', '.')} €</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <label class="form-label">{_'Total price for customer'}</label>
                                        <div class="mb-1 input-group">
                                            {input total_price_eur, class=>'form-control'}
                                            <span class="input-group-text">€</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 col-12" n:if="$insuranceAmount > 0">
                                    <div class="mb-1">
                                        <label class="form-label">{_'Insurance amount:'}</label>
                                        {number_format($insuranceAmount, 2, ',', '.')} €
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <label class="form-label">{_'Total price'}</label>
                                        <div class="mb-1 input-group">
                                            {input amount, class=>'form-control'}
                                            <span class="input-group-text">€</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <label class="form-label">{_'Reservation - VG - Iznos prve uplate'}</label>
                                        <div class="mb-1 input-group">
                                            {input payment_first_installment_amount, class=>'form-control'}
                                            <span class="input-group-text">€</span>
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - Status plaćanja prve uplate'}</label>
                                            {input first_installment_payment_status, class=>'form-control'}
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - datum prve uplate'}</label>
                                            {input payment_first_installment_date, class=>'form-control datepicker'}
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - payment type'}</label>
                                            {input payment_type, class=>'form-control datepicker'}
                                        </div>
                                        <div class="mb-1 form-check">
                                            <label class="form-check-label">{_'Allow manual change prices'}</label>
                                            {input manual_change_price, class=>'form-check-input'}
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        <label class="form-label">{_'Reservation - VG - Iznos druge uplate'}</label>
                                        <div class="mb-1 input-group">
                                            {input payment_second_installment_amount, class=>'form-control'}
                                            <span class="input-group-text">€</span>
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - Status plaćanja druge uplate'}</label>
                                            {input second_installment_payment_status, class=>'form-control'}
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - datum druge uplate'}</label>
                                            {input payment_second_installment_date, class=>'form-control datepicker'}
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - second payment type'}</label>
                                            {input payment_type_for_second_installment, class=>'form-control datepicker'}
                                        </div>
                                        <div class="mb-1" n:if="$recruiterName">
                                            <div class="mb-1">
                                                {_'Recruiter'}: <strong>{$recruiterName}</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-12">
                    <div class="form-card">
                        <h4 class="form-card-label">{_'Plaćanje prema iznajmljivaču'}</h4>
                        <div class="">
                            <div class="row">
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <label class="form-label">{_'Reservation - VG - Partner iznos za uplatu prve rate'}</label>
                                        <div class="mb-1 input-group">
                                            {input partner_first_installment_payment_amount, class=>'form-control'}
                                            <span class="input-group-text">€</span>
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - Partner status plaćanja prve uplate'}</label>
                                            {input partner_first_installment_payment_status, class=>'form-control'}
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - Partner datum prve uplate (rok)'}</label>
                                            {input partner_first_installment_payment_due, class=>'form-control datepicker'}
                                        </div>
                                        <div class="mb-1">
                                            <label class="form-label">{_'Reservation - VG - Partner datum isplate prve rate'}</label>
                                            {input partner_first_installment_paid_date, class=>'form-control datepicker'}
                                        </div>
                                        <div class="mb-1 form-check">
                                            <label class="form-check-label">{_'Allow manual change prices'}</label>
                                            {input manual_change_price_for_partner, class=>'form-check-input'}
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        <label class="form-label {if $isReservationSecondInstallmentDirectlyToRenter} mb-1{/if}">{_'Reservation - VG - Partner iznos za uplatu druge rate'}</label>
                                        {if $isReservationSecondInstallmentDirectlyToRenter}
                                            <div class="mb-2 fst-italic text-capitalize">
                                                {_'Nije primjenjivo'}
                                            </div>
                                        {else}
                                            <div class="mb-1 input-group">
                                                {input partner_second_installment_payment_amount, class=>'form-control'}
                                                <span class="input-group-text">€</span>
                                            </div>
                                        {/if}

                                        <label class="form-label {if $isReservationSecondInstallmentDirectlyToRenter} mb-1{/if}">{_'Reservation - VG - Partner status plaćanja druge uplate'}</label>
                                        {if $isReservationSecondInstallmentDirectlyToRenter}
                                            <div class="mb-2 fst-italic text-capitalize">
                                                {_'Nije primjenjivo'}
                                            </div>
                                        {else}
                                            <div class="mb-1">
                                                {input partner_second_installment_payment_status, class=>'form-control'}
                                            </div>
                                        {/if}

                                        <label class="form-label {if $isReservationSecondInstallmentDirectlyToRenter} mb-1{/if}">{_'Reservation - VG - Partner datum druge uplate (rok)'}</label>
                                        {if $isReservationSecondInstallmentDirectlyToRenter}
                                            <div class="mb-2 fst-italic text-capitalize">
                                                {_'Nije primjenjivo'}
                                            </div>
                                        {else}
                                            <div class="mb-1">
                                                {input partner_second_installment_payment_due, class=>'form-control datepicker'}
                                            </div>
                                        {/if}

                                        <label class="form-label {if $isReservationSecondInstallmentDirectlyToRenter} mb-1{/if}">{_'Reservation - VG - Partner datum isplate druge rate'}</label>
                                        {if $isReservationSecondInstallmentDirectlyToRenter}
                                            <div class="mb-2 fst-italic text-capitalize">
                                                {_'Nije primjenjivo'}
                                            </div>
                                        {else}
                                            <div class="mb-1">
                                                {input partner_second_installment_paid_date, class=>'form-control datepicker'}
                                            </div>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {/if}
            <div class="row single-action-reservation-form-footer" n:if="empty($id) || $user->isInRole('admin')">
                <div class="d-flex justify-content-end">
                    <button type="submit" n:name="submit" class="submit-button-with-loader btn btn-dark mt-1 waves-effect waves-float waves-light">
                        <span class="spinner-border spinner-border-sm me-25" role="status" aria-hidden="true" style="display: none;"></span>
                        <span class="align-middle">{_'Save'}</span>
                    </button>
                </div>
            </div>
            {* only in property->calendar->new reservation *}
            <div n:if="empty($id) || $user->isInRole('admin')">
                <div class="reservation-bottom-action-bar border-top d-none">
                    <a href="#" class="btn btn-outline-dark waves-effect waves-float waves-light me-25 w-100 reservation-sidebar-close">
                        <span class="ms-25 align-middle">{_'Cancel'}</span>
                    </a>
                    <button type="submit" n:name="submit" class="submit-button-with-loader btn btn-dark waves-effect waves-float waves-light w-100 ms-25 confirm-sidebar-reservation">
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                        <span class="ms-25 align-middle">{_'Confirm'}</span>
                    </button>
                </div>
            </div>
            {input arrive}
            {input departure}
            {input id}
    </div>
</div>

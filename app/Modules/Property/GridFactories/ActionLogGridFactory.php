<?php declare (strict_types = 1);

namespace App\Modules\Property\GridFactories;

use App\Common\Enums\ActionLogDbTableEnum;
use App\Models\Entities\ActionLogEntity;
use App\Models\Entities\ForeignReservationEntity;
use App\Modules\Property\Components\DataGrid;
use App\Repositories\ActionLogRepository;
use App\Repositories\ForeignReservationsRepository;
use Dibi\Fluent;

class ActionLogGridFactory extends BaseGridFactory
{

	public function __construct(
		private readonly ActionLogRepository $repository,
		private readonly ForeignReservationsRepository $foreignReservationsRepository)
	{
	}

	public function create(): DataGrid
	{
		$query = $this->getQueryForGrid();

		$grid = $this->dataGridFactory->create($query, 'al.id', 'al.created');
		$grid->setTemplateFile(__DIR__ . '/templates/actionLogGridTemplate.latte');

		$grid->addColumnText(ActionLogEntity::ID, 'id');
		$grid->addColumnText(ActionLogEntity::CREATED, 'Created')->setRenderer(fn ($row) => $row->created->format('d.m.y H:i:s'));
		$grid->addColumnText(ActionLogEntity::SEVERITY, 'Severity');
		$grid->addColumnText(ActionLogEntity::ACTION, 'Action');
		$grid->addColumnText(ActionLogEntity::DETAILS, 'Details');

		return $grid;
	}

	private function getQueryForGrid(): Fluent
	{
		if ($this->presenter->action_log_db_table === ActionLogDbTableEnum::FOREIGN_RESERVATIONS) {
			$foreignReservation = $this->foreignReservationsRepository->getBy([ForeignReservationEntity::ID => $this->presenter->action_log_db_table_id]);

			return $this->repository->getActionLogsForFullReservation($this->presenter->action_log_db_table_id, $foreignReservation?->reservation_id);
		}

		if ($this->presenter->action_log_db_table === ActionLogDbTableEnum::RESERVATIONS) {
			$foreignReservation = $this->foreignReservationsRepository->getBy([ForeignReservationEntity::RESERVATION_ID => $this->presenter->action_log_db_table_id]);

			return $this->repository->getActionLogsForFullReservation($foreignReservation?->id, $this->presenter->action_log_db_table_id);
		}

		return $this->repository->getActionLogsForTable($this->presenter->action_log_db_table, $this->presenter->action_log_db_table_id);
	}

}

<?php declare (strict_types = 1);

namespace App\Modules\Property\GridFactories;

use App\Common\Enums\ActionLogDbTableEnum;
use App\Models\Entities\ActionLogEntity;
use App\Models\Entities\ForeignReservationEntity;
use App\Modules\Property\Components\DataGrid;
use App\Repositories\ActionLogRepository;
use App\Repositories\ForeignReservationsRepository;
use Dibi\Fluent;
use Nette\Utils\Html;

class ActionLogGridFactory extends BaseGridFactory
{

	public function __construct(
		private readonly ActionLogRepository $repository,
		private readonly ForeignReservationsRepository $foreignReservationsRepository)
	{
	}

	public function create(): DataGrid
	{
		$query = $this->getQueryForGrid();

		$grid = $this->dataGridFactory->create($query, 'al.id', 'al.created');
		$grid->setTemplateFile(__DIR__ . '/templates/actionLogGridTemplate.latte');

		$grid->addColumnText(ActionLogEntity::ID, 'ID');
		$grid->addColumnText(ActionLogEntity::CREATED, 'Created')->setRenderer(fn ($row) => $row->created->format('d.m.y H:i:s'));
		$grid->addColumnText(ActionLogEntity::SEVERITY, 'Severity')->setRenderer([$this, 'renderSeverityLabel']);
		$grid->addColumnText(ActionLogEntity::ACTION, 'Action');
		$grid->addColumnText(ActionLogEntity::DETAILS, 'Details');

		return $grid;
	}

	public function renderSeverityLabel($row): Html
	{
		$severity = $row->severity ?? 'info';

		$labelClasses = [
			'success' => 'label label-success',
			'error' => 'label label-danger',
			'info' => 'label label-info'
		];

		$class = $labelClasses[$severity] ?? 'label label-default';

		return Html::el('span')
			->setAttribute('class', $class)
			->setText(ucfirst($severity));
	}

	private function getQueryForGrid(): Fluent
	{
		if ($this->presenter->action_log_db_table === ActionLogDbTableEnum::FOREIGN_RESERVATIONS) {
			$foreignReservation = $this->foreignReservationsRepository->getBy([ForeignReservationEntity::ID => $this->presenter->action_log_db_table_id]);

			return $this->repository->getActionLogsForFullReservation($this->presenter->action_log_db_table_id, $foreignReservation?->reservation_id);
		}

		if ($this->presenter->action_log_db_table === ActionLogDbTableEnum::RESERVATIONS) {
			$foreignReservation = $this->foreignReservationsRepository->getBy([ForeignReservationEntity::RESERVATION_ID => $this->presenter->action_log_db_table_id]);

			return $this->repository->getActionLogsForFullReservation($foreignReservation?->id, $this->presenter->action_log_db_table_id);
		}

		return $this->repository->getActionLogsForTable($this->presenter->action_log_db_table, $this->presenter->action_log_db_table_id);
	}

}

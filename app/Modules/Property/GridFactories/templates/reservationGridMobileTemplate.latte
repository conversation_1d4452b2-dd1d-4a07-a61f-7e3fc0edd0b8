<div class="{block datagrid-class}datagrid datagrid-{$control->getFullName()}{/block}" data-refresh-state="{link refreshState!}">
    <div n:snippet="grid">
        {snippetArea gridSnippets}
        {form filter, class => 'ajax', autocomplete => 'off'}
        {block outer-filters}
        {/block}
        {block data}
        <div class="reservation-page-filter-row">
            {include 'filtersReservationsTemplate.latte', form => $filter, cssClass => 'filter-reservation-pagination', includeFilters => ['type']}
        </div>

        <div class="reservation-table-header-wrapper mobile {$user->isInRole('admin') ? 'admin'}">
            <div class="table-header-reservation mb-2">
                {var $filters = ['date_range', 'name']}
                {if $user->isInRole('admin')}
                    {var $filters[] = 'hide_cancelled'}
                {/if}
                {include 'filtersReservationsTemplate.latte', form => $filter, cssClass => 'filter-reservation-pagination', includeFilters => $filters}
                <div class="export-wrapper">
                    <div class="d-flex justify-content-between gap-2 align-items-center">
                        <div class="dropdown">
                            <span class="btn p-0 px-50 ps-lg-1 dropdown-toggle hide-arrow d-flex align-items-center gap-50 btn-grey" data-bs-toggle="dropdown">
                                {embeddedSvg 'property-assets/app-assets/images/svg/export.svg',
                                class => 'property-card-icon',
                                fill => '#1D2939',
                                height => 14,
                                width => 14}
                                    {_'Export'}
                                {embeddedSvg 'assets/img/svg/arrow-down-bold.svg',
                                class => '',
                                height => 10,
                                width => 10}
                            </span>
                            <div class="dropdown-menu dropdown-menu-with-spacing">
                                <div n:if="$exports">
                                    <span n:if="$exports" n:snippet="exports" n:block="exports">
                                        <a class="dropdown-item with-border text-capitalize"
                                        title="{_'Export to excel'}"
                                        n:href="export! id => 1">
                                        {embeddedSvg 'property-assets/app-assets/images/svg/excel.svg',
                                        height => 18,
                                        width => 18}
                                            {_'Export to excel'}
                                        </a>
                                        <a class="dropdown-item with-border text-capitalize"
                                        title="{_'Export to pdf'}"
                                        n:href="export! id => 2">
                                        {embeddedSvg 'property-assets/app-assets/images/svg/pdf.svg',
                                        height => 18,
                                        width => 18}
                                            {_'Export to pdf'}
                                        </a>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="hide-on-tablet">
                            <div class="d-flex justify-content-center justify-content-xxl-start">
                                <ul class="nav nav-tabs mb-0" role="tablist" n:if="$allowReservationButton">
                                    <li class="nav-item">
                                        <a class="btn btn-dark btn-sm waves-effect btn-36" href="/{$presenter->language|noescape}/property/reservation/new">
                                        <span class="d-flex align-items-center h6 mb-0 gap-25 text-white">
                                            {_'Add new reservation'}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#ffffff" stroke="#ffffff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus font-small-4">
                                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                                <line x1="5" y1="12" x2="19" y2="12"></line>
                                            </svg>
                                        </span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="show-on-mobile">
                <div class="mobile-table-title">
                    <a n:class="$columns['arrive']->isSortedBy() ? 'sort' : '', 'ajax'" href="{link sort!, sort => $control->getSortNext($columns['arrive'])}" id="datagrid-sort-arrive">
                        {_'Upcoming reservations'|upper}
                        {if $columns['arrive']->isSortedBy()}
                            {if $columns['arrive']->isSortAsc()}
                                <i class="{$iconPrefix}caret-up"></i>
                            {else}
                                <i class="{$iconPrefix}caret-down"></i>
                            {/if}
                        {else}
                            <i class="{$iconPrefix}sort"></i>
                        {/if}
                    </a>
                </div>
            </div>
            <div class="table-responsive relative">
                <div class="loader-grid-table d-none">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                </div>
                <table class="table table-hover table-reservation table-sm" n:snippet="table">
                    {block tbody}
                        <tbody n:snippet="tbody">
                        {snippetArea items}
                            {foreach $rows as $row}
                                {var $item = $row->getItem()}
                                {var $linkToDetails = $item->is_foreign_reservation ? '/' . $presenter->language . '/property/foreign-reservation/details?id=' . $item->id : '/' . $presenter->language . '/property/reservation/details?id='. $item->id}
                                {var $initialAmount = $item->total_price_without_discount}
                                {var $bruttoAmount = $item->total_price}
                                {var $discountAmount = $initialAmount - $bruttoAmount}
                                {var $agencyProvisionAmount = $bruttoAmount * ($item->agency_provision_percentage / 100) * 1.25}
                                {var $nettoAmount = $bruttoAmount - $agencyProvisionAmount}

                                {if !$user->isInRole('admin') && !in_array($item->source, ['My own', 'Villas guide'])}
                                    {var $itemSource = !$item->is_agency_provision_property ? 'Villas guide' : 'Distribution'}
                                {else}
                                    {var $itemSource = $item->source}
                                {/if}
                                <tr class="reservation-table-row" data-id="{$row->getId()}" n:snippet="item-{$row->getId()}" n:attr="$row->getControl()->attrs">
                                    <td class="show-on-mobile-table">
                                        <a class="d-flex flex-row align-items-center gap-1" href="/{$presenter->language|noescape}/property/product/details?id={$item->listing_id}">
                                            <img class="reservation-villa-image" src="{$item->picture_path ?? '/property-assets/assets/img/hex-pattern.png'}" alt="{$item->listing|noescape}">
                                            <span class="reservation-mobile-data villa-name--mobile">{$item->listing}</span>
                                        </a>
                                        <span class="reservation-mobile-name mt-50">
                                            {if $item->is_foreign_reservation}
                                                {if $item->customer}
                                                    {$item->customer}
                                                {else}
                                                    -
                                                {/if}
                                            {else}
                                                {if $item->customer}
                                                    {$item->customer}
                                                {else}
                                                    -
                                                {/if}
                                            {/if}
                                        </span>

                                        <span class="reservation-mobile-data reservation-mobile-nights mt-25">
                                            {$item->arrive->format('d.m.Y')}
                                            -
                                            {$item->departure->format('d.m.Y')}
                                            <div class="dot"></div>
                                            <span>
                                            {if $item->number_of_nights > 0}
                                                {$item->number_of_nights} {if $item->number_of_nights === 1}{_'night'}{else}{_'nights'}{/if}
                                            {/if}
                                            </span>
                                        </span>
                                        <span class="reservation-mobile-data">
                                                {if $item->adults > 0}{_'%d adults', $item->adults}{/if}
                                                {if $item->children > 0}{if $item->adults > 0}, {/if}{_'%d child', $item->children}{/if}
                                                {if $item->pets > 0}{if $item->adults > 0 || $item->children > 0}, {/if}{_'%d pets', $item->pets}{/if}
                                            </span>
                                        <span class="reservation-mobile-price mt-25">{$bruttoAmount|number:2,'.'} €</span>
                                        <div class="reservation-mobile-bottom-info mt-50">
                                            {var $reservationSource = $sourceMapping[$itemSource] ?? 'unknown'}
                                            <div class="reservation-source-wrapper no-border">
                                                <div class="reservation-dot reservation_source_{$reservationSource}"></div><span class="reservation-source-label">{if $itemSource === 'Distribution'}{_$itemSource}{else}{$itemSource}{/if}</span>
                                            </div>
                                            <div class="dot"></div>
                                            {if $item->is_foreign_reservation}
                                                -
                                            {else}
                                                {if $item->cancelled}
                                                    <div class="d-flex align-items-center gap-50">
                                                        <div class="reservation-canceled" data-toggle="tooltip" title="{_'Cancelled reservation'}">
                                                            {embeddedSvg 'property-assets/app-assets/images/svg/close-bold.svg',
                                                                class => 'reservation-canceled-icon ',
                                                                fill => '#ffffff',
                                                                height => 14,
                                                                width => 14}
                                                        </div>
                                                        <span class="font-weight-bold">{_'Cancelled'}</span>
                                                    </div>
                                                {else}
                                                    <div class="d-flex align-items-center gap-50">
                                                        <div class="reservation-approved reservation-status-mobile" data-toggle="tooltip" title="{_'Approved reservation'}">
                                                            {embeddedSvg 'property-assets/app-assets/images/svg/checkmark.svg',
                                                                class => 'reservation-approved-icon ',
                                                            fill => '#ffffff',
                                                                height => 14,
                                                                width => 14}
                                                        </div>
                                                        <span class="font-weight-bold reservation-status-mobile">{_'Approved'}</span>
                                                    </div>
                                                {/if}
                                            {/if}
                                        </div>
                                    </td>
                                        <td class="position-relative">
                                            <div class="reservation-col-action">
                                                <span class="text-capitalize details-modal-button reservation-details--js" data-href="{$linkToDetails}" title="{_'Details'}">
                                                    <span class="hide-on-mobile">{_'Details'}</span>
                                                    {embeddedSvg 'assets/img/svg/arrow-down-bold.svg',
                                                        class => 'reservation-link-icon show-on-mobile',
                                                        height => 12,
                                                        width => 12}
                                                </span>
                                                {if $item->is_foreign_reservation}
                                                    {if !$item->approved && $item->status !== 'CONFIRMADA' && !$item->declined}
                                                        <a href="/{$presenter->language|noescape}/property/foreign-reservation/edit?id={$item->id}"
                                                        title="{_'Edit'}"
                                                        class=" text-capitalize hide-on-mobile">
                                                            {embeddedSvg 'property-assets/app-assets/images/svg/edit_outline.svg',
                                                                class => 'reservation-edit-icon',
                                                                height => 20,
                                                                width => 20}
                                                        </a>

                                                    {/if}
                                                {elseif $item->created_by !== null}
                                                    <a href="/{$presenter->language|noescape}/property/reservation/edit?id={$item->id}"
                                                    title="{_'Edit'}"
                                                    class=" text-capitalize hide-on-mobile">
                                                        {embeddedSvg 'property-assets/app-assets/images/svg/edit_outline.svg',
                                                            class => 'reservation-edit-icon',
                                                            height => 20,
                                                            width => 20}
                                                    </a>
                                                {/if}
                                                
                                                {var $showKebab = ($item->is_foreign_reservation && !$item->approved && $item->status !== 'CANCELLED' && !$item->declined)
                                                || (!$item->is_foreign_reservation && $item->created_by !== null && !$item->cancelled)
                                                || (!$item->is_foreign_reservation && $item->cancelled && $item->deleted === null)
                                                }
                                                <div n:if="$showKebab" class="dropdown  dropdown-button--reservation">
                                                    <span class="btn p-0 px-50 ps-lg-1 dropdown-toggle hide-arrow " data-bs-toggle="dropdown">
                                                        <div class="kebab-menu-circle-wrapper {if !$item->is_listing_published} cancelled-item{/if}">
                                                            <div class="kebab-menu-circle "></div>
                                                            <div class="kebab-menu-circle "></div>
                                                            <div class="kebab-menu-circle "></div>
                                                        </div>
                                                    </span>
                                                    <div class="dropdown-menu">
                                                        {if $item->is_foreign_reservation && !$item->approved && $item->status !== 'CANCELLED' && !$item->declined}
                                                            <a href="/{$presenter->language|noescape}/property/foreign-reservation/approve?id={$item->id}"
                                                            title="{_'Approve'}"
                                                            class="action-icon action-icon-first action-icon--approve dropdown-item text-capitalize"
                                                            data-datagrid-confirm="Are you sure?">
                                                                {_'Approve'}
                                                            </a>
                                                        {/if}
                                                        {if $item->is_foreign_reservation && !$item->declined}
                                                            <a href="/{$presenter->language|noescape}/property/foreign-reservation/decline?id={$item->id}"
                                                            title="{_'Decline'}"
                                                            class="action-icon action-icon-first action-icon--cancel dropdown-item text-capitalize"
                                                            data-datagrid-confirm="Are you sure?">
                                                                {_'Cancel'}
                                                            </a>
                                                        {/if}
                                                        {if !$item->is_foreign_reservation && $item->created_by !== null && !$item->cancelled}
                                                            {if isset($product_id)}
                                                                <a href="/{$presenter->language|noescape}/property/reservation/delete?id={$item->id}&product_id={$item->listing_id}"
                                                                   title="{_'Cancel'}"
                                                                   class="action-icon action-icon-first action-icon--cancel dropdown-item text-capitalize"
                                                                   data-datagrid-confirm="Are you sure you want to cancel this reservation?">
                                                                    {_'Cancel'}
                                                                </a>
                                                            {else}
                                                                <a href="/{$presenter->language|noescape}/property/reservation/delete?id={$item->id}"
                                                                   title="{_'Cancel'}"
                                                                   class="action-icon action-icon-first action-icon--cancel dropdown-item text-capitalize"
                                                                   data-datagrid-confirm="Are you sure you want to cancel this reservation?">
                                                                    {_'Cancel'}
                                                                </a>
                                                            {/if}
                                                        {/if}
                                                        {if !$item->is_foreign_reservation && $item->created_by !== null && $item->deleted === null}
                                                            {if isset($product_id)}
                                                                <a href="/{$presenter->language|noescape}/property/reservation/remove?id={$item->id}&product_id={$item->listing_id}"
                                                                   title="{_'Delete'}"
                                                                   class="action-icon action-icon-first action-icon--delete dropdown-item text-capitalize"
                                                                   data-datagrid-confirm="Are you sure you want to delete this reservation?">
                                                                    {_'Delete'}
                                                                </a>
                                                            {else}
                                                                <a href="/{$presenter->language|noescape}/property/reservation/remove?id={$item->id}"
                                                                   title="{_'Delete'}"
                                                                   class="action-icon action-icon-first action-icon--delete dropdown-item text-capitalize"
                                                                   data-datagrid-confirm="Are you sure you want to delete this reservation?">
                                                                    {_'Delete'}
                                                                </a>
                                                            {/if}
                                                        {/if}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                </tr>
                            {/foreach}
                        {/snippetArea}
                        </tbody>
                    {/block}
                    {block tfoot}
                        <tfoot n:snippet="pagination">
                        <tr n:block="pagination">
                            <td {if $user->isInRole('admin')} colspan="10" {else} colspan="9" {/if} class="row-grid-bottom">
                                {var $paginator = $control['paginator']->getPaginator()}
                                <div class="pagination-grid pagination-grid--reservation gx-0">
                                    <div class="pagination-grid-first-child">
                                        <small class="text-muted">
                                            {if $control->getPerPage() === 'all'}
                                                {='ublaboo_datagrid.all'|translate}
                                            {else}
                                                {$paginator->getOffset() > 0 ? $paginator->getOffset() + 1 : ($paginator->getItemCount() > 0 ? 1 : 0)} - {sizeof($rows) + $paginator->getOffset()}
                                                {='ublaboo_datagrid.from'|translate} {$paginator->getItemCount()}
                                            {/if}
                                        </small>
                                    </div>
                                    <div class="pagination-grid-second-child text-center">
                                        {control paginator}
                                    </div>
                                    <div class="col-per-page text-right">
                                        {input $filter['perPage'], data-autosubmit-per-page => TRUE, class => 'form-control input-sm form-control-sm'}
                                        {input $filter['perPage_submit'], class => 'datagrid-per-page-submit'}
                                    </div>
                                </div>
                            </td>
                        </tr>
                        </tfoot>
                    {/block}
                </table>
            </div>
        </div>
        <div class="reservation-details-modal"></div>
        {/block}
        {/form}
        {block js}
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                $('.reservation-details--js').on('click', function(e) {
                    e.preventDefault();
                    var href = $(this).data('href');
                    window.open(href, '_blank');
                });

                $('#hideCancelled').on('change', function() {
                    sendRequest();
                });

                naja.addEventListener('success', function () {
                    $('.loader-grid-table').addClass('d-none');

                    $('#hideCancelled').on('change', function() {
                        sendRequest();
                    });

                    $('.reservation-details--js').on('click', function(e) {
                        e.preventDefault();
                        var href = $(this).data('href');
                        window.open(href, '_blank');
                    });
                });

                naja.addEventListener('start', function (event) {
                    $('.loader-grid-table').removeClass('d-none');
                });

            });
        </script>
        {/block}
        {/snippetArea}
    </div>
</div>



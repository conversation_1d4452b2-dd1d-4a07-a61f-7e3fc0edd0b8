<?php declare(strict_types = 1);

namespace App\Modules\Property\Components;

use App\Common\Components\BaseControl;
use App\Common\Enums\DealStagesEnum;
use App\Common\Enums\InstallmentsPaymentStatusEnum;
use App\Common\Reservation\PartnerInstallmentsAmountResolver;
use App\Models\Entities\custom\ReservationFullDetailsEntity;
use App\Models\Entities\ReservationEntity;
use App\Modules\Property\Logic\InvoiceLogic;
use App\Repositories\GeneratedOffersDealsRepository;
use App\Repositories\ReservationLogRepository;
use App\Repositories\ReservationsRepository;
use Dibi\DateTime;

class RestAmountToBePaidControl extends BaseControl
{

	private ReservationsRepository $reservationsRepository;

	public array $paymentStatus = [
		InstallmentsPaymentStatusEnum::WAITING_PAYMENT => 'Za uplatu',
		InstallmentsPaymentStatusEnum::PAID => 'PLAĆANJE PROVEDENO',
	];

	private InvoiceLogic $invoiceLogic;

	private PartnerInstallmentsAmountResolver $partnerInstallmentsAmountResolver;

	private GeneratedOffersDealsRepository $generatedOffersDealsRepository;

	private ReservationLogRepository $reservationLogRepository;

	private ?ReservationFullDetailsEntity $reservationFullDetails;

	public function __construct(
		?ReservationFullDetailsEntity $reservationFullDetails,
		ReservationsRepository $reservationsRepository,
		InvoiceLogic $invoiceLogic,
		PartnerInstallmentsAmountResolver $partnerInstallmentsAmountResolver,
		GeneratedOffersDealsRepository $generatedOffersDealsRepository,
		ReservationLogRepository $reservationLogRepository
	)
	{
		$this->reservationFullDetails = $reservationFullDetails;
		$this->reservationsRepository = $reservationsRepository;
		$this->invoiceLogic = $invoiceLogic;
		$this->partnerInstallmentsAmountResolver = $partnerInstallmentsAmountResolver;
		$this->generatedOffersDealsRepository = $generatedOffersDealsRepository;
		$this->reservationLogRepository = $reservationLogRepository;
	}

	public function render(): void
	{
		$reservationId = (int) $this->getPresenter()->getParameter('id');
		/** @var ReservationEntity $reservation */
		$reservation = $this->reservationsRepository->getBy([ReservationEntity::ID => $reservationId]);
		$this->template->invoice = $this->invoiceLogic->getInvoice($reservationId);
		$this->template->reservationFullDetails = $this->reservationFullDetails;

		if ($reservation->partner_first_installment_payment_amount === null && $reservation->partner_second_installment_payment_amount === null) {
			$this->partnerInstallmentsAmountResolver->resolve($reservationId, $reservation->amount);
			$this->template->firstInstallmentAmount = $this->partnerInstallmentsAmountResolver->getFirstInstallment();
			$this->template->secondInstallmentAmount = $this->partnerInstallmentsAmountResolver->getSecondInstallment();
		} else {
			$this->template->firstInstallmentAmount = $reservation->partner_first_installment_payment_amount;
			$this->template->secondInstallmentAmount = $reservation->partner_second_installment_payment_amount;
		}
		$firstInstallmentPaymentStatus = $reservation->partner_first_installment_payment_status ?? InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$secondInstallmentPaymentStatus = $reservation->partner_second_installment_payment_status ?? InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$this->template->firstInstallmentPaymentStatus = $this->paymentStatus[$firstInstallmentPaymentStatus];
		$this->template->secondInstallmentPaymentStatus = $this->paymentStatus[$secondInstallmentPaymentStatus];
		$this->template->dateOfPaymentFirstInstallment = $reservation->partner_first_installment_paid_date instanceof DateTime
			? $reservation->partner_first_installment_paid_date->format('d.m.Y') : '';
		$this->template->dateOfPaymentSecondInstallment = $reservation->partner_second_installment_paid_date instanceof DateTime
			? $reservation->partner_second_installment_paid_date->format('d.m.Y') : '';
		$this->template->reservationId = $reservation->id;
		$this->template->second_payment_type = $reservation->payment_type_for_second_installment;
		$deal = $reservation->deal_id !== null ? $this->generatedOffersDealsRepository->getDealByHubspotDealId($reservation->deal_id) : null;

		if ($deal !== null && $deal->deal_stage === DealStagesEnum::STORNO) {
			if ($firstInstallmentPaymentStatus !== InstallmentsPaymentStatusEnum::PAID) {
				$this->template->firstInstallmentAmount = 0;
			}

			if ($secondInstallmentPaymentStatus !== InstallmentsPaymentStatusEnum::PAID) {
				$this->template->secondInstallmentAmount = 0;
			}
		}

		$this->template->reservationLog = $this->reservationLogRepository->getBy(['reservation_id' => $reservationId], 'id DESC');
		$this->template->secondInstallmentAmountFromOldPrice = $this->template->firstInstallmentAmountFromOldPrice = null;

		if ($this->template->reservationLog !== null) {
			$this->partnerInstallmentsAmountResolver->resolve($reservationId, $this->template->reservationLog->amount);
			$this->template->firstInstallmentAmountFromOldPrice = $this->partnerInstallmentsAmountResolver->getFirstInstallment();
			$this->template->secondInstallmentAmountFromOldPrice = $this->partnerInstallmentsAmountResolver->getSecondInstallment();
		}

		if ($this->template->secondInstallmentAmount === 0 && $this->template->firstInstallmentAmount === 0) {
			return;
		}

		parent::render();
	}

}

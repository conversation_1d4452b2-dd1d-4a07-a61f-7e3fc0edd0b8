{varType App\Models\Entities\ReservationDetailsEntity $reservationDetails}
<div class="pt-2 pb-1 border-bottom">
    <h4 class="mb-1 fw-bolder first-letter-capital">{_'Invoice'}</h4>
    <div class="row">
        <div class="col-12 col-lg-7">
            <div class="table-responsive">
                <table class="table table-sm">
                    <tbody>
                    <tr>
                        <td class="ps-0"><span class="fw-bolder">{_'Accommodation price'|firstUpper}</span></td>
                        <td class="ps-0"><span class="fw-bolder">{number_format($reservationDetails->amount, 2, ',', '.')} €</span></td>
                    </tr>
                    <tr>
                        <td class="ps-0"><span class="fw-bolder">{_'Commision (included VAT 25%)'|firstUpper}</span></td>
                        <td class="ps-0"><span class="fw-bolder">{number_format($agencyProvisionAmountVatIncluded, 2, ',', '.')} €</span></td>
                    </tr>
                    <tr>
                        <td class="ps-0"><span class="fw-bolder">{_'Discount'|firstUpper}</span></td>
                        <td class="ps-0"><span class="fw-bolder">{number_format(0, 2, ',', '.')} €</span></td>
                    </tr>
                    <tr>
                        <td class="ps-0"><span class="fw-bolder">{_'Total'|firstUpper}</span></td>
                        <td class="ps-0">
                            <span class="fw-bolder">
                                {number_format($totalAmount, 2, ',', '.')} €
                            </span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

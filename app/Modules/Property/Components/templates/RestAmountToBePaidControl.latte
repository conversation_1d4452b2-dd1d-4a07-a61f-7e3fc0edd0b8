<div class="pt-2 pb-1 border-bottom">
    <h4 class="mb-1 fw-bolder first-letter-capital">{_'Dinami<PERSON> isplate'|firstUpper}</h4>
    <div class="row">
        <div class="col-sm-6">
            <ul class="list-unstyled">
                {if $reservationLog !== null && $firstInstallmentAmount !== $firstInstallmentAmountFromOldPrice}
                    <li class="mb-75"><del>{_'Prva rata'|firstUpper}: {number_format($firstInstallmentAmountFromOldPrice, 2, ',', '.')} € - {$firstInstallmentPaymentStatus} {if !empty($dateOfPaymentFirstInstallment)} ({$dateOfPaymentFirstInstallment}) {/if}</del></li>
                {/if}
                <li class="mb-75">{_'Prva rata'|firstUpper}: <span class="fw-bolder">{number_format($firstInstallmentAmount, 2, ',', '.')} € - {$firstInstallmentPaymentStatus} {if !empty($dateOfPaymentFirstInstallment)} ({$dateOfPaymentFirstInstallment}) {/if}</span></li>
                {if !empty($secondInstallmentAmount)}
                    {if $reservationLog !== null && $secondInstallmentAmount !== $secondInstallmentAmountFromOldPrice}
                        <li class="mb-75">
                            <del>{_'Druga rata'}: {number_format($secondInstallmentAmountFromOldPrice, 2, ',', '.')} € -
                                {if $second_payment_type === App\Common\Enums\PaymentTypeEnum::MONEY}
                                    {_'Rest pay at location'}
                                {else}
                                    {$secondInstallmentPaymentStatus} {if !empty($dateOfPaymentSecondInstallment)} ({$dateOfPaymentSecondInstallment}) {/if}
                                {/if}
                            </del>
                        </li>
                    {/if}
                    <li class="mb-75" n:if="!empty($secondInstallmentAmount)">{_'Druga rata'}: <span class="fw-bolder">{number_format($secondInstallmentAmount, 2, ',', '.')} € -
                        {if $second_payment_type === App\Common\Enums\PaymentTypeEnum::MONEY}
                            {_'Rest pay at location'}
                        {else}
                            {$secondInstallmentPaymentStatus} {if !empty($dateOfPaymentSecondInstallment)} ({$dateOfPaymentSecondInstallment}) {/if}
                        {/if}
                        </span>
                    </li>
                {/if}
                {if $user->isInRole('admin')}
                    <li class="mb-75">
                        <span class="ps-0">{_'Guest payment status'|firstUpper} {_'1st installment'}:</span>
                        <span class="ps-0"><span class="fw-bolder">{$reservationFullDetails->guest_payment_first_installments_amount|number:2:'.'} €  {$reservationFullDetails->guest_payment_first_installments_status}</span></span>
                    </li>
                    <li class="mb-75">
                        <span class="ps-0">{_'Guest payment status'|firstUpper} {_'2nd installment'}:</span>
                        <span class="ps-0"><span class="fw-bolder">{$reservationFullDetails->guest_payment_second_installments_amount|number:2:'.'} €  {$reservationFullDetails->guest_payment_second_installments_status}</span></span>
                    </li>
                {/if}
            </ul>
        </div>
    </div>
</div>

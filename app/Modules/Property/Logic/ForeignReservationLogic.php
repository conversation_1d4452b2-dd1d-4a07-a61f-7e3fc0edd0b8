<?php declare(strict_types = 1);

namespace App\Modules\Property\Logic;

use App\Common\Avantio\Enums\AvantioPaymentTypeEnum;
use App\Common\Avantio\Enums\BookingTypeEnum;
use App\Common\Avantio\Enums\ServiceTypeEnum;
use App\Common\Enums\PaymentTypeEnum;
use App\Common\Enums\TwoLetterLanguageEnum;
use App\Models\Entities\ForeignReservationEntity;
use App\Models\Entities\ForeignReservationPaymentEntity;
use App\Models\Entities\ForeignReservationServiceEntity;
use App\Models\Entities\ProductEntity;
use App\Modules\Api\Requests\BookingRequest;
use App\Repositories\ForeignReservationPaymentsRepository;
use App\Repositories\ForeignReservationServicesRepository;
use App\Repositories\ForeignReservationsRepository;
use App\Repositories\ProductRepository;
use Nette\Utils\Strings;
use Wedo\Api\Exceptions\ResponseException;

class ForeignReservationLogic extends BaseFormLogic
{

	private ForeignReservationsRepository $repository;

	private ProductRepository $productRepository;

	private ForeignReservationPaymentsRepository $foreignReservationPaymentsRepository;

	private ForeignReservationServicesRepository $foreignReservationServiceRepository;

	public function __construct(
		ForeignReservationsRepository $repository,
		ProductRepository $productRepository,
		ForeignReservationPaymentsRepository $foreignReservationPaymentsRepository,
		ForeignReservationServicesRepository $foreignReservationServiceRepository
	)
	{
		$this->repository = $repository;
		$this->productRepository = $productRepository;
		$this->foreignReservationPaymentsRepository = $foreignReservationPaymentsRepository;
		$this->foreignReservationServiceRepository = $foreignReservationServiceRepository;
	}

	public function getRepository(): ForeignReservationsRepository
	{
		return $this->repository;
	}

	public function getBookingRequest(int $id): BookingRequest
	{
		/** @var ForeignReservationEntity $foreignEntity */
		$foreignEntity = $this->repository->getBy([ForeignReservationEntity::ID => $id]);

		if ($foreignEntity === null) {
			throw new ResponseException('Not found!', 400);
		}

		if (empty($foreignEntity->email)) {
			throw new ResponseException('Customer without email!', 400);
		}

		if ($foreignEntity->booking_type === BookingTypeEnum::CANCELLED) {
			throw new ResponseException('The reservation should be cancelled!', 400);
		}

		$foreignPayments = $this->foreignReservationPaymentsRepository->findBy([ForeignReservationPaymentEntity::FOREIGN_ID => $id]);
		$foreignServices = $this->foreignReservationServiceRepository->findBy([ForeignReservationServiceEntity::FOREIGN_ID => $id]);

		$bookingRequest = new BookingRequest();
		$bookingRequest->property_id = (string) $foreignEntity->product_id;
		$bookingRequest->email = $foreignEntity->email;
		$bookingRequest->firstname = $foreignEntity->name;
		$bookingRequest->lastname = $foreignEntity->surname;
		$bookingRequest->phone = $foreignEntity->telephone ?? $foreignEntity->telephone2;
		$bookingRequest->language = $this->getSupportedLanguage(Strings::upper($foreignEntity->booking_language ?? ''));
		$bookingRequest->two_installments = $this->shouldBePaidWithTwoInstallments($foreignPayments);
		$bookingRequest->payment_type_for_first_installment = $this->getPaymentTypeForFirstInstallment($foreignPayments);
		$bookingRequest->payment_type_for_second_installment = $this->getPaymentTypeForSecondInstallment($foreignPayments);
		$bookingRequest->distribution_channel_name = $this->getDistributionChannelByWeb($foreignEntity->web);
		$bookingRequest->arrival = $foreignEntity->arrive->format('Ymd');
		$bookingRequest->departure = $foreignEntity->departure->format('Ymd');
		$bookingRequest->booked_date = $foreignEntity->booking_date->format('Ymd');
		$bookingRequest->creation_date = $foreignEntity->creation_date->format('YmdHis');
		$bookingRequest->adults = $foreignEntity->adults_number;
		$bookingRequest->children = $this->getNumberOfChildren($foreignEntity);
		$bookingRequest->pets = $this->getNumberOfPets($foreignServices);
		$bookingRequest->comments = $foreignEntity->comments;
		$bookingRequest->price_eur = (int) $foreignEntity->rental_price;
		$bookingRequest->amount = $foreignEntity->rental_price;
		$bookingRequest->reference = $foreignEntity->agent_localizator ?? '';

		return $bookingRequest;
	}

	public function getNumberOfChildren(ForeignReservationEntity $foreignEntity)
	{
		$numberOfChildren = 0;

		for ($i = 1; $i <= 6; $i++) {

			$property = 'child' . $i . '_age';

			if (!empty($foreignEntity->$property)) {
				$numberOfChildren++;
			}
		}

		return $numberOfChildren;
	}

	private function getDistributionChannelByWeb(?string $web): ?string
	{
		if (empty($web)) {
			return null;
		}

		return match ($web) {
			'Airbnb.com' => 'Airbnb',
			'Ferienhausmiete.de' => 'Ferienhausmiete',
			'Holidu.com' => 'Holidu',
			'Hometogo.de' => 'Hometogo',
			'Traum-ferienwohnungen.de' => 'Traum-Ferienwohnugen',
			'Vrbo.com' => 'Vrbo',
			'Booking.com' => 'Booking.com',
			default => 'Unknown website',
		};
	}

	/**
	 * @param ForeignReservationPaymentEntity[] $foreignPayments
	 */
	private function shouldBePaidWithTwoInstallments(array $foreignPayments): bool
	{
		if (empty($foreignPayments)) {
			return false;
		}

		$numberOfInstallments = 0;

		foreach ($foreignPayments as $foreignPayment) {
			if (!$foreignPayment->security_deposit) {
				$numberOfInstallments++;
			}
		}

		return $numberOfInstallments > 1;
	}

	/**
	 * @param ForeignReservationPaymentEntity[] $foreignPayments
	 */
	private function getPaymentTypeForFirstInstallment(array $foreignPayments): ?string
	{
		if (empty($foreignPayments)) {
			return null;
		}

		foreach ($foreignPayments as $foreignPayment) {
			if ($foreignPayment->security_deposit) {
				continue;
			}

			if ($foreignPayment->payment_method === AvantioPaymentTypeEnum::BANK_TRANSFER) {
				return PaymentTypeEnum::BANK_TRANSFER;
			}
		}

		return PaymentTypeEnum::BANK_TRANSFER;
	}

	/**
	 * @param ForeignReservationPaymentEntity[] $foreignPayments
	 */
	private function getPaymentTypeForSecondInstallment(array $foreignPayments): ?string
	{
		if (empty($foreignPayments)) {
			return null;
		}

		foreach ($foreignPayments as $index => $foreignPayment) {
			if ($foreignPayment->security_deposit) {
				continue;
			}

			if ($index === 0) {
				continue;
			}

			if ($foreignPayment->payment_method === AvantioPaymentTypeEnum::BANK_TRANSFER) {
				return PaymentTypeEnum::BANK_TRANSFER;
			}
		}

		return PaymentTypeEnum::BANK_TRANSFER;
	}

	/**
	 * @param ForeignReservationServiceEntity[] $foreignServices
	 */
	public function getNumberOfPets(array $foreignServices): int
	{
		if (empty($foreignServices)) {
			return 0;
		}

		foreach ($foreignServices as $foreignService) {
			if ($foreignService->code === ServiceTypeEnum::PET) {
				return $foreignService->amount;
			}
		}

		return 0;
	}

	private function getSupportedLanguage(?string $bookingLanguage): string
	{
		return match ($bookingLanguage) {
			'DE' => TwoLetterLanguageEnum::GERMAN,
			'IT' => TwoLetterLanguageEnum::ITALIAN,
			'HR' => TwoLetterLanguageEnum::CROATIAN,
			default => TwoLetterLanguageEnum::ENGLISH,
		};
	}

	public function markAsApproved(int $reservationId, int $id): void
	{
		$this->repository->update($id, [ForeignReservationEntity::APPROVED => true, ForeignReservationEntity::RESERVATION_ID => $reservationId]);
	}

	public function setReservationId(int $reservationId, int $id): void
	{
		$this->repository->update($id, [ForeignReservationEntity::RESERVATION_ID => $reservationId]);
	}

	public function markAsDeclined(int $id): void
	{
		$this->repository->update($id, [ForeignReservationEntity::DECLINED => true, ForeignReservationEntity::ID => $id]);
	}

	/**
	 * @return ForeignReservationPaymentEntity[]
	 */
	public function getReservationPayments(int $id): array
	{
		return $this->foreignReservationPaymentsRepository->findBy([ForeignReservationPaymentEntity::FOREIGN_ID => $id]);
	}

	public function getProduct(int $productId): ProductEntity
	{
		return $this->productRepository->getBy(['id' => $productId]);
	}

	public function getReservationDetails(int $id): ?ForeignReservationEntity
	{
		return $this->repository->getBy(['id' => $id]);
	}

	/**
	 * @return ForeignReservationServiceEntity[]
	 */
	public function getReservationServices(int $id): array
	{
		return $this->foreignReservationServiceRepository->findBy([ForeignReservationServiceEntity::FOREIGN_ID => $id]);
	}

	public function getReservationId(int $id): ?int
	{
		/** @var ForeignReservationEntity $foreignEntity */
		$foreignEntity = $this->repository->getBy([ForeignReservationEntity::ID => $id]);

		if ($foreignEntity === null) {
			return null;
		}

		$reservationId = $this->repository->getReservationIdForReservationCode($foreignEntity->booking_code, $foreignEntity->product_id);

		return $reservationId;
	}

}

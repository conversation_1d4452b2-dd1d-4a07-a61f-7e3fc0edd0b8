<?php declare(strict_types = 1);

namespace App\Modules\Property\Logic;

use App\Common\AvailabilityStatusChangedEvent;
use App\Common\Enums\ActionLogDbTableEnum;
use App\Common\Enums\AvailabilityStatusEnum;
use App\Common\Enums\DealStagesEnum;
use App\Common\Enums\InstallmentsPaymentFirstStatusEnum;
use App\Common\Enums\InstallmentsPaymentSecondStatusEnum;
use App\Common\Enums\InstallmentsPaymentStatusEnum;
use App\Common\Enums\PaymentTypeEnum;
use App\Common\Enums\RoleEnum;
use App\Common\EnvironmentDetector;
use App\Common\Hubspot;
use App\Common\Logic\ReservationValidatorLogic;
use App\Common\Queue\Parameters\AvailabilityParams;
use App\Common\Queue\Producers\AvailabilityProducer;
use App\Common\Reservation\Helpers\DistributionChannelHelper;
use App\Common\Reservation\PartnerInstallmentsAmountResolver;
use App\Common\Reservation\ReservationAgencyProvisionResolver;
use App\Common\Services\ProductOwnerNotificationService;
use App\Models\Entities\custom\ReservationEventEntity;
use App\Models\Entities\GeneratedOfferDealEntity;
use App\Models\Entities\ReservationEntity;
use App\Models\Entities\ReservationLogEntity;
use App\Modules\Property\Components\BaseForm;
use App\Repositories\GeneratedOffersDealsRepository;
use App\Repositories\ProductRepository;
use App\Repositories\ReservationInsuranceRepository;
use App\Repositories\ReservationLogRepository;
use App\Repositories\ReservationsRepository;
use Dibi\DateTime;
use Dibi\Row;
use Nette\Forms\Container;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Psr\EventDispatcher\EventDispatcherInterface;

class ReservationLogic extends BaseFormLogic
{

	private ReservationsRepository $repository;

	private ProductRepository $productRepository;

	private ReservationInsuranceRepository $insuranceRepository;

	private ProductOwnerNotificationService $notificationService;

	private Hubspot $hubspot;

	private PaymentConfirmationLogic $paymentConfirmationLogic;

	private GeneratedOffersDealsRepository $generatedOffersDealsRepository;

	private ReservationLogRepository $reservationLogRepository;

	private EventDispatcherInterface $dispatcher;

	private EnvironmentDetector $environmentDetector;

	private DistributionChannelHelper $distributionChannelHelper;

	private PartnerInstallmentsAmountResolver $partnerInstallmentsAmountResolver;

	private AvailabilityProducer $availabilityProducer;

	private ReservationValidatorLogic $reservationValidatorLogic;

	private ReservationAgencyProvisionResolver $agencyProvisionResolver;

	public function __construct(
		ReservationsRepository $repository,
		ProductRepository $productRepository,
		ReservationInsuranceRepository $insuranceRepository,
		ProductOwnerNotificationService $notificationService,
		Hubspot $hubspot,
		GeneratedOffersDealsRepository $generatedOffersDealsRepository,
		PaymentConfirmationLogic $paymentConfirmationLogic,
		ReservationLogRepository $reservationLogRepository,
		EventDispatcherInterface $dispatcher,
		EnvironmentDetector $environmentDetector,
		DistributionChannelHelper $distributionChannelHelper,
		PartnerInstallmentsAmountResolver $partnerInstallmentsAmountResolver,
		AvailabilityProducer $availabilityProducer,
		ReservationValidatorLogic $reservationValidatorLogic,
		ReservationAgencyProvisionResolver $agencyProvisionResolver,
	)
	{
		$this->repository = $repository;
		$this->productRepository = $productRepository;
		$this->insuranceRepository = $insuranceRepository;
		$this->notificationService = $notificationService;
		$this->hubspot = $hubspot;
		$this->generatedOffersDealsRepository = $generatedOffersDealsRepository;
		$this->paymentConfirmationLogic = $paymentConfirmationLogic;
		$this->reservationLogRepository = $reservationLogRepository;
		$this->dispatcher = $dispatcher;
		$this->environmentDetector = $environmentDetector;
		$this->distributionChannelHelper = $distributionChannelHelper;
		$this->partnerInstallmentsAmountResolver = $partnerInstallmentsAmountResolver;
		$this->availabilityProducer = $availabilityProducer;
		$this->reservationValidatorLogic = $reservationValidatorLogic;
		$this->agencyProvisionResolver = $agencyProvisionResolver;
	}

	public function getRepository(): ReservationsRepository
	{
		return $this->repository;
	}

	public function initFormValues(ArrayHash $values, Container $form): ArrayHash
	{
		$values = parent::initFormValues($values, $form);

		if (empty($values['id'])) {
			$values[ReservationEntity::CREATED_BY] = $this->user->getId();
			$values[ReservationEntity::CREATED] = $values[ReservationEntity::BOOKED_DATE] = new \DateTime();
		}

		return $values;
	}

	public function validate(ArrayHash $values, BaseForm $form): bool
	{
		if ($values[ReservationEntity::ARRIVE] === NULL || $values[ReservationEntity::ARRIVE] === ''
			|| $values[ReservationEntity::DEPARTURE] === NULL || $values[ReservationEntity::DEPARTURE] === '') {
			$form->addError('Please select arrive and departure date!');

			return false;
		}

		if (!$this->validateUserAccess($values)) {
			$form->addError('Not able to edit customer reservation!');

			return false;
		}

		$productReservationExist = $this->getReservationContainExistingDates($values);

		if ($productReservationExist !== null) {
			$errorMessage = $form->getTranslator()->translate(
				'Dates are allready reserved: %s  - %s',
				$productReservationExist->arrive->format('Y-m-d'),
				$productReservationExist->departure->format('Y-m-d')
			);
			$form->addError($errorMessage, false);

			return false;
		}

		return $form->isValid();
	}

	private function validateUserAccess(ArrayHash $values): bool
	{
		$isRecruiter = $this->user->isInRole(RoleEnum::RECRUITER);

		return $this->productRepository->isProductOwner($this->getProductId($values), $this->user->getId(), !$isRecruiter)
			|| $this->user->isInRole(RoleEnum::ADMIN);
	}

	private function getReservationContainExistingDates(ArrayHash $values): ?ReservationEntity
	{
		$productId = $this->getProductId($values);
		$arrive = $values[ReservationEntity::ARRIVE];
		$departure = $values[ReservationEntity::DEPARTURE];
		$reservationId = ($values['id'] !== NULL && $values['id'] !== '') ? (int) $values['id'] : null;

		return $this->reservationValidatorLogic->getActiveReservationForDates($productId, $arrive, $departure, $reservationId);
	}

	private function getProductId(ArrayHash $values): int
	{
		if ($values['id'] === NULL || $values['id'] === '') {
			return (int) $values[ReservationEntity::PRODUCT_ID];
		}

		$id = (int) $values['id'];
		/** @var ReservationEntity $reservationDetails */
		$reservationDetails = $this->getRepository()->getById($id);

		return $reservationDetails->product_id;
	}

	public function save(BaseForm $form): int
	{
		$values = $form->getUnsafeValues(null);

		if (!$form->isValid() || !$this->validate($values, $form)) {
			throw new \Exception(implode(',', $form->getErrors()));
		}

		if (!empty($values->id)) {
			/** @var ReservationEntity $reservation */
			$reservation = $this->repository->getBy([ReservationEntity::ID => (int) $values->id]);
			$this->updateAvailabilityStatus($reservation, AvailabilityStatusEnum::A);
		}

		$this->initFormValues($values, $form);
		$values[ReservationEntity::PRICE_EUR] = (int) $values[ReservationEntity::AMOUNT];
		$changeManuallyPriceForPartner = !empty($values['manual_change_price_for_partner']);
		$changeManuallyPrice = !empty($values['manual_change_price']);

		unset($values['manual_change_price'], $values['manual_change_price_for_partner']);

		if (!empty($values['id'])) {
			$id = (int) $values['id'];
			unset($values['id']);

			/** @var ReservationEntity $reservation */
			$reservation = $this->repository->getBy([ReservationEntity::ID => $id]);

			if ($reservation->reservation_code !== null) {
				$this->resetAgencyProvision($values, $reservation);
				$this->setReservationPrices($values, $reservation, $changeManuallyPrice);
				$this->setReservationPricesForPartner($values, $reservation, $changeManuallyPriceForPartner);
			}

			if ($this->environmentDetector->isProduction()) {
				//method should be called before update
				$this->paymentConfirmationLogic->sendPaymentConfirmationNotification($reservation, (array) $values);
			}
			$this->update($id, $values);
			$reservationId = $id;
			$this->actionLog->log('Reservation updated', ActionLogDbTableEnum::RESERVATIONS, $reservationId, 'Reservation successfully updated', 'success');
		} else {
			$values[ReservationEntity::TOTAL_PRICE_EUR] = $values[ReservationEntity::AMOUNT];
			$reservationId = $this->insert($values);
			$this->actionLog->log('Reservation inserted', ActionLogDbTableEnum::RESERVATIONS, $reservationId, 'Reservation successfully created', 'success');
		}

		if (!$reservationId) {
			throw new \Exception('Is not possible to save reservation');
		}

		/** @var ReservationEntity $reservation */
		$reservation = $this->repository->getBy([ReservationEntity::ID => $reservationId]);

		if ($this->environmentDetector->isProduction() && $reservation->deal_id !== null) {
			$this->updateDeal($reservation, (array) $values);
		}

		$this->updateAvailabilityStatus($reservation);
		$this->syncAvailabilities($reservation);

		return $reservationId;
	}

	private function updateDeal(ReservationEntity $reservation, array $values)
	{
		$arrivalDate = DateTime::createFromFormat('Y-m-d', $values['arrive'])->setTime(0, 0);
		$departureDate = DateTime::createFromFormat('Y-m-d', $values['departure'])->setTime(0, 0);
		$numberOfInstallments = empty($reservation->payment_second_installment_amount) ? 1 : 2;
		$statusFirstInstallment = $statusSecondInstallment = $partnerStatusFirstInstallment = $partnerStatusSecondInstallment = 'Na čekanju';

		$statusFirstInstallment = $this->getFirstInstallmentStatus($reservation, $numberOfInstallments, $statusFirstInstallment);
		$statusSecondInstallment = $this->getSecondInstallmentStatus($reservation, $statusSecondInstallment);

		if ($reservation->partner_first_installment_payment_status === InstallmentsPaymentStatusEnum::PAID) {
			$partnerStatusFirstInstallment = 'Uplaćena akontacija partneru';
		}

		if ($reservation->partner_second_installment_payment_status === InstallmentsPaymentStatusEnum::PAID) {
			$partnerStatusSecondInstallment = 'Uplaćen ukupan iznos partneru';
		}

		$partnerPaymentFirstInstall = $partnerPaymentSecondInstall = $partnerPaymentFirstInstallDue = $partnerPaymentSecondInstallDue = null;

		if (!empty($reservation->partner_first_installment_paid_date)) {
			$partnerPaymentFirstInstall = $reservation->partner_first_installment_paid_date
				->setTimezone(new \DateTimeZone('UTC'))->setTime(0, 0)->getTimestamp() * 1000;
		}

		if (!empty($reservation->partner_second_installment_paid_date)) {
			$partnerPaymentSecondInstall = $reservation->partner_second_installment_paid_date
				->setTimezone(new \DateTimeZone('UTC'))->setTime(0, 0)->getTimestamp() * 1000;
		}

		if (!empty($reservation->partner_first_installment_payment_due)) {
			$partnerPaymentFirstInstallDue = $reservation->partner_first_installment_payment_due
				->setTimezone(new \DateTimeZone('UTC'))->setTime(0, 0)->getTimestamp() * 1000;
		}

		if (!empty($reservation->partner_second_installment_payment_due)) {
			$partnerPaymentSecondInstallDue = $reservation->partner_second_installment_payment_due
				->setTimezone(new \DateTimeZone('UTC'))->setTime(0, 0)->getTimestamp() * 1000;
		}

		$countryCode = $this->getCountryCode($reservation);

		$dealProperties = [
			['name' => 'reservation_adults', 'value' => $reservation->adults],
			['name' => 'reservation_tip_placanja', 'value' => $reservation->payment_type === 'cc' ? 'Kreditna kartica' : 'Bankovna doznaka'],
			[
				'name' => 'reservation_tip_placanja_2_rata',
				'value' => $this->hubspot->getPaymentTypeForSecondInstallment($reservation->payment_type_for_second_installment),
			],
			['name' => 'reservation_pets', 'value' => $reservation->pets],
			['name' => 'reservation_children', 'value' => $reservation->children],
			['name' => 'reservation_last_name', 'value' => $reservation->contact_lastname],
			['name' => 'reservation_first_name', 'value' => $reservation->contact_firstname],
			['name' => 'reservation_phone_number', 'value' => $reservation->phone],
			['name' => 'reservation_e_mail', 'value' => $reservation->contact_email],
			['name' => 'reservation_vg_broj_rata', 'value' => $numberOfInstallments],
			['name' => 'reservation_country', 'value' => $countryCode],
			['name' => 'reservation_status_placanja_prve_uplate', 'value' => $statusFirstInstallment],
			['name' => 'reservation___vg___partner_status_placanja___akontacija', 'value' => $partnerStatusFirstInstallment],
			['name' => 'reservation_vg_novasol_datum_prve_uplate', 'value' => $partnerPaymentFirstInstallDue],
			['name' => 'reservation_vg_novasol_datum_druge_uplate', 'value' => $partnerPaymentSecondInstallDue],
			[
				'name' => 'reservation_datum_prve_uplate',
				'value' => !empty($reservation->payment_first_installment_date) ? $reservation->payment_first_installment_date->format('d.m.Y.') : NULL,
			],
			[
				'name' => 'reservation_datum_druge_uplate',
				'value' => !empty($reservation->payment_second_installment_date) ? $reservation->payment_second_installment_date->format('d.m.Y.') : NULL,
			],
			['name' => 'reservation___vg___partner_datum_isplate_prve_rate', 'value' => $partnerPaymentFirstInstall],
			['name' => 'reservation___vg___partner_datum_isplate_druge_rate', 'value' => $partnerPaymentSecondInstall],

			['name' => 'reservation___vg___partner_iznos_za_isplatu_prve_rate', 'value' => $reservation->partner_first_installment_payment_amount],
			['name' => 'reservation___vg___partner_iznos_za_isplatu_druge_rate', 'value' => $reservation->partner_second_installment_payment_amount],
		];

		if ($reservation->payment_type_for_second_installment !== PaymentTypeEnum::MONEY) {
			$dealProperties[] = ['name' => 'reservation_status_placanja_druge_uplate', 'value' => $statusSecondInstallment];
			$dealProperties[] = ['name' => 'reservation___vg___partner_status_placanja___ukupan_iznos', 'value' => $partnerStatusSecondInstallment];
		}

		if ($arrivalDate) {
			$arrivalTimestamp = $arrivalDate->modify('+2 hours')
				->setTimezone(new \DateTimeZone('UTC'))->setTime(0, 0)->getTimestamp() * 1000;
			$dealProperties[] = ['name' => 'reservation_arrival_deal_field_test', 'value' => $arrivalTimestamp];
		}

		if ($departureDate) {
			$departureTimestamp = $departureDate->modify('+2 hours')
				->setTimezone(new \DateTimeZone('UTC'))->setTime(0, 0)->getTimestamp() * 1000;
			$dealProperties[] = ['name' => 'reservation_departure', 'value' => $departureTimestamp];
		}

		$dealProperties[] = ['name' => 'amount', 'value' => $reservation->total_price_eur];
		$dealProperties[] = ['name' => 'reservation___original_amount', 'value' => $reservation->amount];
		$dealProperties[] = ['name' => 'reservation_iznos_prve_uplate', 'value' => $reservation->payment_first_installment_amount];
		$dealProperties[] = ['name' => 'reservation_iznos_druge_uplate', 'value' => $reservation->payment_second_installment_amount];

		try {
			$this->hubspot->getClient()->deals()->update($reservation->deal_id, $dealProperties);
			$this->updateReservationInstallmentDates($dealProperties, $reservation);
		} catch (\Throwable $exception) {
			$this->actionLog->log(
				'Hubspot deal update',
				ActionLogDbTableEnum::RESERVATIONS,
				$reservation->id,
				'Error while updating deal: ' . $exception->getMessage(),
				'error'
			);
			$this->logger->error('Failed to update deal - ' . $reservation->deal_id, ['exception' => $exception]);
		}
	}

	private function updateReservationInstallmentDates(array $dealProperties, ReservationEntity $reservation): void
	{
		$updateData = [];

		foreach ($dealProperties as $property) {
			switch ($property['name']) {
				case 'reservation_datum_prve_uplate':
					if (!empty($property['value'])) {
						$updateData[ReservationEntity::PAYMENT_FIRST_INSTALLMENT_DATE] = \DateTime::createFromFormat('d.m.Y.', $property['value']);
					}
					break;

				case 'reservation_datum_druge_uplate':
					if (!empty($property['value'])) {
						$updateData[ReservationEntity::PAYMENT_SECOND_INSTALLMENT_DATE] = \DateTime::createFromFormat('d.m.Y.', $property['value']);
					}
					break;

				case 'reservation_iznos_prve_uplate':
					if (!empty($property['value'])) {
						$updateData[ReservationEntity::PAYMENT_FIRST_INSTALLMENT_AMOUNT] = (float) $property['value'];
					}
					break;

				case 'reservation_iznos_druge_uplate':
					if (!empty($property['value'])) {
						$updateData[ReservationEntity::PAYMENT_SECOND_INSTALLMENT_AMOUNT] = (float) $property['value'];
					}
					break;
			}
		}

		if (!empty($updateData)) {
			try {
				$this->repository->update($reservation->id, $updateData);
			} catch (\Throwable $e) {
				$this->actionLog->log(
					'Database deal update',
					ActionLogDbTableEnum::RESERVATIONS,
					$reservation->id,
					'Error while updating reservation installment dates: ' . $e->getMessage(),
					'error'
				);
				$this->logger->error(
					'Failed to update reservation installment dates - ' . $reservation->deal_id,
					['exception' => $e]
				);
			}
		}
	}

	private function updateHubspotDealStage(ReservationEntity|Row $reservation): void
	{
		if ($reservation->deal_id === null) {
			return;
		}

		$deal = $this->generatedOffersDealsRepository->getBy([GeneratedOfferDealEntity::HUBSPOT_DEAL_ID => $reservation->deal_id]);

		if ($deal instanceof GeneratedOfferDealEntity && $deal->pipeline_id !== null) {
			$stageId = $this->hubspot->getDealStageIdByName(DealStagesEnum::toArray()[DealStagesEnum::STORNO], $deal->pipeline_id);

			if ($stageId === null) {
				return;
			}

			$updated = $this->hubspot->updateToStornoInHubspot($reservation->deal_id, $stageId);

			if (!$updated) {
				return;
			}

			$deal->deal_stage = DealStagesEnum::STORNO;
			$this->generatedOffersDealsRepository->update($deal->id, $deal);
		}
	}

	public function isReservationCancelled(int $id): ?bool
	{
		/** @var ReservationEntity|null $reservation */
		$reservation = $this->repository->getBy([ReservationEntity::ID => $id]);

		if ($reservation === null || $reservation->product_id === null) {
			return null;
		}

		if ($reservation->cancelled === null) {
			return false;
		}

		return $reservation->cancelled;
	}

	public function delete(int $id): ?int
	{
		/** @var ReservationEntity|null $reservation */
		$reservation = $this->repository->getBy([ReservationEntity::ID => $id]);

		if ($reservation === null || $reservation->product_id === null || $reservation->cancelled) {
			return null;
		}

		if (!empty($reservation->reservation_code) && !$this->user->isInRole('admin')) {
			return null;
		}

		if (!$this->user->isInRole('admin') && !$this->productRepository->isProductOwner($reservation->product_id, $this->user->getId())) {
			return null;
		}

		$this->updateAvailabilityStatus($reservation, AvailabilityStatusEnum::A);
		$this->repository->update($id, [ReservationEntity::CANCELLED => 1]);

		$this->actionLog->log('Reservation cancelled', ActionLogDbTableEnum::RESERVATIONS, $id, 'Reservation cancelled successfully', 'success');

		if ($this->environmentDetector->isProduction()) {
			$this->updateHubspotDealStage($reservation);
		}

		$this->syncAvailabilities($reservation);

		if (!$this->user->isInRole(RoleEnum::ADMIN)) {
			$params = $this->notificationService->getParametersWhenReservationIsDeleted($reservation->product_id, $reservation->id);
			$this->notificationService->send($params, '<EMAIL>');
		}

		if (!empty($reservation->reservation_code)) {
			$params = $this->notificationService->getParametersForOwnerWhenReservationIsDeleted($reservation->id, $reservation->reservation_code);
			$this->notificationService->send($params, null, $reservation->product_id);
		}

		return $reservation->product_id;
	}

	public function reservationHasChanges(ReservationEntity $reservation, array $values): bool
	{
		if ($reservation->arrive->format('Y-m-d') !== $values[ReservationEntity::ARRIVE] ||
			$reservation->departure->format('Y-m-d') !== $values[ReservationEntity::DEPARTURE]
		) {
			return true;
		}

		if ((int) $reservation->adults !== (int) $values[ReservationEntity::ADULTS]) {
			return true;
		}

		if ((int) $reservation->pets !== (int) $values[ReservationEntity::PETS]) {
			return true;
		}

		if ((int) $reservation->children !== (int) $values[ReservationEntity::CHILDREN]) {
			return true;
		}

		if ((int) $reservation->amount !== (int) $values[ReservationEntity::AMOUNT]) {
			return true;
		}

		if (!Strings::compare(Strings::trim($reservation->phone ?? ''), Strings::trim($values[ReservationEntity::PHONE] ?? ''))) {
			return true;
		}

		return false;
	}

	public function getProductRepository(): ProductRepository
	{
		return $this->productRepository;
	}

	public function saveReservationLog(array $values): void
	{
		$this->reservationLogRepository->insert($values);
	}

	public function getLastReservationLog(int $reservationId): ?ReservationLogEntity
	{
		return $this->reservationLogRepository->getBy(['reservation_id' => $reservationId], 'id DESC');
	}

	public function getReservationInsurancePrice(string $reservationCode): ?float
	{
		return $this->insuranceRepository->getReservationInsurancePrice($reservationCode);
	}

	private function setReservationPrices(
		ArrayHash $values,
		ReservationEntity $reservation,
		bool $changeManuallyPrice
	): void
	{
		if ($this->userHasPermissionToChangeManuallyPrice()) {
			if ($changeManuallyPrice) {
				$values[ReservationEntity::PAYMENT_SECOND_INSTALLMENT_AMOUNT] = $values[ReservationEntity::TOTAL_PRICE_EUR]
					- $values[ReservationEntity::PAYMENT_FIRST_INSTALLMENT_AMOUNT];

				return;
			}
		} else {
			unset(
				$values[ReservationEntity::TOTAL_PRICE_EUR],
				$values[ReservationEntity::AMOUNT],
				$values[ReservationEntity::PRICE_EUR],
				$values[ReservationEntity::DISTRIBUTION_PRICE],
				$values[ReservationEntity::PAYMENT_FIRST_INSTALLMENT_AMOUNT],
				$values[ReservationEntity::PAYMENT_SECOND_INSTALLMENT_AMOUNT]
			);

			return;
		}

		$reservationInsurance = $this->getReservationInsurancePrice($reservation->reservation_code);

		$values[ReservationEntity::TOTAL_PRICE_EUR] = $this->distributionChannelHelper->getTotalAmount(
			$reservation->product_distribution_channel_id,
			$values[ReservationEntity::AMOUNT],
			$reservationInsurance,
			$values[ReservationEntity::DISTRIBUTION_PRICE]
		);

		if (!$reservation->two_installments) {
			$values[ReservationEntity::PAYMENT_FIRST_INSTALLMENT_AMOUNT] = $values[ReservationEntity::TOTAL_PRICE_EUR];
			$values[ReservationEntity::PAYMENT_SECOND_INSTALLMENT_AMOUNT] = 0;

			return;
		}

		$values[ReservationEntity::PAYMENT_FIRST_INSTALLMENT_AMOUNT] = $this->distributionChannelHelper->getFirstInstallmentAmount(
			$reservation->product_id,
			$reservation->product_distribution_channel_id,
			$values[ReservationEntity::AMOUNT],
			$reservationInsurance,
			$values[ReservationEntity::DISTRIBUTION_PRICE]
		);

		$values[ReservationEntity::PAYMENT_SECOND_INSTALLMENT_AMOUNT] = $values[ReservationEntity::TOTAL_PRICE_EUR]
			- $values[ReservationEntity::PAYMENT_FIRST_INSTALLMENT_AMOUNT];
	}

	private function setReservationPricesForPartner(
		ArrayHash $values,
		ReservationEntity $reservation,
		bool $changeManuallyPriceForPartner
	): void
	{
		if ($this->userHasPermissionToChangeManuallyPrice()) {
			if ($changeManuallyPriceForPartner) {
				return;
			}
		} else {
			unset($values[ReservationEntity::PARTNER_FIRST_INSTALLMENT_PAYMENT_AMOUNT], $values[ReservationEntity::PARTNER_SECOND_INSTALLMENT_PAYMENT_AMOUNT]);

			return;
		}

		$this->partnerInstallmentsAmountResolver->resolve($reservation->id, (float) $values[ReservationEntity::AMOUNT]);
		$values[ReservationEntity::PARTNER_FIRST_INSTALLMENT_PAYMENT_AMOUNT] = $this->partnerInstallmentsAmountResolver->getFirstInstallment();
		$values[ReservationEntity::PARTNER_SECOND_INSTALLMENT_PAYMENT_AMOUNT] = $this->partnerInstallmentsAmountResolver->getSecondInstallment();
	}

	public function saveCustomerData(int $id, ArrayHash $values): void
	{
		/** @var ?ReservationEntity $reservationDb */
		$reservationDb = $this->repository->getBy([ReservationEntity::ID => $id]);

		if ($reservationDb === null) {
			return;
		}

		$reservation = new ReservationEntity();
		$reservation->contact_firstname = $values[ReservationEntity::CONTACT_FIRSTNAME];
		$reservation->contact_lastname = $values[ReservationEntity::CONTACT_LASTNAME];
		$reservation->contact_email = $values[ReservationEntity::CONTACT_EMAIL];
		$reservation->phone = $values[ReservationEntity::PHONE];
		$reservation->guest_street = $values[ReservationEntity::GUEST_STREET];
		$reservation->postcode = $values[ReservationEntity::POSTCODE];
		$reservation->guest_city = $values[ReservationEntity::GUEST_CITY];
		$reservation->contact_country_id = $values[ReservationEntity::CONTACT_COUNTRY_ID];
		$reservation->adults = $values[ReservationEntity::ADULTS];
		$reservation->children = $values[ReservationEntity::CHILDREN];
		$reservation->pets = $values[ReservationEntity::PETS];
		$this->repository->update($id, $reservation);

		$this->actionLog->log('Reservation customer data', ActionLogDbTableEnum::RESERVATIONS, $id, 'Reservation customer date updated', 'success');

		if (!$this->environmentDetector->isProduction() || empty($reservationDb->deal_id)) {
			return;
		}

		$dealProperties = $this->getCustomerDealProperties($reservation);
		$countryCode = $this->getCountryCode($reservation);
		$dealProperties[] = ['name' => 'reservation_country', 'value' => $countryCode];

		try {
			$this->hubspot->getClient()->deals()->update($reservationDb->deal_id, $dealProperties);
		} catch (\Throwable $exception) {
			$this->actionLog->log('Hubspot deal update', ActionLogDbTableEnum::RESERVATIONS, $id, 'Error while updating deal: ' . $exception->getMessage(), 'error');
			$this->logger->error('Failed to update deal - ' . $reservationDb->deal_id, ['exception' => $exception]);
		}
	}

	/**
	 * @return array<mixed>
	 */
	private function getCustomerDealProperties(ReservationEntity $reservation): array
	{
		return [
			['name' => 'reservation_first_name', 'value' => $reservation->contact_firstname],
			['name' => 'reservation_last_name', 'value' => $reservation->contact_lastname],
			['name' => 'reservation_e_mail', 'value' => $reservation->contact_email],
			['name' => 'reservation_phone_number', 'value' => $reservation->phone],
			['name' => 'reservation_adults', 'value' => $reservation->adults],
			['name' => 'reservation_children', 'value' => $reservation->children],
			['name' => 'reservation_pets', 'value' => $reservation->pets],
		];
	}

	private function getCountryCode(ReservationEntity $reservation): ?string
	{
		if (!empty($reservation->contact_country_id)) {
			$countryCode = $this->repository->getHubspotCountryId((int) $reservation->contact_country_id);

			if (empty($countryCode)) {
				$countryCode = 'HR';
			}
		} else {
			$countryCode = 'HR';
		}

		return $countryCode;
	}

	public function updateAvailabilityStatus(ReservationEntity $reservation, string $status = AvailabilityStatusEnum::X): void
	{
		$entity = new ReservationEventEntity($reservation->toArray());
		$entity->status = $status;
		$this->dispatcher->dispatch(new AvailabilityStatusChangedEvent($entity));
	}

	public function syncAvailabilities(ReservationEntity $reservation): void
	{
		if ($this->environmentDetector->isProduction()) {
			$params = new AvailabilityParams($reservation->product_id);
			$this->availabilityProducer->publish($params);
		}
	}

	private function userHasPermissionToChangeManuallyPrice(): bool
	{
		return $this->user->isInRole(RoleEnum::FINANCE);
	}

	private function getFirstInstallmentStatus(ReservationEntity $reservation, int $numberOfInstallments, string $statusFirstInstallment): string
	{
		if ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::PAID) {
			$statusFirstInstallment = $numberOfInstallments === 1 ? 'OK - sjela uplata ukupnog iznosa' : 'OK - sjela uplata akontacije';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::THE_GUEST_PAYS_DIRECTLY_TO_THE_PARTNER) {
			$statusFirstInstallment = 'Gost plaća direktno partneru';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::COPY_OF_PAYMENT_RECEIVED) {
			$statusFirstInstallment = 'Stigla kopija uplate';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::DEBITED_KK_PAY_PER_ADVANCE_PAYMENT) {
			$statusFirstInstallment = 'Terećena KK (PayPer) - akontacija';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::DEBITED_KK_PAY_PER_TOTAL_AMOUNT) {
			$statusFirstInstallment = 'Terećena KK (PayPer) - ukupni iznos';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::OK_ADVANCE_PAYMENT_INVOICE_ISSUED) {
			$statusFirstInstallment = 'OK - ispostavljen račun za predujam';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::OK_INVOICE_ISSUED_FOR_THE_TOTAL_AMOUNT) {
			$statusFirstInstallment = 'OK - ispostavljen račun za ukupan iznos';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::OK_CONFIRMATION_OF_ADVANCE_PAYMENT_SENT) {
			$statusFirstInstallment = 'OK - poslana potvrda plaćanja akontacije';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentFirstStatusEnum::OK_CONFIRMATION_OF_PAYMENT_OF_THE_TOTAL_AMOUNT_SENT) {
			$statusFirstInstallment = 'OK - poslana potvrda plaćanja ukupnog iznosa';
		}

		return $statusFirstInstallment;
	}

	private function getSecondInstallmentStatus(ReservationEntity $reservation, string $statusSecondInstallment): string
	{
		if ($reservation->second_installment_payment_status === InstallmentsPaymentSecondStatusEnum::PAID) {
			$statusSecondInstallment = 'OK - sjela uplata preostalog iznosa';
		} elseif ($reservation->second_installment_payment_status === InstallmentsPaymentSecondStatusEnum::THE_GUEST_PAYS_DIRECTLY_TO_THE_RENTER) {
			$statusSecondInstallment = 'Gost plaća direktno iznajmljivaču';
		} elseif ($reservation->second_installment_payment_status === InstallmentsPaymentSecondStatusEnum::COPY_OF_PAYMENT_RECEIVED) {
			$statusSecondInstallment = 'Stigla kopija uplate';
		} elseif ($reservation->second_installment_payment_status === InstallmentsPaymentSecondStatusEnum::DEBITED_KK_PAY_PER) {
			$statusSecondInstallment = 'Terećena KK (PayPer)';
		} elseif ($reservation->second_installment_payment_status === InstallmentsPaymentSecondStatusEnum::OK_INVOICE_ISSUED_FOR_THE_TOTAL_AMOUNT) {
			$statusSecondInstallment = 'OK - ispostavljen račun za ukupan iznos';
		} elseif ($reservation->second_installment_payment_status === InstallmentsPaymentSecondStatusEnum::OK_CONFIRMATION_OF_ADVANCE_PAYMENT_SENT) {
			$statusSecondInstallment = 'OK - poslana potvrda plaćanja akontacije';
		} elseif ($reservation->second_installment_payment_status === InstallmentsPaymentSecondStatusEnum::OK_CONFIRMATION_OF_PAYMENT_OF_THE_TOTAL_AMOUNT_SENT) {
			$statusSecondInstallment = 'OK - poslana potvrda plaćanja ukupnog iznosa';
		} elseif ($reservation->first_installment_payment_status === InstallmentsPaymentSecondStatusEnum::THE_GUEST_PAYS_DIRECTLY_TO_THE_PARTNER) {
			$statusSecondInstallment = 'Gost plaća direktno partneru';
		}

		return $statusSecondInstallment;
	}

	private function resetAgencyProvision(ArrayHash $values, ReservationEntity $reservation): void
	{
		if (!$this->userHasPermissionToChangeManuallyPrice()) {
			return;
		}

		if ($values[ReservationEntity::AMOUNT] === $reservation->amount) {
			return;
		}

		$agencyProvision = $this->agencyProvisionResolver->getAgencyProvision($reservation->id);

		if ($agencyProvision === null) {
			return;
		}

		$this->agencyProvisionResolver->resolveNewProvision($reservation->id, $values[ReservationEntity::AMOUNT], $agencyProvision->percentage);
	}

}

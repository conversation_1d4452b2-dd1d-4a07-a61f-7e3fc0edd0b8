<?php declare(strict_types = 1);

namespace App\Modules\Property\Logic;

use App\Common\Company\CompanyAddressData;
use App\Common\Enums\InvoiceFixedPartNumberEnum;
use App\Common\Enums\ProductAgencyProvisionEnum;
use App\Common\Hubspot;
use App\Common\Invoice;
use App\Common\ProductInfo\ProductNameResolver;
use App\Common\Reservation\ReservationAgencyProvisionResolver;
use App\Common\Services\ProductOwnerNotificationService;
use App\Models\Entities\custom\InvoiceDataEntity;
use App\Models\Entities\custom\MarkupInvoiceDataEntity;
use App\Models\Entities\InvoiceEntity;
use App\Models\Entities\ProductEntity;
use App\Models\Entities\ProductOwnerEntity;
use App\Models\Entities\ProductOwnerInvoiceEntity;
use App\Models\Entities\ReservationAgencyProvisionEntity;
use App\Models\Entities\ReservationEntity;
use App\Repositories\InvoiceRepository;
use App\Repositories\ProductOwnerInvoiceRepository;
use App\Repositories\ProductOwnerRepository;
use App\Repositories\ProductRepository;
use App\Repositories\ReservationAgencyProvisionRepository;
use App\Repositories\ReservationInsuranceRepository;
use App\Repositories\ReservationsRepository;
use Psr\Log\LoggerInterface;

class InvoiceLogic
{

	private ReservationsRepository $reservationsRepository;

	private CompanyAddressData $companyAddressData;

	private ProductRepository $productRepository;

	private ReservationAgencyProvisionRepository $agencyProvisionRepository;

	private ProductOwnerRepository $productOwnerRepository;

	private InvoiceRepository $invoiceRepository;

	private Invoice $invoice;

	private ReservationAgencyProvisionResolver $agencyProvisionResolver;

	private ReservationInsuranceRepository $reservationInsuranceRepository;

	private ProductOwnerNotificationService $notificationService;

	private ProductOwnerInvoiceRepository $productOwnerInvoiceRepository;

	private LoggerInterface $logger;

	private Hubspot $hubspot;

	public function __construct(
		ReservationsRepository $reservationsRepository,
		CompanyAddressData $companyAddressData,
		ProductRepository $productRepository,
		ReservationAgencyProvisionRepository $agencyProvisionRepository,
		ProductOwnerRepository $productOwnerRepository,
		InvoiceRepository $invoiceRepository,
		Invoice $invoice,
		ReservationAgencyProvisionResolver $agencyProvisionResolver,
		ReservationInsuranceRepository $reservationInsuranceRepository,
		ProductOwnerNotificationService $notificationService,
		ProductOwnerInvoiceRepository $productOwnerInvoiceRepository,
		LoggerInterface $logger,
		Hubspot $hubspot,
	)
	{
		$this->reservationsRepository = $reservationsRepository;
		$this->companyAddressData = $companyAddressData;
		$this->productRepository = $productRepository;
		$this->agencyProvisionRepository = $agencyProvisionRepository;
		$this->productOwnerRepository = $productOwnerRepository;
		$this->invoiceRepository = $invoiceRepository;
		$this->invoice = $invoice;
		$this->agencyProvisionResolver = $agencyProvisionResolver;
		$this->reservationInsuranceRepository = $reservationInsuranceRepository;
		$this->notificationService = $notificationService;
		$this->productOwnerInvoiceRepository = $productOwnerInvoiceRepository;
		$this->logger = $logger;
		$this->hubspot = $hubspot;
	}

	public function getReservationData(int $reservationId): ?ReservationEntity
	{
		/** @var ?ReservationEntity $reservation */
		$reservation = $this->reservationsRepository->getBy([ReservationEntity::ID => $reservationId]);

		if ($reservation === null) {
			return null;
		}

		return $reservation;
	}

	public function getCompanyAddressData(): array
	{
		return $this->companyAddressData->getCompanyAddressInfo();
	}

	public function getProductName(int $productId): string
	{
		$product = $this->productRepository->getProductMainInfo($productId);
		$productNameResolver = new ProductNameResolver($product->location, $product->name);

		return $productNameResolver->resolve();
	}


	/**
	 * @return ReservationAgencyProvisionEntity[]
	 */
	public function getAgencyProvision(int $reservationId): array
	{
		return $this->agencyProvisionRepository->findBy([ReservationAgencyProvisionEntity::RESERVATION_ID => $reservationId]);
	}

	public function getOwner(int $productId): ?ProductOwnerEntity
	{
		/** @var ?ProductEntity $product */
		$product = $this->productRepository->getBy([ProductEntity::ID => $productId]);

		if ($product === null) {
			return null;
		}

		if ($product->owner_id === null) {
			return null;
		}

		return $this->productOwnerRepository->getBy([ProductOwnerEntity::ID => $product->owner_id]);
	}

	public function getAgencyProvisionAmount(int $reservationId): float
	{
		$agencyProvision = $this->getAgencyProvision($reservationId);

		$totalAgencyProvisionAmount = 0;

		foreach ($agencyProvision as $provision) {
			$totalAgencyProvisionAmount += $provision->price;
		}

		return (float) $totalAgencyProvisionAmount;
	}

	public function getFixedExchangeRate(): float
	{
		return 7.53450;
	}

	public function calculateProvisionAndCreateInvoice(int $reservationId, string $type = ProductAgencyProvisionEnum::DIRECT): ?int
	{
		$agencyProvision = $this->agencyProvisionResolver->getAgencyProvision($reservationId);

		if ($agencyProvision === null) {
			$this->agencyProvisionResolver->resolve($reservationId, $type);
		}

		return $this->createInvoice($reservationId, InvoiceFixedPartNumberEnum::INVOICE_FIXED_PART);
	}

	public function createInvoice(int $reservationId, string $invoiceFixedPartNumber): ?int
	{
		$invoices = $this->getInvoices($reservationId, $invoiceFixedPartNumber);

		foreach ($invoices as $invoice) {
			if ($invoice->cancellation_id === null) {
				return null;
			}
		}

		return $this->invoice->create($reservationId, $invoiceFixedPartNumber);
	}


	public function resolveAgencyProvision(int $reservationId, string $type = ProductAgencyProvisionEnum::DIRECT): void
	{
		$this->agencyProvisionResolver->resolve($reservationId, $type);
	}

	public function getInvoiceById(int $id): ?InvoiceEntity
	{
		return $this->invoiceRepository->getBy([InvoiceEntity::ID => $id]);
	}

	public function getInvoice(int $reservationId, string $fixedPart = InvoiceFixedPartNumberEnum::INVOICE_FIXED_PART): ?InvoiceEntity
	{
		return $this->invoiceRepository->getBy([InvoiceEntity::RESERVATION_ID => $reservationId, InvoiceEntity::NUMBER_FIXED_PART => $fixedPart], 'id DESC');
	}

	/**
	 * @return InvoiceEntity[]
	 */
	public function getInvoices(int $reservationId, string $fixedPart = InvoiceFixedPartNumberEnum::INVOICE_FIXED_PART): array
	{
		return $this->invoiceRepository->findBy([InvoiceEntity::RESERVATION_ID => $reservationId, InvoiceEntity::NUMBER_FIXED_PART => $fixedPart], 'id DESC');
	}

	public function getInsurancePrice(string $reservationCode): float
	{
		return $this->reservationInsuranceRepository->getReservationInsurancePrice($reservationCode);
	}

	public function sendInvoice(ReservationEntity $reservation, float $firstInstallment): void
	{
		$invoice = $this->getInvoice($reservation->id);

		if ($invoice->sent) {
			return;
		}

		$productOwner = $this->reservationsRepository->getProductOwner($reservation->product_id);
		$params = $this->notificationService->getParametersWhenInvoiceIsCreated($reservation, $firstInstallment);
		$this->notificationService->send($params, $productOwner->email, $reservation->product_id);
		$invoice->sent = true;
		$this->invoiceRepository->update($invoice->id, $invoice);
	}

	public function getOwnerInvoiceAddress(int $ownerId, int $productId, ?int $ownerInvoiceId = null): ?ProductOwnerInvoiceEntity
	{
		if ($ownerInvoiceId !== null) {
			return $this->productOwnerInvoiceRepository->getBy([ProductOwnerInvoiceEntity::ID => $ownerInvoiceId]);
		}

		/** @var ?ProductOwnerInvoiceEntity $result */
		$result = $this->productOwnerInvoiceRepository->getBy([ProductOwnerInvoiceEntity::PRODUCT_ID => $productId], 'Id ASC');

		if ($result !== null) {
			return $result;
		}

		return $this->productOwnerInvoiceRepository->getBy([ProductOwnerInvoiceEntity::OWNER_ID => $ownerId, ProductOwnerInvoiceEntity::IS_ACTIVE => true]);
	}

	public function cancelInvoice(int $id, string $invoiceFixedPartNumber): void
	{
		$invoice = $this->getInvoiceById($id);

		if ($invoice === null) {
			return;
		}

		if ($invoice->cancellation_id !== null) {
			return;
		}

		$cancellationId = $this->invoice->create($invoice->reservation_id, $invoiceFixedPartNumber, $invoice->data);
		$this->invoiceRepository->update($id, [InvoiceEntity::CANCELLATION_ID => $cancellationId]);

		$netI = null;
		$netII = null;

		if ($invoiceFixedPartNumber === InvoiceFixedPartNumberEnum::MARKUP_CANCELLED_INVOICE_FIXED_PART) {
			$netII = 0.0;
		} elseif ($invoiceFixedPartNumber === InvoiceFixedPartNumberEnum::CANCELLED_INVOICE_FIXED_PART) {
			$netI = 0.0;
		}

		$this->updateDealNetRevenueForReservation($invoice->reservation_id, $netI, $netII);
	}

	private function updateDealNetRevenueForReservation(int $reservationId, ?float $netI = null, ?float $netII = null): void
	{
		$reservation = $this->reservationsRepository->getById($reservationId);

		if ($reservation === null) {
			$this->logger->warning('Hubspot sync total_net_revenue: reservation not found', [
				'reservation_id' => $reservationId,
			]);

			return;
		}

		$dealId = $reservation->deal_id ?? null;

		if ($dealId === null) {
			$this->logger->warning('Hubspot sync total_net_revenue: no deal ID found for reservation', [
				'reservation_id' => $reservationId,
			]);

			return;
		}

		if ($netI === null) {
			$netI = $this->getAgencyProvisionAmount($reservationId);
		}

		if ($netII === null) {
			$netII = $this->getMarkupFromInvoice($reservationId);
		}

		$netI = $netI !== null ? round($netI, 2) : null;
		$netII = $netII !== null ? round($netII, 2) : null;

		$this->hubspot->updateDealNetRevenueInHubspot($dealId, $netI, $netII);
	}


	public function saveInvoiceData(int $id, InvoiceDataEntity $invoiceData): void
	{
		$this->invoiceRepository->update($id, [InvoiceEntity::DATA => $invoiceData->toJson()]);

		$this->updateDealNetRevenueForReservation($invoiceData->invoice->reservation_id);
	}

	public function saveMarkupInvoiceData(int $id, MarkupInvoiceDataEntity $markupInvoiceData, int $reservationId): void
	{
		$this->invoiceRepository->update($id, [InvoiceEntity::DATA => $markupInvoiceData->toJson()]);

		$this->updateDealNetRevenueForReservation($reservationId);
	}

	/**
	 * @return int[]
	 */
	public function findInvoicesWithoutHistory(?int $reservationId): array
	{
		return $this->invoiceRepository->findInvoicesWithoutHistory($reservationId);
	}

	private function getMarkupFromInvoice(int $reservationId): float
	{
		$markupInvoices = $this->getInvoices(
			$reservationId,
			InvoiceFixedPartNumberEnum::MARKUP_INVOICE_FIXED_PART
		);

		$sumMarkup = 0.0;

		foreach ($markupInvoices as $inv) {
			$sumMarkup += $this->getInvoicePriceWithoutVat($inv);
		}

		$cancelledMarkupInvoices = $this->getInvoices(
			$reservationId,
			InvoiceFixedPartNumberEnum::MARKUP_CANCELLED_INVOICE_FIXED_PART
		);

		$sumCancelledMarkup = 0.0;

		foreach ($cancelledMarkupInvoices as $inv) {
			$sumCancelledMarkup += $this->getInvoicePriceWithoutVat($inv);
		}

		return $sumMarkup - $sumCancelledMarkup;
	}

	private function getInvoicePriceWithoutVat(InvoiceEntity $invoice): float
	{
		if (empty($invoice->data)) {
			return 0.0;
		}

		try {
			$decoded = json_decode($invoice->data, true, 512, JSON_THROW_ON_ERROR);
		} catch (\Throwable $e) {
			$this->logger->error('Failed to decode markup invoice, reservation id ' . $invoice->reservation_id, ['exception' => $e->getMessage()]);

			return 0.0;
		}

		if (!is_array($decoded)) {
			return 0.0;
		}

		if (isset($decoded['priceWithoutVAT']) && is_numeric($decoded['priceWithoutVAT'])) {
			return (float) $decoded['priceWithoutVAT'];
		}

		return 0.0;
	}

}

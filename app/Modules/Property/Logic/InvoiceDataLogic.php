<?php declare(strict_types = 1);

namespace App\Modules\Property\Logic;

use App\Common\Company\CompanyPhoneNumber;
use App\Common\Enums\InstallmentsPaymentStatusEnum;
use App\Common\Enums\InvoiceFixedPartNumberEnum;
use app\Common\Helpers\Strings;
use App\Common\Reservation\BookingConditionsFinder;
use App\Common\Reservation\Helpers\DistributionChannelHelper;
use App\Models\Entities\custom\InvoiceDataEntity;
use App\Models\Entities\custom\MarkupInvoiceDataEntity;
use App\Models\Entities\InvoiceEntity;
use App\Models\Entities\ProductOwnerEntity;
use App\Models\Entities\ProductOwnerInvoiceEntity;
use App\Models\Entities\ReservationAgencyProvisionEntity;
use App\Models\Entities\ReservationEntity;
use Dibi\DateTime;
use Nette\Utils\Json;
use Psr\Log\LoggerInterface;

class InvoiceDataLogic
{

	private InvoiceLogic $invoiceLogic;

	private DistributionChannelHelper $distributionChannelHelper;

	private BookingConditionsFinder $bookingConditionsFinder;

	private CompanyPhoneNumber $companyPhoneNumber;

	private LoggerInterface $logger;

	public function __construct(
		InvoiceLogic $invoiceLogic,
		DistributionChannelHelper $distributionChannelHelper,
		BookingConditionsFinder $bookingConditionsFinder,
		CompanyPhoneNumber $companyPhoneNumber,
		LoggerInterface $logger
	)
	{
		$this->invoiceLogic = $invoiceLogic;
		$this->distributionChannelHelper = $distributionChannelHelper;
		$this->bookingConditionsFinder = $bookingConditionsFinder;
		$this->companyPhoneNumber = $companyPhoneNumber;
		$this->logger = $logger;
	}

	public function generateInvoiceData(int $invoiceId): void
	{
		$invoiceData = $this->getInvoiceData(0, false, false, $invoiceId);

		if ($invoiceData === null) {
			return;
		}

		$this->invoiceLogic->saveInvoiceData($invoiceId, $invoiceData);
	}

	public function updateInvoiceHistory(?int $reservationId = null): void
	{
		$invoiceIds = $this->invoiceLogic->findInvoicesWithoutHistory($reservationId);

		if (!empty($invoiceIds)) {
			$this->logger->info('invoice:history - invoices without data found', [
				'reservation_id' => $reservationId,
				'invoice_ids' => array_keys($invoiceIds),
				'count' => count($invoiceIds),
			]);
		}

		foreach ($invoiceIds as $invoiceId) {
			$this->generateInvoiceData($invoiceId);
		}
	}

	public function saveInvoiceData(int $invoiceId, InvoiceDataEntity $invoiceData): void
	{
		$this->invoiceLogic->saveInvoiceData($invoiceId, $invoiceData);
	}

	public function saveMarkupInvoiceData(int $invoiceId, MarkupInvoiceDataEntity $markupInvoiceData, int $reservationId): void
	{
		$this->invoiceLogic->saveMarkupInvoiceData($invoiceId, $markupInvoiceData, $reservationId);
	}

	public function getInvoiceData(int $reservationId, bool $print = false, bool $cancelled = false, ?int $invoiceId = null): ?InvoiceDataEntity
	{
		$invoiceData = new InvoiceDataEntity();

		if ($invoiceId !== null) {
			$invoiceData->invoice = $invoice = $this->invoiceLogic->getInvoiceById($invoiceId);
		} else {
			$type = !$cancelled ? InvoiceFixedPartNumberEnum::INVOICE_FIXED_PART : InvoiceFixedPartNumberEnum::CANCELLED_INVOICE_FIXED_PART;
			$invoiceData->invoice = $invoice = $this->invoiceLogic->getInvoice($reservationId, $type);
		}

		if ($invoice === null) {
			return null;
		}

		if ($invoice->data !== null) {
			$data = $this->fromJson($invoice->data);
			$data->print = $print;

			if ($invoice->number_fixed_part === InvoiceFixedPartNumberEnum::INVOICE_FIXED_PART) {
				return $data;
			}

			$data->invoiceNumber = $invoice->number_numeric_part . $invoice->number_fixed_part;
			$data->isCancelledInvoice = true;
			$data->invoiceDueDate = $invoice->created;
			$data->invoice = $invoice;

			return $data;
		}

		$invoiceData->invoiceNumber = $invoice->number_numeric_part . $invoice->number_fixed_part;
		$invoiceData->invoiceData = $reservationData = $this->invoiceLogic->getReservationData($invoice->reservation_id);

		if ($reservationData === null) {
			return null;
		}

		$invoiceData->firstInstallmentPaid = $reservationData->first_installment_payment_status !== InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		$invoiceData->secondInstallmentPaid = $reservationData->second_installment_payment_status !== InstallmentsPaymentStatusEnum::WAITING_PAYMENT;

		$insurancePrice = 0;

		if ($reservationData->reservation_code !== null) {
			$insurancePrice = $this->invoiceLogic->getInsurancePrice($reservationData->reservation_code);
		}
		$firstInstallmentAmount = $this->distributionChannelHelper->getInstallmentAmount(
			$reservationData->product_id,
			$reservationData->amount,
			$reservationData->two_installments,
			$insurancePrice
		);
		$invoiceData->firstInstallmentAmount = $firstInstallmentAmount - $insurancePrice;

		//fake amount for owner - contract
		if (!$reservationData->two_installments) {
			$percentages = $this->bookingConditionsFinder->getAmountPercentages($reservationData->product_id);
			$invoiceData->firstInstallmentAmount = $invoiceData->firstInstallmentAmount * ($percentages->first_installment_percentage / 100);
		}

		$due = clone $invoice->created;
		$invoiceData->invoiceDueDate = $due->modify('+15 days');
		$invoiceData->print = $print;

		$invoiceData->company = $this->invoiceLogic->getCompanyAddressData();
		$invoiceData->productName = $this->invoiceLogic->getProductName($reservationData->product_id);
		$agencyProvision = $this->invoiceLogic->getAgencyProvision($invoice->reservation_id);
		$invoiceData->agencyProvisionData = $agencyProvision;

		$totalAgencyProvisionAmount = 0;

		foreach ($agencyProvision as $provision) {
			$totalAgencyProvisionAmount += $provision->price;
		}
		$invoiceData->totalAgencyProvisionAmount = $totalAgencyProvisionAmount;

		$owner = $this->invoiceLogic->getOwner($reservationData->product_id);
		$invoiceData->isOwnerForeigner = Strings::contains($owner->ssn, 'IT');
		$invoiceData->owner = $owner;
		$invoiceData->ownerInvoice = $this->invoiceLogic->getOwnerInvoiceAddress(
			$owner->id,
			$reservationData->product_id,
			$invoice->product_owner_invoice_id
		);
		$invoiceData->fixedRate = $this->invoiceLogic->getFixedExchangeRate();
		$invoiceData->companyPhone = $this->companyPhoneNumber->get('hr');
		$invoiceData->isCancelledInvoice = $invoice->number_fixed_part === InvoiceFixedPartNumberEnum::CANCELLED_INVOICE_FIXED_PART;

		if ($invoice->number_fixed_part === InvoiceFixedPartNumberEnum::INVOICE_FIXED_PART) {
			$this->saveInvoiceData($invoiceData->invoice->id, $invoiceData);
		}

		$invoiceData->policyId = $this->bookingConditionsFinder->getPolicy($reservationData->product_id)?->id;

		return $invoiceData;
	}

	public function getMarkupInvoiceData(
		int $reservationId,
		bool $print = false,
		bool $cancelled = false,
		?int $invoiceId = null
	): ?MarkupInvoiceDataEntity
	{
		$markupInvoiceData = new MarkupInvoiceDataEntity();

		if ($invoiceId !== null) {
			$invoice = $this->invoiceLogic->getInvoiceById($invoiceId);
		} else {
			$type = !$cancelled ? InvoiceFixedPartNumberEnum::MARKUP_INVOICE_FIXED_PART : InvoiceFixedPartNumberEnum::MARKUP_CANCELLED_INVOICE_FIXED_PART;
			$invoice = $this->invoiceLogic->getInvoice($reservationId, $type);
		}

		if ($invoice === null) {
			return null;
		}

		if ($invoice->data !== null) {
			$data = $this->getMarkupInvoiceDataFromJson($invoice->data);
			$data->print = $print;

			if ($invoice->number_fixed_part === InvoiceFixedPartNumberEnum::MARKUP_INVOICE_FIXED_PART) {
				return $data;
			}

			$data->invoiceNumber = $invoice->number_numeric_part . $invoice->number_fixed_part;
			$data->isCancelledInvoice = true;
			$data->invoiceCreated = clone $invoice->created;
			$data->invoiceDueDate = clone $invoice->created;

			return $data;
		}

		$reservationData = $this->invoiceLogic->getReservationData($reservationId);
		$markupInvoiceData->reservationCode = $reservationData?->reservation_code;
		$markupInvoiceData->invoiceNumber = $invoice->number_numeric_part . $invoice->number_fixed_part;
		$markupInvoiceData->invoiceCreated = clone $invoice->created;
		$markupInvoiceData->invoiceDueDate = clone $invoice->created;
		$markupInvoiceData->print = $print;
		$markupInvoiceData->company = $this->invoiceLogic->getCompanyAddressData();
		$markupInvoiceData->productName = $this->invoiceLogic->getProductName($reservationData->product_id);
		$markupInvoiceData->invoiceContactName = $reservationData?->contact_firstname . ' ' . $reservationData?->contact_lastname;
		$insuranceAmount = $reservationData->reservation_code !== null
			? $this->invoiceLogic->getInsurancePrice($reservationData->reservation_code)
			: 0;

		$markupInvoiceData->price = $reservationData->distribution_price !== null && $reservationData->amount !== null
			? $reservationData->distribution_price - $reservationData->amount - $insuranceAmount
			: null;
		$markupInvoiceData->priceWithoutVAT = $markupInvoiceData->price !== null ? $markupInvoiceData->price / 1.25 : null;
		$markupInvoiceData->VAT = $markupInvoiceData->price !== null ? ($markupInvoiceData->price - $markupInvoiceData->priceWithoutVAT) : null;
		$markupInvoiceData->isCancelledInvoice = $invoice->number_fixed_part === InvoiceFixedPartNumberEnum::MARKUP_CANCELLED_INVOICE_FIXED_PART;

		if ($invoice->number_fixed_part === InvoiceFixedPartNumberEnum::MARKUP_INVOICE_FIXED_PART) {
			$this->saveMarkupInvoiceData($invoice->id, $markupInvoiceData, $reservationId);
		}

		return $markupInvoiceData;
	}

	private function fromJson(string $json): InvoiceDataEntity
	{
		$arr = Json::decode($json, Json::FORCE_ARRAY);

		$data = new InvoiceDataEntity();
		$data->invoice = InvoiceEntity::fromArray($arr['invoice']);
		$data->invoiceData = ReservationEntity::fromArray($arr['invoiceData']);
		$data->invoiceNumber = $arr['invoiceNumber'];
		$data->firstInstallmentPaid = $arr['firstInstallmentPaid'];
		$data->secondInstallmentPaid = $arr['secondInstallmentPaid'];
		$data->firstInstallmentAmount = $arr['firstInstallmentAmount'];
		$data->invoiceDueDate = new DateTime($arr['invoiceDueDate']['date']);
		$data->print = $arr['print'];
		$data->company = $arr['company'];
		$data->productName = $arr['productName'];

		foreach ($arr['agencyProvisionData'] as $provision) {
			$data->agencyProvisionData[] = ReservationAgencyProvisionEntity::fromArray($provision);
		}

		$data->totalAgencyProvisionAmount = $arr['totalAgencyProvisionAmount'];
		$data->isOwnerForeigner = $arr['isOwnerForeigner'];
		$data->owner = !empty($arr['owner']) ? ProductOwnerEntity::fromArray($arr['owner']) : null;
		$data->ownerInvoice = !empty($arr['ownerInvoice']) ? ProductOwnerInvoiceEntity::fromArray($arr['ownerInvoice']) : null;
		$data->fixedRate = $arr['fixedRate'];
		$data->companyPhone = $arr['companyPhone'];
		$data->isCancelledInvoice = $arr['isCancelledInvoice'];

		return $data;
	}

	private function getMarkupInvoiceDataFromJson(string $json): MarkupInvoiceDataEntity
	{
		$arr = Json::decode($json, Json::FORCE_ARRAY);

		$data = new MarkupInvoiceDataEntity();
		$data->reservationCode = $arr['reservationCode'];
		$data->invoiceNumber = $arr['invoiceNumber'];
		$data->invoiceCreated = new DateTime($arr['invoiceCreated']['date']);
		$data->invoiceDueDate = new DateTime($arr['invoiceDueDate']['date']);
		$data->print = $arr['print'];
		$data->company = $arr['company'];
		$data->productName = $arr['productName'];
		$data->invoiceContactName = $arr['invoiceContactName'] ?? '';
		$data->priceWithoutVAT = $arr['priceWithoutVAT'];
		$data->VAT = $arr['VAT'];
		$data->price = $arr['price'];
		$data->isCancelledInvoice = $arr['isCancelledInvoice'];

		return $data;
	}

	public function getPolicyId(int $reservationId): ?int
	{
		$reservation = $this->invoiceLogic->getReservationData($reservationId);

		return $this->bookingConditionsFinder->getPolicy($reservation->product_id)?->id;
	}

}

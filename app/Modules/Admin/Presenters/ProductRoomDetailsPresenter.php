<?php declare(strict_types = 1);

namespace App\Modules\Admin\Presenters;

use App\Models\Entities\ProductRoomFacilityEntity;
use App\Models\Entities\ProductRoomObjectEntity;
use App\Modules\Admin\Factories\IEditProductRoomFacilityFormFactory;
use App\Modules\Admin\Factories\IEditProductRoomObjectFormFactory;
use App\Modules\Admin\Factories\IRoomDetailsControlFactory;
use App\Modules\Admin\Forms\EditProductRoomFacilityForm;
use App\Modules\Admin\Forms\EditProductRoomObjectForm;

class ProductRoomDetailsPresenter extends BasePresenter
{

	private IEditProductRoomFacilityFormFactory $editProductRoomFacilityFormFactory;

	private IEditProductRoomObjectFormFactory $editProductRoomObjectFormFactory;

	public ?int $productId = 0;

	public ?int $roomId = 0;

	private IRoomDetailsControlFactory $roomDetailsControlFactory;

	public function __construct(
		IEditProductRoomFacilityFormFactory $editProductRoomFacilityFormFactory,
		IEditProductRoomObjectFormFactory $editProductRoomObjectFormFactory,
		IRoomDetailsControlFactory $roomDetailsControlFactory
	)
	{
		parent::__construct();
		$this->editProductRoomFacilityFormFactory = $editProductRoomFacilityFormFactory;
		$this->editProductRoomObjectFormFactory = $editProductRoomObjectFormFactory;
		$this->roomDetailsControlFactory = $roomDetailsControlFactory;
	}


	public function startup(): void
	{
		parent::startup();
		$this->productId = (int) $this->getParameter('productId');
		$this->roomId = (int) $this->getParameter('roomId');
	}

	public function renderRoomDetails(int $productId, int $roomId)
	{
		$this->redrawContent();
		$this->setView('default');
	}

	public function actionNewRoomObject(int $productId, int $roomId)
	{
		$this->redrawContent();
		$this->setView('newRoomObject');
	}

	public function actionNewRoomFacility(int $productId, int $roomId)
	{
		$this->redrawContent();
		$this->setView('newRoomFacility');
	}

	protected function createComponentRoomFacilityForm(): EditProductRoomFacilityForm
	{
		$form = $this->editProductRoomFacilityFormFactory->create($this->productId, $this->roomId);
		$form->onInsert[] = function (ProductRoomFacilityEntity $entity) {
			$this->redirect('RoomDetails', $entity->product_id, $entity->product_room_type_id);
		};

		return $form;
	}

	protected function createComponentRoomObjectForm(): EditProductRoomObjectForm
	{
		$form = $this->editProductRoomObjectFormFactory->create($this->productId, $this->roomId);
		$form->onInsert[] = function (ProductRoomObjectEntity $entity) {
			$this->redirect('RoomDetails', $entity->product_id, $entity->product_room_type_id);
		};
		$form->addHidden(ProductRoomObjectEntity::PRODUCT_ID, $this->productId);
		$form->addHidden(ProductRoomObjectEntity::PRODUCT_ROOM_TYPE_ID, $this->roomId);

		return $form;
	}


	protected function createComponentRoomDetailsControl()
	{
		return $this->roomDetailsControlFactory->create($this->productId, $this->roomId);
	}

}

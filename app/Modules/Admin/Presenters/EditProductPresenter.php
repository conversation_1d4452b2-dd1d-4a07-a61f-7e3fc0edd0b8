<?php declare(strict_types = 1);

namespace App\Modules\Admin\Presenters;

use App\Common\Forms\BaseForm;
use App\Common\TextGenerator\MissingHeadingTextsGenerator;
use App\Models\Entities\ProductEntity;
use App\Modules\Admin\Factories\IEditBasicInfoFormFactory;
use App\Modules\Admin\Factories\IEditOfferGridFactory;
use App\Modules\Admin\Factories\IEditOwnerServicesGridFactory;
use App\Modules\Admin\Factories\IEditPricesGridFactory;
use App\Modules\Admin\Factories\IEditProductFeatureFormFactory;
use App\Modules\Admin\Factories\IEditProductFeaturesGridFactory;
use App\Modules\Admin\Factories\IEditProductRoomFormFactory;
use App\Modules\Admin\Factories\IEditProductRoomGridFactory;
use App\Modules\Admin\Factories\IEditTextFormFactory;
use App\Modules\Admin\Factories\IEditVpiFormFactory;
use App\Modules\Admin\Factories\IEditVprFormFactory;
use App\Modules\Admin\Factories\IImageDetailsListFactory;
use App\Modules\Admin\Factories\INewFeatureFormFactory;
use App\Modules\Admin\Factories\INewServiceFormFactory;
use App\Modules\Admin\Forms\EditProductFeatureForm;
use App\Modules\Admin\Forms\EditProductRoomForm;
use App\Modules\Admin\Forms\EditProductTextForm;
use App\Modules\Admin\Forms\NewFeatureForm;
use App\Modules\Admin\Forms\NewServiceForm;
use App\Modules\Admin\Forms\VpiForm;
use App\Modules\Admin\Forms\VprForm;
use App\Modules\Admin\Grids\EditFeaturesGrid;
use App\Modules\Admin\Grids\EditOffersGrid;
use App\Modules\Admin\Grids\EditOwnerServicesGrid;
use App\Modules\Admin\Grids\EditPricesGrid;
use App\Modules\Admin\Grids\EditRoomsGrid;
use App\Modules\Admin\Grids\ImageDetailsListGrid;
use App\Modules\Property\Logic\DashboardSyncLogic;
use App\Repositories\ProductRepository;

class EditProductPresenter extends BaseEditProductPresenter
{

	public string $message = 'Item saved!';

	private IEditBasicInfoFormFactory $basicInfoFormFactory;

	private IEditProductFeaturesGridFactory $editProductFeaturesGridFactory;

	private IEditProductRoomFormFactory $editProductRoomFormFactory;

	private IEditProductRoomGridFactory $editProductRoomGridFactory;

	private IEditPricesGridFactory $editPricesGridFactory;

	private IEditOwnerServicesGridFactory $editOwnerServicesGridFactory;

	private IEditOfferGridFactory $editOfferGridFactory;

	private IEditTextFormFactory $editTextFormFactory;

	private IEditVpiFormFactory $editVpiFormFactory;

	private IEditVprFormFactory $editVprFormFactory;

	private IEditProductFeatureFormFactory $editProductFeatureFormFactory;

	protected int $productId;

	private INewFeatureFormFactory $newFeatureFormFactory;

	private ProductRepository $productRepository;

	private INewServiceFormFactory $newServiceFormFactory;

	private MissingHeadingTextsGenerator $missingHeadingTextsGenerator;

	private IImageDetailsListFactory $imageDetailsListFactory;

	private DashboardSyncLogic $dashboardSyncLogic;

	public function __construct(
		IEditBasicInfoFormFactory $basicInfoFormFactory,
		IEditProductFeaturesGridFactory $editProductFeaturesGridFactory,
		IEditProductRoomFormFactory $editProductRoomFormFactory,
		IEditProductRoomGridFactory $editProductRoomGridFactory,
		IEditPricesGridFactory $editPricesGridFactory,
		IEditOwnerServicesGridFactory $editOwnerServicesGridFactory,
		IEditOfferGridFactory $editOfferGridFactory,
		IEditTextFormFactory $editTextFormFactory,
		IEditVpiFormFactory $editVpiFormFactory,
		IEditVprFormFactory $editVprFormFactory,
		IEditProductFeatureFormFactory $editProductFeatureFormFactory,
		INewFeatureFormFactory $newFeatureFormFactory,
		ProductRepository $productRepository,
		INewServiceFormFactory $newServiceFormFactory,
		MissingHeadingTextsGenerator $missingHeadingTextsGenerator,
		IImageDetailsListFactory $imageDetailsListFactory,
		DashboardSyncLogic $dashboardSyncLogic
	)
	{
		parent::__construct();
		$this->basicInfoFormFactory = $basicInfoFormFactory;
		$this->editProductFeaturesGridFactory = $editProductFeaturesGridFactory;
		$this->editProductRoomFormFactory = $editProductRoomFormFactory;
		$this->editProductRoomGridFactory = $editProductRoomGridFactory;
		$this->editPricesGridFactory = $editPricesGridFactory;
		$this->editOwnerServicesGridFactory = $editOwnerServicesGridFactory;
		$this->editOfferGridFactory = $editOfferGridFactory;
		$this->editTextFormFactory = $editTextFormFactory;
		$this->editVpiFormFactory = $editVpiFormFactory;
		$this->editVprFormFactory = $editVprFormFactory;
		$this->editProductFeatureFormFactory = $editProductFeatureFormFactory;
		$this->newFeatureFormFactory = $newFeatureFormFactory;
		$this->productRepository = $productRepository;
		$this->newServiceFormFactory = $newServiceFormFactory;
		$this->missingHeadingTextsGenerator = $missingHeadingTextsGenerator;
		$this->imageDetailsListFactory = $imageDetailsListFactory;
		$this->dashboardSyncLogic = $dashboardSyncLogic;
	}


	protected function startup(): void
	{
		parent::startup();
		$this->redrawContent();
		$this->setView('default');
		$this->productId = (int) $this->getParameter('id');
	}

	public function actionSyncWithOwnerDash($id): void
	{
		$this->dashboardSyncLogic->syncProduct((int) $id);
		$this->redirect('BasicInfo', $id);
	}

	public function actionBasicInfo($id): void
	{
		$this->template->product_id = $id;
		$this->template->product = $this->productRepository->getBy([ProductEntity::ID => (int) $id]);
		$this->redrawContent();
	}

	public function actionFeatures($id): void
	{
		$this->redrawContent();
		$this->setView('features');
	}


	public function actionRooms($id): void
	{
		$this->template->id = $id;
		$this->redrawContent();
		$this->setView('rooms');
	}


	public function actionServicesAndOffers($id): void
	{
		$this->template->product_id = $id;
		$this->redrawContent();
		$this->setView('servicesAndOffers');
	}


	public function actionPrices($id): void
	{
		$this->redrawContent();
		$this->setView('prices');
	}

	public function actionImageDetailList($id): void
	{
		$this->redrawContent();
		$this->setView('imageDetailsList');
	}

	public function actionText($id): void
	{
		$this->template->productId = (int) $id;
		$this->redrawContent();
		$this->setView('text');
	}


	public function actionVpi($id): void
	{
		$this->redrawContent();
		$this->setView('vpi');
	}


	public function actionVpr($id): void
	{
		$this->redrawContent();
		$this->setView('vpr');
	}

	protected function createComponentForm(): BaseForm
	{
		$form = $this->basicInfoFormFactory->create();
		$form->onUpdate[] = function () {
			$this->flashMessage($this->message, 'success');
			//redraw is not working as expected (e.g. swimming pool container )
			$this->redirect('BasicInfo', $this->productId);
		};

		return $form;
	}

	protected function createComponentImageDetailsList(): ImageDetailsListGrid
	{
		$url = $this->getHttpRequest()->getUrl();
		$domain = $url->getScheme() . '://' . $url->getHost();

		return $this->imageDetailsListFactory->create($domain);
	}

	protected function createComponentFeaturesGrid(): EditFeaturesGrid
	{
		return $this->editProductFeaturesGridFactory->create();
	}

	protected function createComponentRoomForm(): EditProductRoomForm
	{
		$form = $this->editProductRoomFormFactory->create();
		$form->onInsert[] = function (int $id) {
			$this->redirect('Rooms', $id);
		};

		return $form;
	}


	public function actionNewRoom(int $id): void
	{
		$this->setView('newRoom');
		$this->redrawContent();
	}

	protected function createComponentRoomsGrid(): EditRoomsGrid
	{
		return $this->editProductRoomGridFactory->create();
	}


	protected function createComponentPricesGrid(): EditPricesGrid
	{
		return $this->editPricesGridFactory->create();
	}

	protected function createComponentOwnerServicesGrid(): EditOwnerServicesGrid
	{
		return $this->editOwnerServicesGridFactory->create();
	}

	protected function createComponentOffersGrid(): EditOffersGrid
	{
		return $this->editOfferGridFactory->create();
	}

	protected function createComponentTextForm(): EditProductTextForm
	{
		$form = $this->editTextFormFactory->create();

		$form->onInsert[] = function (int $productId) {
			$this->flashMessage('Success', 'success');
			$this->redirect('text', $productId);
		};

		return $form;
	}

	protected function createComponentVpiForm(): VpiForm
	{
		return $this->editVpiFormFactory->create();
	}

	protected function createComponentVprForm(): VprForm
	{
		return $this->editVprFormFactory->create();
	}

	protected function createComponentFeatureForm(): EditProductFeatureForm
	{
		$form = $this->editProductFeatureFormFactory->create((int) $this->getParameter('id'), NULL, NULL);
		$form->onInsert[] = function () {
			$this->flashMessage('Saved!', 'success');
		};

		return $form;
	}


	protected function createComponentNewFeatureForm(): NewFeatureForm
	{
		return $this->newFeatureFormFactory->create();
	}

	public function actionNewFeature(): void
	{
		$this->setView('new_feature');
		$this->redrawContent();
	}

	protected function createComponentNewServiceForm(): NewServiceForm
	{
		return $this->newServiceFormFactory->create();
	}

	public function actionNewService($id): void
	{
		$this->setView('new_service');
		$this->redrawContent();
	}

	public function actionGenerateTexts(int $productId): void
	{
		$this->missingHeadingTextsGenerator->generateSingle($productId);
		$this->flashMessage('Success!', 'success');
		$this->redirect('EditProduct:text', $productId);
	}

}

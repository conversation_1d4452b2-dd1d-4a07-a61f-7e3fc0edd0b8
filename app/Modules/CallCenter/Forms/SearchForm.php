<?php declare(strict_types = 1);

namespace App\Modules\CallCenter\Forms;

use App\Common\Enums\CcAppliedFiltersEnum;
use App\Common\Enums\LanguageEnum;
use App\Common\Enums\PartnersEnum;
use App\Common\Enums\SwimmingPoolDenormalizedIdsEnum;
use App\Common\Enums\VPI\VpiEnum;
use App\Common\Enums\vSales\FilterGroups\BasicFilterGroupEnum;
use App\Common\Enums\vSales\FilterGroups\KitchenFilterGroupEnum;
use App\Common\Enums\vSales\FilterGroups\OtherFilterGroupEnum;
use App\Common\Enums\vSales\FilterGroups\ParticularitiesFilterEnum;
use App\Common\Enums\YesNoEnum;
use App\Common\Forms\BaseForm;
use App\Models\Entities\ProductEntity;
use App\Modules\CallCenter\Helpers\SalesSearchHelper;
use App\Modules\CallCenter\Logic\SearchFormLogic;
use App\Repositories\ProductRepository;
use Dibi\DateTime;
use Nette\Application\UI\Form;
use Nette\ComponentModel\IComponent;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Localization\ITranslator;

class SearchForm extends BaseForm
{

	/**
	 * @var SessionSection
	 */
	private $sessionSection;

	/**
	 * @var ProductRepository
	 */
	protected $repository;

	/**
	 * @var Session
	 */
	private $session;

	/**
	 * @var ITranslator
	 */
	private $translator;

	/**
	 * @var callable
	 */
	public $onSearch;

	/**
	 * @var callable
	 */
	public $onReset;

	/**
	 * @var SalesSearchHelper
	 */
	private $searchHelper;

	private SearchFormLogic $searchFormLogic;

	public function __construct(
		Session $session,
		ProductRepository $repository,
		ITranslator $translator,
		SalesSearchHelper $searchHelper,
		SearchFormLogic $searchFormLogic
	)
	{
		$this->repository = $repository;
		$this->session = $session;
		$this->translator = clone $translator;
		$this->searchHelper = $searchHelper;
		$this->searchFormLogic = $searchFormLogic;
		parent::__construct();
	}


	public function attached(IComponent $presenter): void
	{
		parent::attached($presenter);
		unset($this['submit']);
	}


	public function prepare(): void
	{
		$this->setMethod('get');
		$this->prepareClient();
		$this->prepareSearchFilter();
	}


	private function prepareClient(): void
	{
		$search_id = $this->searchHelper->generateUniqueSessionId($this->getPresenter()->getParameter('search_id'));
		$this->sessionSection = $this->session->getSection((string) $search_id);

		$this->addButton('link', 'CRM HS profil');
		$this->addHidden('email');
		$this->addText('firstname', 'Ime');
		$this->addText('lastname', 'Prezime');
		$this->addBootstrapDate('arrive', 'Dolazak')->setHtmlAttribute('autocomplete', 'off');
		$this->addBootstrapDate('departure', 'Odlazak')->setHtmlAttribute('autocomplete', 'off');
		$this->addText('nights_stay');
		$this->addText('adultsCount', $this->getTranslator()->translate('Odrasli'));
		$this->addText('childrenCount', $this->getTranslator()->translate('Djeca'));
		$this->addText('petsCount', $this->getTranslator()->translate('Psi'));
		$this->addSelect('contact_language', 'Jezik', LanguageEnum::toArray())->setPrompt('Odaberite jezik');
		$this->addTextArea('text', 'Tekst/napomena');
		$this->addCheckbox('published', 'Samo objavljene kuće');
		$this->addCheckbox('fencedProperty', 'Ograđeno dvorište');

		if (empty($this->sessionSection['values'])) {
			return;
		}
		$language = $this->getPresenter()->getParameter('contact_language');
		$defaultValues = (array) $this->sessionSection['values'];

		if (!empty($language)) {
			unset($defaultValues['contact_language']);
			$this->getComponent('contact_language')->setValue($language);
		}
		$this->setDefaults($defaultValues);
	}


	public function prepareSearchFilter(): void
	{
		$this->addSelect(ProductEntity::PARTNER, 'Partner', PartnersEnum::toArray())->setPrompt('Partner');
		$quality = [1 => 1, 2, 3, 4, 5];
		$areas = $this->searchFormLogic->getAreas();
		$this->addDynamicMultiSelect(CcAppliedFiltersEnum::COUNTRY, $this->getTranslator()->translate('Država'), $this->searchFormLogic->getCountries())
			->setTranslator($this->translator)
			->setDefaultValue(191)
			->getControlPrototype()
			->class('selectpicker')->multiple(true)
			->tags(TRUE);
		$this->addDynamicMultiSelect(CcAppliedFiltersEnum::AREAS, $this->getTranslator()->translate('Regije'), $areas)
			->setTranslator($this->translator)
			->getControlPrototype()
			->class('selectpicker')->multiple(true)
			->tags(TRUE);
		$this->addDynamicMultiSelect(CcAppliedFiltersEnum::PLACES, $this->getTranslator()->translate('Mjesta'), $this->searchFormLogic->getSubAreas())
			->setTranslator($this->translator)
			->getControlPrototype()
			->class('selectpicker')->multiple(true)
			->tags(TRUE);
		$this->addText(CcAppliedFiltersEnum::CAPACITY, 'Kapacitet')->setDefaultValue(1)
			->addRule(Form::INTEGER, 'Odabrano polje mora biti brojčana vrijednost!');

		$propertyIds = $this->getPresenter()->getParameter('property_id');
		$defaultPropertyIds = [];

		if (!empty($propertyIds)) {
			foreach ($propertyIds as $propertyId) {
				$defaultPropertyIds[$propertyId] = $propertyId;
			}
		}

		$this->addDynamicMultiSelect(CcAppliedFiltersEnum::PROPERTY_ID, 'Property ID')
			->setValue($defaultPropertyIds)
			->setTranslator(NULL)
			->getControlPrototype()
			->multiple(TRUE)
			->tags(TRUE);

		$traumIds = $this->getPresenter()->getParameter('traum_id');
		$defaultTraumIds = [];

		if (!empty($traumIds)) {
			foreach ($traumIds as $propertyId) {
				$defaultTraumIds[$propertyId] = $propertyId;
			}
		}

		$this->addDynamicMultiSelect(CcAppliedFiltersEnum::TRAUM_ID, 'Traum ID')
			->setValue($defaultTraumIds)
			->setTranslator(NULL)
			->getControlPrototype()
			->multiple(TRUE)
			->tags(TRUE);

		$this->addSelect(CcAppliedFiltersEnum::PETS, 'Kućni ljubimci', YesNoEnum::toArray())->setPrompt('Kućni ljubimci');
		$this->addDynamicMultiSelect(CcAppliedFiltersEnum::QUALITY, 'Kvaliteta', $quality)
			->setTranslator(NULL)
			->getControlPrototype()
			->class('selectpicker')
			->multiple(TRUE)
			->tags(TRUE);
		$this->addSelect('all_inclusive', 'All inclusive', YesNoEnum::toArray())->setPrompt('All inclusive');
		$this->addText(CcAppliedFiltersEnum::SLEEPING_ROOMS, 'Broj spavaćih soba')->setDefaultValue(0)
			->addRule(Form::INTEGER, 'Odabrano polje mora biti brojčana vrijednost!');
		$this->addText(CcAppliedFiltersEnum::BATHROOMS, 'Broj kupaonica')->setDefaultValue(0)
			->addRule(Form::INTEGER, 'Odabrano polje mora biti brojčana vrijednost!');
		$this->addText(CcAppliedFiltersEnum::DOUBLE_BEDS_NUMBER, 'Broj bračnih kreveta')->setDefaultValue(0)
			->addRule(Form::INTEGER, 'Odabrano polje mora biti brojčana vrijednost!');
		$this->addText(CcAppliedFiltersEnum::SINGLE_BEDS_NUMBER, 'Broj jednoležajnih kreveta')->setDefaultValue(0)
			->addRule(Form::INTEGER, 'Odabrano polje mora biti brojčana vrijednost!');
		$this->addText(CcAppliedFiltersEnum::PARKING_PLACE_COUNT, 'Broj parkirnih mjesta')->setDefaultValue(0)
			->addRule(Form::INTEGER, 'Odabrano polje mora biti brojčana vrijednost!');

		$this->addDynamicMultiSelect(CcAppliedFiltersEnum::SWIMMING_POOL, 'Bazen', SwimmingPoolDenormalizedIdsEnum::toTranslatedArray($this->translator))
			->getControlPrototype()
			->class('selectpicker')
			->multiple(TRUE)
			->tags(TRUE);
		$this->addSelect(CcAppliedFiltersEnum::SMOKING, 'Dozvoljeno pušenje', YesNoEnum::toArray())->setPrompt('Dozvoljeno pušenje')->setDefaultValue(0);

		$this->addText('house_unit_size_from');
		$this->addText('house_unit_size_to');

		$this->addText('price_from');
		$this->addText('price_to');

		$this->addText('capacity_from');
		$this->addText('capacity_to');

		$this->addText('build_year_from');
		$this->addText('build_year_to');

		$this->addText('modernized_year_from');
		$this->addText('modernized_year_to');

		$this->addText('swimming_pool_unit_size_from');
		$this->addText('swimming_pool_unit_size_to');

		$this->addText('parcel_unit_size_from');
		$this->addText('parcel_unit_size_to');

		$this->addText('town_distance_from');
		$this->addText('town_distance_to');

		$this->addText('neighbour_distance_from');
		$this->addText('neighbour_distance_to');

		$this->addText('airport_distance_from');
		$this->addText('airport_distance_to');

		$this->addText('sea_distance_from');
		$this->addText('sea_distance_to');

		$this->addDynamicMultiSelect('basic', 'Osnovni sadržaji', BasicFilterGroupEnum::toArray());
		$this->addDynamicMultiSelect('particularities', 'Osobitosti', ParticularitiesFilterEnum::toArray());
		$this->addDynamicMultiSelect('kitchen', 'Sadržaji kuhinje', KitchenFilterGroupEnum::toArray());
		$this->addDynamicMultiSelect('other', 'Ostali sadržaji', OtherFilterGroupEnum::toArray());
		$this->addDynamicMultiSelect('vpi', 'Vpi', $this->getVpiForFront(VpiEnum::toTranslatedArray($this->translator)));

		$this->addSubmit('search', 'Search');
		$this->addSubmit('reset', 'Reset');

		$this->onSubmit[] = [$this, 'search'];

		if (empty($this->sessionSection['values'])) {
			return;
		}

		$defaultValues = $this->sessionSection['values'];

		if (isset($defaultValues['areas'])) {
			foreach ($defaultValues['areas'] as $key => $areaId) {
				if (!array_key_exists($areaId, $areas)) {
					unset($defaultValues['areas'][$key]);
				}
			}
		}

		unset($defaultValues[CcAppliedFiltersEnum::TRAUM_ID]);
		unset($defaultValues['property_id']);
		unset($defaultValues[CcAppliedFiltersEnum::COUNTRY]);
		$this->setDefaults($defaultValues);
	}

	public function search(): void
	{
		if ($this->getComponent('reset')->isSubmittedBy()) {
			$session = $this->sessionSection['values'];
			$userData = [];
			$userData['email'] = $session['email'];
			$userData['lastname'] = $session['lastname'];
			$userData['firstname'] = $session['firstname'];
			$userData['contact_language'] = $session['contact_language'];
			$userData['childrenCount'] = $session['childrenCount'];
			$userData['petsCount'] = $session['petsCount'];
			$userData['text'] = $session['text'];
			$userData['adultsCount'] = $session['adultsCount'];
			$userData['arrive'] = $session['arrive'];
			$userData['departure'] = $session['departure'];
			$userData['nights_stay'] = $session['nights_stay'];

			$this->reset();
			$this->onReset();
			$this->setDefaults($userData);

			return;
		}

		if ($this->getComponent('search')->isSubmittedBy()) {
			$values = $this->getUnsafeValues(null);

			if (!empty($values['firstname']) || !empty($values['lastname'])) {
				$this->sessionSection->values = (array) $values;
			}

			if (empty($values['arrive']) || empty($values['departure'])) {
				$this->getPresenter()->flashMessage($this->getTranslator()->translate('Datum dolaska ili odlaska nisu ispravno uneseni!'), 'error');

				return;
			}

			$values['arrive'] = $this->formatDateValuesFromForm($values['arrive']);
			$values['departure'] = $this->formatDateValuesFromForm($values['departure']);
			$this->sessionSection->values['arrive'] = $values['arrive'];
			$this->sessionSection->values['departure'] = $values['departure'];

			$this->onSearch($values, $this->sessionSection);
		}
	}


	private function formatDateValuesFromForm($date)
	{
		$oldDate = $date->format('Y-m-d');
		$oldDateTimestamp = strtotime($oldDate);

		return new DateTime($oldDateTimestamp);
	}


	private function getVpiForFront($vpis)
	{
		$array = [];

		foreach ($vpis as $id => $vpi) {
			if ($id === VpiEnum::VILLAS_WITH_POOL || $id === VpiEnum::PANORAMA_VIEW) {
				continue;
			}

			$array[$id] = $vpi;
		}

		return $array;
	}

}

<?php declare(strict_types = 1);

namespace App\Modules\CallCenter\Forms;

use App\Common\Forms\BaseForm;
use Nette\ComponentModel\IComponent;
use Nette\Http\SessionSection;

class ChangeDatesForm extends BaseForm
{

	/**
	 * @var SessionSection $sessionSection
	 */
	private $sessionSection;

	/**
	 * @var callable
	 */
	public $onCalculate;

	/**
	 * @var int
	 */
	private $productId;

	public function attached(IComponent $presenter): void
	{
		parent::attached($presenter);
		unset($this['submit']);
	}

	public function __construct(int $productId, $container = NULL)
	{
		$this->productId = $productId;
		parent::__construct($container);
	}


	public function prepare(): void
	{
		$section = $this->getPresenter()->session->getSection($this->getPresenter()->getParameter('search_id'));

		$this->addBootstrapDate('arrive', 'Dolazak')->setDefaultValue($section['values']['arrive']);
		$this->addBootstrapDate('departure', 'Odlazak')->setDefaultValue($section['values']['departure']);
		$section->calculator['arrive'] = $section['values']['arrive']->format('d.m.Y');
		$section->calculator['departure'] = $section['values']['departure']->format('d.m.Y');
		$this->addSubmit('calculate');
		$this->onSubmit[] = [$this, 'calculate'];
	}


	public function calculate(): void
	{
		$values = $this->getValues();
		$arriveDate = $values['arrive']->setTime(0, 0, 0);
		$departureDate = $values['departure']->setTime(0, 0, 0);

		$productId = $this->productId;
		$searchId = $this->getPresenter()->getParameter('search_id');
		$this->sessionSection = $this->getPresenter()->session->getSection($searchId);

		$this->sessionSection->calculator['arrive'] = $arriveDate->format('d.m.Y');
		$this->sessionSection->calculator['departure'] = $departureDate->format('d.m.Y');

		$this->onCalculate(
			$productId,
			$arriveDate,
			$departureDate,
			(int) $this->sessionSection->values['adultsCount'],
			(int) $this->sessionSection->values['childrenCount']
		);
	}

}

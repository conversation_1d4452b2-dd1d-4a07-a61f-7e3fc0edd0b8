{snippet favorites}
    {if !empty($productSearchRows)}
        {foreach $productSearchRows as $row}
            <div class="col-sm-12">
                <div class="col-sm-12">
                    <hr/>
                    <h4>{$row->location} <a href="{$presenter->link(':Front:SingleVilla:default', $presenter->language, $row->product_id)}" target="_blank">{sprintf("%04d", $row->product_id)}</a>
                        {for $starsNumber = $row->quality; $starsNumber > 0; $starsNumber--}
                            <i class="fa fa-star fa-xs"></i>
                        {/for}
                    </h4>
                </div>
                <div class="form-group row">
                    <div class="col-sm-5">
                        <img class="lib-img-show" src="{$row->thumbnail}" style="width: 100%">
                    </div>
                    <div class="col-sm-7">
                        <div class="col-sm-12">
                            <table style="width:100%">
                                <tr>
                                    {if !empty($row->house_unit_size)}
                                        <th>
                                            <i class="fa fa-home" style="font-size:14px;"></i>
                                        </th>
                                    {/if}
                                    {if !empty($row->people_number)}
                                        <th>
                                            <i class="fa fa-users" style="font-size:14px;"></i>
                                        </th>
                                    {/if}
                                    {if !empty($row->bedrooms_count)}
                                        <th>
                                            <i class="fa fa-bed" style="font-size:14px;"></i>
                                        </th>
                                    {/if}
                                    {if !empty($row->bathrooms_count)}
                                        <th>
                                            <i class="fa fa-bath" style="font-size:14px;"></i>
                                        </th>
                                    {/if}
                                    {if !empty($row->allowed_pet_count)}
                                        <th>
                                            <i class="fa fa-paw" style="font-size:14px;"></i>
                                        </th>
                                    {/if}
                                </tr>
                                <tr>
                                    {if !empty($row->house_unit_size)}
                                        <td>{$row->house_unit_size}</td>
                                    {/if}
                                    {if !empty($row->people_number)}
                                        <td>{$row->people_number}</td>
                                    {/if}
                                    {if !empty($row->bedrooms_count)}
                                        <td>{$row->bedrooms_count}</td>
                                    {/if}
                                    {if !empty($row->bathrooms_count)}
                                        <td>{$row->bathrooms_count}</td>
                                    {/if}
                                    {if !empty($row->allowed_pet_count)}
                                        <td>{$row->allowed_pet_count} allowed</td>
                                    {/if}
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-12" style="margin-top: 12px">
                            {if !empty($row->price)}
                                <span class="currency">EUR </span><span class="num">{$row->price}</span><span class="currency">€</span>
                            {else}
                                <span class="not-available" style="color: red">{_'Not available!'}</span>
                            {/if}
                            <br/>
                            {var $pool_type = isset(App\Common\Enums\SwimmingPoolDenormalizedIdsEnum::toArray()[$row->pool_type]) ? App\Common\Enums\SwimmingPoolDenormalizedIdsEnum::toArray()[$row->pool_type] : ''}

                            <span class="swimming_pool">{_'Bazen'}: </span><span class="num">{if !empty($pool_type)}{$translator->translate($pool_type)}{else}N{/if}</span>
                            <br/>
                            <span class="build_year">{_'Godina izgradnje'}: </span><span class="num">{$row->build_year}</span>
                        </div>
                    </div>
                </div>

            </div>
            <div class="col-sm-12">
                {_'Arrival date'}: <strong>{$row->arrival_date->format('d.m.Y')}</strong>
                <br/>
                {_'Departure date'}: <strong>{$row->departure_date->format('d.m.Y')}</strong>
            </div>
            <div>
                {form favoriteLabelsForm-{$row->favorite_id}}
                    {include '../../Forms/templates/FavoriteLabelsForm.latte'}
                {/form}
            </div>
            <div class="form-group row">
                <div class="col-sm-6">
                    <a id="btnFavoriteDetails" href="{$presenter->link('showModal!', ['productId' => $row->product_id])}" class="btn btn-sm ajax btn-primary float-left">
                        <i class="fa fa-info"></i>
                        {_'Favorites details'}
                    </a>
                </div>
                <div class="col-sm-6">
                    <a id="btnRemove" n:href="RemoveFavorite $row->favorite_id" class="btn btn-danger btn-sm ajax float-right">
                        <i class="fa fa-trash"></i>
                        {_'Remove'}
                    </a>
                </div>
            </div>
            <hr>
        {/foreach}
        <div class="form-group col-sm-12">
            <a class="btn btn-danger btn-lg btn-block ajax" data-naja-history="off" n:href="RemoveAllFavorites">
                <i class="fa fa-trash"></i>
                {_'Remove all favorites'}
            </a>
        </div>
    {/if}
{/snippet}

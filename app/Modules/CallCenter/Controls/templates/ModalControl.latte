{block modal}
    <!-- Modal -->
        <div class="modal fade" id="object-details-modal">
            <div class="modal-dialog modal-xl" style="max-width: 100%; width: 95%;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">{$productSearchDetails->location} - {$productSearchDetails->propertyID} -</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="card">
                            <div class="row">
                                <div n:snippet="flashMessage" class="col-sm-12">
                                    <div n:ifset="$flashMessage" class="container-fluid text-center">
                                        <div n:ifset="$flashMessage" n:class="alert, alert-dismissible, fade, show, alert-success" role="alert">
                                            {$flashMessage|noescape}
                                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <section class="content">
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <div class="card card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title"><i class="fas fa-images"></i> {_'Images'}</h3>

                                                    <div class="card-tools">
                                                        <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip" title="Collapse">
                                                            <i class="fas fa-minus"></i></button>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="gallery-wrap">
                                                        <div class="img-small-wrap">
                                                            <a class="thumbnail thumbnail-gallery fancybox" rel="ligthbox" href="{$thumbnailPicture}">
                                                                <img class="img-gallery img-responsive" alt="" src="{$thumbnailPicture}" />
                                                            </a>
                                                        </div>
                                                        <div class="img-small-wrap" style="display:none">
                                                            {foreach $pictures as $picture}
                                                                {if !empty($picture) && $picture != $thumbnailPicture}
                                                                    <div class='col-sm-4 col-xs-6 col-md-3 col-lg-3'>
                                                                        <a class="thumbnail fancybox" rel="ligthbox" href="{$picture}">
                                                                            <img class="img-responsive" alt="" src="{$picture}" />
                                                                        </a>
                                                                    </div>
                                                                {/if}
                                                            {/foreach}
                                                        </div>
                                                        <div class="gmap">
                                                            <iframe width="100%" height="450" frameborder="0" scrolling="no" marginheight="0" marginwidth="0" src="https://maps.google.com/maps?f=q&amp;source=s_q&amp;hl=hr&amp;geocode=&amp;q={$latitude},{$longitude}&amp;aq=&amp;sll={$latitude},{$longitude}&amp;sspn=0.013634,0.033023&amp;ie=UTF8&amp;hq=&amp;t=m&amp;ll={$latitude},{$longitude}&amp;spn=0.227635,0.439453&amp;z=11&amp;output=embed"></iframe>
                                                        </div>
                                                        {if !empty($floorplanPicture)}
                                                            <div class="img-small-wrap">
                                                                <img class="img-responsive img-floorplan" src="{$floorplanPicture}">
                                                            </div>
                                                        {/if}
                                                    </div>
                                                </div>
                                                <!-- /.card-body -->
                                            </div>
                                            <!-- /.card -->
                                        </div>

                                        <div class="col-sm-4">
                                            <div class="card card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title"><i class="fas fa-thumbs-up"></i> {_'Features'}</h3>

                                                    <div class="card-tools">
                                                        <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip" title="Collapse">
                                                            <i class="fas fa-minus"></i></button>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    {for $starsNumber = $productSearchDetails->quality; $starsNumber > 0; $starsNumber--}
                                                        <i class="fa fa-star"></i>
                                                    {/for}
                                                    {if !empty($price)}
                                                        <p class="price-detail-wrap"><span class="price h3 text-primary"><span class="currency">EUR €</span><span class="num">{$price |number:2}</span></span>
                                                            <br/>
                                                            {if !empty($allInclusive)}
                                                                <span>All inclusive</span>
                                                            {/if}
                                                            <br/>
                                                        <hr>
                                                        <span>Kuća izgrađena {$productSearchDetails->build_year}. godine{if !empty($productSearchDetails->modernized_year)},a renovirana {$productSearchDetails->modernized_year}. godine{else}.{/if}</span>
                                                        <br/>
                                                        {if !empty($buildingMaterial)}<span>Materijal izrade: {$buildingMaterial}</span>{/if}

                                                        <hr>

                                                        <table style="width:100%">
                                                            <tr>
                                                                {if !empty($productSearchDetails->people_number)}
                                                                    <th>
                                                                        <i class="fa fa-users" style="font-size:24px;"></i>
                                                                    </th>
                                                                {/if}
                                                                {if !empty($productSearchDetails->house_unit_size)}
                                                                    <th>
                                                                        <i class="fa fa-home" style="font-size:24px;"></i>
                                                                    </th>
                                                                {/if}
                                                                {if !empty($productSearchDetails->bedrooms_count)}
                                                                    <th>
                                                                        <i class="fa fa-bed" style="font-size:24px;"></i>
                                                                    </th>
                                                                {/if}
                                                                {if !empty($productSearchDetails->bathrooms_count)}
                                                                    <th>
                                                                        <i class="fa fa-bath" style="font-size:24px;"></i>
                                                                    </th>
                                                                {/if}
                                                                {if !empty($productSearchDetails->allowed_pet_count)}
                                                                    <th>
                                                                        <i class="fa fa-paw" style="font-size:24px;"></i>
                                                                    </th>
                                                                {/if}
                                                                {if !empty($swimmingPoolType)}
                                                                    <th>
                                                                        Bazen
                                                                    </th>
                                                                {/if}
                                                            </tr>
                                                            <tr>
                                                                {if !empty($productSearchDetails->people_number)}
                                                                    <td>{$productSearchDetails->people_number}</td>
                                                                {/if}
                                                                {if !empty($productSearchDetails->house_unit_size)}
                                                                    <td>{$productSearchDetails->house_unit_size}</td>
                                                                {/if}
                                                                {if !empty($productSearchDetails->bedrooms_count)}
                                                                    <td>{$productSearchDetails->bedrooms_count}</td>
                                                                {/if}
                                                                {if !empty($productSearchDetails->bathrooms_count)}
                                                                    <td>{$productSearchDetails->bathrooms_count}</td>
                                                                {/if}
                                                                {if !empty($productSearchDetails->allowed_pet_count)}
                                                                    <td>{$productSearchDetails->allowed_pet_count} allowed</td>
                                                                {/if}
                                                                {if !empty($swimmingPoolType)}
                                                                    <td>{$swimmingPoolType}</td>
                                                                {/if}
                                                            </tr>
                                                        </table>

                                                        <br/>
                                                    {/if}

                                                    <div class="card card-primary">
                                                        <div class="card-header">
                                                            <h3 class="card-title">
                                                                <i class="fas fa-home"></i>
                                                                {_('Sadržaji')}
                                                            </h3>
                                                        </div>
                                                        <!-- /.card-header -->
                                                        <div class="card-body">
                                                            <div class="callout callout-info">
                                                                <ul class="list-unstyled">
                                                                    {if (!empty($productSearchDetails->free_wifi))}
                                                                        <li>{_('Wi-Fi gratis')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->sat_tv))}
                                                                        <li>{_('SAT TV')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->aircondition))}
                                                                        <li>{_('Klima uređaj')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->single_beds_count))}
                                                                        <li>{_('Broj jednoležajnih kreveta:')} {$productSearchDetails->single_beds_count}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->double_beds_count))}
                                                                        <li>{_('Broj bračnih kreveta:')} {$productSearchDetails->double_beds_count}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->sofa_beds_count))}
                                                                        <li>{_('Broj pomoćnih ležaja:')} {$productSearchDetails->sofa_beds_count}</li>
                                                                    {/if}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!-- /.card-body -->
                                                    </div>

                                                    <div class="card card-primary">
                                                        <div class="card-header">
                                                            <h3 class="card-title">
                                                                <i class="fas fa-utensils"></i>
                                                                {_('Sadržaji kuhinje')}
                                                            </h3>
                                                        </div>
                                                        <!-- /.card-header -->
                                                        <div class="card-body">
                                                            <div class="callout callout-info">
                                                                <ul class="list-unstyled">
                                                                    {if (!empty($productSearchDetails->microwave))}
                                                                        <li>{_('Mikrovalna pećnica')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->freezer))}
                                                                        <li>{_('Frižider')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->coffee_percolator))}
                                                                        <li>{_('Kuhalo za vodu')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->coffee_brewer))}
                                                                        <li>{_('Aparat za kavu')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->wash_machine))}
                                                                        <li>{_('Perilica rublja')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->dishwasher))}
                                                                        <li>{_('Perilica suđa')}</li>
                                                                    {/if}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!-- /.card-body -->
                                                    </div>

                                                    <div class="card card-primary">
                                                        <div class="card-header">
                                                            <h3 class="card-title">
                                                                <i class="fas fa-tree"></i>
                                                                {_('Vanjski sadržaji')}
                                                            </h3>
                                                        </div>
                                                        <!-- /.card-header -->
                                                        <div class="card-body">
                                                            <div class="callout callout-info">
                                                                <ul class="list-unstyled">
                                                                    {if (!empty($productSearchDetails->grill))}
                                                                        <li>{_('Roštilj')}</li>
                                                                    {/if}
                                                                    {if (!empty($productSearchDetails->garden_furniture))}
                                                                        <li>{_('Vrtni namještaj')}</li>
                                                                    {/if}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!-- /.card-body -->
                                                    </div>

                                                    <div class="card card-primary">
                                                        <div class="card-header">
                                                            <h3 class="card-title">
                                                                <i class="fas fa-info"></i>
                                                                {_('Posebnosti')}
                                                            </h3>
                                                        </div>
                                                        <!-- /.card-header -->
                                                        <div class="card-body">
                                                            <div class="callout callout-info">
                                                                <ul class="list-unstyled">
                                                                    {if !empty($concepts)}
                                                                        {foreach $concepts as $concept}
                                                                            <li>{$concept->name}</li>
                                                                        {/foreach}
                                                                    {/if}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!-- /.card-body -->
                                                    </div>

                                                    <!-- /.card -->
                                                </div>
                                                <!-- /.card-body -->
                                            </div>
                                            <!-- /.card -->
                                        </div>


                                        <div class="col-sm-5">
                                            <div class="card card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title"><i class="fas fa-calculator"></i> {_'Prices and availability'}</h3>

                                                    <div class="card-tools">
                                                        <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip" title="Collapse">
                                                            <i class="fas fa-minus"></i></button>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="col-sm-12" style="text-align: center">
                                                        <input type="hidden" name="daterange" id="mainrange" class="daterange-picker" />
                                                    </div>
                                                    <div class="col-sm-12">
                                                        {form changeDatesForm, class => 'change-dates-form'}
                                                            <div class="form-group">
                                                                <div class="input-group input-daterange">
                                                                    {input arrive, class => 'form-control date invisible', placeholder => 'Dolazak'}
                                                                    {input departure, class => 'form-control date invisible', placeholder => 'Odlazak'}
                                                                    {input calculate, class => 'calculate-product-price ajax invisible'}
                                                                </div>
                                                            </div>
                                                        {/form}
                                                    </div>

                                                    {control weekPricesControl}

                                                    <div class="card card-primary">
                                                        <div class="card-header">
                                                            <h3 class="card-title">{_'Dates'}</h3>
                                                            <div class="card-tools">
                                                              <button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fas fa-minus"></i></button>
                                                            </div>
                                                        </div>
                                                        <div class="card-body" n:snippet="price">
                                                            <h5 style="text-align: center">
                                                                {_'Dolazak'}: <strong>{$arriveDate}</strong>
                                                                <br/><br/>
                                                                {_'Odlazak'}: <strong>{$departureDate}</strong>
                                                                <br/><br/>
                                                                {if !empty($price)}
                                                                {_'Cijena'}: <strong>{$price} €</strong>
                                                                {else}
                                                                <strong>{_'Not available!'}</strong>
                                                                {/if}
                                                            </h5>
                                                            <div class='overlay invisible' id="dates-spinner">
                                                                <i class="fas fa-2x fa-sync-alt fa-spin"></i>
                                                            </div>
                                                        </div>
                                                    </div>


                                                    <div class="form-group row">
                                                        <div class="col-sm-3"></div>
                                                        <div class="col-sm-3" n:snippet="checkOfferButton">
                                                            <a id="btnCheckOffer" target="_blank" n:href="checkOffer, $productId, $search_id, $arriveDate, $departureDate" class="btn btn-info float-right">
                                                                <i class="fa fa-eye"></i>
                                                                {_'Check offer'}
                                                            </a>
                                                        </div>
                                                        <div class="col-sm-3" n:snippet="addFavoriteButton">
                                                            <a id="btnCheckOffer" data-naja-history="off" n:href="addFavorite, $productId, $search_id, $arriveDate, $departureDate" class="btn btn-danger ajax float-right">
                                                                <i class="fa fa-star"></i>
                                                                {_'Add to favorite'}
                                                            </a>
                                                        </div>
                                                        <div class="col-sm-3" n:snippet="bookingUrl">
                                                            <a href="{$presenter->link(':Front:Booking:default',
                                                            $presenter->language, $contactInfo['firstname'], $contactInfo['lastname'], $contactInfo['email'],
                                                            $arriveDate, $departureDate, $contactInfo['adultsCount'], $contactInfo['childrenCount'], $contactInfo['petsCount'], null, $productId
                                                            )}" class="btn btn-primary float-lg-right" target="_blank">
                                                                <i class="fa fa-plus"></i>
                                                                {_'Book now'}
                                                            </a>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        {control offerGeneratorControl}
                                                    </div>
                                                </div>
                                                <!-- /.card-body -->
                                            </div>
                                            <!-- /.card -->
                                        </div>

                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>
{/block}

{block controlJs}
    <script n:srcv="/admin_assets/js/jquery.tipTip.min.js"></script>
    <script>
        function openInNewTab(url) {
            var win = window.open(url, '_blank');
            win.focus();
        }

        $("#close").click(function () {
            $("#myModal").modal("hide");
        });

        jQuery(document).on('keyup',function(evt) {
            if (evt.keyCode == 27) {
                document.getElementById("close").click();
            }
        });

        function get_switch_day(php_day_id) {
            var weekday = new Array(7);
            weekday[1] = 1;
            weekday[2] = 2;
            weekday[3] = 3;
            weekday[4] = 4;
            weekday[5] = 5;
            weekday[6] = 6;
            weekday[7] = 0;
            return weekday[php_day_id];
        }

        function handleOnlySaturday () {
            var only_saturday = '.only-saturday';

            $(only_saturday).not('disabled').tipTip({
                defaultPosition: 'top',
                activation: 'click',
                content: {_'Only saturday'}
            });

            $('.only-saturday').on('click', function(e) {
                e.preventDefault();
                return false;
            });
        }

        var dates = {$dateStatus},
            dates_with_concepts = {$datesWithConcepts},
            minimum_stay = {$productSearchDetails->minimum_days} - 1,
            specialOfferDates = {$specialOfferDates};

        var mainpickr = flatpickr('#mainrange', {
            altInputClass: 'date-rangepicker',
            inline: true,
            disable: [
                function(date) {
                    var key = flatpickr.formatDate(date, "Y-m-d");

                    if (flatpickr.formatDate(date, "d.m.Y") === {$arriveDate}) {
                        return;
                    }

                    return !dates[key];
                }],
            showMonths: 2,
            defaultDate: [{$arriveDate}, {$departureDate}],
            mode: 'range',
            minDate: {$arriveDate},
            altFormat: 'd.m.Y',
            dateFormat: 'd.m.Y',
            disableMobile: true,
            locale: $language,
            altInput: true,
            allowInput: false,
            onDayCreate: function(dObj, dStr, fp, dayElem) {
                var calendar_day = flatpickr.formatDate(dayElem.dateObj, "Y-m-d"),
                    calendar_day_dd_mm = flatpickr.formatDate(dayElem.dateObj, "d.m."),
                    optional_arrival = 'optional-arrival',
                    mini_vacation = 'mini-vacation';

                //Don't use the logic if the day has state of disabled
                if(dayElem.classList.contains('disabled')) {
                    return;
                }

                if (calendar_day_dd_mm === '24.12.' || calendar_day_dd_mm === '31.12.') {
                    dayElem.classList.add('disabled', 'holiday-dates');
                    return;
                }

                if (specialOfferDates.includes(calendar_day)) {
                    dayElem.innerHTML += "<span class='special-offer'></span>";
                }

                dayElem.classList.add('available');

                if (dates_with_concepts[calendar_day]) {
                    if (dates_with_concepts[calendar_day].optional_arrival === true) {
                        if (dates_with_concepts[calendar_day].mini_vacation === true) {
                            dayElem.classList.add(mini_vacation);
                        }
                        dayElem.classList.add(optional_arrival);
                        return;
                    }
                }

                var js_switch_day = get_switch_day({$productSearchDetails->switch_day});
                if (dayElem.dateObj.getDay() === js_switch_day) {
                    dayElem.classList.add('saturday');
                }

                if (dayElem.dateObj.getDay() !== js_switch_day) {
                    dayElem.classList.add('only-saturday', 'disabled');
                }
            },
            onMonthChange: function (selectedDates, dateStr, instance) {
                handleOnlySaturday();

                if(selectedDates.length === 1) {
                    var current_date_clicked = new Date(selectedDates),
                        has_optional_arrival = instance.selectedDateElem.className.includes('optional-arrival'),
                        has_mini_vacation = instance.selectedDateElem.className.includes('mini-vacation');

                    $(instance.selectedDateElem).addClass('disabled');
                    $('.flatpickr-day.available').removeClass('available');

                    if (has_optional_arrival) {
                        $(instance.selectedDateElem).nextUntil('.disabled').addClass('optional-arrival-bolded');

                        if(has_mini_vacation) {
                            $(instance.selectedDateElem).nextAll("*:lt(" + minimum_stay + ")").addClass('disabled minimum-stay-day');
                        } else {
                            $(instance.selectedDateElem).nextAll('*:lt(6)').addClass('disabled minimum-stay-day');
                        }

                    }
                }
            },
            onChange: function (selectedDates, dateStr, instance) {
                handleOnlySaturday();

                var holiday_dates = $('.holiday-dates');
                $.each(holiday_dates, function (key, element) {
                    $(element).removeClass('disabled');
                });

                var current_date_clicked = new Date(selectedDates),
                    has_optional_arrival = instance.selectedDateElem.className.includes('optional-arrival'),
                    has_mini_vacation = instance.selectedDateElem.className.includes('mini-vacation');

                // Choose arrival date
                if(selectedDates.length === 1) {
                    $(instance.selectedDateElem).addClass('disabled');
                    $('.flatpickr-day.available').removeClass('available');

                    if (has_optional_arrival) {
                        $(instance.selectedDateElem).nextUntil('.disabled').addClass('optional-arrival-bolded');

                        if(has_mini_vacation) {
                            $(instance.selectedDateElem).nextAll("*:lt(" + minimum_stay + ")").addClass('disabled minimum-stay-day');
                        } else {
                            $(instance.selectedDateElem).nextAll('*:lt(6)').addClass('disabled minimum-stay-day');
                        }

                    }
                }

                // Choose departure date
                if (selectedDates.length === 2) {
                    $('#frm-modalControl-changeDatesForm-arrive').val(flatpickr.formatDate(selectedDates[0], 'd.m.Y'));
                    $('#frm-modalControl-changeDatesForm-departure').val(flatpickr.formatDate(selectedDates[1], 'd.m.Y'));
                    $('#dates-spinner').removeClass('invisible');
                    $('.calculate-product-price').trigger('click');
                }
            }
        });
    </script>


{/block}

<a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
    <span class="sr-only">Toggle navigation</span>
    <span class="icon-bar"></span>
    <span class="icon-bar"></span>
    <span class="icon-bar"></span>
</a>
<ul class="sidebar-menu">
    <li class="header">MAIN NAVIGATION</li>
    {foreach $mainMenu as $itemTree}
        {var $subItemActive = false}
        {var $mainItemClass = null}
        {var $isFirst = true}
        {var $isMain = true}
        {var $isSub = true}
    {var $startedSub = false}
        {var $i = 0}
        {var $active = false}
        {foreach $itemTree as $menuItem}
            {continueIf isset($menuItem['allowed']) && !$menuItem['allowed']}

            {var $i = $i+1}
            {if $menuItem['active']}
                {var $active = true}
            {/if}
        {/foreach}

        {if $active && $i > 1}
            {var $mainItemClass = "active"}
        {elseif $active}
            {var $mainItemClass = "active"}
        {/if}

        {foreach $itemTree as $menuItem}
            {continueIf isset($menuItem['allowed']) && !$menuItem['allowed']}
            {if $menuItem['active']}
                {var $activeClass = 'active'}
            {else}
                {var $activeClass = null}
            {/if}

            {if $isMain}
                {var $isMain = false}
    {var $mainItem =$menuItem}
    <li class="treeview {$mainItemClass}"><a
                href="{$menuItem['link']}" n:class="$activeClass"><i n:class="'fa', !empty($menuItem['icon']) ? 'fa-'. $menuItem['icon'] : 'fa-file'"></i><span>{_$menuItem['name']}</span><i
                    class="fa fa-angle-left pull-right"></i></a>
            {elseif $isSub}
        {var $isSub = false}
        {var $startedSub = true}
                <ul class="treeview-menu">
                    {if !empty($mainItem)}
                        <li class="Sub"><a href="{$mainItem['link']}" n:class="$mainItemClass, Nette\Utils\Strings::startsWith($mainItem['link'], '/cms/') ? ajax">{_$mainItem['name']}</a>
                        </li>
                        {var $mainItem = false}
                    {/if}
                <li class="Sub"><a href="{$menuItem['link']}" n:class="$activeClass, Nette\Utils\Strings::startsWith($menuItem['link'], '/cms/') ? ajax">{_$menuItem['name']}</a></li>
            {else}
                <li class="NotSub"><a href="{$menuItem['link']}" n:class="$activeClass, Nette\Utils\Strings::startsWith($menuItem['link'], '/cms/') ? ajax">{_$menuItem['name']}</a></li>
            {/if}
                    {last} </li>{if $startedSub} {var $startedSub = false} </ul> {/if}{/last}
        {/foreach}
    {/foreach}
</ul>

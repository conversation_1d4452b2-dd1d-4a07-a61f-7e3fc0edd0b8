{block head}
    <link rel="stylesheet" href="https://cdn.rawgit.com/tonystar/bootstrap-float-label/v3.0.1/dist/bootstrap-float-label.min.css"/>
{/block}

{block modal}
    <div n:snippet="modal">
        {if !empty($details)}
            {control modalControl}
        {/if}
    </div>
{/block}

{block content}
    <div class="row">
        <div class="col-sm-12 filter-form" n:snippet="searchForm">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-search"></i>
                        {_'Pretraživanje'}</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fas fa-minus"></i></button>
                    </div>
                    <!-- /.card-tools -->
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-4"><h4>{_"Osnovne info o gostu"}</h4></div>
                        <div class="col-sm-4"><h4>{_"Osnovne info o kući"}</h4></div>
                        <div class="col-sm-4">
                            <a n:href="newSearch" target="_blank" class="btn-outline-secondary float-right margin">
                                <i class="fa fa-search"></i>
                                {_'Novo Pretraživanje'}
                            </a>
                        </div>
                    </div>
                    <div class="col-sm-12"><hr class="style-head"/></div>
                    {form findClientForm}
                        <div class="col-sm-12">
                            <div class="col-sm-4">
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'E-mail'}</label>
                                    <div id="email" class="col-sm-7">
                                        <input id="email" n:name="email" class="form-control email-find-client">
                                    </div>
                                    <div class="col-sm-3">
                                        <input n:name="checkIfUserExist" id="hubspot_contact_check" class="btn btn-primary float-right">
                                    </div>
                                </div>
                            </div>
                        </div>
                    {/form}
                    {form searchForm, class => 'search-helper-form', role => 'form'}
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Jezik'}</label>
                                    <div class="col-sm-10">{input contact_language, class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Ime'}</label>
                                    <div class="col-sm-4">{input firstname, class => 'form-control'}</div>
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Prezime'}</label>
                                    <div class="col-sm-4">{input lastname, class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Dolazak'}</label>
                                    <div class="col-sm-4">{input arrive, class => 'form-control date', data-format => 'd.m.Y'}</div>
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Odlazak'}</label>
                                    <div class="col-sm-4">{input departure, class => 'form-control date', data-format => 'd.m.Y'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-6 col-form-label" for="frm-searchForm-nights_stay">{_'Broj nočenja'}</label>
                                    <div class="col-sm-6">
                                        {input nights_stay, class => 'form-control'}
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Odrasli'}</label>
                                    <div class="col-sm-4">{input adultsCount, class => 'form-control'}</div>
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Djeca'}</label>
                                    <div class="col-sm-4">{input childrenCount, class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Psi'}</label>
                                    <div class="col-sm-4">{input petsCount, class => 'form-control'}</div>
                                    <div class="col-sm-6">{input link, formtarget=>"_blank", class => 'btn btn-link', onclick=>'openContactDetails()'}</div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="frm-searchForm-email">{_'Tekst'}</label>
                                    <div class="col-sm-10">{input text, rows=>5, id=>'text-area-note', class => 'form-control'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Kapacitet'}</label>
                                    <div class="col-sm-8">{input capacity, type => number, class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Kvaliteta'}</label>
                                    <div class="col-sm-8">{input quality ,class => 'selectpicker form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Bazen'}</label>
                                    <div class="col-sm-8">{input swimming_pool, class => 'selectpicker form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Broj spavaćih soba'}</label>
                                    <div class="col-sm-8">{input sleeping_rooms ,class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Broj kupaonica'}</label>
                                    <div class="col-sm-8">{input bathrooms ,class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Bračni kreveti'}</label>
                                    <div class="col-sm-8">{input double_beds_number ,class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Jednoležajni kreveti'}</label>
                                    <div class="col-sm-8">{input single_beds_number ,class => 'form-control'}</div>
                                </div>
                                <div class="form-check row">
                                    {input fencedProperty}
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-country">{_'Country'}</label>
                                    <div class="col-sm-8">{input country, class => 'selectpicker form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-partner">{_'Partner'}</label>
                                    <div class="col-sm-8">{input partner, class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Regije'}</label>
                                    <div class="col-sm-8">{input areas, class => 'selectpicker form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Mjesta'}</label>
                                    <div class="col-sm-8">{input places, class => 'selectpicker form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'ID kuće'}</label>
                                    <div class="col-sm-8">{input property_id, id => 'property_ids', class => 'selectpicker-product-ids form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-traum-id">{_'Traum ID'}</label>
                                    <div class="col-sm-8">{input traum_id, id => 'traum_ids', class => 'selectpicker-traum-ids form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-email">{_'Kućni ljubimci'}</label>
                                    <div class="col-sm-8">{input pets, class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-parking_place_count">{_'Broj parkirnih mjesta'}</label>
                                    <div class="col-sm-8">{input parking_place_count, class => 'form-control'}</div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label" for="frm-searchForm-smoking">{_'Dozvoljeno pušenje'}</label>
                                    <div class="col-sm-8">{input smoking, class => 'form-control'}</div>
                                </div>
                                <div class="form-check row">
                                    {input published}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button type="submit" n:name="search" id="search" class="btn btn-lg btn-primary ajax float-right">
                                    <i class="fas fa-search"></i> {_'Search'}
                                </button>
                                <button type="submit" n:name="reset" id="reset" class="btn btn-lg btn-primary ajax float-right">
                                    <i class="fas fa-search"></i> {_'Reset'}
                                </button>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="card card-primary collapsed-card">
                                    <div class="card-header border-0">
                                        <div class="d-flex justify-content-between">
                                            <h5 class="card-title"><i class="fas fa-filter"></i> {_'Filtriranje'}</h5>
                                            <div class="card-tools">
                                                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>

                                    </div>
                                    <!-- /.card-header -->
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group row">
                                                    <label class="col-form-label col-sm-4">{_'unit_size'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="house_unit_size_from" placeholder="{if !empty($minMaxValues['house_unit_size_min'])}{$minMaxValues['house_unit_size_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="house_unit_size_to" placeholder="{if !empty($minMaxValues['house_unit_size_max'])}{$minMaxValues['house_unit_size_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'Price'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="price_from" placeholder="{if !empty($minMaxValues['price_min'])}{$minMaxValues['price_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="price_to" placeholder="{if !empty($minMaxValues['price_max'])}{$minMaxValues['price_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'Capacity'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="capacity_from" placeholder="{if !empty($minMaxValues['people_number_min'])}{$minMaxValues['people_number_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="capacity_to" placeholder="{if !empty($minMaxValues['people_number_max'])}{$minMaxValues['people_number_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'build_year'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="build_year_from" placeholder="{if !empty($minMaxValues['build_year_min'])}{$minMaxValues['build_year_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="build_year_to" placeholder="{if !empty($minMaxValues['build_year_max'])}{$minMaxValues['build_year_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'modernized_year'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="modernized_year_from" placeholder="{if !empty($minMaxValues['modernized_year_min'])}{$minMaxValues['modernized_year_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="modernized_year_to" placeholder="{if !empty($minMaxValues['modernized_year_max'])}{$minMaxValues['modernized_year_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="form-group row">
                                                    <label class="col-sm-3 col-form-label">{_'swimmingpool_unit_size'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="swimming_pool_unit_size_from" placeholder="{if !empty($minMaxValues['pool_size_min'])}{$minMaxValues['pool_size_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="swimming_pool_unit_size_to" placeholder="{if !empty($minMaxValues['pool_size_max'])}{$minMaxValues['pool_size_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'parcel_unit_size'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="parcel_unit_size_from" placeholder="{if !empty($minMaxValues['parcel_unit_size_min'])}{$minMaxValues['parcel_unit_size_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="parcel_unit_size_to" placeholder="{if !empty($minMaxValues['parcel_unit_size_max'])}{$minMaxValues['parcel_unit_size_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'town_distance'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="town_distance_from" placeholder="{if !empty($minMaxValues['town_distance_min'])}{$minMaxValues['town_distance_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="town_distance_to" placeholder="{if !empty($minMaxValues['town_distance_max'])}{$minMaxValues['town_distance_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'neighbour_distance'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="neighbour_distance_from" placeholder="{if !empty($minMaxValues['neighbour_distance_min'])}{$minMaxValues['neighbour_distance_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="neighbour_distance_to" placeholder="{if !empty($minMaxValues['neighbour_distance_max'])}{$minMaxValues['neighbour_distance_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'airport_distance'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="airport_distance_from" placeholder="{if !empty($minMaxValues['airport_distance_min'])}{$minMaxValues['airport_distance_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="airport_distance_to" placeholder="{if !empty($minMaxValues['airport_distance_max'])}{$minMaxValues['airport_distance_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'distance_to_water'}</label>
                                                    <div class="col-sm-3">
                                                        <input n:name="sea_distance_from" placeholder="{if !empty($minMaxValues['sea_distance_min'])}{$minMaxValues['sea_distance_min']}{else}0{/if}" class="form-control" />
                                                    </div>
                                                    <div class="col-sm-1">-</div>
                                                    <div class="col-sm-3">
                                                        <input n:name="sea_distance_to" placeholder="{if !empty($minMaxValues['sea_distance_max'])}{$minMaxValues['sea_distance_max']}{else}~{/if}" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'Osnovni sadržaji'}</label>
                                                    <div class="col-sm-8">
                                                        {input basic, class => 'selectpicker form-control'}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'Osobitosti'}</label>
                                                    <div class="col-sm-8">
                                                        {input particularities, class => 'selectpicker form-control'}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-sm-4">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'Sadržaji kuhinje'}</label>
                                                    <div class="col-sm-8">
                                                        {input kitchen, class => 'selectpicker form-control'}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'Ostali sadržaji'}</label>
                                                    <div class="col-sm-8">
                                                        {input other, class => 'selectpicker form-control'}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">{_'Vpi'}</label>
                                                    <div class="col-sm-8">
                                                        {input vpi, class => 'selectpicker form-control'}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {input email, class => 'email-search-form'}
                    {/form}
                </div>
            </div>
        </div>
        <div class="col-sm-9" n:snippet="grid">
            <div class="card card-primary">
                <div class="card-header border-0">
                    <div class="d-flex justify-content-between">
                        <h3 class="card-title"><i class="fas fa-book"></i> {_'Rezultati'}</h3>
                    </div>
                </div>
                <div class="card-body">
                    {if !empty($appliedFilters['sleeping_rooms'])}
                        <div class="col-sm-12">
                            {_'Broj spavaćih soba'} : <strong>{$appliedFilters['sleeping_rooms']}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['country'])}
                        <div class="col-sm-12">
                            {_'Država'} :
                            {var $countries = implode(', ', $appliedFilters['country'])}
                            <strong>{$countries}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['capacity'])}
                        <div class="col-sm-12">
                            {_'Kapacitet'} : <strong>{$appliedFilters['capacity']}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['single_beds_number'])}
                        <div class="col-sm-12">
                            {_'Broj jednokrevetnih soba'} : <strong>{$appliedFilters['single_beds_number']}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['traum_id'])}
                        <div class="col-sm-12">
                            {_'Traum id'} :
                            {var $traum_ids=implode(', ', $appliedFilters['traum_id'])}
                            <strong>{$traum_ids}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['bathrooms'])}
                        <div class="col-sm-12">
                            {_'Broj kupaona'} : <strong>{$appliedFilters['bathrooms']}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['double_beds_number'])}
                        <div class="col-sm-12">
                            {_'Broj bračnih kreveta'} : <strong>{$appliedFilters['double_beds_number']}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['partner'])}
                        <div class="col-sm-12">
                            {_'Partner'} : <strong>{$appliedFilters['partner']}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['areas'])}
                        <div class="col-sm-12">
                            {_'Regije'} :
                            {var $areas = implode(', ', $appliedFilters['areas'])}
                            <strong>{$areas}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['places'])}
                        <div class="col-sm-12">
                            {_'Destinacije'} :
                            {var $places = implode(', ', $appliedFilters['places'])}
                            <strong>{$places}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['pets'])}
                        <div class="col-sm-12">
                            {_'Kućni ljubimci'} :
                            {if $appliedFilters['pets']==1}
                                <strong>{_'Da'}</strong>
                            {/if}
                        </div>
                    {/if}
                    {if !empty($appliedFilters['quality'])}
                        <div class="col-sm-12">
                            {_'Kvaliteta'} :
                            {var $qualities = implode(', ', $appliedFilters['quality'])}
                            <strong>{$qualities}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['swimming_pool'])}
                        <div class="col-sm-12">
                            {_'Bazen'} :
                            {var $pools = implode(', ', $appliedFilters['swimming_pool'])}
                            <strong>{$pools}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['parking_place_count'])}
                        <div class="col-sm-12">
                            {_'Broj parkirnih mjesta'} : <strong>{$appliedFilters['parking_place_count']}</strong>
                        </div>
                    {/if}
                    {if !empty($appliedFilters['smoking'])}
                        <div class="col-sm-12">
                            {_'Dozvoljeno pušenje'} :
                            {if $appliedFilters['smoking']==1}
                                <strong>{_'Da'}</strong>
                            {/if}
                        </div>
                    {/if}
                    {if !empty($showGrid) && !empty($sessionSection)}
                        <div class="col-sm-12"><hr class="style-head"/></div>
                        {control searchResultGrid}
                    {/if}
                </div>
            </div>
        </div>
        <div class="col-sm-3" n:snippet="basic_info">
            <div class="card card-primary">
                <div class="card-header border-0">
                    <div class="d-flex justify-content-between">
                        <h3 class="card-title"><i class="fas fa-info"></i> {_'Osnovne informacije'}</h3>
                    </div>
                </div>
                <div class="card-body">
                    {if !empty($contact_info['firstname'])}
                        <div class="col-sm-12">
                            {_'Ime'} : <strong>{$contact_info['firstname']}</strong>
                        </div>
                    {/if}
                    {if !empty($contact_info['lastname'])}
                        <div class="col-sm-12">
                            {_'Prezime'} : <strong>{$contact_info['lastname']}</strong>
                        </div>
                    {/if}
                    {if !empty($contact_info['arrive'])}
                        <div class="col-sm-12">
                            {_'Dolazak'} : <strong>{$contact_info['arrive']}</strong>
                        </div>
                    {/if}
                    {if !empty($contact_info['departure'])}
                        <div class="col-sm-12">
                            {_'Odlazak'} : <strong>{$contact_info['departure']}</strong>
                        </div>
                        <hr>
                    {/if}
                    {if !empty($contact_info['adultsCount'])}
                        <div class="col-sm-12">
                            {_'Odrasli'} : <strong>{$contact_info['adultsCount']}</strong>
                        </div>
                        <hr>
                    {/if}
                    {if !empty($contact_info['childrenCount'])}
                        <div class="col-sm-12">
                            {_'Djeca'} : <strong>{$contact_info['childrenCount']}</strong>
                        </div>
                        <hr>
                    {/if}
                    {if !empty($contact_info['petsCount'])}
                        <div class="col-sm-12">
                            {_'Psi'} : <strong>{$contact_info['petsCount']}</strong>
                        </div>
                        <hr>
                    {/if}
                </div>
            </div>

            <div class="card card-primary">
                <div class="card-header border-0">
                    <div class="d-flex justify-content-between">
                        <h3 class="card-title"><i class="fas fa-star"></i> {_'Favoriti'}</h3>
                    </div>
                </div>
                <div class="card-body" n:snippet="favoritesBlock">
                    <div n:snippet="favorites">
                        {control favoritesControl}
                    </div>
                {if !empty($contact_info)}
                    <div n:snippet="offerGenerator">
                        {control offerGeneratorControl}
                    </div>
                {/if}
                </div>
            </div>
        </div>
        <!-- /.box -->
    </div>
{/block}

{block js}

    {block controlJs}{/block}

    <script type="text/javascript">
        (function() {
            function formatAutocomplete(item)
            {
                return $('<div>' + item.id + ' - ' + item.propertyID +  '</div>');
            }

            function formatTraumAutocomplete(item)
            {
                return $('<div>' + item.id + ' - ' + item.traum_ferienwohnungen_id +  '</div>');
            }

            function formatSelection(item) {
                return $('<span>' + item.id + '</span>');
            }

            function setControls() {
                $('.selectpicker-product-ids').select2Remote({plink Sales:autoComplete}, {
                    tags: false,
                    templateResult: formatAutocomplete,
                    templateSelection: formatSelection
                });

                $('.selectpicker-traum-ids').select2Remote({plink Sales:traumIdAutoComplete}, {
                    tags: false,
                    templateResult: formatTraumAutocomplete,
                    templateSelection: formatSelection,
                });

            }
            setControls();
            function SetControlsExtension(naja) {
                naja.addEventListener('complete', function () {
                    setControls();
                }.bind(this));
                return this;
            }
            naja.registerExtension(SetControlsExtension);
        })();
    </script>

    <script>
        $(function () {

            $(".email-find-client").on("input", function () {
                $(".email-search-form").val($(".email-find-client").val());
            });


            $("#frm-searchForm-adultsCount").on("input", function () {
                if ($("#frm-searchForm-adultsCount").val() != "" && $("#frm-searchForm-childrenCount").val() != "") {
                    terms = parseInt($("#frm-searchForm-childrenCount").val()) + parseInt($("#frm-searchForm-adultsCount").val());
                } else terms = terms = $("#frm-searchForm-adultsCount").val();

                $("#frm-searchForm-capacity").val(terms);
            });

            $("#frm-searchForm-childrenCount").on("input", function () {
                if ($("#frm-searchForm-adultsCount").val() != "" && $("#frm-searchForm-childrenCount").val() != "") {
                    terms = parseInt($("#frm-searchForm-childrenCount").val()) + parseInt($("#frm-searchForm-adultsCount").val());
                } else terms = terms = $("#frm-searchForm-childrenCount").val();

                $("#frm-searchForm-capacity").val(terms);
            });

            $("#frm-searchForm-petsCount").on("input", function () {
                if ($("#frm-searchForm-petsCount").val() != "" && $("#frm-searchForm-petsCount").val() > 0) {
                    terms = 1;
                } else terms = terms = "";

                $("#frm-searchForm-pets").val(terms);
            });

            $("#frm-searchForm-arrive").on("click", function () {
                $("#frm-searchForm-arrive").val("");
            });

            $("#frm-searchForm-departure").on("click", function () {
                $("#frm-searchForm-departure").val("");
            });


            $("#frm-searchForm-departure").on("blur", function () {
                setNightsStayField();
            });

            $("#frm-searchForm-nights_stay").on("input", function () {
                var arrive = $("#frm-searchForm-arrive").val();
                var days = $("#frm-searchForm-nights_stay").val();
                if (days !== null && days !== undefined && arrive !== null && arrive !== undefined) {
                    var arriveParts = arrive.split(".");
                    var arriveDate = new Date(arriveParts[2], arriveParts[1] - 1, arriveParts[0]);
                    arriveDate.setDate(arriveDate.getDate() + parseInt(days));

                    $("#frm-searchForm-departure").val(arriveDate.getDate() + '.' + (arriveDate.getMonth()+1) + '.' + arriveDate.getFullYear());
                }
            });


            $("#frm-searchForm-arrive").on("blur", function () {

                if ($("#frm-searchForm-arrive").val() != "") {
                    var dateInString = $("#frm-searchForm-arrive").val().split('.');
                    var day = dateInString[0];
                    var month = dateInString[1];
                    var year = dateInString[2];
                    var dateIn = new Date(year,month-1,day);
                    var dateOutTimestamp = dateIn.setDate(dateIn.getDate() + 7);
                    var resOut = new Date(dateOutTimestamp).toLocaleDateString("en-US");
                    var DateOutStringParts = resOut.split('/');
                    var dateOutString = DateOutStringParts[1]+'.'+(parseInt(DateOutStringParts[0]))+'.'+DateOutStringParts[2];

                    terms = dateOutString;
                } else terms = terms = "";

                $("#frm-searchForm-departure").val(terms);
                setNightsStayField();
            });
        });

        function setNightsStayField() {
            var oneDay = 24*60*60*1000;
            var arrive = $("#frm-searchForm-arrive").val();
            var departure = $("#frm-searchForm-departure").val();
            if (arrive !== null && departure !== null && arrive !== undefined && departure !== undefined) {
                var arriveParts = arrive.split(".");
                var arriveDate = new Date(arriveParts[2], arriveParts[1] - 1, arriveParts[0]);

                var departureParts = departure.split(".");
                var departureDate = new Date(departureParts[2], departureParts[1] - 1, departureParts[0]);

                $("#frm-searchForm-nights_stay").val(Math.round(Math.abs((departureDate - arriveDate)/(oneDay))));
            }
        }
    </script>

    <script>
        $(document).ready(function() {
            $(".fancybox").fancybox({
                openEffect: "none",
                closeEffect: "none"
            });

            $('#media').carousel({
                pause: true,
                interval: false,
            });

            $(".clickable-row").click(function() {
                window.location = $(this).data("href");
            });
        });

        $(function(){
            $('#frm-searchForm input').keydown(function(e) {
                if (e.keyCode === 13) {
                    e.preventDefault();
                    var elementId = this.getAttribute('id');
                    if (elementId === 'email' || elementId === 'hubspot_contact_check') {
                        document.getElementById("hubspot_contact_check").click();
                    } else if(elementId !== null) {
                        document.getElementById("search").click();
                    }
                    //return false;
                }
            });
        });

        jQuery(document).ready(function($) {
            $(".clickable-row").click(function() {
                window.location = $(this).data("href");
            });
        });

        function ModalExtension(naja) {
            naja.addEventListener('success', function (params) {
                if (params.response.snippets['snippet--modal']) {
                    $('#object-details-modal').modal("show");
                }
            }.bind(this));
            return this;
        }
        naja.registerExtension(ModalExtension);
    </script>

    <script>
        function openContactDetails() {
            {if (!empty($linkToContact))}
                window.open({$linkToContact}, 'name');
            {/if}
        }
    </script>


{/block}
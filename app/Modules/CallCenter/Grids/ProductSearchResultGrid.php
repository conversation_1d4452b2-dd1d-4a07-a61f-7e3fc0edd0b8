<?php declare(strict_types = 1);

namespace App\Modules\CallCenter\Grids;

use App\Common\Components\Grids\BaseGrid;
use App\Common\ProductInfo\ProductPriceResolver;
use App\Common\ProductInfo\ProductSortedPictures;
use App\Common\ProductSearch;
use App\Models\Entities\custom\PriceParameterEntity;
use App\Repositories\ProductRepository;
use Nette\Forms\Form;
use Nette\Http\SessionSection;
use Ublaboo\DataGrid\Column\Column;

class ProductSearchResultGrid extends BaseGrid
{

	/**
	 * @var SessionSection
	 */
	private $sessionSection;

	/**
	 * @var ProductSortedPictures
	 */
	private $sortedPictures;

	/**
	 * @var ProductSearch
	 */
	private $productSearch;

	private ProductRepository $productRepository;

	private ProductPriceResolver $productPriceResolver;

	public function __construct(
		ProductSortedPictures $sortedPictures,
		ProductSearch $productSearch,
		ProductRepository $productRepository,
		ProductPriceResolver $productPriceResolver
	)
	{
		$this->sortedPictures = $sortedPictures;
		$this->productSearch = $productSearch;
		$this->onAnchor[] = function ($presenter) {
			$this->gridAttached($presenter);
		};
		parent::__construct();
		$this->productRepository = $productRepository;
		$this->productPriceResolver = $productPriceResolver;
	}

	public function prepare(): void
	{
		if (!$this->getPresenter()->getParameter('do')) {
			$searchForm = $this->getPresenter()->getComponent('searchForm');
			$searchForm->search();
		}
		$this->setTemplateFile(__DIR__ . '/templates/grid.latte');
		$this->setOuterFilterRendering(true);

		//hack (items per page)
		$this->setDefaultPerPage(100);
		$this->setItemsPerPageList([25, 50, 100, 200, 400]);
		$this->saveSessionData('_grid_perPage', $this->perPage);

		if ($this->getPresenter()->getParameter('do') === 'searchResultGrid-filter-submit') {
			/** @var Form $filterForm */
			$filterForm = $this->getComponent('filter');
			$filterValues = $filterForm->getUnsafeValues();
			$this->saveSessionData('_grid_perPage', $filterValues['perPage']);
			$this->perPage = $filterValues['perPage'];
		}

		$this->sessionSection = $this->getPresenter()->session->getSection($this->getPresenter()->getParameter('search_id'));
		$dataSource = $this->sessionSection['results'];

		self::$formMethod = 'post';
		$this->setAutoSubmit(FALSE)->setRememberState(FALSE);
		$this->setItemsPerPageList([25, 50, 100, 200, 400], FALSE)->findDefaultPerPage();
		$this->setColumnsHideable();
		$this->setPrimaryKey('product_id');
		$this->setDataSource($dataSource);
		$this->setTemplatePictures($dataSource);
		$this->setDefaultSort('product_id');
		$this->addColumnText('thumbnail', 'Img')->setTemplate(__DIR__ . '/templates/thumbnail.latte');
		$this->addColumnText('propertyID', $this->getTranslator()->translate('ID kuće'))->setSortable();
		$this->addColumnLink('location', $this->getTranslator()->translate('Vila'), 'ProductDetails:Details')
			->setSortable()->setOpenInNewTab();
		$this->addColumnText('quality', $this->getTranslator()->translate('Kategorija'))
			->setSortable();
		$this->addColumnText('swimming_pool', $this->getTranslator()->translate('Bazen'))
			->setSortable()->setReplacement(['1' => 'Y', '0' => 'N']);
		$this->addColumnText('people_number', $this->getTranslator()->translate('Kapacitet'))
			->setSortable();
		$this->addColumnText('pool_size', 'Površina bazena')->setSortable();
		$this->addColumnText('parcel_unit_size', 'Površina parcele')->setSortable();
		$this->addColumnText('modernized_year', 'Godina moderniziranja')->setSortable();
		$this->addColumnText('build_year', 'Godina izgradnje')->setSortable();
		$this->addColumnText('total_price', 'Cijena')->setSortable();
		$this->addColumnText('partner', 'Partner')->setSortable();

		$this->getColumn('total_price')
			->setRenderer(function ($item) {
				if (empty($item['total_price'])) {
					return 'Not available!';
				}

				return (int) $item['total_price'] . ' €';
			});

		$this->addAction('addFavorite', $this->getTranslator()->translate('Favorite'), 'addFavorite!', ['productId' => 'product_id'])
			->setDataAttribute('naja-history', 'off')
			->setClass('btn btn-xs btn-default ajax')->setIcon('star');

		$this->addAction('product_details_modal', 'Details', 'showModal!', ['productId' => 'product_id'])
			->setDataAttribute('naja-history', 'off')
			->setClass('btn btn-xs btn-default ajax')
			->setIcon('info');

		$this->addActionCallback('checkPrice', 'Check price')
			->setClass('btn btn-xs btn-default ajax')
			->setIcon('euro-sign')
			->onClick[] = function ($item_id): void {
				$price = $this->checkPrice((int) $item_id);
				$dataSource = $this->sessionSection['results'];
				$dataSource[$item_id]['total_price'] = $price;
				$this->redrawControl('grid');
			};
	}

	public function checkPrice(int $productId): ?int
	{
		$propertyId = $this->productRepository->getPropertyIdByProductId($productId);
		$arriveDate = $this->sessionSection->values['arrive'];
		$departureDate = $this->sessionSection->values['departure'];
		$adults = (int) $this->sessionSection->values['adultsCount'];
		$children = (int) $this->sessionSection->values['childrenCount'];

		$priceParameterEntity = new PriceParameterEntity();
		$priceParameterEntity ->productId = $productId;
		$priceParameterEntity ->propertyID = $propertyId;
		$priceParameterEntity ->arrival = $arriveDate;
		$priceParameterEntity ->departure = $departureDate;
		$priceParameterEntity ->adultsCount = $adults;
		$priceParameterEntity ->childrenCount = $children;
		$priceParameterEntity ->salesmarket = 999;

		$priceChecked = $this->productPriceResolver->resolve($productId, $priceParameterEntity);

		return $priceChecked->price;
	}


	public function getSortNext(Column $column): array
	{
		$sort = $column->getSortNext();
		$key = key($sort);
		$sortType = reset($sort);
		$newSortType = $this->getSortType($sortType);
		$newSort = [$key => $newSortType];

		if ($this->isMultiSortEnabled()) {
			$newSort = array_merge($this->sort, $newSort);
		}

		return $newSort;
	}

	/**
	 * hack!!
	 */
	private function getSortType($sort): string
	{
		if ($sort === 'ASC') {
			return 'DESC';
		} elseif ($sort === 'DESC') {
			return 'ASC';
		}

		return 'ASC';
	}


	private function setTemplatePictures($dataSource)
	{
		$offset = null;

		if ($this->getPaginator() !== null) {
			$offset = $this->getPaginator()->getPaginator()->offset;
		}

		$productIdsOnPage = $this->setProductIdsOnPage($dataSource, $offset, $this->sort);
		$sortedPictures = $this->sortedPictures->getSortedProductsPictures($productIdsOnPage, TRUE);
		$picArray = [];

		foreach ($sortedPictures as $productId => $pictures) {
			$picArray[$productId] = [];
			$i = 0;

			foreach ($pictures as $pic) {
				$picArray[$productId][$i] = $pic;
				$i++;
			}
		}
		$this->template->pictures = $picArray;
	}


	private function setProductIdsOnPage($dataSource, ?int $offset, $sort): array
	{
		if (!empty($sort)) {
			$key = array_keys($sort)[0];
			$value = reset($sort);
			$dataSource = $this->productSearch->getVsalesResults($this->sessionSection['values'], $key, $value);
			$this->setDataSource($dataSource);
		}

		$productIdsOnPage = [];

		if ($offset === null) {
			$productIdsOnPage = array_keys($dataSource);
		} else {
			$products = array_slice($dataSource, $offset, $this->getPerPage());

			foreach ($products as $product) {
				$productIdsOnPage[] = $product['product_id'];
			}

			if (empty($productIdsOnPage)) {
				$productIdsOnPage = array_keys($dataSource);
			}
		}

		return $productIdsOnPage;
	}

}

<?php declare(strict_types = 1);

namespace App\Common\Avantio;

use App\Common\Avantio\Type\Accommodation;
use App\Common\Avantio\Type\Credentials;
use App\Common\Logger\ActionLog;
use App\Models\Entities\ProductBasicInfoEntity;
use Nette\DI\Attributes\Inject;
use Psr\Log\LoggerInterface;

class BaseAvantio
{

	private array $config;

	private AvantioClientFactory $clientFactory;

	private LoggerInterface $logger;

	private ?AvantioClient $client = null;

	#[Inject]
	private ActionLog $actionLog;

	public function setConfig(array $config): void
	{
		$this->config = $config;
	}

	public function setClientFactory(AvantioClientFactory $clientFactory): void
	{
		$this->clientFactory = $clientFactory;
	}

	public function setLogger(LoggerInterface $logger): void
	{
		$this->logger = $logger;
	}

	public function getLogger(): LoggerInterface
	{
		return $this->logger;
	}

	public function getClient(): AvantioClient
	{
		if ($this->client === null) {
			$this->client = $this->clientFactory->create();
		}

		return $this->client;
	}

	public function getAccommodation(ProductBasicInfoEntity $productBasicInfoEntity): Accommodation
	{
		return new Accommodation($productBasicInfoEntity->foreign_id, $productBasicInfoEntity->id);
	}

	public function getAccommodationByParameters(int $foreignId, ?int $productId = null): Accommodation
	{
		return new Accommodation($foreignId, $productId);
	}

	public function getCredentials(): Credentials
	{
		$config = $this->config['credentials'];

		return new Credentials($config['username'], $config['password']);
	}

	public function getPropertyValue(mixed $object, string $property): mixed
	{
		if (!isset($object->$property)) {
			return null;
		}

		return $object->$property;
	}

	public function setActionLog(ActionLog $actionLog): void
	{
		$this->actionLog = $actionLog;
	}

	public function getActionLog(): ActionLog
	{
		return $this->actionLog;
	}

}

<?php declare(strict_types = 1);

namespace App\Common\Avantio;

use App\Common\AvailabilityStatusChangedEvent;
use App\Common\Avantio\Enums\BookingTypeEnum;
use App\Common\Avantio\Type\CancelBookingRQ;
use App\Common\Avantio\Type\CancelBookingRS;
use App\Common\Avantio\Type\GetBookingNotificationsRQ;
use App\Common\Avantio\Type\GetBookingRQ;
use App\Common\Avantio\Type\GetBookingRS;
use App\Common\Avantio\Type\Localizer;
use App\Common\Avantio\Type\UpdateBookingRQ;
use App\Common\Enums\ActionLogDbTableEnum;
use App\Common\Enums\AvailabilityStatusEnum;
use App\Common\EnvironmentDetector;
use App\Models\Entities\custom\ReservationEventEntity;
use App\Models\Entities\ForeignReservationEntity;
use App\Models\Entities\ProductEntity;
use App\Modules\Api\Logic\BookingLogic;
use App\Modules\Property\Logic\ForeignReservationHistoryLogic;
use App\Modules\Property\Logic\ForeignReservationLogic;
use App\Modules\Property\Logic\ForeignReservationPaymentLogic;
use App\Modules\Property\Logic\ForeignReservationServiceLogic;
use App\Modules\Property\Logic\ReservationLogic;
use App\Repositories\ProductRepository;
use Nette\Utils\Strings;
use Nette\Utils\Validators;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;
use Wedo\Api\Exceptions\ResponseException;

class BookingManager extends BaseAvantio
{

	private ProductRepository $productRepository;

	private ForeignReservationLogic $foreignReservationLogic;

	private ForeignReservationPaymentLogic $foreignReservationPaymentLogic;

	private ForeignReservationServiceLogic $foreignReservationServiceLogic;

	private EventDispatcherInterface $dispatcher;

	private EnvironmentDetector $environmentDetector;

	private BookingLogic $bookingLogic;

	private LoggerInterface $logger;

	private ReservationLogic $reservationLogic;

	private ForeignReservationHistoryLogic $foreignReservationHistoryLogic;

	public function __construct(
		ProductRepository $productRepository,
		ForeignReservationLogic $foreignReservationsLogic,
		ForeignReservationPaymentLogic $foreignReservationPaymentLogic,
		ForeignReservationServiceLogic $foreignReservationServiceLogic,
		EventDispatcherInterface $dispatcher,
		EnvironmentDetector $environmentDetector,
		BookingLogic $bookingLogic,
		LoggerInterface $logger,
		ReservationLogic $reservationLogic,
		ForeignReservationHistoryLogic $foreignReservationHistoryLogic
	)
	{
		$this->productRepository = $productRepository;
		$this->foreignReservationLogic = $foreignReservationsLogic;
		$this->foreignReservationPaymentLogic = $foreignReservationPaymentLogic;
		$this->foreignReservationServiceLogic = $foreignReservationServiceLogic;
		$this->dispatcher = $dispatcher;
		$this->environmentDetector = $environmentDetector;
		$this->bookingLogic = $bookingLogic;
		$this->logger = $logger;
		$this->reservationLogic = $reservationLogic;
		$this->foreignReservationHistoryLogic = $foreignReservationHistoryLogic;
	}

	public function confirmBooking(int $id): void
	{
		/** @var ForeignReservationEntity $foreignEntity */
		$foreignEntity = $this->foreignReservationLogic->getRepository()->getBy([ForeignReservationEntity::ID => $id]);

		if ($foreignEntity === null) {
			throw new ResponseException('Not found!', 400);
		}

		if ($foreignEntity->booking_type === BookingTypeEnum::CONFIRMED) {
			return;
		}

		$localizer = new Localizer();
		$localizer->setBookingCode($foreignEntity->booking_code);
		$localizer->setLocalizator($foreignEntity->localizator);
		$localizer->setAgentLocalizator($foreignEntity->agent_localizator);
		$request = new UpdateBookingRQ($this->getCredentials(), $localizer, 'CONFIRMED');
		$response = $this->getClient()->updateBooking($request);

		if (!$response->getSucceed()) {
			$errors = $response->getErrorList()->getError();
			$messages = null;

			foreach ($errors as $error) {
				$messages .= 'ErrorId: ' . $error->getErrorId() . ' ' . $error->getErrorMessage();
			}

			$details = 'Booking successfully error: ' . $messages;
			$severity = 'error';

			$this->getActionLog()->log('confirm_booking', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $foreignEntity->id, $details, $severity);

			throw new ResponseException($messages, 400);
		} else {
			$severity = 'success';
			$details = 'Booking successfully confirmed on Avantio';

			$this->getActionLog()->log('confirm_booking', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $foreignEntity->id, $details, $severity);
		}
	}

	public function cancelBooking(int $id): CancelBookingRS
	{
		/** @var ForeignReservationEntity $foreignEntity */
		$foreignEntity = $this->foreignReservationLogic->getRepository()->getBy([ForeignReservationEntity::ID => $id]);

		if ($foreignEntity === null) {
			throw new ResponseException('Not found!', 400);
		}

		$localizer = new Localizer();
		$localizer->setBookingCode($foreignEntity->booking_code);
		$localizer->setLocalizator($foreignEntity->localizator);
		$localizer->setAgentLocalizator($foreignEntity->agent_localizator);
		$request = new CancelBookingRQ($this->getCredentials(), $localizer, $foreignEntity->total_price);

		return $this->getClient()->cancelBooking($request);
	}

	public function checkNotifications(): void
	{
		$request = new GetBookingNotificationsRQ($this->getCredentials());
		$response = $this->getClient()->getBookingNotifications($request);

		if (!isset($response->Localizer)) {
			return;
		}

		foreach ($response->Localizer as $localizer) {
			$request = new GetBookingRQ($this->getCredentials(), $localizer);
			$response = $this->getClient()->getBooking($request);

			if ($response->getLocalizer() === null) {
				$this->getLogger()->error('Missing booking references');

				continue;
			}

			$bookingDetails = $response->getBookingDetails();
			$foreignId = $bookingDetails->getAccommodation()->getAccommodationCode();

			/** @var ProductEntity $product */
			$product = $this->productRepository->getBy(['foreign_id' => $foreignId]);

			if ($product === null) {
				continue;
			}

			$foreignReservation = $this->saveForeignReservation($response, $product->id);

			if ($foreignReservation === null) {
				return;
			}

			if (in_array($foreignReservation->web, ['Traum-ferienwohnungen.de', 'Ferienhausmiete.de'])) {
				return;
			}

			$bookingStatusList = [
				BookingTypeEnum::CONFIRMED,
				BookingTypeEnum::BILLED,
				BookingTypeEnum::PAID,
				BookingTypeEnum::UNPAID,
			];

			if ($bookingDetails->getBookingType() === BookingTypeEnum::CANCELLED) {
				$this->handleCancelledBooking($foreignReservation);

				return;
			}

			if (!in_array($foreignReservation->booking_type, $bookingStatusList)) {
				return;
			}

			$this->createReservation($foreignReservation->id);
		}
	}

	private function saveForeignReservation(GetBookingRS $response, int $productId): ?ForeignReservationEntity
	{
		$localizer = $response->getLocalizer();
		$foreignReservation = $this->foreignReservationLogic->getRepository()->getBy([
			ForeignReservationEntity::BOOKING_CODE => $localizer->getBookingCode(),
			ForeignReservationEntity::PRODUCT_ID => $productId,
		]);

		if ($foreignReservation === null) {
			return $this->createNewForeignReservation($response, $productId);
		}

		//this means no changes on reservation were made
		if ($foreignReservation->last_modified_date->format('Y-m-d H:i:s') === $response->getBookingDetails()->getLastModifyDateTime()->format('Y-m-d H:i:s')) {
			return null;
		}

		return $this->handleExistingReservation($foreignReservation, $response);
	}

	private function getFixedEmail(ForeignReservationEntity $foreignReservation): string
	{
		if (Validators::isEmail($foreignReservation->email ?? '')) {
			return $foreignReservation->email;
		}

		$name = Strings::trim($foreignReservation->name ?? 'f');
		$surname = Strings::trim($foreignReservation->surname ?? '');

		return Strings::lower($name . $surname) . '@villas-guide.com';
	}

	public function openAvailabilities(int $id): void
	{
		/** @var ForeignReservationEntity $foreignEntity */
		$foreignEntity = $this->foreignReservationLogic->getRepository()->getBy([ForeignReservationEntity::ID => $id]);

		$reservationEventEntity = new ReservationEventEntity();
		$reservationEventEntity->status = AvailabilityStatusEnum::A;
		$reservationEventEntity->product_id = $foreignEntity->product_id;
		$reservationEventEntity->arrive = $foreignEntity->arrive;
		$reservationEventEntity->departure = $foreignEntity->departure;
		$this->dispatcher->dispatch(new AvailabilityStatusChangedEvent($reservationEventEntity));
	}

	public function createReservation(int $id, bool $sendPreBookingMail = true): void
	{
		try {
			$bookingRequest = $this->foreignReservationLogic->getBookingRequest($id);
			$bookingRequest->test = !$this->environmentDetector->isProduction();
			$this->bookingLogic->validateRequest($bookingRequest);
			$this->confirmBooking($id);
			$reservation = $this->bookingLogic->create($id, $bookingRequest, $sendPreBookingMail);
			$this->foreignReservationLogic->setReservationId($reservation->id, $id);
		} catch (\Throwable $exception) {
			$this->logger->error($exception->getMessage(), ['exception' => $exception]);
		}
	}

	private function createNewForeignReservation(GetBookingRS $response, int $productId): ?ForeignReservationEntity
	{
		if (!$this->validateBookingRequestData($response, $productId)) {
			return null;
		}

		$foreignReservation = $this->getForeignReservationData($response, $productId);

		$id = $this->foreignReservationLogic->getRepository()->insert($foreignReservation);

		if (!$id) {
			$this->getActionLog()->log(
				'Foreign reservation not created',
				ActionLogDbTableEnum::PRODUCTS,
				$productId,
				'Error while creating foreign reservation',
				'error'
			);

			return null;
		}

		$this->getActionLog()->log(
			'foreign_reservation_created',
			ActionLogDbTableEnum::FOREIGN_RESERVATIONS,
			$id,
			'Foreign reservation created'
		);

		$this->saveAdditionalForeignReservationData($id, $response);

		$foreignReservation->id = $id;

		return $foreignReservation;
	}

	private function handleExistingReservation(
		ForeignReservationEntity $foreignReservation,
		GetBookingRS $response
	): ?ForeignReservationEntity
	{
		if (!$this->validateBookingRequestData($response, $foreignReservation->product_id)) {
			return null;
		}

		$id = $this->foreignReservationHistoryLogic->archive($foreignReservation);

		if (!$id) {
			$this->getActionLog()->log(
				'Foreign reservation archive failed',
				ActionLogDbTableEnum::FOREIGN_RESERVATIONS,
				$foreignReservation->id,
				'Foreign reservation archive failed',
				'error'
			);

			return null;
		}

		$foreignReservationData = $this->getForeignReservationData($response, $foreignReservation->product_id);
		$this->foreignReservationLogic->getRepository()->update($foreignReservation->id, $foreignReservationData);

		$this->getActionLog()->log(
			'Foreign reservation updated',
			ActionLogDbTableEnum::FOREIGN_RESERVATIONS,
			$foreignReservation->id,
			'Foreign reservation updated'
		);

		$this->saveAdditionalForeignReservationData($foreignReservation->id, $response);

		return $foreignReservation;
	}

	private function getForeignReservationData(GetBookingRS $response, int $productId): ?ForeignReservationEntity
	{
		$localizer = $response->getLocalizer();
		$bookingDetails = $response->getBookingDetails();

		$client = $this->getPropertyValue($bookingDetails, 'ClientData');

		$foreignReservation = new ForeignReservationEntity();
		$foreignReservation->product_id = $productId;

		$foreignReservation->booking_language = $this->getPropertyValue($bookingDetails, 'BookingLanguage');
		$foreignReservation->board = $this->getPropertyValue($bookingDetails, 'Board');
		$foreignReservation->booking_date = $bookingDetails->getBookingDate();
		$foreignReservation->web = $this->getPropertyValue($bookingDetails, 'Web');
		$foreignReservation->arrive = $bookingDetails->getArrivalDate();
		$foreignReservation->departure = $bookingDetails->getDepartureDate();
		$foreignReservation->booking_type = $bookingDetails->getBookingType();
		$foreignReservation->payment_method = $this->getPropertyValue($bookingDetails, 'PaymentMethod');

		$occupants = $bookingDetails->getOccupants();
		$foreignReservation->adults_number = $occupants->getAdultsNumber();
		$foreignReservation->child1_age = $this->getPropertyValue($occupants, 'Child1_Age');
		$foreignReservation->child2_age = $this->getPropertyValue($occupants, 'Child2_Age');
		$foreignReservation->child3_age = $this->getPropertyValue($occupants, 'Child3_Age');
		$foreignReservation->child4_age = $this->getPropertyValue($occupants, 'Child4_Age');
		$foreignReservation->child5_age = $this->getPropertyValue($occupants, 'Child5_Age');
		$foreignReservation->child6_age = $this->getPropertyValue($occupants, 'Child6_Age');

		$foreignReservation->name = $this->getPropertyValue($client, 'Name');
		$foreignReservation->surname = $this->getPropertyValue($client, 'Surname');
		$foreignReservation->dni = $this->getPropertyValue($client, 'DNI');
		$foreignReservation->address = $this->getPropertyValue($client, 'Address');
		$foreignReservation->locality = $this->getPropertyValue($client, 'Locality');
		$foreignReservation->post_code = $this->getPropertyValue($client, 'PostCode');
		$foreignReservation->city = $this->getPropertyValue($client, 'City');
		$foreignReservation->country = $this->getPropertyValue($client, 'Country');
		$foreignReservation->iso_country_code = $this->getPropertyValue($client, 'IsoCountryCode');
		$foreignReservation->telephone = $this->getPropertyValue($client, 'Telephone');
		$foreignReservation->telephone2 = $this->getPropertyValue($client, 'Telephone2');
		$foreignReservation->email = $this->getPropertyValue($client, 'EMail');
		$foreignReservation->fax = $this->getPropertyValue($client, 'Fax');
		$foreignReservation->language = $this->getPropertyValue($client, 'Language');
		$foreignReservation->fiscal_code = $this->getPropertyValue($client, 'FiscalCode');

		$foreignReservation->check_in_done = $this->getPropertyValue($bookingDetails, 'CheckInDone');
		$foreignReservation->check_in_schedule = $this->getPropertyValue($bookingDetails, 'CheckInSchedule');
		$foreignReservation->check_out_schedule = $this->getPropertyValue($bookingDetails, 'CheckOutSchedule');
		$foreignReservation->creation_date = $this->getPropertyValue($bookingDetails, 'CreationDateTime');
		$foreignReservation->last_modified_date = $this->getPropertyValue($bookingDetails, 'LastModifyDateTime');

		$foreignReservation->booking_code = $localizer->getBookingCode();
		$foreignReservation->localizator = $localizer->getLocalizator();
		$foreignReservation->agent_localizator = $this->getPropertyValue($localizer, 'AgentLocalizator');

		$accommodation = $bookingDetails->getAccommodation();
		$foreignReservation->accommodation_code = $accommodation->getAccommodationCode();

		$foreignReservation->comments = $this->getPropertyValue($bookingDetails, 'Comment');
		$foreignReservation->comments_date = $this->getPropertyValue($bookingDetails, 'CommentsDate');

		$amounts = $response->getBookingAmounts();

		if ($amounts !== null) {
			$foreignReservation->total_price = $amounts->getTotalPrice();
			$foreignReservation->rental_price = $amounts->getRentalPrice();
			$foreignReservation->currency = $amounts->getCurrency();
		}

		$foreignReservation->email = $this->getFixedEmail($foreignReservation);
		$foreignReservation->declined = false;

		return $foreignReservation;
	}

	private function validateBookingRequestData(GetBookingRS $response, int $productId): bool
	{
		$localizer = $response->getLocalizer();
		$bookingDetails = $response->getBookingDetails();

		$client = $this->getPropertyValue($bookingDetails, 'ClientData');

		if ($client === null) {
			$this->getActionLog()->log(
				'Foreign reservation client data missing',
				ActionLogDbTableEnum::PRODUCTS,
				$productId,
				'Foreign reservation client data missing, booking_code: ' . $localizer->getBookingCode()
			);

			return false;
		}

		return true;
	}

	private function saveAdditionalForeignReservationData(int $id, GetBookingRS $response): void
	{
		$bookingServices = null;

		$amounts = $response->getBookingAmounts();

		if ($amounts !== null) {
			$bookingServices = $this->getPropertyValue($amounts, 'Services');
		}
		$payments = $response->getBookingPayments();

		$this->foreignReservationServiceLogic->saveBookingServices($id, $bookingServices);
		$this->foreignReservationPaymentLogic->saveBookingPayments($id, $payments);
	}

	private function handleCancelledBooking(ForeignReservationEntity $foreignReservation): void
	{
		if ($foreignReservation->declined) {
			return;
		}

		$reservationId = $this->foreignReservationLogic->getReservationId($foreignReservation->reservation_id);

		if ($reservationId !== null) {
			$this->reservationLogic->delete($reservationId);
		} else {
			$this->openAvailabilities($foreignReservation->id);
		}

		$this->foreignReservationLogic->markAsDeclined($foreignReservation->id);
		$this->getActionLog()->log(
			'Foreign reservation cancelled',
			ActionLogDbTableEnum::FOREIGN_RESERVATIONS,
			$foreignReservation->id,
			'Foreign reservation cancelled'
		);
	}

}

<?php declare(strict_types = 1);

namespace App\Common\Avantio;

use App\Common\AvailabilityStatusChangedEvent;
use App\Common\Avantio\Enums\BookingTypeEnum;
use App\Common\Avantio\Type\CancelBookingRQ;
use App\Common\Avantio\Type\GetBookingNotificationsRQ;
use App\Common\Avantio\Type\GetBookingRQ;
use App\Common\Avantio\Type\GetBookingRS;
use App\Common\Avantio\Type\Localizer;
use App\Common\Avantio\Type\UpdateBookingRQ;
use App\Common\Enums\ActionLogDbTableEnum;
use App\Common\Enums\AvailabilityStatusEnum;
use App\Common\Logic\ReservationValidatorLogic;
use App\Models\Entities\custom\ReservationEventEntity;
use App\Models\Entities\ForeignReservationEntity;
use App\Models\Entities\ForeignReservationPaymentEntity;
use App\Models\Entities\ForeignReservationServiceEntity;
use App\Models\Entities\ProductEntity;
use App\Repositories\ForeignReservationPaymentsRepository;
use App\Repositories\ForeignReservationServicesRepository;
use App\Repositories\ForeignReservationsRepository;
use App\Repositories\ProductRepository;
use Nette\Utils\Strings;
use Nette\Utils\Validators;
use Psr\EventDispatcher\EventDispatcherInterface;
use Wedo\Api\Exceptions\ResponseException;

class BookingManager extends BaseAvantio
{

	private ProductRepository $productRepository;

	private ForeignReservationsRepository $foreignReservationsRepository;

	private ForeignReservationPaymentsRepository $foreignReservationPaymentsRepository;

	private ForeignReservationServicesRepository $foreignReservationServicesRepository;

	private EventDispatcherInterface $dispatcher;

	private ReservationValidatorLogic $reservationValidatorLogic;

	public function __construct(
		ProductRepository $productRepository,
		ForeignReservationsRepository $foreignReservationsRepository,
		ForeignReservationPaymentsRepository $foreignReservationPaymentsRepository,
		ForeignReservationServicesRepository $foreignReservationServicesRepository,
		EventDispatcherInterface $dispatcher,
		ReservationValidatorLogic $reservationValidatorLogic,
	)
	{
		$this->productRepository = $productRepository;
		$this->foreignReservationsRepository = $foreignReservationsRepository;
		$this->foreignReservationPaymentsRepository = $foreignReservationPaymentsRepository;
		$this->foreignReservationServicesRepository = $foreignReservationServicesRepository;
		$this->dispatcher = $dispatcher;
		$this->reservationValidatorLogic = $reservationValidatorLogic;
	}

	public function confirmBooking(int $id): void
	{
		/** @var ForeignReservationEntity $foreignEntity */
		$foreignEntity = $this->foreignReservationsRepository->getBy([ForeignReservationEntity::ID => $id]);

		if ($foreignEntity === null) {
			throw new ResponseException('Not found!', 400);
		}

		if ($foreignEntity->booking_type === BookingTypeEnum::CONFIRMED) {
			return;
		}

		$localizer = new Localizer();
		$localizer->setBookingCode($foreignEntity->booking_code);
		$localizer->setLocalizator($foreignEntity->localizator);
		$localizer->setAgentLocalizator($foreignEntity->agent_localizator);
		$request = new UpdateBookingRQ($this->getCredentials(), $localizer, 'CONFIRMED');
		$response = $this->getClient()->updateBooking($request);

		if (!$response->getSucceed()) {
			$errors = $response->getErrorList()->getError();
			$messages = null;

			foreach ($errors as $error) {
				$messages .= 'ErrorId: ' . $error->getErrorId() . ' ' . $error->getErrorMessage();
			}

			$details = 'Booking successfully error: ' . $messages;
			$severity = 'error';

			$this->getActionLog()->log('confirm_booking', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $foreignEntity->id, $details, $severity);

			throw new ResponseException($messages, 400);
		} else {
			$severity = 'success';
			$details = 'Booking successfully confirmed on Avantio';

			$this->getActionLog()->log('confirm_booking', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $foreignEntity->id, $details, $severity);
		}
	}

	public function cancelBooking(int $id): bool
	{
		/** @var ForeignReservationEntity $foreignEntity */
		$foreignEntity = $this->foreignReservationsRepository->getBy([ForeignReservationEntity::ID => $id]);

		if ($foreignEntity === null) {
			throw new ResponseException('Not found!', 400);
		}

		$localizer = new Localizer();
		$localizer->setBookingCode($foreignEntity->booking_code);
		$localizer->setLocalizator($foreignEntity->localizator);
		$localizer->setAgentLocalizator($foreignEntity->agent_localizator);
		$request = new CancelBookingRQ($this->getCredentials(), $localizer, $foreignEntity->total_price);
		$response = $this->getClient()->cancelBooking($request);

		return $response->getSucceed();
	}

	public function checkNotifications(): void
	{
		$request = new GetBookingNotificationsRQ($this->getCredentials());
		$response = $this->getClient()->getBookingNotifications($request);

		if (!isset($response->Localizer)) {
			return;
		}

		foreach ($response->Localizer as $localizer) {
			$request = new GetBookingRQ($this->getCredentials(), $localizer);
			$response = $this->getClient()->getBooking($request);

			if ($response->getLocalizer() === null) {
				$this->getLogger()->error('Missing booking references');

				continue;
			}

			$bookingDetails = $response->getBookingDetails();
			$foreignId = $bookingDetails->getAccommodation()->getAccommodationCode();

			/** @var ProductEntity $product */
			$product = $this->productRepository->getBy(['foreign_id' => $foreignId]);

			if ($product === null) {
				continue;
			}

			$foreignReservation = $this->saveForeignReservation($response, $product->id);

			if ($foreignReservation === null) {
				return;
			}

			if (in_array($foreignReservation->web, ['Traum-ferienwohnungen.de', 'Ferienhausmiete.de'])) {
				return;
			}

			$bookingStatusList = [
				BookingTypeEnum::CONFIRMED,
				BookingTypeEnum::BILLED,
				BookingTypeEnum::PAID,
				BookingTypeEnum::UNPAID,
				BookingTypeEnum::BOOKING_REQUEST,
			];

			if ($bookingDetails->getBookingType() === BookingTypeEnum::CANCELLED) {
				$this->getLogger()->info('Accommodation code: ' . $bookingDetails->getAccommodation()->getAccommodationCode()
					. ', Distribution: ' . $bookingDetails->getWeb()
					. ', Arrival: ' . $bookingDetails->getArrivalDate()->format('Y-m-d')
					. ', Departure: ' . $bookingDetails->getDepartureDate()->format('Y-m-d H:i:s')
					. ', creation_date: ' . $bookingDetails->getCreationDateTime()->format('Y-m-d H:i:s')
					. ', last_modified_date: ' . $bookingDetails->getLastModifyDateTime()->format('Y-m-d H:i:s'));
			}

			if (!in_array($foreignReservation->booking_type, $bookingStatusList)) {
				return;
			}

			$this->closeAvailabilities($foreignReservation);
		}
	}

	private function saveForeignReservation(GetBookingRS $response, int $productId): ?ForeignReservationEntity
	{
		$localizer = $response->getLocalizer();
		$bookingDetails = $response->getBookingDetails();
		$reservationExists = $this->foreignReservationsRepository->reservationExists(
			$localizer->getBookingCode(),
			$productId,
			$bookingDetails->getLastModifyDateTime()
		);

		if ($reservationExists) {
			return null;
		}

		$foreignReservation = new ForeignReservationEntity();
		$foreignReservation->product_id = $productId;

		$foreignReservation->booking_language = $this->getPropertyValue($bookingDetails, 'BookingLanguage');
		$foreignReservation->board = $this->getPropertyValue($bookingDetails, 'Board');
		$foreignReservation->booking_date = $bookingDetails->getBookingDate();
		$foreignReservation->web = $this->getPropertyValue($bookingDetails, 'Web');
		$foreignReservation->arrive = $bookingDetails->getArrivalDate();
		$foreignReservation->departure = $bookingDetails->getDepartureDate();
		$foreignReservation->booking_type = $bookingDetails->getBookingType();
		$foreignReservation->payment_method = $this->getPropertyValue($bookingDetails, 'PaymentMethod');

		$occupants = $bookingDetails->getOccupants();
		$foreignReservation->adults_number = $occupants->getAdultsNumber();
		$foreignReservation->child1_age = $this->getPropertyValue($occupants, 'Child1_Age');
		$foreignReservation->child2_age = $this->getPropertyValue($occupants, 'Child2_Age');
		$foreignReservation->child3_age = $this->getPropertyValue($occupants, 'Child3_Age');
		$foreignReservation->child4_age = $this->getPropertyValue($occupants, 'Child4_Age');
		$foreignReservation->child5_age = $this->getPropertyValue($occupants, 'Child5_Age');
		$foreignReservation->child6_age = $this->getPropertyValue($occupants, 'Child6_Age');

		$client = $this->getPropertyValue($bookingDetails, 'ClientData');

		if ($client === null) {
			return null;
		}

		$foreignReservation->name = $this->getPropertyValue($client, 'Name');
		$foreignReservation->surname = $this->getPropertyValue($client, 'Surname');
		$foreignReservation->dni = $this->getPropertyValue($client, 'DNI');
		$foreignReservation->address = $this->getPropertyValue($client, 'Address');
		$foreignReservation->locality = $this->getPropertyValue($client, 'Locality');
		$foreignReservation->post_code = $this->getPropertyValue($client, 'PostCode');
		$foreignReservation->city = $this->getPropertyValue($client, 'City');
		$foreignReservation->country = $this->getPropertyValue($client, 'Country');
		$foreignReservation->iso_country_code = $this->getPropertyValue($client, 'IsoCountryCode');
		$foreignReservation->telephone = $this->getPropertyValue($client, 'Telephone');
		$foreignReservation->telephone2 = $this->getPropertyValue($client, 'Telephone2');
		$foreignReservation->email = $this->getPropertyValue($client, 'EMail');
		$foreignReservation->fax = $this->getPropertyValue($client, 'Fax');
		$foreignReservation->language = $this->getPropertyValue($client, 'Language');
		$foreignReservation->fiscal_code = $this->getPropertyValue($client, 'FiscalCode');

		$foreignReservation->check_in_done = $this->getPropertyValue($bookingDetails, 'CheckInDone');
		$foreignReservation->check_in_schedule = $this->getPropertyValue($bookingDetails, 'CheckInSchedule');
		$foreignReservation->check_out_schedule = $this->getPropertyValue($bookingDetails, 'CheckOutSchedule');
		$foreignReservation->creation_date = $this->getPropertyValue($bookingDetails, 'CreationDateTime');
		$foreignReservation->last_modified_date = $this->getPropertyValue($bookingDetails, 'LastModifyDateTime');

		$foreignReservation->booking_code = $localizer->getBookingCode();
		$foreignReservation->localizator = $localizer->getLocalizator();
		$foreignReservation->agent_localizator = $this->getPropertyValue($localizer, 'AgentLocalizator');

		$accommodation = $bookingDetails->getAccommodation();
		$foreignReservation->accommodation_code = $accommodation->getAccommodationCode();

		$foreignReservation->comments = $this->getPropertyValue($bookingDetails, 'Comment');
		$foreignReservation->comments_date = $this->getPropertyValue($bookingDetails, 'CommentsDate');

		$amounts = $response->getBookingAmounts();
		$bookingServices = null;

		if ($amounts !== null) {
			$foreignReservation->total_price = $amounts->getTotalPrice();
			$foreignReservation->rental_price = $amounts->getRentalPrice();
			$foreignReservation->currency = $amounts->getCurrency();
			$bookingServices = $this->getPropertyValue($amounts, 'Services');
		}
		$foreignReservation->email = $this->getFixedEmail($foreignReservation);
		$id = $this->foreignReservationsRepository->insert($foreignReservation);
		$this->getActionLog()->log('foreign_reservation_created', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $id, 'Foreign reservation created');

		$this->saveBookingServices($id, $bookingServices);
		$payments = $response->getBookingPayments();

		if ($payments === null) {
			return null;
		}

		$this->saveBookingPayments($id, $payments);
		$foreignReservation->id = $id;

		return $foreignReservation;
	}

	private function saveBookingServices(int $id, ?Type\Services $bookingServices): void
	{
		if (empty($bookingServices)) {
			return;
		}

		$services = $this->getPropertyValue($bookingServices, 'Service');

		if (empty($services)) {
			return;
		}

		foreach ($services as $service) {
			$entity = new ForeignReservationServiceEntity();
			$entity->code = $service->getCode();
			$entity->foreign_id = $id;
			$entity->name = $this->getPropertyValue($service, 'Name');
			$entity->amount = $service->getAmount();
			$entity->price = $service->getPrice();
			$entity->category = $this->getPropertyValue($service, 'Category');
			$entity->application_date = $service->getApplicationDate();

			if (empty($entity->name) || empty($entity->price)) {
				continue;
			}

			$this->foreignReservationServicesRepository->insert($entity);
		}
	}

	private function saveBookingPayments(int $id, Type\BookingPayments $payments): void
	{
		$payments = $this->getPropertyValue($payments, 'BookingPayment');

		if ($payments === null) {
			return;
		}

		foreach ($payments as $payment) {
			$entity = new ForeignReservationPaymentEntity();
			$entity->foreign_id = $id;
			$entity->payment_date = $this->getPropertyValue($payment, 'PaymentDate');
			$entity->amount = $this->getPropertyValue($payment, 'Amount');
			$entity->payment_method = $this->getPropertyValue($payment, 'PaymentMethod');
			$entity->payment_status = $this->getPropertyValue($payment, 'PaymentStatus');
			$entity->security_deposit = $this->getPropertyValue($payment, 'SecurityDeposit') === 'true';
			$entity->payment_id = $payment->getPaymentId();

			$this->foreignReservationPaymentsRepository->insert($entity);
		}
	}

	private function closeAvailabilities(ForeignReservationEntity $foreignReservation): void
	{
		$activeReservation = $this->reservationValidatorLogic->getActiveReservationForDates(
			$foreignReservation->product_id,
			$foreignReservation->arrive->format('Y-m-d'),
			$foreignReservation->departure->format('Y-m-d')
		);

		if ($activeReservation !== null) {
			$this->getLogger()->error('Reservation exist in DB for this period ID: ' . $foreignReservation->id);

			return;
		}

		$reservationRule = new ReservationEventEntity();
		$reservationRule->status = AvailabilityStatusEnum::X;
		$reservationRule->product_id = $foreignReservation->product_id;
		$reservationRule->arrive = $foreignReservation->arrive;
		$reservationRule->departure = $foreignReservation->departure;
		$this->dispatcher->dispatch(new AvailabilityStatusChangedEvent($reservationRule));
	}

	private function getFixedEmail(ForeignReservationEntity $foreignReservation): string
	{
		if (Validators::isEmail($foreignReservation->email ?? '')) {
			return $foreignReservation->email;
		}

		$name = Strings::trim($foreignReservation->name ?? 'f');
		$surname = Strings::trim($foreignReservation->surname ?? '');

		return Strings::lower($name . $surname) . '@villas-guide.com';
	}

	public function openAvailabilities(int $id): void
	{
		/** @var ForeignReservationEntity $foreignEntity */
		$foreignEntity = $this->foreignReservationsRepository->getBy([ForeignReservationEntity::ID => $id]);

		$reservationEventEntity = new ReservationEventEntity();
		$reservationEventEntity->status = AvailabilityStatusEnum::A;
		$reservationEventEntity->product_id = $foreignEntity->product_id;
		$reservationEventEntity->arrive = $foreignEntity->arrive;
		$reservationEventEntity->departure = $foreignEntity->departure;
		$this->dispatcher->dispatch(new AvailabilityStatusChangedEvent($reservationEventEntity));
	}

}

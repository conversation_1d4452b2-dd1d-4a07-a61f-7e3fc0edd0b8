<?php declare(strict_types = 1);

namespace App\Common\Reservation;

use app\Common\Helpers\Strings;
use App\Models\Entities\BookingPaymentAmountConditionEntity;
use App\Models\Entities\BookingTermEntity;
use App\Models\Entities\PolicyEntity;
use App\Repositories\BookingTermsRepository;
use DOMDocument;
use DOMXPath;

class BookingConditionsFinder
{

	private BookingTermsRepository $termsRepository;

	private CancellationPolicyResolver $cancellationPolicyResolver;

	public function __construct(BookingTermsRepository $termsRepository, CancellationPolicyResolver $cancellationPolicyResolver)
	{
		$this->termsRepository = $termsRepository;
		$this->cancellationPolicyResolver = $cancellationPolicyResolver;
	}

	public function getAmountPercentages(int $productId): BookingPaymentAmountConditionEntity
	{
		$result = new BookingPaymentAmountConditionEntity();

		$policy = $this->cancellationPolicyResolver->getPolicy($productId);

		if ($policy === null || $policy->id === 7) {
			$result->first_installment_percentage = 30;
			$result->second_installment_percentage = 70;

			return $result;
		}

		$result->first_installment_percentage = 100;
		$result->second_installment_percentage = 0;

		return $result;
	}

	public function getAmountPercentagesForPartner(int $productId): BookingPaymentAmountConditionEntity
	{
		$result = new BookingPaymentAmountConditionEntity();

		$policy = $this->cancellationPolicyResolver->getPolicy($productId);

		if ($policy === null || $policy->id === 7) {
			$result->first_installment_percentage = 30;
			$result->second_installment_percentage = 70;

			return $result;
		}

		$result->first_installment_percentage = 100;
		$result->second_installment_percentage = 0;

		return $result;
	}

	public function getPaymentDate(
		int $productId,
		?\DateTimeInterface $arrival,
		?\DateTimeInterface $today,
		?\DateTimeInterface $defaultDate = null,
		bool $secondInstallment = false,
	): PaymentDate
	{
		$policy = $this->cancellationPolicyResolver->getPolicy($productId);
		$paymentDate = new PaymentDate($arrival, $today, $defaultDate, $secondInstallment);
		$paymentDate->set($policy);

		return $paymentDate;
	}

	public function getBookingConditions(int $partnerId, string $language, ?int $productId = null): array
	{
		if ($productId !== null) {
			$policy = $this->termsRepository->getBy([
				BookingTermEntity::PRODUCT_ID => $productId,
				BookingTermEntity::LANGUAGE_ID => $language,
			]);
		}

		if (empty($policy)) {
			$policy = $this->termsRepository->getBy([
				BookingTermEntity::PARTNER_ID => $partnerId,
				BookingTermEntity::LANGUAGE_ID => $language,
			]);
		}

		if ($policy === null) {
			return [];
		}

		$dom = new DOMDocument();
		$dom->loadHTML('<?xml encoding="UTF-8">' . $policy->terms);

		$xpath = new DOMXPath($dom);
		$paragraphs = $xpath->query('//p');

		$result = [];

		foreach ($paragraphs as $paragraph) {
			$result[] = $paragraph->nodeValue;
		}

		return $result;
	}

	public function getCancellationPolicy(
		int $partnerId,
		string $language,
		int $numberOfPersons,
		?int $productId = null
	): array
	{
		return $this->cancellationPolicyResolver->find($partnerId, $language, $numberOfPersons, $productId);
	}

	public function getPolicy(int $productId, ?string $language = null): ?PolicyEntity
	{
		$policy = $this->termsRepository->getProductPolicy($productId);

		if ($language === null || $policy === null) {
			return $policy;
		}

		$policy->file_path = Strings::replace($policy->file_path, '#/en/#', '/' . $language . '/');

		return $policy;
	}

}

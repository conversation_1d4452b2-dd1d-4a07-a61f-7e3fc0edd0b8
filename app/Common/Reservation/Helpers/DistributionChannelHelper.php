<?php declare(strict_types = 1);

namespace App\Common\Reservation\Helpers;

use App\Common\Enums\InstallmentsPaymentStatusEnum;
use App\Common\Enums\PaymentTypeEnum;
use App\Common\Reservation\BookingConditionsFinder;
use App\Models\Entities\custom\DistributionChannelDetailEntity;
use App\Repositories\ProductDistributionChannelRepository;

class DistributionChannelHelper
{

	private ProductDistributionChannelRepository $productDistributionChannelRepository;

	private DistributionChannelDetailEntity $distributionChannelDetailEntity;

	private BookingConditionsFinder $bookingConditionsFinder;

	public function __construct(ProductDistributionChannelRepository $productDistributionChannelRepository, BookingConditionsFinder $bookingConditionsFinder)
	{
		$this->productDistributionChannelRepository = $productDistributionChannelRepository;
		$this->bookingConditionsFinder = $bookingConditionsFinder;
	}

	public function getAmount(?int $productDistributionChannelId, ?float $baseAmount, ?float $totalAmount, ?float $insurance = 0): ?float
	{
		if (empty($productDistributionChannelId)) {
			return $totalAmount;
		}

		$distributionChannelDetail = $this->getDistributionChannelDetail($productDistributionChannelId);

		return $baseAmount + ($baseAmount * $distributionChannelDetail->percentage / 100) + $insurance ?? 0;
	}

	public function getTotalAmount(?int $productDistributionChannelId, ?float $baseAmount, ?float $insurance = 0, ?float $distributionPrice = null): ?float
	{
		if (empty($productDistributionChannelId)) {
			return $baseAmount + $insurance ?? 0;
		}

		if (!empty($distributionPrice)) {
			return $distributionPrice + $insurance ?? 0;
		}

		$distributionChannelDetail = $this->getDistributionChannelDetail($productDistributionChannelId);

		return $baseAmount + ($baseAmount * $distributionChannelDetail->percentage / 100) + $insurance ?? 0;
	}

	public function getFirstInstallmentAmount(
		int $productId,
		?int $productDistributionChannelId,
		?float $baseAmount,
		?float $insurance = 0,
		?float $distributionPrice = null
	): ?float
	{
		$amountConditions = $this->bookingConditionsFinder->getAmountPercentages($productId);

		if (empty($productDistributionChannelId)) {
			return $baseAmount * ($amountConditions->first_installment_percentage / 100) + $insurance ?? 0;
		}

		if (!empty($distributionPrice)) {
			return $distributionPrice * ($amountConditions->first_installment_percentage / 100) + $insurance ?? 0;
		}

		$distributionChannelDetail = $this->getDistributionChannelDetail($productDistributionChannelId);
		$price = $baseAmount + ($baseAmount * $distributionChannelDetail->percentage / 100);

		return $price * ($amountConditions->first_installment_percentage / 100) + $insurance ?? 0;
	}

	public function getSecondInstallmentAmount(int $productId, ?int $productDistributionChannelId, ?float $baseAmount, ?float $distributionPrice = null): ?float
	{
		$amountConditions = $this->bookingConditionsFinder->getAmountPercentages($productId);

		if (empty($productDistributionChannelId)) {
			return $baseAmount * ($amountConditions->second_installment_percentage / 100);
		}

		if (!empty($distributionPrice)) {
			return $distributionPrice * ($amountConditions->second_installment_percentage / 100);
		}

		$distributionChannelDetail = $this->getDistributionChannelDetail($productDistributionChannelId);
		$price = $baseAmount + ($baseAmount * $distributionChannelDetail->percentage / 100);

		return $price * ($amountConditions->second_installment_percentage / 100);
	}

	public function getInstallmentAmount(int $productId, ?float $baseAmount, ?bool $isTwoInstallments, ?float $insurance = 0): ?float
	{
		if (!$isTwoInstallments) {
			return $baseAmount + $insurance;
		}

		$amountConditions = $this->bookingConditionsFinder->getAmountPercentages($productId);

		return $baseAmount * ($amountConditions->first_installment_percentage / 100) + $insurance ?? 0;
	}

	public function getSource(?int $productDistributionChannelId): string
	{
		if (empty($productDistributionChannelId)) {
			return 'Web';
		}

		$distributionChannelDetail = $this->getDistributionChannelDetail($productDistributionChannelId);

		return $distributionChannelDetail->name;
	}

	private function getDistributionChannelDetail(int $productDistributionChannelId): DistributionChannelDetailEntity
	{
		if (isset($this->distributionChannelDetailEntity)) {
			return $this->distributionChannelDetailEntity;
		}

		$this->distributionChannelDetailEntity = $this->productDistributionChannelRepository->getChannelDetailById($productDistributionChannelId);

		return $this->distributionChannelDetailEntity;
	}

	public function getStatusForFirstInstallment(?int $productDistributionChannelId = null, ?int $dealId = null): ?string
	{
		if (empty($dealId)) {
			return null;
		}

		if (empty($productDistributionChannelId)) {
			return InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		}

		$distributionChannelDetail = $this->getDistributionChannelDetail($productDistributionChannelId);

		if ($distributionChannelDetail->name === 'Airbnb' || $distributionChannelDetail->name === 'Holidu') {
			return InstallmentsPaymentStatusEnum::THE_GUEST_PAYS_DIRECTLY_TO_THE_PARTNER;
		}

		return InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
	}

	public function getStatusForSecondInstallment(?string $secondPaymentType = null, ?int $productDistributionChannelId = null, ?int $dealId = null): ?string
	{
		if (empty($dealId) || empty($secondPaymentType)) {
			return null;
		}

		if ($secondPaymentType === PaymentTypeEnum::MONEY) {
			return InstallmentsPaymentStatusEnum::THE_GUEST_PAYS_DIRECTLY_TO_THE_RENTER;
		}

		if (empty($productDistributionChannelId)) {
			return InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
		}

		$distributionChannelDetail = $this->getDistributionChannelDetail($productDistributionChannelId);

		if ($distributionChannelDetail->name === 'Airbnb' || $distributionChannelDetail->name === 'Holidu') {
			return InstallmentsPaymentStatusEnum::THE_GUEST_PAYS_DIRECTLY_TO_THE_PARTNER;
		}

		return InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
	}

	public function isSetDistributionChannel(string $channelName, ?int $productDistributionChannelId = null): bool
	{
		if (empty($productDistributionChannelId)) {
			return false;
		}

		$distributionChannelDetail = $this->getDistributionChannelDetail($productDistributionChannelId);

		return $distributionChannelDetail->name === $channelName;
	}

	public function getDistributionChannelIdByName(int $productId, ?string $distributionChannelName): ?int
	{
		if (empty($distributionChannelName)) {
			return null;
		}

		return $this->productDistributionChannelRepository->getDistributionChannelIdByName($productId, $distributionChannelName);
	}

}

<?php declare(strict_types = 1);

namespace App\Common\Reservation;

use App\Common\Enums\DealStagesEnum;
use App\Common\Enums\InstallmentsPaymentStatusEnum;
use App\Models\Entities\GeneratedOfferDealEntity;
use App\Models\Entities\ReservationEntity;
use App\Modules\Property\Logic\InvoiceLogic;
use App\Repositories\GeneratedOffersDealsRepository;
use App\Repositories\ReservationsRepository;
use Nette\Utils\Strings;

class PartnerInstallmentsAmountResolver
{

	protected float $firstInstallment = 0;

	protected float $secondInstallment = 0;

	private InvoiceLogic $invoiceLogic;

	private BookingConditionsFinder $bookingConditionsFinder;

	private GeneratedOffersDealsRepository $generatedOffersDealsRepository;

	private ReservationsRepository $reservationsRepository;

	public array $paymentStatus = [
		InstallmentsPaymentStatusEnum::WAITING_PAYMENT => 'Za uplatu',
		InstallmentsPaymentStatusEnum::PAID => 'PLAĆANJE PROVEDENO',
	];

	public function __construct(
		InvoiceLogic $invoiceLogic,
		BookingConditionsFinder $bookingConditionsFinder,
		GeneratedOffersDealsRepository $generatedOffersDealsRepository,
		ReservationsRepository $reservationsRepository
	)
	{
		$this->invoiceLogic = $invoiceLogic;
		$this->bookingConditionsFinder = $bookingConditionsFinder;
		$this->generatedOffersDealsRepository = $generatedOffersDealsRepository;
		$this->reservationsRepository = $reservationsRepository;
	}


	public function resolve(int $reservationId, ?float $amount): void
	{
		/** @var ?ReservationEntity $reservation */
		$reservation = $this->reservationsRepository->getBy([ReservationEntity::ID => $reservationId]);

		if ($reservation === null || $amount === null) {
			return;
		}

		$percentages = $this->bookingConditionsFinder->getAmountPercentagesForPartner($reservation->product_id);
		$agencyProvisionAmount = $this->invoiceLogic->getAgencyProvisionAmount($reservationId);
		$vat = $agencyProvisionAmount * 0.25;
		$owner = $this->invoiceLogic->getOwner($reservation->product_id);
		$this->firstInstallment = $amount * ($percentages->first_installment_percentage / 100)
			- $agencyProvisionAmount - (Strings::contains($owner->ssn ?? '', 'IT') ? 0 : $vat);

		$this->secondInstallment = $amount - $this->firstInstallment - $agencyProvisionAmount - (Strings::contains($owner->ssn ?? '', 'IT') ? 0 : $vat);

		if ($percentages->first_installment_percentage === 100) {
			$this->secondInstallment = 0;
		}

		if (is_int($reservation->deal_id)) {
			/** @var ?GeneratedOfferDealEntity $deal */
			$deal = $this->generatedOffersDealsRepository->getDealByHubspotDealId($reservation->deal_id);
			$firstInstallmentPaymentStatus = $reservation->partner_first_installment_payment_status ?? InstallmentsPaymentStatusEnum::WAITING_PAYMENT;
			$secondInstallmentPaymentStatus = $reservation->partner_second_installment_payment_status ?? InstallmentsPaymentStatusEnum::WAITING_PAYMENT;

			if ($deal->deal_stage === DealStagesEnum::STORNO) {
				if ($this->paymentStatus[$firstInstallmentPaymentStatus] !== $this->paymentStatus[InstallmentsPaymentStatusEnum::PAID]) {
					$this->firstInstallment = 0;
				}

				if ($this->paymentStatus[$secondInstallmentPaymentStatus] !== $this->paymentStatus[InstallmentsPaymentStatusEnum::PAID]) {
					$this->secondInstallment = 0;
				}
			}
		}
	}

	public function getFirstInstallment(): float
	{
		return round(max($this->firstInstallment, 0), 2);
	}

	public function getSecondInstallment(): float
	{
		return $this->secondInstallment;
	}

}

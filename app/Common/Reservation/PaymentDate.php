<?php declare(strict_types = 1);

namespace App\Common\Reservation;

use App\Models\Entities\PolicyEntity;

class PaymentDate
{

	protected \DateTimeInterface|null $date = NULL;

	protected bool $immediatelyPayment = false;

	private ?\DateTimeInterface $arrival;

	private ?\DateTimeInterface $reservationDate;

	private bool $secondInstallment;

	private ?\DateTimeInterface $defaultDate;

	public function __construct(
		?\DateTimeInterface $arrival = null,
		?\DateTimeInterface $today = null,
		?\DateTimeInterface $defaultDate = null,
		bool $secondInstallment = false,
	)
	{
		$this->arrival = $arrival;
		$this->reservationDate = $today;
		$this->secondInstallment = $secondInstallment;
		$this->defaultDate = $defaultDate;
	}

	public function getPaymentDate(): ?\DateTimeInterface
	{
		return $this->date->setTime(0, 0);
	}

	public function getImmediatelyPayment(): bool
	{
		return $this->immediatelyPayment;
	}

	public function set(?PolicyEntity $policy): void
	{
		$diffBetweenReservationAndArrive = (int) date_diff($this->reservationDate, $this->arrival)->days;
		$arrival = clone $this->arrival;
		$reservationDate = clone $this->reservationDate;

		$immediatelyPaymentDays = $secondInstallmentDays = $this->getDaysByPolicyId($policy?->id);

		if ($policy !== null && $policy->id < 7) {
			$this->immediatelyPayment = TRUE;

			if ($diffBetweenReservationAndArrive > $immediatelyPaymentDays) {
				$this->date = $arrival->modify('-' . $immediatelyPaymentDays . ' days')->setTime(0, 0, 0);

				return;
			}

			$this->date = $reservationDate->setTime(0, 0);

			return;
		}

		if ($diffBetweenReservationAndArrive > $immediatelyPaymentDays) {
			if (!$this->secondInstallment) {
				$this->date = $reservationDate->modify('+2 days')->setTime(0, 0, 0);
			} else {
				$firstInstallmentDate = $reservationDate->modify('+2 days')->setTime(0, 0, 0);
				$this->date = $arrival->modify('-' . $secondInstallmentDays . ' days')->setTime(0, 0, 0);

				//e.g. arrival date is 2023-06-07, reservation date is 2021-05-02
				if ($this->date < $firstInstallmentDate) {
					$this->date = $firstInstallmentDate;
				}
			}
		} else {
			$this->immediatelyPayment = TRUE;
			$this->date = !empty($this->defaultDate) ? $this->defaultDate->setTime(0, 0) : $reservationDate->modify('+2 days')->setTime(0, 0, 0);
		}
	}

	private function getDaysByPolicyId(?int $id): int
	{
		if ($id === null) {
			return 35;
		}

		return match ($id) {
			1 => 29,
			3, 4, 5, 6 => 30,
			default => 35,
		};
	}

}

<?php declare(strict_types = 1);

namespace App\Common\Enums;

class InstallmentsPaymentSecondStatusEnum extends BaseEnum
{

	public const WAITING_PAYMENT = 'waiting_for_payment';

	public const PAID = 'paid';

	public const COPY_OF_PAYMENT_RECEIVED = 'copy_of_payment_received';

	public const DEBITED_KK_PAY_PER = 'debited_kk_pay_per';

	public const OK_INVOICE_ISSUED_FOR_THE_TOTAL_AMOUNT = 'ok_invoice_issued_for_the_total_amount';

	public const OK_CONFIRMATION_OF_ADVANCE_PAYMENT_SENT = 'ok_confirmation_of_advance_payment_sent';

	public const OK_CONFIRMATION_OF_PAYMENT_OF_THE_TOTAL_AMOUNT_SENT = 'ok_confirmation_of_payment_of_the_total_amount_sent';

	public const THE_GUEST_PAYS_DIRECTLY_TO_THE_PARTNER = 'the_guest_pays_directly_to_the_partner';

	public const THE_GUEST_PAYS_DIRECTLY_TO_THE_RENTER = 'the_guest_pays_directly_to_the_renter';

}

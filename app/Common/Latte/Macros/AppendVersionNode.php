<?php declare(strict_types = 1);

namespace App\Common\Latte\Macros;

use Latte\Compiler\Nodes\Php\ExpressionNode;
use Latte\Compiler\Nodes\StatementNode;
use Latte\Compiler\PrintContext;
use Latte\Compiler\Tag;
use Latte\Compiler\TemplateParser;

class AppendVersionNode extends StatementNode
{

	public string $tagName;

	public ExpressionNode $path;

	private static array $validTags = [
		'script' => 'src',
		'link' => 'href',
		'img' => 'src',
	];

	/**
	 * Creates the node from the tag and validates the usage.
	 */
	public static function create(Tag $tag, TemplateParser $templateParser): self
	{
		$node = new static();

		$tagName = $tag->htmlElement->name;

		$node->tagName = $tagName;

		$tag->expectArguments();
		$node->path = $tag->parser->parseUnquotedStringOrExpression();

		return $node;
	}

	/**
	 * Outputs the formatted versioned asset path.
	 */
	public static function output(string $path, string $tagName): string
	{
		$timestamp = filemtime(WWW_DIR . $path);
		$versionedPath = $path . '?v=' . $timestamp;

		$pathFormatted = htmlspecialchars($versionedPath, ENT_QUOTES);

		$urlAttr = self::$validTags[$tagName];

		return ' ' . $urlAttr . '="' . $pathFormatted . '"';
	}

	public function print(PrintContext $context): string
	{
		return $context->format(
			'echo ' . self::class . '::output(%args, %dump);',
			[$this->path],
			$this->tagName
		);
	}

	// @codingStandardsIgnoreLine
	public function &getIterator(): \Generator
	{
		yield $this->path;
	}

}

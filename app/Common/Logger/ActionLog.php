<?php declare(strict_types = 1);

namespace App\Common\Logger;

use App\Models\Entities\ActionLogEntity;
use App\Repositories\ActionLogRepository;

class ActionLog
{

	public function __construct(private readonly ActionLogRepository $actionLogRepository)
	{
	}

	public function log($action, $dbTable, $dbTableId, $details, $severity = 'info'): int
	{
		$actionLog = new ActionLogEntity();

		$actionLog->action = $action;
		$actionLog->db_table = $dbTable;
		$actionLog->db_table_id = $dbTableId;
		$actionLog->details = $details;
		$actionLog->severity = $severity;

		return $this->actionLogRepository->insert($actionLog);
	}

}

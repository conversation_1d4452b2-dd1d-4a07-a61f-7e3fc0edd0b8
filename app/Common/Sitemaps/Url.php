<?php declare(strict_types = 1);

namespace App\Common\Sitemaps;

use Nette\Utils\DateTime;

class Url
{

	public string $loc;

	public DateTime $lastmod;

	public string $frequency;

	public float $priority;

	public function __construct(?string $loc = null, ?DateTime $lastmod = null, ?string $frequency = null, ?float $priority = null, bool $shouldTrim = false)
	{
		$this->loc = $shouldTrim && $loc !== null ? rtrim($loc, '/') : ($loc ?? '');
		$this->lastmod = $lastmod ?? new DateTime();
		$this->frequency = $frequency ?? 'daily';
		$this->priority = $priority ?? 0.5;
	}

}

<?php declare(strict_types = 1);

namespace App\Common\ProductInfo;

use App\Common\Reservation\BookingConditionsFinder;
use App\Models\Entities\custom\PriceParameterEntity;
use App\Models\Entities\custom\PricesEntity;

class ProductPriceResolver
{

	/**
	 * @var ProductPrice
	 */
	private $productPrice;

	private BookingConditionsFinder $bookingConditionsFinder;

	public function __construct(
		ProductPrice $productPrice,
		BookingConditionsFinder $bookingConditionsFinder
	)
	{
		$this->productPrice = $productPrice;
		$this->bookingConditionsFinder = $bookingConditionsFinder;
	}

	public function resolve(int $productId, PriceParameterEntity $parameters): PricesEntity
	{
		$entity = new PricesEntity();
		$entity->price = $entity->priceWithoutDiscount = null;
		$departure = $parameters->departure;

		if ($parameters->arrival->format('d.m.Y.') === $parameters->departure->format('d.m.Y.')) {
			$departure = (clone $parameters->departure)->modify('+1 days');
		}

		$priceEntity = $this->productPrice->calculate(
			$productId,
			$parameters->arrival,
			$departure,
			$parameters->adultsCount,
			$parameters->childrenCount
		);

		$entity->priceWithoutDiscount = $priceEntity->priceWithoutDiscount ?? null;
		$entity->price = $priceEntity->price ?? null;

		$amountPercentages = $this->bookingConditionsFinder->getAmountPercentages($productId);
		$entity->firstInstallmentPrice = $entity->price * ($amountPercentages->first_installment_percentage / 100);

		return $entity;
	}

	public function setParameters(
		int $productId,
		\DateTimeInterface $arrival,
		\DateTimeInterface $departure,
		int $adults,
		int $children,
		string $test
	): PriceParameterEntity
	{
		$propertyId = $this->productPrice->getProductRepository()->getPropertyIdByProductId($productId);

		$priceParameterEntity = new PriceParameterEntity();
		$priceParameterEntity->productId = $productId;
		$priceParameterEntity->propertyID = $propertyId;
		$priceParameterEntity->arrival = $arrival;
		$priceParameterEntity->departure = $departure;
		$priceParameterEntity->adultsCount = !empty($adults) ? $adults : 2;
		$priceParameterEntity->childrenCount = $children;
		$priceParameterEntity->test = $test;
		$priceParameterEntity->salesmarket = 999;

		return $priceParameterEntity;
	}

}

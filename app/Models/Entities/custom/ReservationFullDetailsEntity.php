<?php declare(strict_types = 1);

namespace App\Models\Entities\custom;

use App\Models\Entities\BaseTEntity;

class ReservationFullDetailsEntity extends BaseTEntity
{

	public int $id;

	public ?int $foreign_id;

	public ?\DateTimeInterface $created;

	public ?int $created_by;

	public bool $is_owner_reservation;

	public ?string $source;

	public ?string $status;

	public ?string $guest_payment_first_installments_status;

	public ?int $guest_payment_first_installments_amount;

	public ?string $guest_payment_second_installments_status;

	public ?int $guest_payment_second_installments_amount;

	public ?string $property_owner_first_installments_status;

	public ?int $property_owner_first_installments_amount;

	public ?string $property_owner_second_installments_status;

	public ?int $property_owner_second_installments_amount;

	public string $customer;

	public ?int $adults;

	public ?int $children;

	public ?int $pets;

	public int $arrive_in_days;

	public ?\DateTimeInterface $arrive;

	public ?\DateTimeInterface $departure;

	public int $number_of_nights;

	public ?\DateTimeInterface $booked_date;

	public ?string $listing;

	public ?int $listing_id;

	public ?bool $is_listing_published;

	public ?string $reservation_code;

	public int $total_price;

	public int $total_price_without_discount;

	public int $agency_provision_percentage;

	public ?string $notes;

	public bool $approved;

	public bool $declined;

	public bool $cancelled;

	public bool $hidden;

	public ?\DateTimeInterface $deleted;

	public bool $is_foreign_reservation;

	public bool $is_agency_provision_property;

	public bool $has_insurance_amount;

	public ?int $cancellation_policy_rule_id;

	public ?string $picture_path;

}

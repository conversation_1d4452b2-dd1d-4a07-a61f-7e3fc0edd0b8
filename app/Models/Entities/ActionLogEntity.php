<?php declare(strict_types = 1);

namespace App\Models\Entities;

class ActionLogEntity extends BaseTEntity
{

	public const TABLE_NAME = 'action_logs';
	public const ID = 'id';
	public const ACTION = 'action';
	public const DETAILS = 'details';
	public const DB_TABLE = 'db_table';
	public const DB_TABLE_ID = 'db_table_id';
	public const SEVERITY = 'severity';
	public const CREATED = 'created';
	public const CREATED_BY = 'created_by';

	public int $id;

	public ?string $action;

	public ?string $details;

	public ?string $db_table;

	public ?int $db_table_id;

	public ?string $severity;

	public ?\DateTimeInterface $created;

	public ?int $created_by;

}

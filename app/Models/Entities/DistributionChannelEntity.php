<?php declare(strict_types = 1);

namespace App\Models\Entities;

class DistributionChannelEntity extends BaseTEntity
{

	public const TABLE_NAME = 'distribution_channels';
	public const ID = 'id';
	public const NAME = 'name';
	public const UPDATED = 'updated';
	public const UPDATED_BY = 'updated_by';
	public const CREATED = 'created';
	public const CREATED_BY = 'created_by';

	public int $id;

	public ?string $name;

	public ?\DateTimeInterface $updated;

	public ?int $updated_by;

	public ?\DateTimeInterface $created;

	public ?int $created_by;

}

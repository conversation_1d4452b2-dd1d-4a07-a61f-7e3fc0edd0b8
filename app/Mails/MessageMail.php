<?php declare(strict_types = 1);

namespace App\Mails;

use Latte\Engine;
use Latte\Essential\TranslatorExtension;
use Nette\Mail\SmtpMailer;
use Nette\Utils\DateTime;

class MessageMail extends BaseMail
{

	/**
	 * @param mixed $params
	 */
	public function create($params = NULL): void
	{
		if (isset($params['reservation_code'])) {
			$today = new DateTime();
			$day = $this->translator->translate($today->format('D'));

			$this->message->setSubject(
				$this->translator->translate($params['mail_subject'])
				. ' (' . $params['reservation_code'] . ', ' . $day . ', ' . $today->format('d.m.Y.') . ')'
			);
		} else {
			$this->message->setSubject($this->translator->translate($params['subject'], $params['object'] ?? ''));
		}
		$this->message->setFrom('<EMAIL>', 'VillasGuide');

		if (!isset($params['text_addition'])) {
			$params['text_addition'] = '';
		}

		if (!isset($params['title_addition'])) {
			$params['title_addition'] = '';
		}

		$engine = new Engine();

		if ($this->translator) {
			$engine->addExtension(new TranslatorExtension($this->translator));
		}

		$templateParams = [
			'params' => $params,
			'support_phone' => $this->translator ? $this->translator->translate('company_phone_number_vg') : '',
			'support_mail' => '<EMAIL>',
		];

		$templatePath = __DIR__ . '/templates/MessageMail.latte';

		$htmlContent = $engine->renderToString($templatePath, $templateParams);

		$this->message->setHtmlBody($htmlContent, __DIR__ . '/templates/img');

		if (isset($params['config']) && isset($params['config']['host'])) {
			$customMailer = new SmtpMailer(
				$params['config']['host'] ?? '',
				$params['config']['username'] ?? '',
				$params['config']['password'] ?? '',
				$params['config']['port'] ?? null,
				$params['config']['encryption'] ?? null,
				$params['config']['persistent'] ?? false,
				$params['config']['timeout'] ?? 20,
				$params['config']['client_host'] ?? null,
				$params['config']['stream_options'] ?? null,
			);
			$customMailer->send($this->message);
		} else {
			$this->send();
		}
	}

}

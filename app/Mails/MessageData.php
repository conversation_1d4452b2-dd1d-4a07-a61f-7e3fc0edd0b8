<?php declare (strict_types = 1);

namespace App\Mails;

class MessageData implements \ArrayAccess
{

	/**
	 * @var mixed[]
	 */
	public array $data;

	public function __construct(?array $data)
	{
		$this->data = $data ?? [];
	}

	public function __set($name, $value)
	{
		$this->data[$name] = $value;
	}

	public function __get($name)
	{
		return $this->data[$name];
	}

	public function __isset($name)
	{
		return isset($this->data[$name]);
	}

	public function offsetSet($offset, $value): void
	{
		$this->__set($offset, $value);
	}


	public function offsetGet($offset): mixed
	{
		return $this->data[$offset];
	}


	public function offsetExists($offset): bool
	{
		return isset($this->data[$offset]);
	}


	public function offsetUnset($offset): void
	{
		unset($this->data[$offset]);
	}

}

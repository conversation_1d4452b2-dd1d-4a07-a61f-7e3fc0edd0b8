<?php declare(strict_types = 1);

namespace App\Mails;

use Nette\Mail\Mailer;
use Nette\Mail\SmtpMailer;

class SmtpMailerFactory implements IMailerFactory
{

	private array $config;

	public function __construct(array $config)
	{
		$this->config = $config;
	}

	public function create(): Mailer
	{
		return new SmtpMailer(
			$this->config['host'] ?? '',
			$this->config['username'] ?? '',
			$this->config['password'] ?? '',
			$this->config['port'] ?? null,
			$this->config['encryption'] ?? null,
			$this->config['persistent'] ?? false,
			$this->config['timeout'] ?? 20,
			$this->config['client_host'] ?? null,
			$this->config['stream_options'] ?? null
		);
	}

}

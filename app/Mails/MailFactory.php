<?php declare(strict_types = 1);

namespace App\Mails;

use App\Common\Translator;
use Contributte\Mailing\MailBuilderFactory;
use Nette\DI\Container;
use Nette\Mail\Mailer;
use Tracy\ILogger;

class MailFactory
{

	private MailBuilderFactory $factory;

	private Translator $translator;

	private Container $container;

	public function __construct(MailBuilderFactory $factory, Translator $translator, Container $container)
	{
		$this->factory = $factory;
		$this->translator = $translator;
		$this->container = $container;
	}

	/**
	 * @param string|string[] $recipient
	 * @param mixed $args
	 */
	public function create(string $type, $recipient, $args = NULL): BaseMail
	{
		if ($args === NULL) {
			$args = [];
		}

		$args['recipient'] = $recipient;

		if (strpos($type, '\\') !== false) {
			$mailClass = $type;
			$templateName = basename(str_replace('\\', '/', $type));
		} else {
			$mailClass = 'App\\Mails\\' . $type;
			$templateName = $type;
		}

		if (!class_exists($mailClass)) {
			throw new \InvalidArgumentException(sprintf('Mail class %s does not exist', $mailClass));
		}

		$mail = new $mailClass();

		try {
			$mailer = $this->container->getByType(Mailer::class);
			$mail->setMailer($mailer);
		} catch (\Throwable $e) {
			// Mailer not available, will use fallback
		}

		try {
			$logger = $this->container->getByType(ILogger::class);
			$mail->setLogger($logger);
		} catch (\Throwable $e) {
			// Logger not available
		}

		$mailBuilder = $this->factory->create();

		$templateFile = __DIR__ . '/templates/' . $templateName . '.latte';

		if (file_exists($templateFile)) {
			$mailBuilder->setTemplateFile($templateFile);
		}

		$mail->message = $mailBuilder->getMessage();
		$mail->template = $mailBuilder->getTemplate();

		if (!empty($this->translator)) {
			$this->translator->setFrontLanguage($args['language'] ?? null);
			$mail->setTranslator($this->translator);
		}

		$mail->create(new MessageData($args));

		return $mail;
	}

}

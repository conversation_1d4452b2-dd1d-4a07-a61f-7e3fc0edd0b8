<?php declare(strict_types = 1);

namespace App\Mails;

use App\Common\Enums\PartnersEnum;
use App\Common\Novasol\MyBookingLink;
use App\Common\ProductInfo\ProductNameResolver;
use App\Models\Entities\ProductBasicInfoEntity;
use Dibi\DateTime;

class BookingConfirmationMail extends BaseMail
{

	/**
	 * @param mixed $params
	 */
	public function create($params = NULL): void
	{
		$this->template->translator = $this->translator;
		$this->template->partnerData = $params['partner_data'];

		/** @var ProductBasicInfoEntity $objectGeneralInfo */
		$objectGeneralInfo = $params['basicInfo'];
		$productNameResolver = new ProductNameResolver($objectGeneralInfo->location, $objectGeneralInfo->name);
		$this->template->productName = $productNameResolver->resolve();
		$this->template->heading = $params['heading'] ?? '';
		$this->message->setSubject(
			$this->translator->translate($this->prepareSubject($params))
			. ' - ' . $params['contact_lastname'] . ' - "' . $this->template->productName . '"'
		);

		if ($objectGeneralInfo->partner === PartnersEnum::KOMPAS) {
			$this->message->addBcc('<EMAIL>');
			$this->message->addBcc('<EMAIL>');
		}

		$today = new \DateTime();
		$today = $today->setTime(0, 0);
		$date = !$params['installmentsDate'][1] instanceof \DateTimeInterface
			? DateTime::createFromFormat('d.m.Y.', $params['installmentsDate'][1])
			: $params['installmentsDate'][1];

		$this->template->diffTodayFirstIntallment = $date->setTime(0, 0)->diff($today)->days;
		$this->template->objectGeneralInfo = $objectGeneralInfo;
		$this->template->params = $params;

		if ($objectGeneralInfo->partner === PartnersEnum::NOVASOL) {
			$myBookingLink = new MyBookingLink();
			$this->template->novasolBookingUrl = $myBookingLink->get(
				(string) $params['website_lang'],
				(string) $params['email'],
				(string) $objectGeneralInfo?->propertyID,
				(string) $params['bookingId']
			);
		}
	}

	private function prepareSubject(mixed $params = null): string
	{
		if (isset($params['isPreBooking']) && $params['isPreBooking']) {
			return 'Prebooking';
		}

		return 'Booking confirmation';
	}

}

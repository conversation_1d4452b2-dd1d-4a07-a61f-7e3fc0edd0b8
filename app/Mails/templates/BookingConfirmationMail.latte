<center>
	{varType App\Models\Entities\ProductBasicInfoEntity $objectGeneralInfo}
	{varType App\Models\Entities\PartnerEntity $partnerData}

	<table width="100%" border="0" cellpadding="0" cellspacing="0" bgcolor="#F2F2F2">
		<tr>
			<td align="center" valign="top">

				<table width="640" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#FFFFFF" n:if="isset($params['distribution_channel_name']) && $params['distribution_channel_name'] === 'Booking.com'">
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
					<tr>
						<td align="center" valign="top">

							<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
								<tr>
									<td align="left" valign="top">
										{_'Booking.com confirmation mail header message', $params['contact_lastname'], $productName|noescape}
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
				</table>

				<table width="640" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#FFFFFF">
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
					<tr>
						<td align="center" valign="top">

							<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
								<tr>
									<td align="left" valign="top">
										<img src="https://villas-guide.com/assets/img/logo-black.png" title="Villas Guide" width="165" height="45" style="margin:0; padding:0; border:none; display:block;" border="0" class="img" alt="Villas Guide">
									</td>
								</tr>
							</table>

						</td>
					</tr>
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
				</table>


				<table width="640" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#FFFFFF">
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
					<tr>
						<td align="center" valign="top">

							<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
								<tr>
									<td align="center" valign="top">
										<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
											<tr>
												<td align="left" valign="top">
													{if isset($params['isPreBooking']) && $params['isPreBooking']}
														<h2 style="color: #252525; font-size: 30px; margin-top: 20px; margin-bottom: 10px;">{_'hvala na predrezervaciji!'}</h2>
														<p style="line-height: 23px;">
															{_'Predrezervacija će biti obrađena unutar 48 sati od strane Villas Guide tima te će te biti pravovremeno obaviješteni.'|noescape}
														</p>
													{else}
														<h2 style="color: #252525; font-size: 30px; margin-top: 20px; margin-bottom: 10px;">{_'hvala na rezervaciji!'}</h2>
														<p style="line-height: 23px;">
															{_'Hvala što ste odabrali VillasGuide i s nama rezervirali željenu kuću za odmor!<br />Potvrdu rezervacije vam dostavljamo u nastavku, a detaljnije informacije o smještaju i organizaciji vašeg putovanja poslati ćemo Vam nakon izvršene uplate.'|noescape}
														</p>
													{/if}
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>

						</td>
					</tr>
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
				</table>


				<br />


				<table width="640" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#FFFFFF">
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
					<tr>
						<td align="center" valign="top">

							<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
								<tr>
									<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
								</tr>
								<tr>
									<td align="left" valign="top">
										<h4 style="text-transform: uppercase; font-size: 18px; font-weight: 700; color:#232d54; margin-top: 0; margin-bottom: 10px;">{_'informacije o rezervaciji'}:</h4>
										<table width="600" cellpadding="0" cellspacing="0" border="0" class="reservation-information">
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'rezervirani objekt'}:</td>
												<td style="line-height: 23px;">
													<a href="https://villas-guide.com{str_replace('https://villas-guide.com', '', $params['productLink'])}">
														{if empty($heading)}
															{$productName}
														{else}
															{$heading}
														{/if}
														{for $stars_count = 0; $stars_count < $objectGeneralInfo->quality; $stars_count++}*{/for}
													</a>
												</td>
											</tr>
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'broj rezervacije'}:</td>
												<td style="line-height: 23px;">{(int) $params['bookingId']}</td>
											</tr>
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'datum'}:</td>
												<td style="line-height: 23px;">{(string) $params['bookedDate']|date:'d.m.Y'}</td>
											</tr>
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'šifra agencije'}:</td>
												<td style="line-height: 23px;">2051883</td>
											</tr>
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'period najma'}:</td>
												<td style="line-height: 23px;">
													{$params['arrive']|date:'d.m.Y'}
														-
													{$params['departure']|date:'d.m.Y'}</td>
											</tr>
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'broj gostiju'}:</td>
												<td style="line-height: 23px;">
													{(int) $params['adults']} {_'odraslih'},
													{(int) $params['children']} {_'djece'},
													{(int) $params['pets']} {_'kućnih ljubimaca'}</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>

						</td>
					</tr>
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
				</table>


				<table width="640" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#FFFFFF">
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
					<tr>
						<td align="center" valign="top">

							<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
								<tr>
									<td align="left" valign="top">
										<h4 style="text-transform: uppercase; font-size: 18px; font-weight: 700; color:#232d54; margin-top: 0; margin-bottom: 10px; padding-top: 20px; border-top: 1px solid #eee;">{_'informacije o gostu'}:</h4>
										<table width="600" cellpadding="0" cellspacing="0" border="0" class="customer-information">
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'ime gosta'}:</td>
												<td style="line-height: 23px;">{$params['contact_lastname']} {$params['contact_firstname']}</td>
											</tr>
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'telefonski broj gosta'}:</td>
												<td style="line-height: 23px;">{$params['phone']}</td>
											</tr>
											<tr>
												<td style="padding-right: 10px; line-height: 23px;">{_'email gosta'}:</td>
												<td style="line-height: 23px;">{(string) $params['email']}</td>
											</tr>
											<tr n:if="!empty($params['request_from_guests'])">
												<td style="padding-right: 10px; line-height: 23px;">{_'Request from guest'}:</td>
												<td style="line-height: 23px;">{$params['request_from_guests']}</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>

						</td>
					</tr>
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
				</table>


				<table width="640" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#FFFFFF">
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
					<tr>
						<td align="center" valign="top">

							<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
								<tr>
									<td align="left" valign="top">

										<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
											<tr>
												<td align="left" valign="top">
													<h4 style="text-transform: uppercase; font-size: 18px; font-weight: 700; color:#232d54; margin-top: 0; margin-bottom: 10px; padding-top: 20px; border-top: 1px solid #eee;">{_'informacije o plaćanju'}:</h4>
													<table cellpadding="0" cellspacing="0" border="0" class="payment-information">
														<tr>
															<td style="padding-right: 10px;">{_'cijena najma'}:</td>
															<td>
																<strong>€{(string) $params['total_price_eur']|number:2,',','.'}</strong>
															</td>
														</tr>
														{if !empty($params['insuranceList'])}
															<tr>
																<td style="padding-right: 10px;">{_'Accommodation'}:</td>
																{if !empty($distribution_price)}
																	<td>
																		<strong>€{(string) $params['distribution_price']|number:2,',','.'}</strong>
																	</td>
																{else}
																	<td>
																		<strong>€{(string) $params['price_eur']|number:2,',','.'}</strong>
																	</td>
																{/if}
															</tr>
															{foreach $params['insuranceList'] as $policy}
																<tr>
																	<td style="padding-right: 10px;">{$translator->translate($policy->type)}:</td>
																	<td>
																		<strong>€{(string) $policy->price|number:2,',','.'}</strong>
																	</td>
																</tr>
															{/foreach}
														{/if}
													</table>
													{if !(isset($params['distribution_channel_name']) && in_array($params['distribution_channel_name'], ['Airbnb', 'Holidu']))}
														{if isset($params['distribution_channel_name']) && $params['distribution_channel_name'] === 'Booking.com'}
															<table>
																<tr>
																	<td>{_'Ukupan iznos rezervacije potrebno je uplatiti odmah'}</td>
																</tr>
															</table>
														{else}
															{if !empty($immediatelyPayment)}
																<table>
																	<tr>
																		<td>{_'Ukupan iznos rezervacije potrebno je uplatiti odmah isključivo putem kreditne kartice.'}</td>
																	</tr>
																</table>
															{else}
																{if (empty($params['installmentsDate'][2]))}
																	<table>
																		<tr>
																			<td>
																				{_'Ukupan iznos rezervacije moguće je uplatiti odmah ili najkasnije %s dana od izvršenja rezervacije (najkasnije do <strong>%s</strong>)', $diffTodayFirstIntallment, ($params['installmentsDate'][1]|date:'d.m.Y')|noescape}
																			</td>
																		</tr>
																	</table>
																{/if}
																{if (!empty($params['installmentsDate'][2]))}
																	<table width="600" cellpadding="0" cellspacing="0" border="0">
																		<tr>
																			<td style="line-height: 25px;">{_'ukupan iznos rezervacije moguće je uplatiti odmah ili u 2 rate'}:</td>
																		</tr>
																	</table>

																	<table cellpadding="0" cellspacing="0" border="0" class="payment-information-rates">
																		<tr>
																			<td style="padding-right: 10px; line-height: 25px;">1. {_'rata'}:
																				{_'najkasnije do'}
																				<strong>{$params['installmentsDate'][1]|date:'d.m.Y'}</strong>
																			</td>
																			<td style="line-height: 25px;"><strong>€{$params['first_installment_price']|number:2,',','.'}</strong></td>
																		</tr>
																		<tr>
																			<td style="padding-right: 10px; line-height: 25px;">2. {_'rata'}:
																				{if $params['second_payment_type'] === App\Common\Enums\PaymentTypeEnum::MONEY}
																					{_'Rest pay at location'}
																				{else}
																					{_'najkasnije do'}
																					<strong>{$params['installmentsDate'][2]|date:'d.m.Y'}</strong>
																				{/if}
																			</td>
																			<td style="line-height: 25px;"><strong>€{$params['second_installment_price']|number:2,',','.'}</strong></td>
																		</tr>
																		{if $params['credit_card'] && $mailParams['second_payment_type'] === App\Common\Enums\PaymentTypeEnum::CREDIT_CARD}
																			<tr>
																				<td>{_'Nekoliko dana prije datuma dospijeća dostavit ćemo vam putem maila poziv za plaćanje 2. rate, putem kojeg možete izvršiti plaćanje kreditnom karticom.'}</td>
																			</tr>
																		{/if}
																	</table>
																{/if}
															{/if}
														{/if}
													{/if}


													<p style="color: #252525; font-weight: 600; margin-bottom: 10px; margin-top: 20px;">{_'Cijena uključuje'}:</p>
													<table width="600" cellpadding="0" cellspacing="0" border="0" class="price-includes-information">
														<tr>
															{foreach $params['priceIncludeFeatures'] as $key => $value}
																{if !empty($value)}
																	<td style="padding-right: 10px;">{$translator->translate($key)}</td>
																{/if}
															{/foreach}
															{if $objectGeneralInfo->partner === App\Common\Enums\PartnersEnum::PRIVATE && !empty($params['conciergesInPrice'])}
																<td n:foreach="$params['conciergesInPrice'] as $concierge" style="padding-right: 10px;">{_$concierge->name}</td>
															{/if}
														</tr>
													</table>

													<p style="color: #252525; font-weight: 600; margin-bottom: 10px; margin-top: 20px;">{_'Dodatni troškovi'}:</p>
													<table width="600" cellpadding="0" cellspacing="0" border="0" class="price-includes-information">
														<tr>
															{var $extraCosts = 0}
															{foreach $params['ownerServices'] as $value}
																{if !empty($value->price)}
																	<li>{$translator->translate($value->name)}: {$value->price}{$translator->translate($value->currency)} {_(str_replace('_', ' ',$value->price_type))}
																		{if $value->service_type}
																			<strong>({_'Mandatory'})</strong>
																		{else}
																			<strong>({_'Optional'})</strong>
																		{/if}
																	</li>
																	{var $extraCosts = 1}
																{/if}
															{/foreach}
															{if $objectGeneralInfo->partner === App\Common\Enums\PartnersEnum::PRIVATE && !empty($params['conciergesNotInPrice'])}
															{foreach $params['conciergesNotInPrice'] as $concierge}
																	<li>{_$concierge->name}
																		{if $concierge->price}
																			<strong>{$concierge->price|number:2} €</strong>
																		{else}
																			<strong>{_'Price on request'}</strong>
																		{/if}
																	</li>
																{/foreach}
															{/if}
															{if empty($extraCosts) && empty($params['conciergesNotInPrice'])}
																<li>{_'No extra costs'}</li>
															{/if}
														</tr>
													</table>
												</td>
											</tr>
										</table>

										{if !empty($params['insuranceList'])}
											<p style="color: #252525; font-weight: 600; margin-bottom: 10px; margin-top: 20px;">
												{if count($params['insuranceList']) === 1}
													{_'Vašu policu osiguranja možete preuzeti na linku niže'}:
												{else}
													{_'Vaše police osiguranja možete preuzeti na linkovima niže'}:
												{/if}
											</p>
											<table width="600" cellpadding="0" cellspacing="0" border="0" class="price-includes-information">
												{foreach $params['insuranceList'] as $policy}
													<tr>
														<td>
                                                            <a href="{link Front:InsurancePolicy:default, $policy->type, $params['bookingId']}">{$translator->translate($policy->type)}</a>
                                                        </td>
													</tr>
												{/foreach}
											</table>
										{/if}
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
				</table>

				<table width="640" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#FFFFFF">
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>

					{if !(isset($params['distribution_channel_name']) && in_array($params['distribution_channel_name'], ['Airbnb', 'Holidu']))}
						<tr>
							<td align="center" valign="top">

								<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
									<tr>
										<td align="left" valign="top">
											{if !$params['credit_card']}
												<h4 style="text-transform: uppercase; font-size: 18px; font-weight: 700; color:#252525; margin-top: 0; margin-bottom: 10px; padding-top: 20px; border-top: 1px solid #eee;">{_'Bankovni podaci'}:</h4>
												<p style="margin-bottom: 10px; margin-top: 0;">{_'molimo vas da uplatu izvršite na sljedeći račun'}:</p>

												<table width="600" cellpadding="0" cellspacing="0" border="0">
													<tr>
														<td>
															<p style="color: #252525; font-weight: 600; margin-bottom: 5px; margin-top: 5px;">{_'ime agencije i adresa'}:</p>
														</td>
													</tr>
													<tr>
														<td style="line-height: 23px;">{$partnerData->company_name}</td>
													</tr>
													<tr>
														<td style="line-height: 23px;">{$partnerData->company_street}, {$partnerData->company_zip} {$partnerData->city} - {$partnerData->company_country}</td>
													</tr>
													{if !empty($partnerData->company_oib)}
														<tr>
															<td style="line-height: 23px;">VAT ID (OIB): <strong>{$partnerData->company_oib}</strong></td>
														</tr>
													{/if}
												</table>

												<br />
											{/if}
											<table width="600" cellpadding="0" cellspacing="0" border="0">
												{if !$params['credit_card']}
													<tr>
														<td>
															<p style="color: #252525; font-weight: 600; margin-bottom: 5px; margin-top: 5px;">{_'ime banke i adresa:'}</p>
														</td>
													</tr>
													<tr>
														<td style="line-height: 23px;">{$partnerData->bank_name}</td>
													</tr>
													<tr>
														<td style="line-height: 23px;">{$partnerData->bank_street}</td>
													</tr>
													<tr>
														<td style="line-height: 23px;">IBAN: <strong>{$partnerData->bank_iban}</strong></td>
													</tr>
													<tr>
														<td style="line-height: 23px; padding-bottom: 10px;">SWIFT: <strong>{$partnerData->bank_swift}</strong></td>
													</tr>
													<tr>
														<td style="border-top: 1px solid #eee; padding-top: 10px;">
															<p style="margin: 0; padding: 0; line-height: 23px; font-weight: 600;">{_'u opisu uplate molimo obavezno navesti broj rezervacije!'}</p>
															<p style="margin: 0; padding: 0; line-height: 23px; font-weight: 600;">{_'kopiju uplate dostaviti na sljedeću mail adresu:'} <EMAIL></p>
														</td>
													</tr>
												{/if}
											{if !(isset($params['distribution_channel_name']) && $params['distribution_channel_name'] === 'Booking.com')}
												{if !empty($product_cancellation_policy)}
													<tr>
														<td>
															<p style="color: #252525; font-weight: 600; margin-bottom: 5px; margin-top: 15px; padding-top: 10px; border-top: 1px solid #eee;">{_'Pravila otkazivanja'}</p>
														</td>
													</tr>
													<tr>
														<td>
															{_'Cancellation policy ' . $product_cancellation_policy->name|noescape}
														</td>
													</tr>
												{else}
													<tr>
														<td>
															<p style="color: #252525; font-weight: 600; margin-bottom: 5px; margin-top: 15px; padding-top: 10px; border-top: 1px solid #eee;">{_'mogućnost otkazivanja'}:</p>
														</td>
													</tr>
													<tr>
														<td style="line-height: 21px;">{_'Rezervacija se može otkazati isključivo pisanim oblikom.'}</td>
													</tr>
													{foreach $params['cancellation_policy'] as $policy}
														<tr>
															<td style="line-height: 21px;">{$policy|noescape}.</td>
														</tr>
													{/foreach}
												{/if}
											{/if}
											</table>

										</td>
									</tr>
								</table>

							</td>
						</tr>
					{/if}
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
				</table>

				<br />

				<table width="640" cellpadding="0" cellspacing="0" border="0" class="wrapper" bgcolor="#FFFFFF">
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
					<tr>
						<td align="center" valign="top">

							<table width="600" cellpadding="0" cellspacing="0" border="0" class="container">
								<tr>
									<td align="center" valign="top">


										<table width="600" cellpadding="0" cellspacing="0" border="0">
											<tr>
												<td valign="top"><img src="https://villas-guide.com/assets/img/logo-black.png" width="165" height="45" style="margin:0; padding:0; border:none; display:block;" title="Villas Guide" border="0" class="img" alt="Villas Guide"></td>
												<td valign="top" style="padding-top: 10px; padding-left: 10px;">
													<p style="color: #252525; font-weight: 600; margin: 0; padding: 0; padding-bottom: 5px;">{_'Trebate li pomoć?'}</p>
													<p style="margin: 0; padding: 0; line-height: 23px;">{_'nazovite nas:'} <br /><strong>{$params['company_phone_number']}</strong></p>
												</td>
												<td valign="top" style="padding-top: 10px; padding-left: 10px;">
													<p style="color: #252525; font-weight: 600; margin: 0; padding: 0; padding-bottom: 5px;">{_'Služba za korisnike:'}</p>
													<p style="margin: 0; padding: 0; line-height: 23px;">{_'ponedjeljak'} - {_'petak'} - 8:00 - 16:00<br />{_'subotom i nedjeljom zatvoreno'}</p>
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>

						</td>
					</tr>
					<tr>
						<td height="10" style="font-size:10px; line-height:10px;">&nbsp;</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</center>

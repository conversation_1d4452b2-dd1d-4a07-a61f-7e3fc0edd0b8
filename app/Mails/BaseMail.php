<?php declare(strict_types = 1);

namespace App\Mails;

use Nette\Application\UI\ITemplate;
use Nette\Localization\ITranslator;
use Nette\Mail\Mailer;
use Nette\Mail\Message;
use Tracy\ILogger;

abstract class BaseMail
{

	/**
	 * There you will always have your mail addresses from configuration file
	 *
	 * @var string[]
	 */
	protected array $mails;

	public ?ITranslator $translator = null;

	public ?Mailer $mailer = null;

	public ITemplate $template;

	public Message $message;

	public ?ILogger $logger = null;

	public function setTranslator(?ITranslator $translator): void
	{
		if ($translator !== null) {
			$this->translator = clone $translator;
		}
		//$this->template->setTranslator($translator);
	}

	public function setMailer(?Mailer $mailer): void
	{
		$this->mailer = $mailer;
	}

	public function setLogger(?ILogger $logger): void
	{
		$this->logger = $logger;
	}


	public function compose(Message $message, ?MessageData $params = null): void
	{
		foreach ($params->data as $key => $param) {
			$this->template->$key = $param;
		}

		if (is_array($params->data['recipient'])) {
			foreach ($params->data['recipient'] as $recipient) {
				$message->addTo($recipient);
			}
		} else {
			$message->addTo($params->data['recipient']);
		}

		//$message->setFrom($this->mails['default_sender'], $this->mails['default_sender']);
		//hardcoded because $this->mails object is empty
		$message->setFrom('<EMAIL>', '<EMAIL>');
	}

	/**
	 * Send the message using the injected mailer
	 */
	public function send(): void
	{
		if ($this->mailer !== null) {
			try {
				$this->mailer->send($this->message);
			} catch (\Throwable $e) {
				if ($this->logger !== null) {
					$this->logger->log('Failed to send email: ' . $e->getMessage(), ILogger::ERROR);
				}
			}
		} else {
			if ($this->logger !== null) {
				$this->logger->log('Mailer not injected, message not sent', ILogger::WARNING);
			}
		}
	}

	/**
	 * @param mixed $params
	 * @return mixed
	 */
	abstract public function create($params = NULL);

}

parameters:
    domain: https://villas-guide.com
    production_domain: https://villas-guide.com
    imageDestinationFolders:
        salesRep: /images/salesRep
        villaImages:
            images2048 : %appDir%/../www/villas_images/2048
            images1280 : %appDir%/../www/villas_images/1280
            images1024 : %appDir%/../www/villas_images/1024
            images425 : %appDir%/../www/villas_images/425
            images400 : %appDir%/../www/villas_images/400
            images500 : %appDir%/../www/villas_images/500
    countriesMenu:
        novasol_code: [191]
        country_id: [1003]
    novasol:
        agency_id: 2053143
        base_uri: https://partnerapi.novasol.com/api/
        image_url:
            deprecated_base_url: https://sdc.novasol.com
            base_url: https://image.novasol.com
        options:
            headers:
                api_key: bBYgDxChEsRGj3GeGFgklLh51cKCsR
                User-Agent: AdriaCamps-bot
        batch_receive_address: 'http://villas-guide.com/default/receive-batch'

    kompas_villas:
        credentials:
            base_uri: http://www.kompas-villas.com/KompasService2012/ServiceService2011.asmx
            username: sinisamrsic
            password: 3p2JK8HYdxXnPFVhqFpX
            agency_id: 622

    propertyhub:
        url: https://propertyhub.gaveia.com/api/
        url-web: https://propertyhub.gaveia.com/web-api/
        api-key: VG1TOd31nu6cQwFUhP1l

    istria_homes:
        credentials:
            username: VillasGuide
            password: auzHr-32A
        endpoints:
            objects: https://www.istria-home.com/api/villasguide/objects.xml
            prices: https://www.istria-home.com/api/villasguide/prices.xml
            booking: https://www.istria-home.com/api/villasguide/booking

    traum_ferienwohnungen:
        customerId_hr: 122902
        customerId_fr: 132497
        customerId_it: 132496
        base_uri: https://clientapi.traum-ferienwohnungen.de/
        options:
            headers:
                username_hr: <EMAIL>
                password_hr: 7249229601551d66b2b11bb
                username_fr: <EMAIL>
                password_fr: 6f74af860eea9e5dc0c73fd
                username_it: <EMAIL>
                password_it: 6fb5bc35e9243a2845d60f2
    gtm:
    	id: GTM-KWGZ7LFZ
    	auth: X1p2PKGBATPd1y5LwNL_Bg
    	env: local

    elasticsearch:
        connections:
            default:
                host: kibana.gaveia.com
                username: elastic
                password: k1ban442aP

    secondMail:
        host: smtp.gmail.com
        username: <EMAIL>
        password: xbcnnqbsyiamkfbk
        port: 587
        secure: tls
    logDir: %appDir%/../log
    evisitor:
        encryptionKey: '22fbe5d23e1dad03d730ea2bb1977981d5e14865b553248a494b9638de7a6cb7'
        encryptionIv: 'd5ebf21653c9d7f1'
        url: 'https://www.evisitor.hr/testApi/'
application:
	errorPresenter: Front:Error
	catchExceptions: true
	mapping:
		Front: App\Modules\Front\Presenters\*Presenter
		CallCenter: App\Modules\CallCenter\Presenters\*Presenter
		Admin: App\Modules\Admin\Presenters\*Presenter
		Api: App\Modules\Api\Controllers\*\*Controller
		Property: App\Modules\Property\Presenters\*Presenter
session:
	expiration: 14 days
	autoStart: true
	debugger: true

mailing:
    do: both # log|send|both
    logDirectory: '%appDir%/../log/mails' # this is default option
    mailImagesBasePath: %appDir%/Common/Mails/templates/img # this is default option
    mails: [
    		default_sender: <EMAIL>
    	]

extensions:
    dibi: Dibi\Bridges\Nette\DibiExtension22
    mailing: Ublaboo\Mailing\DI\MailingExtension
    guzzlette: Contributte\Guzzlette\DI\GuzzleExtension
    events: Contributte\EventDispatcher\DI\EventDispatcherExtension
    aop: Contributte\Aop\DI\AopExtension
    aspects: Contributte\Aop\DI\AspectsExtension
    monolog: Contributte\Monolog\DI\MonologExtension
    - App\Common\Forms\FormsExtension
    api: Wedo\Api\DI\ApiExtension
    migrations: Nextras\Migrations\Bridges\NetteDI\MigrationsExtension
    elastica: Contributte\Elastica\DI\ElasticaExtension
    embeddedSvg: Milo\EmbeddedSvg\Extension
    - Contributte\FormWizard\DI\WizardExtension
    entityGenerator: DodoIt\DibiEntityGenerator\DI\DibiEntityGeneratorExtension
    - Contributte\FormMultiplier\DI\MultiplierExtension
    rabbitmq: Contributte\RabbitMQ\DI\RabbitMQExtension
    menu: Contributte\MenuControl\DI\MenuExtension

embeddedSvg:
    baseDir: %wwwDir%
    macroName: embeddedSvg
    # pretty format SVG content (indent tags)
    prettyOutput: yes
    # default <svg> tag attributes, for example
    defaultAttributes:
        class: embedded

aspects:
	- Wedo\Utilities\Aop\CachedAspect(tmpDir: %appDir%/../temp/cache)
	- Wedo\Utilities\Aop\LoginRequiredAspect
latte:
    macros:
        - App\Common\Latte\Macros\AppendVersionMacro::install
        - App\Common\Latte\Macros\VarTypeMacro
guzzlette:
    debug: %debugMode%
dibi:
    host: %database.host%
    username: %database.user%
    password: %database.password%
    database: %database.database%
    port: %database.port%
    lazy: TRUE
migrations:
    dir: %appDir%/../migrations # migrations base directory
    driver: mysql               # pgsql or mysql
    dbal: dibi               # nextras, nette, doctrine or dibi
api:
    url: /api/v1/
    controller: App\Modules\Api\Controllers\BaseController
elastica:
  debug: %debugMode%
search:
    app:
        in: %appDir%
        classes:
            - App\Common\Novasol\Services\Denormalization\*
            - App\Common\Images\*Helper
            - App\Repositories\*Repository
            - App\Common\TextGenerator\*
            - App\Common\Services\*
            - App\Common\ProductInfo\*
            - App\Common\Novasol\Request\*Request
            - App\Common\Novasol\Lists\*List
            - App\Common\Novasol\Services\*Service
            - App\Modules\Front\Services\*
            - App\Common\Novasol\Services\Update\*Service
            - App\Common\Novasol\Services\Update\Helpers\*
            - App\Modules\Admin\Grids\*Grid
            - App\Modules\Admin\Grids\Menu\*Grid
            - App\Modules\Admin\Forms\*Form
            - App\Modules\Admin\Logic\*Logic
            - App\Modules\Front\Forms\*Form
            - App\Modules\Front\Forms\Booking\*Form
            - App\Modules\Admin\Factories\*Factory
            - App\Modules\CallCenter\Factories\*Factory
            - App\Modules\Front\Factories\*Factory
            - App\Modules\Front\Logic\*Logic
            - App\Modules\Admin\Services\*Service
            - App\Common\IstriaHomes\Lists\*
            - App\Common\Sitemaps\*
            - App\Common\PropertyHub\*
            - App\Common\PropertyHub\*\*
            - App\Common\PropertyHub\Reviews\Logic\*
            - App\Common\PropertyOwnerDashboard\*
            - App\Common\PropertyOwnerDashboard\*\*
            - App\Common\IstriaHomes\Importers\ProductImagesImporter
            - App\Common\TraumFerienwohnungen\Services\*Service
            - App\Common\TraumFerienwohnungen\Logic\*\*Logic
            - App\Common\API\HNB\Requests\*Request
            - App\Common\API\HNB\Services\*
            - App\Common\ProductInfo\Helpers\*Helper
            - App\Common\Helpers\*Helper
            - App\Common\Lists\*List
            - App\Common\Reservation\**
            - App\Modules\Api\Controllers\**Controller
            - App\Modules\Api\Logic\*Logic
            - App\Modules\CallCenter\Logic\*Logic
            - App\Common\KompasVillas\Requests\*
            - App\Common\KompasVillas\Logic\*
            - App\Common\KompasVillas\Importer\*
            - App\Common\KompasVillas\Lists\*
            - App\Common\IstriaHomes\Logic\*
            - App\Common\IstriaHomes\Services\*
            - App\Common\IstriaHomes\Requests\*
            - App\Common\IstriaHomes\Importers\*
            - App\Common\TraumFerienwohnungen\Request\Listing\*Request
            - App\Common\TraumFerienwohnungen\Request\Customer\*Request
            - App\Common\TraumFerienwohnungen\Request\Account\*Request
            - App\Modules\Property\Logic\**
            - App\Modules\Property\**\*Factory
            - App\Modules\Front\Controls\PrivateVilla\*Control
            - App\Common\Files\*
            - App\Common\PaymentConfirmation\*
            - App\Modules\Front\Dependencies\*

        exclude:
            classes:
                - App\Common\ProductInfo\ProductSortedPictures
                - App\Common\Novasol\Services\PicturesDownloadService
                - App\Modules\Admin\Logic\UploadFloorPlanLogic
                - App\Modules\Admin\Services\UploadImageService
                - App\Common\TraumFerienwohnungen\Services\ImagesService
                - App\Common\KompasVillas\Importer\ProductImagesImporter
        tags: [nette.inject]
services:
    routerFactory:
        class: App\Router\RouterFactory
        setup:
            - addRouter(@api.routerFactory::create())
    router: @routerFactory::createRouter()
    authenticator: App\Common\Authenticator
    authorizator: App\Common\Authorizator
    - App\Common\ProductInfo\EnclosedPlotCheck
    - Nette\Caching\Cache
    - App\Common\Novasol\Services\UpdateProductsService(%countriesMenu.novasol_code%)
    - App\Modules\Admin\Forms\FormFactory
    - App\Modules\Admin\Grids\GridFactory
    - App\Common\Translator
    - App\Common\Components\Menu\MenuGenerator
    - App\Common\Components\Menu\FrontVpiMenu\VpiMenuGenerator
    - App\Common\Utils\DbHelper
    - App\Common\Logger\LogLoader
    - App\Common\ProductSearch
    - App\Common\SingleVillaHelper(%countriesMenu%)
    - App\Modules\Front\Presenters\VpiVprPresenter(%countriesMenu%)
    - App\Modules\Front\Presenters\PagePresenter(%countriesMenu%)
    - App\Modules\Front\Logic\SearchAndFilteringLogic(%countriesMenu%)
    - App\Modules\Front\Presenters\HomepagePresenter(%countriesMenu%)
    - App\Modules\Front\Presenters\SingleVillaPresenter(%countriesMenu%)
    - App\Common\Novasol\Services\PicturesDownloadService(%imageDestinationFolders%)
    - App\Common\Hubspot
    - App\Modules\Front\Controls\SearchResultsControl
    - App\Modules\Front\Controls\ListingResultsControl
    - App\Modules\Front\Controls\FaqControl
    - App\Models\Entities\ProductPoolEntity
    - App\Mails\MailFactory
    - App\Mails\BookingProductOwnerConfirmationMailFactory
    - App\Common\HubspotContactData
    - App\Common\Offers
    - App\Common\Deals
    - App\Modules\CallCenter\Helpers\SalesSearchHelper
    - App\Modules\Front\Controls\SimilarVillas
    - App\Common\BookingHelper
    - App\Common\Novasol\Importer\ImportService
    - App\Common\BrowserHelper
    - App\Common\Payment(%shopData.paymentGatewayConfig%)
    - App\Common\BookingPageHelper
    - App\Common\Novasol\Services\ImagesUpdateHelper
    - App\Models\Entities\custom\ConceptsEntity
    - App\Common\GuzzleClient\GuzzleClient(%proxy%, %novasol%, %traum_ferienwohnungen%)
    - App\Common\TraumFerienwohnungen\ListingCreator(%traum_ferienwohnungen%)
    - App\Common\TraumFerienwohnungen\InsertListingInformationService
    - App\Common\TraumFerienwohnungen\ListingInformationCreator
    - App\Common\TraumFerienwohnungen\ListingLanguageResolver
    - App\Common\GoogleCalendar\GoogleCalendarService(%googleCredentials%)
    - App\Common\GoogleCalendar\UpdateGoogleCalendarService
    - App\Common\GoogleCalendar\ICalImportService
    - App\Common\GoogleCalendar\ICalEventsService
    - App\Modules\Admin\Services\UploadImageService(%domain%)
    - App\Common\ProductInfo\ProductSortedPictures(%domain%)
    - App\Common\ProductInfo\PriceCalculator
    - App\Modules\Admin\Logic\UploadFloorPlanLogic(%domain%)
    - App\Common\TraumFerienwohnungen\UpdateProductPricesTableService
    - App\Common\TraumFerienwohnungen\Services\ImagesService(%imageDestinationFolders%)
    - App\Common\Hubspot\Forms\HubspotContext(%production_domain%)
    - App\Common\KompasVillas\Importer\ProductImagesImporter(%domain%)
    - App\Common\IstriaHomes\Importers\ProductImagesImporter(%domain%)
    - App\Common\Company\CompanyPhoneNumber
    - App\Common\Company\CompanyAddressData
    - App\EventHandlers\Booking\HubspotBookingProcessComplete
    - App\EventHandlers\Hubspot\GeneratedOfferDealNameUpdater
    - App\Modules\Property\Components\FormWizardControl
    - App\Modules\Property\Components\GalleryControl
    - App\Modules\Property\Components\RestAmountToBePaidControl
    - App\Modules\Property\Components\ProductSeasonsControl
    - App\Modules\Property\Components\ProductDetailsFeaturesOptions
    - App\Modules\Property\Components\InvoiceControl
    - App\Common\Invoice
    - App\Common\SlugsUpdater\SlugsUpdater
    - App\Common\Services\ProductOwnerNotificationService(%secondMail%)
    - App\Common\Logic\PreReservationLogic
    - App\Common\Logic\ReservationValidatorLogic
    - App\EventHandlers\Product\AvailabilityRuleUpdater
    - App\EventHandlers\Product\AvailabilityStatusUpdater
    - App\EventHandlers\Product\AvailabilityPriceUpdater
    - App\EventHandlers\Product\AvailabilityOfferUpdater
    - App\EventHandlers\Product\AvailabilityUpdater
    - App\Common\EnvironmentDetector
    - App\Common\GtmCredentials
    - App\Common\EVisitor\EVisitorHelperService(config: %evisitor%)
    - App\Common\EVisitor\EVisitorService
    - App\Common\EVisitor\SyncEVisitorDataService
    - App\Common\EVisitor\Api\EVisitorApiServiceFactory(url: %evisitor.url%)
    - App\Common\Logger\ActionLog
    countriesFactory: App\Common\Countries\CountriesFactory
    -
        class: App\Common\Countries\Countries
        factory:  @countriesFactory::create()
    hubSpotFactory: App\Common\Hubspot\ClientFactory('********************************************')
    -
        class: App\Common\Hubspot\Client
        factory:  @hubSpotFactory::create()
    featuredHubSpotFactory: App\Common\Hubspot\FeaturedHubspotClientFactory('********************************************')
    -
        class: App\Common\Hubspot\FeaturedHubspotClient
        factory:  @featuredHubSpotFactory::create()
    elastica.client: Contributte\Elastica\Client(config: %elasticsearch%, logger: NULL)

decorator:
    App\Common\PropertyHub\BaseRequest:
        setup:
        - setCredentials(%propertyhub%)
        inject: true
    App\Common\TraumFerienwohnungen\Request\BaseRequest:
        setup:
        - setLogger(@monolog.logger.traum)
        inject: true
    App\Common\KompasVillas\Requests\BaseRequest:
        setup:
            - setCredentials(%kompas_villas%)
    App\Common\IstriaHomes\Requests\BaseRequest:
        setup:
            - setCredentials(%istria_homes%)
            - setEndpoints(%istria_homes%)
    App\Common\Helpers\BaseHelper:
        inject: true
    App\Common\GoogleCalendar\BaseService:
        inject: true
    App\Common\Sitemaps\Data:
        setup:
            - setCountries(%countriesMenu%)
    App\Common\Services\VillasListCsvFileGenerator:
        setup:
            - setCountries(%countriesMenu%)
    App\Presenters\BaseCmsPresenter:
        setup:
           - $imageDestinationFolders = %imageDestinationFolders%
    App\Modules\Admin\Presenters\BasePresenter:
        setup:
            - setMenu(filename: %appDir%/config/admin.menu.neon)
    App\Modules\CallCenter\Presenters\BasePresenter:
        setup:
            - setMenu(filename: %appDir%/config/callcenter.menu.neon)
    App\Modules\Front\Presenters\BasePresenter:
        setup:
            - setVpiMenu(filename: %appDir%/config/front.vpi.menu.neon)
    App\Modules\Front\Logic\BaseLogic:
        setup:
            - setTranslator
            - setUser
    App\Common\TextGenerator\BaseTextGenerator:
        setup:
            - setRepository
    App\EventHandlers\Product\BaseAvailabilityUpdater:
        setup:
            - setRepository
    App\Common\EnvironmentDetector:
        setup:
        - setProductionMode(%production_mode%)

    App\Common\GtmCredentials:
        setup:
        - setCredentials(%gtm%)
    App\Modules\Front\Presenters\PaymentPresenter:
    	inject: true
    	setup:
    		- setPaymentGatewayConfig(%shopData.paymentGatewayConfig%)
monolog:
    channel:
        default: # default channel is required
            handlers:
                - App\Common\Logger\Handlers\RotatingFileHandler(%logDir%)
                - App\Common\Logger\Handlers\ElasticSearchHandler(options: [type: logEvent])
            processors:
                - App\Common\Logger\Processors\Processor
        traum:
            handlers:
                - App\Common\Logger\Handlers\RotatingFileHandler(%logDir%)
                - App\Common\Logger\Handlers\ElasticSearchHandler(options: [type: logEvent])
            processors:
                - App\Common\Logger\Processors\Processor
entityGenerator:
    path: %appDir%/Models/Entities
    namespace: App\Models\Entities
    extends: App\Models\Entities\BaseTEntity
    generateGetters: false
    rewrite: true
    suffix: 'Entity'
    generateColumnConstant: true
    generateSetters: false
    generateProperties: true
    addDeclareStrictTypes: true
    strictlyTypedProperties: true
    propertyVisibility: public
    generatePhpDocProperties: false
    generateMapping: false
    primaryKeyConstant: null
    columnConstantPrefix: ''

includes:
    - property.menu.neon
    - queue.neon
    - avantio.neon
    - payment.neon
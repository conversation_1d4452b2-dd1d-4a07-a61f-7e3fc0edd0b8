parameters:
	avantio:
	    soap:
	        wsdl: %appDir%/Common/Avantio/wsdl/AvantioServiceV2.xml
	        options:
	            exceptions: true
	        credentials:
	            username: GAeyecayapek373
	            password: azareruqueje106
services:
    - App\Common\Avantio\AvantioClientFactory(config: %avantio.soap%)
    - App\Common\Avantio\AvantioService
    - App\Common\Avantio\AccommodationManager
    - App\Common\Avantio\GalleryManager
    - App\Common\Avantio\RateManager
    - App\Common\Avantio\ServiceManager
    - App\Common\Avantio\BookingManager

decorator:
    App\Common\Avantio\BaseAvantio:
        setup:
            - setConfig(%avantio.soap%)
            - setClientFactory
            - setLogger
            - setActionLog
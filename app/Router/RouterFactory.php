<?php declare(strict_types = 1);

namespace App\Router;

use App\Common\Translator;
use App\Modules\Front\Logic\VillasInRegionLogic;
use App\Repositories\AreasTranslationsRepository;
use App\Repositories\RouterRepository;
use Nette\Application\Routers\Route;
use Nette\Application\Routers\RouteList;
use Nette\Routing\Router;
use Nette\Security\User;

class RouterFactory
{

	private RouterRepository $repository;

	private Translator $translator;

	/**
	 * @var Router[]
	 */
	private array $additionalRouters = [];

	private VillasInRegionLogic $villasInRegionLogic;

	private AreasTranslationsRepository $areasTranslationsRepository;

	private User $user;

	public function __construct(
		RouterRepository $repository,
		Translator $translator,
		VillasInRegionLogic $villasInRegionLogic,
		AreasTranslationsRepository $areasTranslationsRepository,
		User $user
	)
	{
		$this->repository = $repository;
		$this->translator = clone $translator;
		$this->villasInRegionLogic = $villasInRegionLogic;
		$this->areasTranslationsRepository = $areasTranslationsRepository;
		$this->user = $user;
	}


	public function createRouter(): Router
	{
		$router = new RouteList();

		$router[] = new Route('offer/<language [a-z]{2}>/<salesRepUsername>/<offerName>', [
		'module' => 'Front',
		'language' => 'de',
		'presenter' => 'Offer',
		'action' => 'view',
		]);

		$router[] = new Route('sitemaps/listing-sitemap.xml', [
			'module' => 'Front',
			'presenter' => 'Sitemaps',
			'action' => 'listing',
		]);

		$router[] = new Route('sitemaps/static-pages-sitemap.xml', [
			'module' => 'Front',
			'presenter' => 'Sitemaps',
			'action' => 'static',
		]);

		$router[] = new Route('sitemaps/single-objects-sitemap.xml', [
			'module' => 'Front',
			'presenter' => 'Sitemaps',
			'action' => 'singleObject',
		]);

		$router[] = new SingleVillaRoute($this->repository, $this->translator, $this->user);

		$router[] = new Route('<language [a-z]{2}>/', [
			'module' => 'Front',
			'presenter' => 'Homepage',
			'language' => 'en',
			'action' => 'default',
		]);

		$router[] = new PageRoute($this->repository);

		$router[] = new VpiRoute($this->translator);

		$router[] = new VprRoute();

		$router[] = new RegionRouter($this->repository, $this->villasInRegionLogic);

		$router[] = new InterestsRouter($this->repository);

		$router[] = new DestinationRouter(
			$this->repository,
			$this->areasTranslationsRepository
		);

		$router[] = new Route('<language [a-z]{2}>/booking', [
			'module' => 'Front',
			'presenter' => 'Booking',
			'action' => 'default',
		]);

		$router[] = new Route('<language [a-z]{2}>/booking-confirmation', [
			'module' => 'Front',
			'presenter' => 'BookingConfirmation',
			'action' => 'default',
		]);

		foreach ($this->additionalRouters as $additionalRoute) {
			$router[] = $additionalRoute;
		}

		$router[] = new SearchResultRoute($this->translator);

		$router[] = new Route('<language [a-z]{2}>/searchresults/', [
		'module' => 'Front',
		'presenter' => 'SearchResults',
		'action' => 'results',
		]);

		$router[] = new Route('hr/kontakt', [
			'module' => 'Front',
			'presenter' => 'ContactUs',
			'action' => 'default',
			'language' => 'hr',
		]);

		$router[] = new Route('en/contact-us', [
			'module' => 'Front',
			'presenter' => 'ContactUs',
			'action' => 'default',
			'language' => 'en',
		]);

		$router[] = new Route('de/kontakt', [
			'module' => 'Front',
			'presenter' => 'ContactUs',
			'action' => 'default',
			'language' => 'de',
		]);

		$router[] = new Route('it/contattateci', [
			'module' => 'Front',
			'presenter' => 'ContactUs',
			'action' => 'default',
			'language' => 'it',
		]);

		$router[] = new Route('<language [a-z]{2}>/contact-us', [
			'module' => 'Front',
			'presenter' => 'ContactUs',
			'action' => 'default',
		]);

		$router[] = new Route('admin/<presenter>/<action>/<id>', [
			'module' => 'Admin',
			'presenter' => 'Product',
			'action' => 'default',
			'id' => NULL,
		]);

		$router[] = new Route('call-center/<language [a-z]{2}>/<presenter>/<action>/<search_id>', [
			'module' => 'CallCenter',
			'language' => 'hr',
			'presenter' => 'Sales',
			'action' => 'search',
			'search_id' => NULL,
		]);

		$router[] = new Route('<language [a-z]{2}>/contract-of-accommodation-unit-rental', [
			'module' => 'Front',
			'presenter' => 'ContractOfAccommodationUnitRental',
			'action' => 'default',
		]);

		$router[] = new Route('<language [a-z]{2}>/evisitor/<hash>', [
			'module' => 'Front',
			'presenter' => 'Evisitor',
			'action' => 'default',
		]);

		$router[] = new Route('<language [a-z]{2}>/evisitor/', [
			'module' => 'Front',
			'presenter' => 'Evisitor',
			'action' => 'default',
		], Route::ONE_WAY);

		$router[] = new Route('confirm-evisitor-data', [
			'module' => 'Front',
			'presenter' => 'Evisitor',
			'action' => 'confirmEvisitorData',
		]);

		$router[] = new Route('<language [a-z]{2}>/<module>/<presenter>/<action>', [
			'module' => 'Front',
			'language' => 'hr',
			'presenter' => 'Homepage',
			'action' => 'default',
		]);

		$router[] = new Route('property/<presenter>/<action>/<id>', [
			'module' => 'Property',
			'presenter' => 'Default',
			'action' => 'default',
			'id' => NULL,
		]);

		return $router;
	}

	public function addRouter(Router $router): void
	{
		$this->additionalRouters[] = $router;
	}

}

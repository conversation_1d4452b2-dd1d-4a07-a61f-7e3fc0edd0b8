<?php declare(strict_types = 1);

namespace App\Repositories;

use App\Common\Enums\PartnersEnum;
use App\Models\Entities\ForeignReservationEntity;
use Dibi\Fluent;

/**
 * @method ForeignReservationEntity|null getBy(array $conditions, ?string $orderBy = NULL)
 */
class ForeignReservationsRepository extends AuditedRepository
{

	/**
	 * @var string
	 */
	protected $table = ForeignReservationEntity::TABLE_NAME;

	protected $entityType = ForeignReservationEntity::class;

	public function getSourceList(): Fluent
	{
		return $this->db->select('r.id, r.product_id, r.booking_language, r.board, r.booking_date, r.arrive, r.departure, 
		    r.web, r.booking_type, r.payment_method, r.adults_number, r.child1_age, r.child2_age, r.child3_age, r.child4_age, 
		    r.child5_age, r.child6_age, r.name, r.surname, r.dni, r.address, r.locality, r.post_code, r.city, r.iso_country_code, r.country, 
		    r.telephone, r.telephone2, r.email, r.fax, r.`language`, r.fiscal_code, r.check_in_done, r.check_in_schedule, r.check_out_schedule, 
		    r.creation_date, r.last_modified_date, r.booking_code, r.localizator, r.agent_localizator,
		    r.total_price, r.rental_price, r.currency, r.accommodation_code, r.comments, r.comments_date, 
		    r.reservation_id, r.updated, r.updated_by, r.created, r.created_by,
		    r.rental_price as amount,
		    r.adults_number as adults,
		    SUM((r.child1_age IS NOT NULL AND r.child1_age > 0) +
        	(r.child2_age IS NOT NULL AND r.child2_age > 0) +
        	(r.child3_age IS NOT NULL AND r.child3_age > 0) +
        	(r.child4_age IS NOT NULL AND r.child4_age > 0) +
        	(r.child5_age IS NOT NULL AND r.child5_age > 0) +
        	(r.child6_age IS NOT NULL AND r.child6_age > 0)) as children,
		    r.booking_code as reservation_code,
		    r.comments as message_from_guests,
		    (select MAX(approved) from foreign_reservations fr where fr.booking_code = r.booking_code) as approved,
            (select MAX(declined) from foreign_reservations fr where fr.booking_code = r.booking_code) as declined,
			(select concat("https://villas-guide.com", replace(path, "1024", "400"), internal_filename) FROM pictures pi where pi.product_id = p.id AND pi.active = 1 LIMIT 1) as picture_path')
			->select('p.name as product_name')
			->from('(SELECT *, ROW_NUMBER() OVER (PARTITION BY booking_code ORDER BY created DESC) AS row_num FROM foreign_reservations)')->as('r')
			->join('products')->as('p')->on('p.id = r.product_id')
			->where('p.partner = %s', PartnersEnum::PRIVATE)
			->where('p.is_deleted = 0')
			->where('r.row_num = 1')
			->groupBy('r.booking_code');
	}

	public function reservationExists(string $bookingCode, int $productId, \DateTimeInterface $lastModifiedDate): bool
	{
		$query = $this->db->select('count(*)')
			->from($this->table)
			->where('booking_code = %i and product_id = %i', $bookingCode, $productId)
			->where('last_modified_date = %dt', $lastModifiedDate);

		return (bool) $query->fetchSingle();
	}

	public function getReservationIdForReservationCode(string $bookingCode, int $productId): ?int
	{
		return $this->db->select('reservation_id')
			->from($this->table)
			->where('booking_code = %i and product_id = %i', $bookingCode, $productId)
			->where('approved = %b AND reservation_id is not null', true)
			->fetchSingle();
	}

}

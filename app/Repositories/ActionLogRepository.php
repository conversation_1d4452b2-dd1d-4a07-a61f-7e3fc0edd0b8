<?php declare(strict_types = 1);

namespace App\Repositories;

use App\Common\Enums\ActionLogDbTableEnum;
use App\Models\Entities\ActionLogEntity;
use Dibi\Fluent;

class ActionLogRepository extends AuditedRepository
{

	/**
	 * @var string
	 */
	protected $table = 'action_logs';

	protected $entityType = ActionLogEntity::class;

	public function getActionLogsForTable(string $dbTable, int $dbTableId): Fluent
	{
		return $this->db->select('al.*')
			->from($this->table)->as('al')
			->where('db_table = %s', $dbTable)
			->where('db_table_id = %i', $dbTableId)
			->groupBy('al.id')
			->orderBy('al.created DESC');
	}
	public function getActionLogsForFullReservation(?int $foreignReservationId, ?int $reservationId): Fluent
	{
		$fluent = $this->db->select('al.*')
			->from($this->table)->as('al');

		if ($foreignReservationId === null) {
			$fluent->where('db_table = %s AND db_table_id = %i', ActionLogDbTableEnum::RESERVATIONS, $reservationId);
		} else {
			$fluent->where('(db_table = %s AND db_table_id = %i)', ActionLogDbTableEnum::FOREIGN_RESERVATIONS, $foreignReservationId)
				->or('(db_table = %s AND db_table_id = %i)', ActionLogDbTableEnum::RESERVATIONS, $reservationId);
		}

		$fluent
			->groupBy('al.id')
			->orderBy('al.created DESC');

		return $fluent;
	}

}

# Villas-guide

[![build](https://github.com/gaveia/villas-guide/workflows/build/badge.svg)](https://github.com/gaveia/villas-guide/actions?query=workflow%3Abuild)

site URL: https://villas-guide.com

staging URL: https://staging.villas-guide.com

## Project Setup

### Prerequisites
- Git
- Docker and Docker Compose
- Node.js/npm

### Installation Steps

1. Clone the repository
   ```
   <NAME_EMAIL>:gaveia/villas-guide.git
   cd villas-guide
   ```

2. Create required directories
   ```
   mkdir -m 777 log
   ```

3. Configure the application
   ```
   cp app/config/config.local.template.neon app/config/config.local.neon
   ```

4. Add local domain to hosts file
   ```
   # Add to /etc/hosts
   127.0.0.1 villas-guide.local
   ```

5. Start Docker containers
   ```
   docker compose up -d
   ```

6. Install dependencies and build assets
   ```
   # Install PHP dependencies
   docker compose exec vg_php composer install

   # Install and build frontend assets
   docker compose exec vg_php bash -c "cd www && yarn install && npx gulp js-compile-all && npx gulp css-clean"
   ```

7. Access the site at http://villas-guide.local

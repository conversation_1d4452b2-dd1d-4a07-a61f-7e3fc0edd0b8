server {
    root /var/www/villas-guide/www;

    include       mime.types;
    default_type  application/octet-stream;
    listen 80 default;
    server_name  villas-guide.local;

    client_max_body_size 108M;
    sendfile        on;
    keepalive_timeout  650;
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    index  index.php;

#    location ~* \.(?:css|js|jpg|jpeg|gif|png|woff|ttf|eot|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
#        expires 365d;
#        add_header Pragma public;
#        add_header Cache-Control "public";
#    }

        # Matches any URL containing /wp-content/uploads/
        location ~ "^(.*)/villas_images/(.*)$" {
            try_files $uri @prod_serv;
        }

        location ~ "^(.*)/images/(.*)$" {
        	try_files $uri @prod_serv;
        }

        # Will redirect requests to your production server
        location @prod_serv {
            rewrite "^(.*)/villas_images/(.*)$" "https://villas-guide.com/villas_images/$2" redirect;
            rewrite "^(.*)/images/(.*)$" "https://villas-guide.com/images/$2" redirect;
        }

        location ~ ^/images/(.*) {
          rewrite ^/images/(.*)$ http://villas-guide.com/images/$1 redirect;
        }

        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_pass vg_php:9000;
            fastcgi_index  index.php;
            fastcgi_param  SCRIPT_FILENAME	$document_root$fastcgi_script_name;
            include        fastcgi_params;
        }


		location / {
			try_files $uri $uri/ /index.php$is_args$args;
		}


}

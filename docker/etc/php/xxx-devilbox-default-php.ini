; ############################################################
; # Devilbox PHP defaults for 8.0-work
; ############################################################

; Each PHP flavour (base, mods, prod, work) might have its own php.ini.
; If none is present, the one from the previous flavour is inherited.


[PHP]

; Memory
; Note: "memory_limit" should be larger than "post_max_size"
memory_limit              = 512M


; Timeouts
max_execution_time        = 120
max_input_time            = 120


; Uploads
; Note: "post_max_size" should be greater than "upload_max_filesize"
post_max_size             = 72M
upload_max_filesize       = 64M
max_file_uploads          = 20


; Vars
variables_order           = EGPCS
max_input_vars            = 8000
max_input_nesting_level   = 64


; Error reporting
; Note: error_log is dynamic and handled during start to set appropriate setting
error_reporting           = E_ALL | E_NOTICE | E_STRICT | E_DEPRECATED
xmlrpc_errors             = Off
report_memleaks           = On
display_errors            = On
display_startup_errors    = On
log_errors                = On
html_errors               = On


; Xdebug settings
xdebug.mode               = debug
xdebug.start_with_request = default
xdebug.client_port        = 9000
xdebug.idekey             = PHPSTORM
xdebug.client_host        = **********
